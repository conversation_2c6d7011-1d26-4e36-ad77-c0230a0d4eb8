{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@primeuix/themes": "^1.1.1", "@primevue/forms": "^4.3.5", "@tailwindcss/vite": "^4.1.8", "nuxt": "^3.17.5", "primevue": "^4.3.5", "tailwindcss": "^4.1.8", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@primevue/nuxt-module": "^4.3.5"}}