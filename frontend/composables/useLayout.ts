/**
 * Layout composable for LapXpert Admin Dashboard
 * Manages layout state, theme switching, and responsive behavior
 */

export interface LayoutConfig {
  preset: string
  primary: string
  surface: string | null
  darkTheme: boolean
  menuMode: 'static' | 'overlay'
}

export interface LayoutState {
  staticMenuDesktopInactive: boolean
  overlayMenuActive: boolean
  profileSidebarVisible: boolean
  configSidebarVisible: boolean
  staticMenuMobileActive: boolean
  menuHoverActive: boolean
  activeMenuItem: any
}

const layoutConfig = reactive<LayoutConfig>({
  preset: 'Aura',
  primary: 'emerald',
  surface: null,
  darkTheme: false,
  menuMode: 'static'
})

const layoutState = reactive<LayoutState>({
  staticMenuDesktopInactive: false,
  overlayMenuActive: false,
  profileSidebarVisible: false,
  configSidebarVisible: false,
  staticMenuMobileActive: false,
  menuHoverActive: false,
  activeMenuItem: null
})

export const useLayout = () => {
  const setActiveMenuItem = (item: any) => {
    layoutState.activeMenuItem = item.value || item
  }

  const toggleMenu = () => {
    if (process.client) {
      if (window.innerWidth > 991) {
        layoutState.staticMenuDesktopInactive = !layoutState.staticMenuDesktopInactive
      } else {
        layoutState.staticMenuMobileActive = !layoutState.staticMenuMobileActive
      }
    }
  }

  const toggleDarkMode = () => {
    if (!process.client) return

    if (!document.startViewTransition) {
      executeDarkModeToggle()
      return
    }

    document.startViewTransition(() => executeDarkModeToggle())
  }

  const executeDarkModeToggle = () => {
    layoutConfig.darkTheme = !layoutConfig.darkTheme
    
    if (process.client) {
      document.documentElement.classList.toggle('app-dark')
      
      // Store theme preference
      localStorage.setItem('theme', layoutConfig.darkTheme ? 'dark' : 'light')
    }
  }

  const initializeTheme = () => {
    if (!process.client) return

    // Check for stored theme preference or system preference
    const storedTheme = localStorage.getItem('theme')
    const systemDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches

    const shouldUseDarkMode = storedTheme 
      ? storedTheme === 'dark'
      : systemDarkMode

    if (shouldUseDarkMode !== layoutConfig.darkTheme) {
      layoutConfig.darkTheme = shouldUseDarkMode
      document.documentElement.classList.toggle('app-dark', shouldUseDarkMode)
    }
  }

  const onMenuToggle = () => {
    if (layoutConfig.menuMode === 'overlay') {
      layoutState.overlayMenuActive = !layoutState.overlayMenuActive
    }

    if (process.client && window.innerWidth > 991) {
      layoutState.staticMenuDesktopInactive = !layoutState.staticMenuDesktopInactive
    } else {
      layoutState.staticMenuMobileActive = !layoutState.staticMenuMobileActive
    }
  }

  const resetMenu = () => {
    layoutState.overlayMenuActive = false
    layoutState.staticMenuMobileActive = false
    layoutState.menuHoverActive = false
  }

  const isSidebarActive = computed(() => {
    return layoutState.overlayMenuActive || layoutState.staticMenuMobileActive
  })

  const isDarkTheme = computed(() => layoutConfig.darkTheme)

  const isDesktop = computed(() => {
    if (!process.client) return true
    return window.innerWidth > 991
  })

  const isMobile = computed(() => {
    if (!process.client) return false
    return window.innerWidth <= 991
  })

  // Initialize theme on client side
  onMounted(() => {
    initializeTheme()
    
    // Listen for system theme changes
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          layoutConfig.darkTheme = e.matches
          document.documentElement.classList.toggle('app-dark', e.matches)
        }
      })
    }

    // Handle window resize
    const handleResize = () => {
      if (window.innerWidth > 991) {
        layoutState.staticMenuMobileActive = false
        layoutState.overlayMenuActive = false
      }
    }

    window.addEventListener('resize', handleResize)

    // Cleanup
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
    })
  })

  return {
    layoutConfig: readonly(layoutConfig),
    layoutState: readonly(layoutState),
    setActiveMenuItem,
    toggleMenu,
    toggleDarkMode,
    onMenuToggle,
    resetMenu,
    initializeTheme,
    isSidebarActive,
    isDarkTheme,
    isDesktop,
    isMobile
  }
}
