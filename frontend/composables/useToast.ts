/**
 * Toast composable for LapXpert Admin Dashboard
 * Provides a unified interface for toast notifications
 */

export interface ToastMessage {
  severity?: 'success' | 'info' | 'warn' | 'error'
  summary?: string
  detail?: string
  life?: number
  closable?: boolean
  group?: string
}

export const useToast = () => {
  const toast = useNuxtApp().$toast

  const add = (message: ToastMessage) => {
    if (toast) {
      toast.add({
        severity: 'info',
        life: 3000,
        closable: true,
        ...message
      })
    }
  }

  const success = (detail: string, summary: string = 'Thành công') => {
    add({
      severity: 'success',
      summary,
      detail,
      life: 3000
    })
  }

  const error = (detail: string, summary: string = 'Lỗi') => {
    add({
      severity: 'error',
      summary,
      detail,
      life: 5000
    })
  }

  const warn = (detail: string, summary: string = 'Cảnh báo') => {
    add({
      severity: 'warn',
      summary,
      detail,
      life: 4000
    })
  }

  const info = (detail: string, summary: string = 'Thông tin') => {
    add({
      severity: 'info',
      summary,
      detail,
      life: 3000
    })
  }

  const clear = (group?: string) => {
    if (toast) {
      toast.removeGroup(group)
    }
  }

  const clearAll = () => {
    if (toast) {
      toast.removeAllGroups()
    }
  }

  return {
    add,
    success,
    error,
    warn,
    info,
    clear,
    clearAll
  }
}
