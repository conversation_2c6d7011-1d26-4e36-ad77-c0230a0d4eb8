{"version": 3, "file": "index.mjs", "sources": ["../../src/tabmenu/BaseTabMenu.vue", "../../src/tabmenu/TabMenu.vue", "../../src/tabmenu/TabMenu.vue?vue&type=template&id=c9708f4e&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TabMenuStyle from 'primevue/tabmenu/style';\n\nexport default {\n    name: 'BaseTabMenu',\n    extends: BaseComponent,\n    props: {\n        model: {\n            type: Array,\n            default: null\n        },\n        activeIndex: {\n            type: Number,\n            default: 0\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        }\n    },\n    style: TabMenuStyle,\n    provide() {\n        return {\n            $pcTabMenu: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <ul ref=\"nav\" :class=\"cx('tablist')\" role=\"menubar\" :aria-labelledby=\"ariaLabelledby\" :aria-label=\"ariaLabel\" v-bind=\"ptm('tablist')\">\n            <template v-for=\"(item, i) of model\" :key=\"label(item) + '_' + i.toString()\">\n                <li\n                    v-if=\"visible(item)\"\n                    ref=\"tab\"\n                    :class=\"[cx('item', { item, index: i }), item.class]\"\n                    role=\"presentation\"\n                    @click=\"onItemClick($event, item, i)\"\n                    @keydown=\"onKeydownItem($event, item, i)\"\n                    v-bind=\"getPTOptions('item', item, i)\"\n                    :data-p-active=\"d_activeIndex === i\"\n                    :data-p-disabled=\"disabled(item)\"\n                >\n                    <template v-if=\"!$slots.item\">\n                        <a ref=\"tabLink\" v-ripple role=\"menuitem\" :href=\"item.url\" :class=\"cx('itemLink')\" :target=\"item.target\" :aria-label=\"label(item)\" :aria-disabled=\"disabled(item)\" :tabindex=\"-1\" v-bind=\"getPTOptions('itemLink', item, i)\">\n                            <component v-if=\"$slots.itemicon\" :is=\"$slots.itemicon\" :item=\"item\" :class=\"cx('itemIcon')\" />\n                            <span v-else-if=\"item.icon\" :class=\"[cx('itemIcon'), item.icon]\" v-bind=\"getPTOptions('itemIcon', item, i)\" />\n                            <span :class=\"cx('itemLabel')\" v-bind=\"getPTOptions('itemLabel', item, i)\">{{ label(item) }}</span>\n                        </a>\n                    </template>\n                    <component v-else :is=\"$slots.item\" :item=\"item\" :index=\"i\" :active=\"i === d_activeIndex\" :label=\"label(item)\" :props=\"getMenuItemProps(item, i)\"></component>\n                </li>\n            </template>\n            <li ref=\"inkbar\" role=\"none\" :class=\"cx('activeBar')\" v-bind=\"ptm('activeBar')\"></li>\n        </ul>\n    </div>\n</template>\n\n<script>\nimport { getAttribute, find, findSingle, getWidth, getOffset } from '@primeuix/utils/dom';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\nimport BaseTabMenu from './BaseTabMenu.vue';\n\nexport default {\n    name: 'TabMenu',\n    extends: BaseTabMenu,\n    inheritAttrs: false,\n    emits: ['update:activeIndex', 'tab-change'],\n    data() {\n        return {\n            d_activeIndex: this.activeIndex\n        };\n    },\n    watch: {\n        activeIndex: {\n            flush: 'post',\n            handler(newValue) {\n                this.d_activeIndex = newValue;\n                this.updateInkBar();\n            }\n        }\n    },\n    mounted() {\n        this.$nextTick(() => {\n            this.updateInkBar();\n        });\n\n        const activeItem = this.findActiveItem();\n\n        activeItem && (activeItem.tabIndex = '0');\n    },\n    updated() {\n        this.updateInkBar();\n    },\n    methods: {\n        getPTOptions(key, item, index) {\n            return this.ptm(key, {\n                context: {\n                    item,\n                    index\n                }\n            });\n        },\n        onItemClick(event, item, index) {\n            if (this.disabled(item)) {\n                event.preventDefault();\n\n                return;\n            }\n\n            if (item.command) {\n                item.command({\n                    originalEvent: event,\n                    item: item\n                });\n            }\n\n            if (index !== this.d_activeIndex) {\n                this.d_activeIndex = index;\n                this.$emit('update:activeIndex', this.d_activeIndex);\n            }\n\n            this.$emit('tab-change', {\n                originalEvent: event,\n                index: index\n            });\n        },\n        onKeydownItem(event, item, index) {\n            switch (event.code) {\n                case 'ArrowRight': {\n                    this.navigateToNextItem(event.target);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowLeft': {\n                    this.navigateToPrevItem(event.target);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'Home': {\n                    this.navigateToFirstItem(event.target);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'End': {\n                    this.navigateToLastItem(event.target);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'Space':\n                case 'NumpadEnter':\n\n                case 'Enter': {\n                    this.onItemClick(event, item, index);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'Tab': {\n                    this.onTabKey();\n                    break;\n                }\n\n                default:\n                    break;\n            }\n        },\n        navigateToNextItem(target) {\n            const nextItem = this.findNextItem(target);\n\n            nextItem && this.setFocusToMenuitem(target, nextItem);\n        },\n        navigateToPrevItem(target) {\n            const prevItem = this.findPrevItem(target);\n\n            prevItem && this.setFocusToMenuitem(target, prevItem);\n        },\n        navigateToFirstItem(target) {\n            const firstItem = this.findFirstItem(target);\n\n            firstItem && this.setFocusToMenuitem(target, firstItem);\n        },\n        navigateToLastItem(target) {\n            const lastItem = this.findLastItem(target);\n\n            lastItem && this.setFocusToMenuitem(target, lastItem);\n        },\n        findNextItem(item) {\n            const nextItem = item.parentElement.nextElementSibling;\n\n            return nextItem ? (getAttribute(nextItem, 'data-p-disabled') === true ? this.findNextItem(nextItem.children[0]) : nextItem.children[0]) : null;\n        },\n        findPrevItem(item) {\n            const prevItem = item.parentElement.previousElementSibling;\n\n            return prevItem ? (getAttribute(prevItem, 'data-p-disabled') === true ? this.findPrevItem(prevItem.children[0]) : prevItem.children[0]) : null;\n        },\n        findFirstItem() {\n            const firstSibling = findSingle(this.$refs.nav, '[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n\n            return firstSibling ? firstSibling.children[0] : null;\n        },\n        findLastItem() {\n            const siblings = find(this.$refs.nav, '[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n\n            return siblings ? siblings[siblings.length - 1].children[0] : null;\n        },\n        findActiveItem() {\n            const activeItem = findSingle(this.$refs.nav, '[data-pc-section=\"item\"][data-p-disabled=\"false\"][data-p-active=\"true\"]');\n\n            return activeItem ? activeItem.children[0] : null;\n        },\n        setFocusToMenuitem(target, focusableItem) {\n            target.tabIndex = '-1';\n            focusableItem.tabIndex = '0';\n            focusableItem.focus();\n        },\n        onTabKey() {\n            const activeItem = findSingle(this.$refs.nav, '[data-pc-section=\"item\"][data-p-disabled=\"false\"][data-p-active=\"true\"]');\n            const focusedItem = findSingle(this.$refs.nav, '[data-pc-section=\"itemlink\"][tabindex=\"0\"]');\n\n            if (focusedItem !== activeItem.children[0]) {\n                activeItem && (activeItem.children[0].tabIndex = '0');\n                focusedItem.tabIndex = '-1';\n            }\n        },\n        visible(item) {\n            return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n        },\n        disabled(item) {\n            return typeof item.disabled === 'function' ? item.disabled() : item.disabled === true;\n        },\n        label(item) {\n            return typeof item.label === 'function' ? item.label() : item.label;\n        },\n        updateInkBar() {\n            let tabs = this.$refs.nav.children;\n            let inkHighlighted = false;\n\n            for (let i = 0; i < tabs.length; i++) {\n                let tab = tabs[i];\n\n                if (getAttribute(tab, 'data-p-active')) {\n                    this.$refs.inkbar.style.width = getWidth(tab) + 'px';\n                    this.$refs.inkbar.style.left = getOffset(tab).left - getOffset(this.$refs.nav).left + 'px';\n                    inkHighlighted = true;\n                }\n            }\n\n            if (!inkHighlighted) {\n                this.$refs.inkbar.style.width = '0px';\n                this.$refs.inkbar.style.left = '0px';\n            }\n        },\n        getMenuItemProps(item, index) {\n            return {\n                action: mergeProps(\n                    {\n                        class: this.cx('itemLink'),\n                        tabindex: -1,\n                        onClick: ($event) => this.onItemClick($event, item, index),\n                        onKeyDown: ($event) => this.onKeydownItem($event, item, index)\n                    },\n                    this.getPTOptions('itemLink', item, index)\n                ),\n                icon: mergeProps(\n                    {\n                        class: [this.cx('itemIcon'), item.icon]\n                    },\n                    this.getPTOptions('itemIcon', item, index)\n                ),\n                label: mergeProps(\n                    {\n                        class: this.cx('itemLabel')\n                    },\n                    this.getPTOptions('itemLabel', item, index)\n                )\n            };\n        }\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <ul ref=\"nav\" :class=\"cx('tablist')\" role=\"menubar\" :aria-labelledby=\"ariaLabelledby\" :aria-label=\"ariaLabel\" v-bind=\"ptm('tablist')\">\n            <template v-for=\"(item, i) of model\" :key=\"label(item) + '_' + i.toString()\">\n                <li\n                    v-if=\"visible(item)\"\n                    ref=\"tab\"\n                    :class=\"[cx('item', { item, index: i }), item.class]\"\n                    role=\"presentation\"\n                    @click=\"onItemClick($event, item, i)\"\n                    @keydown=\"onKeydownItem($event, item, i)\"\n                    v-bind=\"getPTOptions('item', item, i)\"\n                    :data-p-active=\"d_activeIndex === i\"\n                    :data-p-disabled=\"disabled(item)\"\n                >\n                    <template v-if=\"!$slots.item\">\n                        <a ref=\"tabLink\" v-ripple role=\"menuitem\" :href=\"item.url\" :class=\"cx('itemLink')\" :target=\"item.target\" :aria-label=\"label(item)\" :aria-disabled=\"disabled(item)\" :tabindex=\"-1\" v-bind=\"getPTOptions('itemLink', item, i)\">\n                            <component v-if=\"$slots.itemicon\" :is=\"$slots.itemicon\" :item=\"item\" :class=\"cx('itemIcon')\" />\n                            <span v-else-if=\"item.icon\" :class=\"[cx('itemIcon'), item.icon]\" v-bind=\"getPTOptions('itemIcon', item, i)\" />\n                            <span :class=\"cx('itemLabel')\" v-bind=\"getPTOptions('itemLabel', item, i)\">{{ label(item) }}</span>\n                        </a>\n                    </template>\n                    <component v-else :is=\"$slots.item\" :item=\"item\" :index=\"i\" :active=\"i === d_activeIndex\" :label=\"label(item)\" :props=\"getMenuItemProps(item, i)\"></component>\n                </li>\n            </template>\n            <li ref=\"inkbar\" role=\"none\" :class=\"cx('activeBar')\" v-bind=\"ptm('activeBar')\"></li>\n        </ul>\n    </div>\n</template>\n\n<script>\nimport { getAttribute, find, findSingle, getWidth, getOffset } from '@primeuix/utils/dom';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\nimport BaseTabMenu from './BaseTabMenu.vue';\n\nexport default {\n    name: 'TabMenu',\n    extends: BaseTabMenu,\n    inheritAttrs: false,\n    emits: ['update:activeIndex', 'tab-change'],\n    data() {\n        return {\n            d_activeIndex: this.activeIndex\n        };\n    },\n    watch: {\n        activeIndex: {\n            flush: 'post',\n            handler(newValue) {\n                this.d_activeIndex = newValue;\n                this.updateInkBar();\n            }\n        }\n    },\n    mounted() {\n        this.$nextTick(() => {\n            this.updateInkBar();\n        });\n\n        const activeItem = this.findActiveItem();\n\n        activeItem && (activeItem.tabIndex = '0');\n    },\n    updated() {\n        this.updateInkBar();\n    },\n    methods: {\n        getPTOptions(key, item, index) {\n            return this.ptm(key, {\n                context: {\n                    item,\n                    index\n                }\n            });\n        },\n        onItemClick(event, item, index) {\n            if (this.disabled(item)) {\n                event.preventDefault();\n\n                return;\n            }\n\n            if (item.command) {\n                item.command({\n                    originalEvent: event,\n                    item: item\n                });\n            }\n\n            if (index !== this.d_activeIndex) {\n                this.d_activeIndex = index;\n                this.$emit('update:activeIndex', this.d_activeIndex);\n            }\n\n            this.$emit('tab-change', {\n                originalEvent: event,\n                index: index\n            });\n        },\n        onKeydownItem(event, item, index) {\n            switch (event.code) {\n                case 'ArrowRight': {\n                    this.navigateToNextItem(event.target);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowLeft': {\n                    this.navigateToPrevItem(event.target);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'Home': {\n                    this.navigateToFirstItem(event.target);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'End': {\n                    this.navigateToLastItem(event.target);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'Space':\n                case 'NumpadEnter':\n\n                case 'Enter': {\n                    this.onItemClick(event, item, index);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'Tab': {\n                    this.onTabKey();\n                    break;\n                }\n\n                default:\n                    break;\n            }\n        },\n        navigateToNextItem(target) {\n            const nextItem = this.findNextItem(target);\n\n            nextItem && this.setFocusToMenuitem(target, nextItem);\n        },\n        navigateToPrevItem(target) {\n            const prevItem = this.findPrevItem(target);\n\n            prevItem && this.setFocusToMenuitem(target, prevItem);\n        },\n        navigateToFirstItem(target) {\n            const firstItem = this.findFirstItem(target);\n\n            firstItem && this.setFocusToMenuitem(target, firstItem);\n        },\n        navigateToLastItem(target) {\n            const lastItem = this.findLastItem(target);\n\n            lastItem && this.setFocusToMenuitem(target, lastItem);\n        },\n        findNextItem(item) {\n            const nextItem = item.parentElement.nextElementSibling;\n\n            return nextItem ? (getAttribute(nextItem, 'data-p-disabled') === true ? this.findNextItem(nextItem.children[0]) : nextItem.children[0]) : null;\n        },\n        findPrevItem(item) {\n            const prevItem = item.parentElement.previousElementSibling;\n\n            return prevItem ? (getAttribute(prevItem, 'data-p-disabled') === true ? this.findPrevItem(prevItem.children[0]) : prevItem.children[0]) : null;\n        },\n        findFirstItem() {\n            const firstSibling = findSingle(this.$refs.nav, '[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n\n            return firstSibling ? firstSibling.children[0] : null;\n        },\n        findLastItem() {\n            const siblings = find(this.$refs.nav, '[data-pc-section=\"item\"][data-p-disabled=\"false\"]');\n\n            return siblings ? siblings[siblings.length - 1].children[0] : null;\n        },\n        findActiveItem() {\n            const activeItem = findSingle(this.$refs.nav, '[data-pc-section=\"item\"][data-p-disabled=\"false\"][data-p-active=\"true\"]');\n\n            return activeItem ? activeItem.children[0] : null;\n        },\n        setFocusToMenuitem(target, focusableItem) {\n            target.tabIndex = '-1';\n            focusableItem.tabIndex = '0';\n            focusableItem.focus();\n        },\n        onTabKey() {\n            const activeItem = findSingle(this.$refs.nav, '[data-pc-section=\"item\"][data-p-disabled=\"false\"][data-p-active=\"true\"]');\n            const focusedItem = findSingle(this.$refs.nav, '[data-pc-section=\"itemlink\"][tabindex=\"0\"]');\n\n            if (focusedItem !== activeItem.children[0]) {\n                activeItem && (activeItem.children[0].tabIndex = '0');\n                focusedItem.tabIndex = '-1';\n            }\n        },\n        visible(item) {\n            return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n        },\n        disabled(item) {\n            return typeof item.disabled === 'function' ? item.disabled() : item.disabled === true;\n        },\n        label(item) {\n            return typeof item.label === 'function' ? item.label() : item.label;\n        },\n        updateInkBar() {\n            let tabs = this.$refs.nav.children;\n            let inkHighlighted = false;\n\n            for (let i = 0; i < tabs.length; i++) {\n                let tab = tabs[i];\n\n                if (getAttribute(tab, 'data-p-active')) {\n                    this.$refs.inkbar.style.width = getWidth(tab) + 'px';\n                    this.$refs.inkbar.style.left = getOffset(tab).left - getOffset(this.$refs.nav).left + 'px';\n                    inkHighlighted = true;\n                }\n            }\n\n            if (!inkHighlighted) {\n                this.$refs.inkbar.style.width = '0px';\n                this.$refs.inkbar.style.left = '0px';\n            }\n        },\n        getMenuItemProps(item, index) {\n            return {\n                action: mergeProps(\n                    {\n                        class: this.cx('itemLink'),\n                        tabindex: -1,\n                        onClick: ($event) => this.onItemClick($event, item, index),\n                        onKeyDown: ($event) => this.onKeydownItem($event, item, index)\n                    },\n                    this.getPTOptions('itemLink', item, index)\n                ),\n                icon: mergeProps(\n                    {\n                        class: [this.cx('itemIcon'), item.icon]\n                    },\n                    this.getPTOptions('itemIcon', item, index)\n                ),\n                label: mergeProps(\n                    {\n                        class: this.cx('itemLabel')\n                    },\n                    this.getPTOptions('itemLabel', item, index)\n                )\n            };\n        }\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "model", "type", "Array", "activeIndex", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "String", "aria<PERSON><PERSON><PERSON>", "style", "TabMenuStyle", "provide", "$pcTabMenu", "$parentInstance", "BaseTabMenu", "inheritAttrs", "emits", "data", "d_activeIndex", "watch", "flush", "handler", "newValue", "updateInkBar", "mounted", "_this", "$nextTick", "activeItem", "findActiveItem", "tabIndex", "updated", "methods", "getPTOptions", "key", "item", "index", "ptm", "context", "onItemClick", "event", "disabled", "preventDefault", "command", "originalEvent", "$emit", "onKeydownItem", "code", "navigateToNextItem", "target", "navigateToPrevItem", "navigateToFirstItem", "navigateToLastItem", "onTabKey", "nextItem", "findNextItem", "setFocusToMenuitem", "prevItem", "findPrevItem", "firstItem", "findFirstItem", "lastItem", "findLastItem", "parentElement", "nextElement<PERSON><PERSON>ling", "getAttribute", "children", "previousElementSibling", "firstSibling", "findSingle", "$refs", "nav", "siblings", "find", "length", "focusableItem", "focus", "focusedItem", "visible", "label", "tabs", "inkHighlighted", "i", "tab", "inkbar", "width", "getWidth", "left", "getOffset", "getMenuItemProps", "_this2", "action", "mergeProps", "cx", "tabindex", "onClick", "$event", "onKeyDown", "icon", "directives", "ripple", "<PERSON><PERSON><PERSON>", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "ptmi", "_createElementVNode", "ref", "role", "_Fragment", "_renderList", "$options", "toString", "onKeydown", "$data", "$slots", "_withDirectives", "href", "url", "itemicon", "_createBlock", "_resolveDynamicComponent", "ref_for", "_toDisplayString", "active"], "mappings": ";;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,aAAa;AACnB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAEC,KAAK;MACX,SAAS,EAAA;KACZ;AACDC,IAAAA,WAAW,EAAE;AACTF,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,cAAc,EAAE;AACZJ,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,SAAS,EAAE;AACPN,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDE,EAAAA,KAAK,EAAEC,YAAY;EACnBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,UAAU,EAAE,IAAI;AAChBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACID,aAAe;AACXf,EAAAA,IAAI,EAAE,SAAS;AACf,EAAA,SAAA,EAASgB,QAAW;AACpBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,KAAK,EAAE,CAAC,oBAAoB,EAAE,YAAY,CAAC;EAC3CC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;MACHC,aAAa,EAAE,IAAI,CAACd;KACvB;GACJ;AACDe,EAAAA,KAAK,EAAE;AACHf,IAAAA,WAAW,EAAE;AACTgB,MAAAA,KAAK,EAAE,MAAM;AACbC,MAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,QAAQ,EAAE;QACd,IAAI,CAACJ,aAAY,GAAII,QAAQ;QAC7B,IAAI,CAACC,YAAY,EAAE;AACvB;AACJ;GACH;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AAAA,IAAA,IAAAC,KAAA,GAAA,IAAA;IACN,IAAI,CAACC,SAAS,CAAC,YAAM;MACjBD,KAAI,CAACF,YAAY,EAAE;AACvB,KAAC,CAAC;AAEF,IAAA,IAAMI,UAAS,GAAI,IAAI,CAACC,cAAc,EAAE;AAExCD,IAAAA,UAAS,KAAMA,UAAU,CAACE,QAAS,GAAE,GAAG,CAAC;GAC5C;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACP,YAAY,EAAE;GACtB;AACDQ,EAAAA,OAAO,EAAE;IACLC,YAAY,EAAA,SAAZA,YAAYA,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAE;AAC3B,MAAA,OAAO,IAAI,CAACC,GAAG,CAACH,GAAG,EAAE;AACjBI,QAAAA,OAAO,EAAE;AACLH,UAAAA,IAAI,EAAJA,IAAI;AACJC,UAAAA,KAAI,EAAJA;AACJ;AACJ,OAAC,CAAC;KACL;IACDG,WAAW,EAAA,SAAXA,WAAWA,CAACC,KAAK,EAAEL,IAAI,EAAEC,KAAK,EAAE;AAC5B,MAAA,IAAI,IAAI,CAACK,QAAQ,CAACN,IAAI,CAAC,EAAE;QACrBK,KAAK,CAACE,cAAc,EAAE;AAEtB,QAAA;AACJ;MAEA,IAAIP,IAAI,CAACQ,OAAO,EAAE;QACdR,IAAI,CAACQ,OAAO,CAAC;AACTC,UAAAA,aAAa,EAAEJ,KAAK;AACpBL,UAAAA,IAAI,EAAEA;AACV,SAAC,CAAC;AACN;AAEA,MAAA,IAAIC,KAAM,KAAI,IAAI,CAACjB,aAAa,EAAE;QAC9B,IAAI,CAACA,aAAY,GAAIiB,KAAK;QAC1B,IAAI,CAACS,KAAK,CAAC,oBAAoB,EAAE,IAAI,CAAC1B,aAAa,CAAC;AACxD;AAEA,MAAA,IAAI,CAAC0B,KAAK,CAAC,YAAY,EAAE;AACrBD,QAAAA,aAAa,EAAEJ,KAAK;AACpBJ,QAAAA,KAAK,EAAEA;AACX,OAAC,CAAC;KACL;IACDU,aAAa,EAAA,SAAbA,aAAaA,CAACN,KAAK,EAAEL,IAAI,EAAEC,KAAK,EAAE;MAC9B,QAAQI,KAAK,CAACO,IAAI;AACd,QAAA,KAAK,YAAY;AAAE,UAAA;AACf,YAAA,IAAI,CAACC,kBAAkB,CAACR,KAAK,CAACS,MAAM,CAAC;YACrCT,KAAK,CAACE,cAAc,EAAE;AACtB,YAAA;AACJ;AAEA,QAAA,KAAK,WAAW;AAAE,UAAA;AACd,YAAA,IAAI,CAACQ,kBAAkB,CAACV,KAAK,CAACS,MAAM,CAAC;YACrCT,KAAK,CAACE,cAAc,EAAE;AACtB,YAAA;AACJ;AAEA,QAAA,KAAK,MAAM;AAAE,UAAA;AACT,YAAA,IAAI,CAACS,mBAAmB,CAACX,KAAK,CAACS,MAAM,CAAC;YACtCT,KAAK,CAACE,cAAc,EAAE;AACtB,YAAA;AACJ;AAEA,QAAA,KAAK,KAAK;AAAE,UAAA;AACR,YAAA,IAAI,CAACU,kBAAkB,CAACZ,KAAK,CAACS,MAAM,CAAC;YACrCT,KAAK,CAACE,cAAc,EAAE;AACtB,YAAA;AACJ;AAEA,QAAA,KAAK,OAAO;AACZ,QAAA,KAAK,aAAa;AAElB,QAAA,KAAK,OAAO;AAAE,UAAA;YACV,IAAI,CAACH,WAAW,CAACC,KAAK,EAAEL,IAAI,EAAEC,KAAK,CAAC;YACpCI,KAAK,CAACE,cAAc,EAAE;AACtB,YAAA;AACJ;AAEA,QAAA,KAAK,KAAK;AAAE,UAAA;YACR,IAAI,CAACW,QAAQ,EAAE;AACf,YAAA;AACJ;AAIJ;KACH;AACDL,IAAAA,kBAAkB,EAAlBA,SAAAA,kBAAkBA,CAACC,MAAM,EAAE;AACvB,MAAA,IAAMK,QAAS,GAAE,IAAI,CAACC,YAAY,CAACN,MAAM,CAAC;MAE1CK,QAAO,IAAK,IAAI,CAACE,kBAAkB,CAACP,MAAM,EAAEK,QAAQ,CAAC;KACxD;AACDJ,IAAAA,kBAAkB,EAAlBA,SAAAA,kBAAkBA,CAACD,MAAM,EAAE;AACvB,MAAA,IAAMQ,QAAS,GAAE,IAAI,CAACC,YAAY,CAACT,MAAM,CAAC;MAE1CQ,QAAO,IAAK,IAAI,CAACD,kBAAkB,CAACP,MAAM,EAAEQ,QAAQ,CAAC;KACxD;AACDN,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAACF,MAAM,EAAE;AACxB,MAAA,IAAMU,SAAQ,GAAI,IAAI,CAACC,aAAa,CAACX,MAAM,CAAC;MAE5CU,SAAU,IAAG,IAAI,CAACH,kBAAkB,CAACP,MAAM,EAAEU,SAAS,CAAC;KAC1D;AACDP,IAAAA,kBAAkB,EAAlBA,SAAAA,kBAAkBA,CAACH,MAAM,EAAE;AACvB,MAAA,IAAMY,QAAS,GAAE,IAAI,CAACC,YAAY,CAACb,MAAM,CAAC;MAE1CY,QAAO,IAAK,IAAI,CAACL,kBAAkB,CAACP,MAAM,EAAEY,QAAQ,CAAC;KACxD;AACDN,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACpB,IAAI,EAAE;AACf,MAAA,IAAMmB,WAAWnB,IAAI,CAAC4B,aAAa,CAACC,kBAAkB;AAEtD,MAAA,OAAOV,QAAS,GAAGW,YAAY,CAACX,QAAQ,EAAE,iBAAiB,CAAA,KAAM,OAAO,IAAI,CAACC,YAAY,CAACD,QAAQ,CAACY,QAAQ,CAAC,CAAC,CAAC,CAAE,GAAEZ,QAAQ,CAACY,QAAQ,CAAC,CAAC,CAAC,GAAI,IAAI;KACjJ;AACDR,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACvB,IAAI,EAAE;AACf,MAAA,IAAMsB,QAAO,GAAItB,IAAI,CAAC4B,aAAa,CAACI,sBAAsB;AAE1D,MAAA,OAAOV,QAAS,GAAGQ,YAAY,CAACR,QAAQ,EAAE,iBAAiB,CAAA,KAAM,OAAO,IAAI,CAACC,YAAY,CAACD,QAAQ,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAE,GAAET,QAAQ,CAACS,QAAQ,CAAC,CAAC,CAAC,GAAI,IAAI;KACjJ;IACDN,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,IAAMQ,YAAW,GAAIC,UAAU,CAAC,IAAI,CAACC,KAAK,CAACC,GAAG,EAAE,mDAAmD,CAAC;MAEpG,OAAOH,eAAeA,YAAY,CAACF,QAAQ,CAAC,CAAC,IAAI,IAAI;KACxD;IACDJ,YAAY,EAAA,SAAZA,YAAYA,GAAG;MACX,IAAMU,WAAWC,IAAI,CAAC,IAAI,CAACH,KAAK,CAACC,GAAG,EAAE,mDAAmD,CAAC;AAE1F,MAAA,OAAOC,QAAO,GAAIA,QAAQ,CAACA,QAAQ,CAACE,MAAO,GAAE,CAAC,CAAC,CAACR,QAAQ,CAAC,CAAC,CAAA,GAAI,IAAI;KACrE;IACDrC,cAAc,EAAA,SAAdA,cAAcA,GAAG;MACb,IAAMD,UAAW,GAAEyC,UAAU,CAAC,IAAI,CAACC,KAAK,CAACC,GAAG,EAAE,yEAAyE,CAAC;MAExH,OAAO3C,UAAS,GAAIA,UAAU,CAACsC,QAAQ,CAAC,CAAC,CAAA,GAAI,IAAI;KACpD;AACDV,IAAAA,kBAAkB,WAAlBA,kBAAkBA,CAACP,MAAM,EAAE0B,aAAa,EAAE;MACtC1B,MAAM,CAACnB,QAAO,GAAI,IAAI;MACtB6C,aAAa,CAAC7C,QAAS,GAAE,GAAG;MAC5B6C,aAAa,CAACC,KAAK,EAAE;KACxB;IACDvB,QAAQ,EAAA,SAARA,QAAQA,GAAG;MACP,IAAMzB,UAAW,GAAEyC,UAAU,CAAC,IAAI,CAACC,KAAK,CAACC,GAAG,EAAE,yEAAyE,CAAC;MACxH,IAAMM,cAAcR,UAAU,CAAC,IAAI,CAACC,KAAK,CAACC,GAAG,EAAE,4CAA4C,CAAC;MAE5F,IAAIM,WAAU,KAAMjD,UAAU,CAACsC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACxCtC,UAAW,KAAIA,UAAU,CAACsC,QAAQ,CAAC,CAAC,CAAC,CAACpC,WAAW,GAAG,CAAC;QACrD+C,WAAW,CAAC/C,WAAW,IAAI;AAC/B;KACH;AACDgD,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAAC3C,IAAI,EAAE;AACV,MAAA,OAAO,OAAOA,IAAI,CAAC2C,YAAY,UAAW,GAAE3C,IAAI,CAAC2C,OAAO,EAAC,GAAI3C,IAAI,CAAC2C,OAAQ,KAAI,KAAK;KACtF;AACDrC,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAACN,IAAI,EAAE;AACX,MAAA,OAAO,OAAOA,IAAI,CAACM,aAAa,UAAW,GAAEN,IAAI,CAACM,QAAQ,KAAKN,IAAI,CAACM,QAAO,KAAM,IAAI;KACxF;AACDsC,IAAAA,KAAK,EAALA,SAAAA,KAAKA,CAAC5C,IAAI,EAAE;AACR,MAAA,OAAO,OAAOA,IAAI,CAAC4C,KAAM,KAAI,UAAW,GAAE5C,IAAI,CAAC4C,KAAK,EAAC,GAAI5C,IAAI,CAAC4C,KAAK;KACtE;IACDvD,YAAY,EAAA,SAAZA,YAAYA,GAAG;MACX,IAAIwD,IAAG,GAAI,IAAI,CAACV,KAAK,CAACC,GAAG,CAACL,QAAQ;MAClC,IAAIe,cAAa,GAAI,KAAK;AAE1B,MAAA,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIF,IAAI,CAACN,MAAM,EAAEQ,CAAC,EAAE,EAAE;AAClC,QAAA,IAAIC,GAAI,GAAEH,IAAI,CAACE,CAAC,CAAC;AAEjB,QAAA,IAAIjB,YAAY,CAACkB,GAAG,EAAE,eAAe,CAAC,EAAE;AACpC,UAAA,IAAI,CAACb,KAAK,CAACc,MAAM,CAAC1E,KAAK,CAAC2E,KAAM,GAAEC,QAAQ,CAACH,GAAG,IAAI,IAAI;UACpD,IAAI,CAACb,KAAK,CAACc,MAAM,CAAC1E,KAAK,CAAC6E,IAAG,GAAIC,SAAS,CAACL,GAAG,CAAC,CAACI,IAAK,GAAEC,SAAS,CAAC,IAAI,CAAClB,KAAK,CAACC,GAAG,CAAC,CAACgB,IAAG,GAAI,IAAI;AAC1FN,UAAAA,cAAa,GAAI,IAAI;AACzB;AACJ;MAEA,IAAI,CAACA,cAAc,EAAE;QACjB,IAAI,CAACX,KAAK,CAACc,MAAM,CAAC1E,KAAK,CAAC2E,QAAQ,KAAK;QACrC,IAAI,CAACf,KAAK,CAACc,MAAM,CAAC1E,KAAK,CAAC6E,IAAG,GAAI,KAAK;AACxC;KACH;AACDE,IAAAA,gBAAgB,WAAhBA,gBAAgBA,CAACtD,IAAI,EAAEC,KAAK,EAAE;AAAA,MAAA,IAAAsD,MAAA,GAAA,IAAA;MAC1B,OAAO;QACHC,MAAM,EAAEC,UAAU,CACd;AACI,UAAA,OAAA,EAAO,IAAI,CAACC,EAAE,CAAC,UAAU,CAAC;UAC1BC,QAAQ,EAAE,EAAE;AACZC,UAAAA,OAAO,EAAE,SAATA,OAAOA,CAAGC,MAAM,EAAA;YAAA,OAAKN,MAAI,CAACnD,WAAW,CAACyD,MAAM,EAAE7D,IAAI,EAAEC,KAAK,CAAC;AAAA,WAAA;AAC1D6D,UAAAA,SAAS,EAAE,SAAXA,SAASA,CAAGD,MAAM,EAAA;YAAA,OAAKN,MAAI,CAAC5C,aAAa,CAACkD,MAAM,EAAE7D,IAAI,EAAEC,KAAK,CAAA;AAAA;SAChE,EACD,IAAI,CAACH,YAAY,CAAC,UAAU,EAAEE,IAAI,EAAEC,KAAK,CAC7C,CAAC;QACD8D,IAAI,EAAEN,UAAU,CACZ;UACI,OAAO,EAAA,CAAC,IAAI,CAACC,EAAE,CAAC,UAAU,CAAC,EAAE1D,IAAI,CAAC+D,IAAI;SACzC,EACD,IAAI,CAACjE,YAAY,CAAC,UAAU,EAAEE,IAAI,EAAEC,KAAK,CAC7C,CAAC;QACD2C,KAAK,EAAEa,UAAU,CACb;AACI,UAAA,OAAA,EAAO,IAAI,CAACC,EAAE,CAAC,WAAW;SAC7B,EACD,IAAI,CAAC5D,YAAY,CAAC,WAAW,EAAEE,IAAI,EAAEC,KAAK,CAC9C;OACH;AACL;GACH;AACD+D,EAAAA,UAAU,EAAE;AACRC,IAAAA,MAAM,EAAEC;AACZ;AACJ,CAAC;;;;;;;ECnQG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CA0BK,OA1BLC,UA0BK,CAAA;AA1BC,IAAA,OAAA,EAAOC,IAAE,CAAAZ,EAAA,CAAA,MAAA;KAAkBY,IAAI,CAAAC,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACjCC,kBAAA,CAwBI,MAxBJH,UAwBI,CAAA;AAxBAI,IAAAA,GAAG,EAAC,KAAM;AAAC,IAAA,OAAA,EAAOH,IAAE,CAAAZ,EAAA,CAAA,SAAA,CAAA;AAAagB,IAAAA,IAAI,EAAC,SAAU;IAAC,iBAAe,EAAEJ,IAAc,CAAAlG,cAAA;IAAG,YAAU,EAAEkG,IAAS,CAAAhG;KAAUgG,IAAG,CAAApE,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EACrHiE,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAqBUO,QArBoB,EAAA,IAAA,EAAAC,UAAA,CAAAN,IAAA,CAAAvG,KAAK,EAAjB,UAAAiC,IAAI,EAAE+C,CAAC,EAAA;;AAAkBhD,MAAAA,GAAA,EAAA8E,QAAA,CAAAjC,KAAK,CAAC5C,IAAI,CAAU,GAAA,GAAA,GAAA+C,CAAC,CAAC+B,QAAQ;QAE3DD,QAAA,CAAAlC,OAAO,CAAC3C,IAAI,CAAA,IADtBmE,SAAA,EAAA,EAAAC,kBAAA,CAmBI,MAnBJC,UAmBI,CAAA;;;AAjBAI,MAAAA,GAAG,EAAC,KAAI;AACP,MAAA,OAAA,EAAK,CAAGH,OAAE,CAAW,MAAA,EAAA;AAAAtE,QAAAA,IAAI,EAAJA,IAAI;eAAS+C;AAAA,OAAA,CAAA,EAAM/C,IAAI,CAAA,OAAA,CAAM,CAAA;AACnD0E,MAAAA,IAAI,EAAC,cAAa;AACjBd,MAAAA,OAAK,WAALA,OAAKA;eAAEiB,QAAW,CAAAzE,WAAA,CAACyD,MAAM,EAAE7D,IAAI,EAAE+C,CAAC,CAAA;OAAA;AAClCgC,MAAAA,SAAO,WAAPA,SAAOA;eAAEF,QAAa,CAAAlE,aAAA,CAACkD,MAAM,EAAE7D,IAAI,EAAE+C,CAAC,CAAA;AAAA;;;OAC/B8B,QAAY,CAAA/E,YAAA,CAAA,MAAA,EAASE,IAAI,EAAE+C,CAAC,CAAA,EAAA;AACnC,MAAA,eAAa,EAAEiC,KAAY,CAAAhG,aAAA,KAAM+D,CAAC;AAClC,MAAA,iBAAe,EAAE8B,QAAQ,CAAAvE,QAAA,CAACN,IAAI;SAEd,CAAAsE,IAAA,CAAAW,MAAM,CAACjF,IAAI,GACxBkF,cAAA,EAAAf,SAAA,EAAA,EAAAC,kBAAA,CAIG,KAJHC,UAIG,CAAA;;;AAJAI,MAAAA,GAAG,EAAC,SAAQ;AAAWC,MAAAA,IAAI,EAAC,UAAS;MAAGS,IAAI,EAAEnF,IAAI,CAACoF,GAAG;AAAG,MAAA,OAAA,EAAOd,IAAE,CAAAZ,EAAA,CAAA,UAAA,CAAA;MAAe5C,MAAM,EAAEd,IAAI,CAACc,MAAM;AAAG,MAAA,YAAU,EAAE+D,QAAK,CAAAjC,KAAA,CAAC5C,IAAI,CAAA;AAAI,MAAA,eAAa,EAAE6E,QAAQ,CAAAvE,QAAA,CAACN,IAAI,CAAA;AAAI2D,MAAAA,QAAQ,EAAE;;;OAAYkB,QAAY,CAAA/E,YAAA,CAAA,UAAA,EAAaE,IAAI,EAAE+C,CAAC,CAAA,CAAA,EAAA,CACrMuB,IAAA,CAAAW,MAAM,CAACI,QAAQ,iBAAhCC,WAA8F,CAAAC,uBAAA,CAAvDjB,IAAM,CAAAW,MAAA,CAACI,QAAQ,CAAA,EAAA;;AAAGrF,MAAAA,IAAI,EAAEA,IAAI;AAAG,MAAA,OAAA,iBAAOsE,IAAE,CAAAZ,EAAA,CAAA,UAAA,CAAA;sCAC9D1D,IAAI,CAAC+D,IAAI,IAA1BI,SAAA,EAAA,EAAAC,kBAAA,CAA6G,QAA7GC,UAA6G,CAAA;;MAAhF,OAAQ,EAAA,CAAAC,IAAA,CAAAZ,EAAE,CAAc,UAAA,CAAA,EAAA1D,IAAI,CAAC+D,IAAI;;;OAAWc,QAAY,CAAA/E,YAAA,CAAA,UAAA,EAAaE,IAAI,EAAE+C,CAAC,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,kCACzGyB,kBAAA,CAAkG,QAAlGH,UAAkG,CAAA;AAA3F,MAAA,OAAA,EAAOC,IAAE,CAAAZ,EAAA,CAAA,WAAA;AAAuB,KAAA,EAAA;AAAA8B,MAAAA,OAAA,EAAA;KAAA,EAAAX,QAAA,CAAA/E,YAAY,cAAcE,IAAI,EAAE+C,CAAC,CAAM,CAAA,EAAA0C,eAAA,CAAAZ,QAAA,CAAAjC,KAAK,CAAC5C,IAAI,CAAA,CAAA,EAAA,EAAA,CAAA,4DAGhGsF,WAA6J,CAAAC,uBAAA,CAAtIjB,IAAM,CAAAW,MAAA,CAACjF,IAAI,CAAA,EAAA;;AAAGA,MAAAA,IAAI,EAAEA,IAAI;AAAGC,MAAAA,KAAK,EAAE8C,CAAC;AAAG2C,MAAAA,MAAM,EAAE3C,CAAA,KAAMiC,KAAa,CAAAhG,aAAA;AAAG4D,MAAAA,KAAK,EAAEiC,QAAK,CAAAjC,KAAA,CAAC5C,IAAI,CAAA;AAAIlC,MAAAA,KAAK,EAAE+G,QAAA,CAAAvB,gBAAgB,CAACtD,IAAI,EAAE+C,CAAC;;aAGvJyB,kBAAA,CAAoF,MAApFH,UAAoF,CAAA;AAAhFI,IAAAA,GAAG,EAAC,QAAS;AAAAC,IAAAA,IAAI,EAAC,MAAK;AAAG,IAAA,OAAA,EAAOJ,IAAE,CAAAZ,EAAA,CAAA,WAAA;KAAuBY,IAAG,CAAApE,GAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA;;;;;;;"}