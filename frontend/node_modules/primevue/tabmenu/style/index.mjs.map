{"version": 3, "file": "index.mjs", "sources": ["../../../src/tabmenu/style/TabMenuStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/tabmenu';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-tabmenu p-component',\n    tablist: 'p-tabmenu-tablist',\n    item: ({ instance, index, item }) => [\n        'p-tabmenu-item',\n        {\n            'p-tabmenu-item-active': instance.d_activeIndex === index,\n            'p-disabled': instance.disabled(item)\n        }\n    ],\n    itemLink: 'p-tabmenu-item-link',\n    itemIcon: 'p-tabmenu-item-icon',\n    itemLabel: 'p-tabmenu-item-label',\n    activeBar: 'p-tabmenu-active-bar'\n};\n\nexport default BaseStyle.extend({\n    name: 'tabmenu',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "tablist", "item", "_ref", "instance", "index", "d_activeIndex", "disabled", "itemLink", "itemIcon", "itemLabel", "activeBar", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,uBAAuB;AAC7BC,EAAAA,OAAO,EAAE,mBAAmB;AAC5BC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;MAAEH,KAAI,GAAAC,IAAA,CAAJD,IAAI;IAAA,OAAO,CACjC,gBAAgB,EAChB;AACI,MAAA,uBAAuB,EAAEE,QAAQ,CAACE,aAAa,KAAKD,KAAK;AACzD,MAAA,YAAY,EAAED,QAAQ,CAACG,QAAQ,CAACL,KAAI;AACxC,KAAC,CACJ;AAAA,GAAA;AACDM,EAAAA,QAAQ,EAAE,qBAAqB;AAC/BC,EAAAA,QAAQ,EAAE,qBAAqB;AAC/BC,EAAAA,SAAS,EAAE,sBAAsB;AACjCC,EAAAA,SAAS,EAAE;AACf,CAAC;AAED,mBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,SAAS;AACfC,EAAAA,KAAK,EAALA,KAAK;AACLhB,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}