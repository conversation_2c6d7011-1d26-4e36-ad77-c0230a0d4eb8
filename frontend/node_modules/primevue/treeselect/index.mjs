import { findSingle, isTouchDevice, relativePosition, getOuterWidth, absolutePosition, getFocusableElements, addStyle, focus, find, getLastFocusableElement, getFirstFocusableElement } from '@primeuix/utils/dom';
import { isNotEmpty, isEmpty } from '@primeuix/utils/object';
import { ZIndex } from '@primeuix/utils/zindex';
import { ConnectedOverlayScrollHandler } from '@primevue/core/utils';
import ChevronDownIcon from '@primevue/icons/chevrondown';
import TimesIcon from '@primevue/icons/times';
import Chip from 'primevue/chip';
import OverlayEventBus from 'primevue/overlayeventbus';
import Portal from 'primevue/portal';
import Ripple from 'primevue/ripple';
import Tree from 'primevue/tree';
import BaseInput from '@primevue/core/baseinput';
import TreeSelectStyle from 'primevue/treeselect/style';
import { resolveComponent, createElementBlock, openBlock, mergeProps, createElementVNode, renderSlot, createCommentVNode, createVNode, Fragment, createTextVNode, toDisplayString, renderList, normalizeClass, createBlock, resolveDynamicComponent, withCtx, Transition, withModifiers, createSlots } from 'vue';

var script$1 = {
  name: 'BaseTreeSelect',
  "extends": BaseInput,
  props: {
    options: Array,
    scrollHeight: {
      type: String,
      "default": '20rem'
    },
    placeholder: {
      type: String,
      "default": null
    },
    tabindex: {
      type: Number,
      "default": null
    },
    selectionMode: {
      type: String,
      "default": 'single'
    },
    selectedItemsLabel: {
      type: String,
      "default": null
    },
    maxSelectedLabels: {
      type: Number,
      "default": null
    },
    appendTo: {
      type: [String, Object],
      "default": 'body'
    },
    emptyMessage: {
      type: String,
      "default": null
    },
    display: {
      type: String,
      "default": 'comma'
    },
    metaKeySelection: {
      type: Boolean,
      "default": false
    },
    loading: {
      type: Boolean,
      "default": false
    },
    loadingIcon: {
      type: String,
      "default": undefined
    },
    loadingMode: {
      type: String,
      "default": 'mask'
    },
    showClear: {
      type: Boolean,
      "default": false
    },
    clearIcon: {
      type: String,
      "default": undefined
    },
    filter: {
      type: Boolean,
      "default": false
    },
    filterBy: {
      type: [String, Function],
      "default": 'label'
    },
    filterMode: {
      type: String,
      "default": 'lenient'
    },
    filterPlaceholder: {
      type: String,
      "default": null
    },
    filterLocale: {
      type: String,
      "default": undefined
    },
    inputId: {
      type: String,
      "default": null
    },
    inputClass: {
      type: [String, Object],
      "default": null
    },
    inputStyle: {
      type: Object,
      "default": null
    },
    inputProps: {
      type: null,
      "default": null
    },
    panelClass: {
      type: [String, Object],
      "default": null
    },
    panelProps: {
      type: null,
      "default": null
    },
    ariaLabelledby: {
      type: String,
      "default": null
    },
    ariaLabel: {
      type: String,
      "default": null
    },
    expandedKeys: {
      type: null,
      "default": null
    }
  },
  style: TreeSelectStyle,
  provide: function provide() {
    return {
      $pcTreeSelect: this,
      $parentInstance: this
    };
  }
};

function _typeof$1(o) { "@babel/helpers - typeof"; return _typeof$1 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof$1(o); }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: true } : { done: false, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = true, u = false; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = true, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), true).forEach(function (r) { _defineProperty$1(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty$1(e, r, t) { return (r = _toPropertyKey$1(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey$1(t) { var i = _toPrimitive$1(t, "string"); return "symbol" == _typeof$1(i) ? i : i + ""; }
function _toPrimitive$1(t, r) { if ("object" != _typeof$1(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof$1(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
var script = {
  name: 'TreeSelect',
  "extends": script$1,
  inheritAttrs: false,
  emits: ['before-show', 'before-hide', 'change', 'show', 'hide', 'node-select', 'node-unselect', 'node-expand', 'node-collapse', 'focus', 'blur', 'update:expandedKeys'],
  inject: {
    $pcFluid: {
      "default": null
    }
  },
  data: function data() {
    return {
      focused: false,
      overlayVisible: false,
      d_expandedKeys: this.expandedKeys || {}
    };
  },
  watch: {
    modelValue: {
      handler: function handler() {
        if (!this.selfChange) {
          this.updateTreeState();
        }
        this.selfChange = false;
      },
      immediate: true
    },
    options: function options() {
      this.updateTreeState();
    },
    expandedKeys: function expandedKeys(value) {
      this.d_expandedKeys = value;
    }
  },
  outsideClickListener: null,
  resizeListener: null,
  scrollHandler: null,
  overlay: null,
  selfChange: false,
  selfClick: false,
  beforeUnmount: function beforeUnmount() {
    this.unbindOutsideClickListener();
    this.unbindResizeListener();
    if (this.scrollHandler) {
      this.scrollHandler.destroy();
      this.scrollHandler = null;
    }
    if (this.overlay) {
      ZIndex.clear(this.overlay);
      this.overlay = null;
    }
  },
  mounted: function mounted() {
    this.updateTreeState();
  },
  methods: {
    show: function show() {
      this.$emit('before-show');
      this.overlayVisible = true;
    },
    hide: function hide() {
      this.$emit('before-hide');
      this.overlayVisible = false;
      this.$refs.focusInput.focus();
    },
    onFocus: function onFocus(event) {
      this.focused = true;
      this.$emit('focus', event);
    },
    onBlur: function onBlur(event) {
      var _this$formField$onBlu, _this$formField;
      this.focused = false;
      this.$emit('blur', event);
      (_this$formField$onBlu = (_this$formField = this.formField).onBlur) === null || _this$formField$onBlu === void 0 || _this$formField$onBlu.call(_this$formField);
    },
    onClick: function onClick(event) {
      if (this.disabled) {
        return;
      }
      if (event.target.tagName === 'INPUT' || event.target.getAttribute('data-pc-section') === 'clearicon' || event.target.closest('[data-pc-section="clearicon"]')) {
        return;
      } else if (!this.overlay || !this.overlay.contains(event.target)) {
        if (this.overlayVisible) this.hide();else this.show();
        focus(this.$refs.focusInput);
      }
    },
    onClearClick: function onClearClick() {
      this.onSelectionChange(null);
    },
    onSelectionChange: function onSelectionChange(keys) {
      this.selfChange = true;
      this.writeValue(keys);
      this.$emit('change', keys);
    },
    onNodeSelect: function onNodeSelect(node) {
      this.$emit('node-select', node);
      if (this.selectionMode === 'single') {
        this.hide();
      }
    },
    onNodeUnselect: function onNodeUnselect(node) {
      this.$emit('node-unselect', node);
    },
    onNodeToggle: function onNodeToggle(keys) {
      this.d_expandedKeys = keys;
      this.$emit('update:expandedKeys', this.d_expandedKeys);
    },
    getSelectedItemsLabel: function getSelectedItemsLabel() {
      var pattern = /{(.*?)}/;
      var selectedItemsLabel = this.selectedItemsLabel || this.$primevue.config.locale.selectionMessage;
      if (pattern.test(selectedItemsLabel)) {
        return selectedItemsLabel.replace(selectedItemsLabel.match(pattern)[0], Object.keys(this.d_value).length + '');
      }
      return selectedItemsLabel;
    },
    onFirstHiddenFocus: function onFirstHiddenFocus(event) {
      var focusableEl = event.relatedTarget === this.$refs.focusInput ? getFirstFocusableElement(this.overlay, ':not([data-p-hidden-focusable="true"])') : this.$refs.focusInput;
      focus(focusableEl);
    },
    onLastHiddenFocus: function onLastHiddenFocus(event) {
      var focusableEl = event.relatedTarget === this.$refs.focusInput ? getLastFocusableElement(this.overlay, ':not([data-p-hidden-focusable="true"])') : this.$refs.focusInput;
      focus(focusableEl);
    },
    onKeyDown: function onKeyDown(event) {
      switch (event.code) {
        case 'ArrowDown':
          this.onArrowDownKey(event);
          break;
        case 'Space':
        case 'Enter':
        case 'NumpadEnter':
          this.onEnterKey(event);
          break;
        case 'Escape':
          this.onEscapeKey(event);
          break;
        case 'Tab':
          this.onTabKey(event);
          break;
      }
    },
    onArrowDownKey: function onArrowDownKey(event) {
      var _this = this;
      if (this.overlayVisible) return;
      this.show();
      this.$nextTick(function () {
        var treeNodeEl = find(_this.$refs.tree.$el, '[data-pc-section="treeitem"]');
        var focusedElement = _toConsumableArray(treeNodeEl).find(function (item) {
          return item.getAttribute('tabindex') === '0';
        });
        focus(focusedElement);
      });
      event.preventDefault();
    },
    onEnterKey: function onEnterKey(event) {
      if (this.overlayVisible) {
        this.hide();
      } else {
        this.onArrowDownKey(event);
      }
      event.preventDefault();
    },
    onEscapeKey: function onEscapeKey(event) {
      if (this.overlayVisible) {
        this.hide();
        event.preventDefault();
      }
    },
    onTabKey: function onTabKey(event) {
      var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      if (!pressedInInputText) {
        if (this.overlayVisible && this.hasFocusableElements()) {
          focus(this.$refs.firstHiddenFocusableElementOnOverlay);
          event.preventDefault();
        }
      }
    },
    hasFocusableElements: function hasFocusableElements() {
      return getFocusableElements(this.overlay, ':not([data-p-hidden-focusable="true"])').length > 0;
    },
    onOverlayEnter: function onOverlayEnter(el) {
      ZIndex.set('overlay', el, this.$primevue.config.zIndex.overlay);
      addStyle(el, {
        position: 'absolute',
        top: '0'
      });
      this.alignOverlay();
      this.focus();

      // Issue: #7508
      this.$attrSelector && el.setAttribute(this.$attrSelector, '');
    },
    onOverlayAfterEnter: function onOverlayAfterEnter() {
      this.bindOutsideClickListener();
      this.bindScrollListener();
      this.bindResizeListener();
      this.scrollValueInView();
      this.$emit('show');
    },
    onOverlayLeave: function onOverlayLeave() {
      this.unbindOutsideClickListener();
      this.unbindScrollListener();
      this.unbindResizeListener();
      this.$emit('hide');
      this.overlay = null;
    },
    onOverlayAfterLeave: function onOverlayAfterLeave(el) {
      ZIndex.clear(el);
    },
    focus: function focus() {
      var focusableElements = getFocusableElements(this.overlay);
      if (focusableElements && focusableElements.length > 0) {
        focusableElements[0].focus();
      }
    },
    alignOverlay: function alignOverlay() {
      if (this.appendTo === 'self') {
        relativePosition(this.overlay, this.$el);
      } else {
        this.overlay.style.minWidth = getOuterWidth(this.$el) + 'px';
        absolutePosition(this.overlay, this.$el);
      }
    },
    bindOutsideClickListener: function bindOutsideClickListener() {
      var _this2 = this;
      if (!this.outsideClickListener) {
        this.outsideClickListener = function (event) {
          if (_this2.overlayVisible && !_this2.selfClick && _this2.isOutsideClicked(event)) {
            _this2.hide();
          }
          _this2.selfClick = false;
        };
        document.addEventListener('click', this.outsideClickListener, true);
      }
    },
    unbindOutsideClickListener: function unbindOutsideClickListener() {
      if (this.outsideClickListener) {
        document.removeEventListener('click', this.outsideClickListener, true);
        this.outsideClickListener = null;
      }
    },
    bindScrollListener: function bindScrollListener() {
      var _this3 = this;
      if (!this.scrollHandler) {
        this.scrollHandler = new ConnectedOverlayScrollHandler(this.$refs.container, function () {
          if (_this3.overlayVisible) {
            _this3.hide();
          }
        });
      }
      this.scrollHandler.bindScrollListener();
    },
    unbindScrollListener: function unbindScrollListener() {
      if (this.scrollHandler) {
        this.scrollHandler.unbindScrollListener();
      }
    },
    bindResizeListener: function bindResizeListener() {
      var _this4 = this;
      if (!this.resizeListener) {
        this.resizeListener = function () {
          if (_this4.overlayVisible && !isTouchDevice()) {
            _this4.hide();
          }
        };
        window.addEventListener('resize', this.resizeListener);
      }
    },
    unbindResizeListener: function unbindResizeListener() {
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
        this.resizeListener = null;
      }
    },
    isOutsideClicked: function isOutsideClicked(event) {
      return !(this.$el.isSameNode(event.target) || this.$el.contains(event.target) || this.overlay && this.overlay.contains(event.target));
    },
    overlayRef: function overlayRef(el) {
      this.overlay = el;
    },
    onOverlayClick: function onOverlayClick(event) {
      OverlayEventBus.emit('overlay-click', {
        originalEvent: event,
        target: this.$el
      });
      this.selfClick = true;
    },
    onOverlayKeydown: function onOverlayKeydown(event) {
      if (event.code === 'Escape') this.hide();
    },
    fillNodeMap: function fillNodeMap(node, nodeMap) {
      var _node$children,
        _this5 = this;
      nodeMap[node.key] = node;
      if ((_node$children = node.children) !== null && _node$children !== void 0 && _node$children.length) {
        node.children.forEach(function (children) {
          return _this5.fillNodeMap(children, nodeMap);
        });
      }
    },
    isSelected: function isSelected(node, keys) {
      return this.selectionMode === 'checkbox' ? keys[node.key] && keys[node.key].checked : keys[node.key];
    },
    updateTreeState: function updateTreeState() {
      var keys = _objectSpread$1({}, this.d_value);
      if (keys && this.options) {
        this.updateTreeBranchState(null, null, keys);
      }
    },
    updateTreeBranchState: function updateTreeBranchState(node, path, keys) {
      if (node) {
        if (this.isSelected(node, keys)) {
          this.expandPath(path);
          delete keys[node.key];
        }
        if (Object.keys(keys).length && node.children) {
          var _iterator = _createForOfIteratorHelper(node.children),
            _step;
          try {
            for (_iterator.s(); !(_step = _iterator.n()).done;) {
              var childNode = _step.value;
              path.push(node.key);
              this.updateTreeBranchState(childNode, path, keys);
            }
          } catch (err) {
            _iterator.e(err);
          } finally {
            _iterator.f();
          }
        }
      } else {
        var _iterator2 = _createForOfIteratorHelper(this.options),
          _step2;
        try {
          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
            var _childNode = _step2.value;
            this.updateTreeBranchState(_childNode, [], keys);
          }
        } catch (err) {
          _iterator2.e(err);
        } finally {
          _iterator2.f();
        }
      }
    },
    expandPath: function expandPath(path) {
      if (path.length > 0) {
        var _iterator3 = _createForOfIteratorHelper(path),
          _step3;
        try {
          for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
            var key = _step3.value;
            this.d_expandedKeys[key] = true;
          }
        } catch (err) {
          _iterator3.e(err);
        } finally {
          _iterator3.f();
        }
        this.d_expandedKeys = _objectSpread$1({}, this.d_expandedKeys);
        this.$emit('update:expandedKeys', this.d_expandedKeys);
      }
    },
    scrollValueInView: function scrollValueInView() {
      if (this.overlay) {
        var selectedItem = findSingle(this.overlay, '[data-p-selected="true"]');
        if (selectedItem) {
          selectedItem.scrollIntoView({
            block: 'nearest',
            inline: 'start'
          });
        }
      }
    }
  },
  computed: {
    nodeMap: function nodeMap() {
      var _this$options,
        _this6 = this;
      var nodeMap = {};
      (_this$options = this.options) === null || _this$options === void 0 || _this$options.forEach(function (node) {
        return _this6.fillNodeMap(node, nodeMap);
      });
      return nodeMap;
    },
    selectedNodes: function selectedNodes() {
      var _this7 = this;
      var selectedNodes = [];
      if (this.d_value && this.options) {
        Object.keys(this.d_value).forEach(function (key) {
          var node = _this7.nodeMap[key];
          if (_this7.isSelected(node, _this7.d_value)) {
            selectedNodes.push(node);
          }
        });
      }
      return selectedNodes;
    },
    label: function label() {
      var value = this.selectedNodes;
      var label;
      if (value.length) {
        if (isNotEmpty(this.maxSelectedLabels) && value.length > this.maxSelectedLabels) {
          label = this.getSelectedItemsLabel();
        } else {
          label = value.map(function (node) {
            return node.label;
          }).join(', ');
        }
      } else {
        label = this.placeholder;
      }
      return label;
    },
    chipSelectedItems: function chipSelectedItems() {
      return isNotEmpty(this.maxSelectedLabels) && this.d_value && Object.keys(this.d_value).length > this.maxSelectedLabels;
    },
    emptyMessageText: function emptyMessageText() {
      return this.emptyMessage || this.$primevue.config.locale.emptyMessage;
    },
    emptyValue: function emptyValue() {
      return !this.$filled;
    },
    emptyOptions: function emptyOptions() {
      return !this.options || this.options.length === 0;
    },
    listId: function listId() {
      return this.$id + '_list';
    },
    hasFluid: function hasFluid() {
      return isEmpty(this.fluid) ? !!this.$pcFluid : this.fluid;
    },
    isClearIconVisible: function isClearIconVisible() {
      return this.showClear && this.d_value != null && isNotEmpty(this.options);
    }
  },
  components: {
    TSTree: Tree,
    Chip: Chip,
    Portal: Portal,
    ChevronDownIcon: ChevronDownIcon,
    TimesIcon: TimesIcon
  },
  directives: {
    ripple: Ripple
  }
};

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), true).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var _hoisted_1 = ["id", "disabled", "tabindex", "aria-labelledby", "aria-label", "aria-expanded", "aria-controls"];
var _hoisted_2 = {
  key: 0
};
var _hoisted_3 = ["aria-expanded"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_Chip = resolveComponent("Chip");
  var _component_TSTree = resolveComponent("TSTree");
  var _component_Portal = resolveComponent("Portal");
  return openBlock(), createElementBlock("div", mergeProps({
    ref: "container",
    "class": _ctx.cx('root'),
    style: _ctx.sx('root'),
    onClick: _cache[10] || (_cache[10] = function () {
      return $options.onClick && $options.onClick.apply($options, arguments);
    })
  }, _ctx.ptmi('root')), [createElementVNode("div", mergeProps({
    "class": "p-hidden-accessible"
  }, _ctx.ptm('hiddenInputContainer'), {
    "data-p-hidden-accessible": true
  }), [createElementVNode("input", mergeProps({
    ref: "focusInput",
    id: _ctx.inputId,
    type: "text",
    role: "combobox",
    "class": _ctx.inputClass,
    style: _ctx.inputStyle,
    readonly: "",
    disabled: _ctx.disabled,
    tabindex: !_ctx.disabled ? _ctx.tabindex : -1,
    "aria-labelledby": _ctx.ariaLabelledby,
    "aria-label": _ctx.ariaLabel,
    "aria-haspopup": "tree",
    "aria-expanded": $data.overlayVisible,
    "aria-controls": $options.listId,
    onFocus: _cache[0] || (_cache[0] = function ($event) {
      return $options.onFocus($event);
    }),
    onBlur: _cache[1] || (_cache[1] = function ($event) {
      return $options.onBlur($event);
    }),
    onKeydown: _cache[2] || (_cache[2] = function ($event) {
      return $options.onKeyDown($event);
    })
  }, _objectSpread(_objectSpread({}, _ctx.inputProps), _ctx.ptm('hiddenInput'))), null, 16, _hoisted_1)], 16), createElementVNode("div", mergeProps({
    "class": _ctx.cx('labelContainer')
  }, _ctx.ptm('labelContainer')), [createElementVNode("div", mergeProps({
    "class": _ctx.cx('label')
  }, _ctx.ptm('label')), [renderSlot(_ctx.$slots, "value", {
    value: $options.selectedNodes,
    placeholder: _ctx.placeholder
  }, function () {
    return [_ctx.display === 'comma' ? (openBlock(), createElementBlock(Fragment, {
      key: 0
    }, [createTextVNode(toDisplayString($options.label || 'empty'), 1)], 64)) : _ctx.display === 'chip' ? (openBlock(), createElementBlock(Fragment, {
      key: 1
    }, [$options.chipSelectedItems ? (openBlock(), createElementBlock("span", _hoisted_2, toDisplayString($options.label), 1)) : (openBlock(), createElementBlock(Fragment, {
      key: 1
    }, [(openBlock(true), createElementBlock(Fragment, null, renderList($options.selectedNodes, function (node) {
      return openBlock(), createElementBlock("div", mergeProps({
        key: node.key,
        "class": _ctx.cx('chipItem')
      }, {
        ref_for: true
      }, _ctx.ptm('chipItem')), [createVNode(_component_Chip, {
        "class": normalizeClass(_ctx.cx('pcChip')),
        label: node.label,
        unstyled: _ctx.unstyled,
        pt: _ctx.ptm('pcChip')
      }, null, 8, ["class", "label", "unstyled", "pt"])], 16);
    }), 128)), $options.emptyValue ? (openBlock(), createElementBlock(Fragment, {
      key: 0
    }, [createTextVNode(toDisplayString(_ctx.placeholder || 'empty'), 1)], 64)) : createCommentVNode("", true)], 64))], 64)) : createCommentVNode("", true)];
  })], 16)], 16), $options.isClearIconVisible ? renderSlot(_ctx.$slots, "clearicon", {
    key: 0,
    "class": normalizeClass(_ctx.cx('clearIcon')),
    clearCallback: $options.onClearClick
  }, function () {
    return [(openBlock(), createBlock(resolveDynamicComponent(_ctx.clearIcon ? 'i' : 'TimesIcon'), mergeProps({
      ref: "clearIcon",
      "class": [_ctx.cx('clearIcon'), _ctx.clearIcon],
      onClick: $options.onClearClick
    }, _ctx.ptm('clearIcon'), {
      "data-pc-section": "clearicon"
    }), null, 16, ["class", "onClick"]))];
  }) : createCommentVNode("", true), createElementVNode("div", mergeProps({
    "class": _ctx.cx('dropdown'),
    role: "button",
    "aria-haspopup": "tree",
    "aria-expanded": $data.overlayVisible
  }, _ctx.ptm('dropdown')), [renderSlot(_ctx.$slots, _ctx.$slots.dropdownicon ? 'dropdownicon' : 'triggericon', {
    "class": normalizeClass(_ctx.cx('dropdownIcon'))
  }, function () {
    return [(openBlock(), createBlock(resolveDynamicComponent('ChevronDownIcon'), mergeProps({
      "class": _ctx.cx('dropdownIcon')
    }, _ctx.ptm('dropdownIcon')), null, 16, ["class"]))];
  })], 16, _hoisted_3), createVNode(_component_Portal, {
    appendTo: _ctx.appendTo
  }, {
    "default": withCtx(function () {
      return [createVNode(Transition, mergeProps({
        name: "p-connected-overlay",
        onEnter: $options.onOverlayEnter,
        onAfterEnter: $options.onOverlayAfterEnter,
        onLeave: $options.onOverlayLeave,
        onAfterLeave: $options.onOverlayAfterLeave
      }, _ctx.ptm('transition')), {
        "default": withCtx(function () {
          return [$data.overlayVisible ? (openBlock(), createElementBlock("div", mergeProps({
            key: 0,
            ref: $options.overlayRef,
            onClick: _cache[8] || (_cache[8] = function () {
              return $options.onOverlayClick && $options.onOverlayClick.apply($options, arguments);
            }),
            "class": [_ctx.cx('panel'), _ctx.panelClass],
            onKeydown: _cache[9] || (_cache[9] = function () {
              return $options.onOverlayKeydown && $options.onOverlayKeydown.apply($options, arguments);
            })
          }, _objectSpread(_objectSpread({}, _ctx.panelProps), _ctx.ptm('panel'))), [createElementVNode("span", mergeProps({
            ref: "firstHiddenFocusableElementOnOverlay",
            role: "presentation",
            "class": "p-hidden-accessible p-hidden-focusable",
            tabindex: 0,
            onFocus: _cache[3] || (_cache[3] = function () {
              return $options.onFirstHiddenFocus && $options.onFirstHiddenFocus.apply($options, arguments);
            })
          }, _ctx.ptm('hiddenFirstFocusableEl'), {
            "data-p-hidden-accessible": true,
            "data-p-hidden-focusable": true
          }), null, 16), renderSlot(_ctx.$slots, "header", {
            value: _ctx.d_value,
            options: _ctx.options
          }), createElementVNode("div", mergeProps({
            "class": _ctx.cx('treeContainer'),
            style: {
              'max-height': _ctx.scrollHeight
            }
          }, _ctx.ptm('treeContainer')), [createVNode(_component_TSTree, {
            ref: "tree",
            id: $options.listId,
            value: _ctx.options,
            selectionMode: _ctx.selectionMode,
            loading: _ctx.loading,
            loadingIcon: _ctx.loadingIcon,
            loadingMode: _ctx.loadingMode,
            filter: _ctx.filter,
            filterBy: _ctx.filterBy,
            filterMode: _ctx.filterMode,
            filterPlaceholder: _ctx.filterPlaceholder,
            filterLocale: _ctx.filterLocale,
            "onUpdate:selectionKeys": $options.onSelectionChange,
            selectionKeys: _ctx.d_value,
            expandedKeys: $data.d_expandedKeys,
            "onUpdate:expandedKeys": $options.onNodeToggle,
            metaKeySelection: _ctx.metaKeySelection,
            onNodeExpand: _cache[4] || (_cache[4] = function ($event) {
              return _ctx.$emit('node-expand', $event);
            }),
            onNodeCollapse: _cache[5] || (_cache[5] = function ($event) {
              return _ctx.$emit('node-collapse', $event);
            }),
            onNodeSelect: $options.onNodeSelect,
            onNodeUnselect: $options.onNodeUnselect,
            onClick: _cache[6] || (_cache[6] = withModifiers(function () {}, ["stop"])),
            level: 0,
            unstyled: _ctx.unstyled,
            pt: _ctx.ptm('pcTree')
          }, createSlots({
            _: 2
          }, [_ctx.$slots.option ? {
            name: "default",
            fn: withCtx(function (optionSlotProps) {
              return [renderSlot(_ctx.$slots, "option", {
                node: optionSlotProps.node,
                expanded: optionSlotProps.expanded,
                selected: optionSlotProps.selected
              })];
            }),
            key: "0"
          } : undefined, _ctx.$slots.itemtoggleicon ? {
            name: "toggleicon",
            fn: withCtx(function (iconSlotProps) {
              return [renderSlot(_ctx.$slots, "itemtoggleicon", {
                node: iconSlotProps.node,
                expanded: iconSlotProps.expanded,
                "class": normalizeClass(iconSlotProps["class"])
              })];
            }),
            key: "1"
          } : _ctx.$slots.itemtogglericon ? {
            name: "togglericon",
            fn: withCtx(function (iconSlotProps) {
              return [renderSlot(_ctx.$slots, "itemtogglericon", {
                node: iconSlotProps.node,
                expanded: iconSlotProps.expanded,
                "class": normalizeClass(iconSlotProps["class"])
              })];
            }),
            key: "2"
          } : undefined, _ctx.$slots.itemcheckboxicon ? {
            name: "checkboxicon",
            fn: withCtx(function (iconSlotProps) {
              return [renderSlot(_ctx.$slots, "itemcheckboxicon", {
                checked: iconSlotProps.checked,
                partialChecked: iconSlotProps.partialChecked,
                "class": normalizeClass(iconSlotProps["class"])
              })];
            }),
            key: "3"
          } : undefined]), 1032, ["id", "value", "selectionMode", "loading", "loadingIcon", "loadingMode", "filter", "filterBy", "filterMode", "filterPlaceholder", "filterLocale", "onUpdate:selectionKeys", "selectionKeys", "expandedKeys", "onUpdate:expandedKeys", "metaKeySelection", "onNodeSelect", "onNodeUnselect", "unstyled", "pt"]), $options.emptyOptions && !_ctx.loading ? (openBlock(), createElementBlock("div", mergeProps({
            key: 0,
            "class": _ctx.cx('emptyMessage')
          }, _ctx.ptm('emptyMessage')), [renderSlot(_ctx.$slots, "empty", {}, function () {
            return [createTextVNode(toDisplayString($options.emptyMessageText), 1)];
          })], 16)) : createCommentVNode("", true)], 16), renderSlot(_ctx.$slots, "footer", {
            value: _ctx.d_value,
            options: _ctx.options
          }), createElementVNode("span", mergeProps({
            ref: "lastHiddenFocusableElementOnOverlay",
            role: "presentation",
            "class": "p-hidden-accessible p-hidden-focusable",
            tabindex: 0,
            onFocus: _cache[7] || (_cache[7] = function () {
              return $options.onLastHiddenFocus && $options.onLastHiddenFocus.apply($options, arguments);
            })
          }, _ctx.ptm('hiddenLastFocusableEl'), {
            "data-p-hidden-accessible": true,
            "data-p-hidden-focusable": true
          }), null, 16)], 16)) : createCommentVNode("", true)];
        }),
        _: 3
      }, 16, ["onEnter", "onAfterEnter", "onLeave", "onAfterLeave"])];
    }),
    _: 3
  }, 8, ["appendTo"])], 16);
}

script.render = render;

export { script as default };
//# sourceMappingURL=index.mjs.map
