{"version": 3, "file": "index.mjs", "sources": ["../../src/styleclass/BaseStyleClass.js", "../../src/styleclass/StyleClass.js"], "sourcesContent": ["import BaseDirective from '@primevue/core/basedirective';\nimport StyleClassStyle from 'primevue/styleclass/style';\n\nconst BaseStyleClass = BaseDirective.extend({\n    style: StyleClassStyle\n});\n\nexport default BaseStyleClass;\n", "import { addClass, hasClass, removeClass } from '@primeuix/utils/dom';\nimport BaseStyleClass from './BaseStyleClass';\n\nconst StyleClass = BaseStyleClass.extend('styleclass', {\n    mounted(el, binding) {\n        el.setAttribute('data-pd-styleclass', true);\n\n        this.bind(el, binding);\n    },\n    unmounted(el) {\n        this.unbind(el);\n    },\n    methods: {\n        bind(el, binding) {\n            const target = this.resolveTarget(el, binding);\n\n            this.$el = target;\n\n            el.$_pstyleclass_clicklistener = () => {\n                if (binding.value.toggleClass) {\n                    if (hasClass(target, binding.value.toggleClass)) removeClass(target, binding.value.toggleClass);\n                    else addClass(target, binding.value.toggleClass);\n                } else {\n                    if (target.offsetParent === null) this.enter(target, el, binding);\n                    else this.leave(target, binding);\n                }\n            };\n\n            el.addEventListener('click', el.$_pstyleclass_clicklistener);\n        },\n        unbind(el) {\n            if (el.$_pstyleclass_clicklistener) {\n                el.removeEventListener('click', el.$_pstyleclass_clicklistener);\n                el.$_pstyleclass_clicklistener = null;\n            }\n\n            this.unbindDocumentListener(el);\n        },\n        enter(target, el, binding) {\n            if (binding.value.enterActiveClass) {\n                if (!target.$_pstyleclass_animating) {\n                    target.$_pstyleclass_animating = true;\n\n                    if (binding.value.enterActiveClass.includes('slidedown')) {\n                        target.style.height = '0px';\n                        removeClass(target, binding.value.hiddenClass || binding.value.enterFromClass);\n                        target.style.maxHeight = target.scrollHeight + 'px';\n                        addClass(target, binding.value.hiddenClass || binding.value.enterActiveClass);\n                        target.style.height = '';\n                    }\n\n                    addClass(target, binding.value.enterActiveClass);\n\n                    if (binding.value.enterFromClass) {\n                        removeClass(target, binding.value.enterFromClass);\n                    }\n\n                    target.$p_styleclass_enterlistener = () => {\n                        removeClass(target, binding.value.enterActiveClass);\n\n                        if (binding.value.enterToClass) {\n                            addClass(target, binding.value.enterToClass);\n                        }\n\n                        target.removeEventListener('animationend', target.$p_styleclass_enterlistener);\n\n                        if (binding.value.enterActiveClass.includes('slidedown')) {\n                            target.style.maxHeight = '';\n                        }\n\n                        target.$_pstyleclass_animating = false;\n                    };\n\n                    target.addEventListener('animationend', target.$p_styleclass_enterlistener);\n                }\n            } else {\n                if (binding.value.enterFromClass) {\n                    removeClass(target, binding.value.enterFromClass);\n                }\n\n                if (binding.value.enterToClass) {\n                    addClass(target, binding.value.enterToClass);\n                }\n            }\n\n            if (binding.value.hideOnOutsideClick) {\n                this.bindDocumentListener(target, el, binding);\n            }\n        },\n        leave(target, binding) {\n            if (binding.value.leaveActiveClass) {\n                if (!target.$_pstyleclass_animating) {\n                    target.$_pstyleclass_animating = true;\n                    addClass(target, binding.value.leaveActiveClass);\n\n                    if (binding.value.leaveFromClass) {\n                        removeClass(target, binding.value.leaveFromClass);\n                    }\n\n                    target.$p_styleclass_leavelistener = () => {\n                        removeClass(target, binding.value.leaveActiveClass);\n\n                        if (binding.value.leaveToClass) {\n                            addClass(target, binding.value.leaveToClass);\n                        }\n\n                        target.removeEventListener('animationend', target.$p_styleclass_leavelistener);\n                        target.$_pstyleclass_animating = false;\n                    };\n\n                    target.addEventListener('animationend', target.$p_styleclass_leavelistener);\n                }\n            } else {\n                if (binding.value.leaveFromClass) {\n                    removeClass(target, binding.value.leaveFromClass);\n                }\n\n                if (binding.value.leaveToClass) {\n                    addClass(target, binding.value.leaveToClass);\n                }\n            }\n\n            if (binding.value.hideOnOutsideClick) {\n                this.unbindDocumentListener(target);\n            }\n        },\n        resolveTarget(el, binding) {\n            switch (binding.value.selector) {\n                case '@next':\n                    return el.nextElementSibling;\n\n                case '@prev':\n                    return el.previousElementSibling;\n\n                case '@parent':\n                    return el.parentElement;\n\n                case '@grandparent':\n                    return el.parentElement.parentElement;\n\n                default:\n                    return document.querySelector(binding.value.selector);\n            }\n        },\n        bindDocumentListener(target, el, binding) {\n            if (!target.$p_styleclass_documentlistener) {\n                target.$p_styleclass_documentlistener = (event) => {\n                    if (!this.isVisible(target) || getComputedStyle(target).getPropertyValue('position') === 'static') {\n                        this.unbindDocumentListener(target);\n                    } else if (this.isOutsideClick(event, target, el)) {\n                        this.leave(target, binding);\n                    }\n                };\n\n                target.ownerDocument.addEventListener('click', target.$p_styleclass_documentlistener);\n            }\n        },\n        unbindDocumentListener(target) {\n            if (target.$p_styleclass_documentlistener) {\n                target.ownerDocument.removeEventListener('click', target.$p_styleclass_documentlistener);\n                target.$p_styleclass_documentlistener = null;\n            }\n        },\n        isVisible(target) {\n            return target.offsetParent !== null;\n        },\n        isOutsideClick(event, target, el) {\n            return !el.isSameNode(event.target) && !el.contains(event.target) && !target.contains(event.target);\n        }\n    }\n});\n\nexport default StyleClass;\n"], "names": ["BaseStyleClass", "BaseDirective", "extend", "style", "StyleClassStyle", "StyleClass", "mounted", "el", "binding", "setAttribute", "bind", "unmounted", "unbind", "methods", "_this", "target", "<PERSON><PERSON><PERSON><PERSON>", "$el", "$_pstyleclass_clicklistener", "value", "toggleClass", "hasClass", "removeClass", "addClass", "offsetParent", "enter", "leave", "addEventListener", "removeEventListener", "unbindDocumentListener", "enterActiveClass", "$_pstyleclass_animating", "includes", "height", "hiddenClass", "enterFromClass", "maxHeight", "scrollHeight", "$p_styleclass_enterlistener", "enterToClass", "hideOnOutsideClick", "bindDocumentListener", "leaveActiveClass", "leaveFromClass", "$p_styleclass_leavelistener", "leaveToClass", "selector", "nextElement<PERSON><PERSON>ling", "previousElementSibling", "parentElement", "document", "querySelector", "_this2", "$p_styleclass_documentlistener", "event", "isVisible", "getComputedStyle", "getPropertyValue", "isOutsideClick", "ownerDocument", "isSameNode", "contains"], "mappings": ";;;;AAGA,IAAMA,cAAc,GAAGC,aAAa,CAACC,MAAM,CAAC;AACxCC,EAAAA,KAAK,EAAEC;AACX,CAAC,CAAC;;ACFF,IAAMC,UAAU,GAAGL,cAAc,CAACE,MAAM,CAAC,YAAY,EAAE;AACnDI,EAAAA,OAAO,WAAPA,OAAOA,CAACC,EAAE,EAAEC,OAAO,EAAE;AACjBD,IAAAA,EAAE,CAACE,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC;AAE3C,IAAA,IAAI,CAACC,IAAI,CAACH,EAAE,EAAEC,OAAO,CAAC;GACzB;AACDG,EAAAA,SAAS,EAATA,SAAAA,SAASA,CAACJ,EAAE,EAAE;AACV,IAAA,IAAI,CAACK,MAAM,CAACL,EAAE,CAAC;GAClB;AACDM,EAAAA,OAAO,EAAE;AACLH,IAAAA,IAAI,WAAJA,IAAIA,CAACH,EAAE,EAAEC,OAAO,EAAE;AAAA,MAAA,IAAAM,KAAA,GAAA,IAAA;MACd,IAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACT,EAAE,EAAEC,OAAO,CAAC;MAE9C,IAAI,CAACS,GAAG,GAAGF,MAAM;MAEjBR,EAAE,CAACW,2BAA2B,GAAG,YAAM;AACnC,QAAA,IAAIV,OAAO,CAACW,KAAK,CAACC,WAAW,EAAE;AAC3B,UAAA,IAAIC,QAAQ,CAACN,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACC,WAAW,CAAC,EAAEE,WAAW,CAACP,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACC,WAAW,CAAC,CAAC,KAC3FG,QAAQ,CAACR,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACC,WAAW,CAAC;AACpD,SAAC,MAAM;UACH,IAAIL,MAAM,CAACS,YAAY,KAAK,IAAI,EAAEV,KAAI,CAACW,KAAK,CAACV,MAAM,EAAER,EAAE,EAAEC,OAAO,CAAC,CAAC,KAC7DM,KAAI,CAACY,KAAK,CAACX,MAAM,EAAEP,OAAO,CAAC;AACpC;OACH;MAEDD,EAAE,CAACoB,gBAAgB,CAAC,OAAO,EAAEpB,EAAE,CAACW,2BAA2B,CAAC;KAC/D;AACDN,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACL,EAAE,EAAE;MACP,IAAIA,EAAE,CAACW,2BAA2B,EAAE;QAChCX,EAAE,CAACqB,mBAAmB,CAAC,OAAO,EAAErB,EAAE,CAACW,2BAA2B,CAAC;QAC/DX,EAAE,CAACW,2BAA2B,GAAG,IAAI;AACzC;AAEA,MAAA,IAAI,CAACW,sBAAsB,CAACtB,EAAE,CAAC;KAClC;IACDkB,KAAK,EAAA,SAALA,KAAKA,CAACV,MAAM,EAAER,EAAE,EAAEC,OAAO,EAAE;AACvB,MAAA,IAAIA,OAAO,CAACW,KAAK,CAACW,gBAAgB,EAAE;AAChC,QAAA,IAAI,CAACf,MAAM,CAACgB,uBAAuB,EAAE;UACjChB,MAAM,CAACgB,uBAAuB,GAAG,IAAI;UAErC,IAAIvB,OAAO,CAACW,KAAK,CAACW,gBAAgB,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;AACtDjB,YAAAA,MAAM,CAACZ,KAAK,CAAC8B,MAAM,GAAG,KAAK;AAC3BX,YAAAA,WAAW,CAACP,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACe,WAAW,IAAI1B,OAAO,CAACW,KAAK,CAACgB,cAAc,CAAC;YAC9EpB,MAAM,CAACZ,KAAK,CAACiC,SAAS,GAAGrB,MAAM,CAACsB,YAAY,GAAG,IAAI;AACnDd,YAAAA,QAAQ,CAACR,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACe,WAAW,IAAI1B,OAAO,CAACW,KAAK,CAACW,gBAAgB,CAAC;AAC7Ef,YAAAA,MAAM,CAACZ,KAAK,CAAC8B,MAAM,GAAG,EAAE;AAC5B;UAEAV,QAAQ,CAACR,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACW,gBAAgB,CAAC;AAEhD,UAAA,IAAItB,OAAO,CAACW,KAAK,CAACgB,cAAc,EAAE;YAC9Bb,WAAW,CAACP,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACgB,cAAc,CAAC;AACrD;UAEApB,MAAM,CAACuB,2BAA2B,GAAG,YAAM;YACvChB,WAAW,CAACP,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACW,gBAAgB,CAAC;AAEnD,YAAA,IAAItB,OAAO,CAACW,KAAK,CAACoB,YAAY,EAAE;cAC5BhB,QAAQ,CAACR,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACoB,YAAY,CAAC;AAChD;YAEAxB,MAAM,CAACa,mBAAmB,CAAC,cAAc,EAAEb,MAAM,CAACuB,2BAA2B,CAAC;YAE9E,IAAI9B,OAAO,CAACW,KAAK,CAACW,gBAAgB,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;AACtDjB,cAAAA,MAAM,CAACZ,KAAK,CAACiC,SAAS,GAAG,EAAE;AAC/B;YAEArB,MAAM,CAACgB,uBAAuB,GAAG,KAAK;WACzC;UAEDhB,MAAM,CAACY,gBAAgB,CAAC,cAAc,EAAEZ,MAAM,CAACuB,2BAA2B,CAAC;AAC/E;AACJ,OAAC,MAAM;AACH,QAAA,IAAI9B,OAAO,CAACW,KAAK,CAACgB,cAAc,EAAE;UAC9Bb,WAAW,CAACP,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACgB,cAAc,CAAC;AACrD;AAEA,QAAA,IAAI3B,OAAO,CAACW,KAAK,CAACoB,YAAY,EAAE;UAC5BhB,QAAQ,CAACR,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACoB,YAAY,CAAC;AAChD;AACJ;AAEA,MAAA,IAAI/B,OAAO,CAACW,KAAK,CAACqB,kBAAkB,EAAE;QAClC,IAAI,CAACC,oBAAoB,CAAC1B,MAAM,EAAER,EAAE,EAAEC,OAAO,CAAC;AAClD;KACH;AACDkB,IAAAA,KAAK,WAALA,KAAKA,CAACX,MAAM,EAAEP,OAAO,EAAE;AACnB,MAAA,IAAIA,OAAO,CAACW,KAAK,CAACuB,gBAAgB,EAAE;AAChC,QAAA,IAAI,CAAC3B,MAAM,CAACgB,uBAAuB,EAAE;UACjChB,MAAM,CAACgB,uBAAuB,GAAG,IAAI;UACrCR,QAAQ,CAACR,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACuB,gBAAgB,CAAC;AAEhD,UAAA,IAAIlC,OAAO,CAACW,KAAK,CAACwB,cAAc,EAAE;YAC9BrB,WAAW,CAACP,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACwB,cAAc,CAAC;AACrD;UAEA5B,MAAM,CAAC6B,2BAA2B,GAAG,YAAM;YACvCtB,WAAW,CAACP,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACuB,gBAAgB,CAAC;AAEnD,YAAA,IAAIlC,OAAO,CAACW,KAAK,CAAC0B,YAAY,EAAE;cAC5BtB,QAAQ,CAACR,MAAM,EAAEP,OAAO,CAACW,KAAK,CAAC0B,YAAY,CAAC;AAChD;YAEA9B,MAAM,CAACa,mBAAmB,CAAC,cAAc,EAAEb,MAAM,CAAC6B,2BAA2B,CAAC;YAC9E7B,MAAM,CAACgB,uBAAuB,GAAG,KAAK;WACzC;UAEDhB,MAAM,CAACY,gBAAgB,CAAC,cAAc,EAAEZ,MAAM,CAAC6B,2BAA2B,CAAC;AAC/E;AACJ,OAAC,MAAM;AACH,QAAA,IAAIpC,OAAO,CAACW,KAAK,CAACwB,cAAc,EAAE;UAC9BrB,WAAW,CAACP,MAAM,EAAEP,OAAO,CAACW,KAAK,CAACwB,cAAc,CAAC;AACrD;AAEA,QAAA,IAAInC,OAAO,CAACW,KAAK,CAAC0B,YAAY,EAAE;UAC5BtB,QAAQ,CAACR,MAAM,EAAEP,OAAO,CAACW,KAAK,CAAC0B,YAAY,CAAC;AAChD;AACJ;AAEA,MAAA,IAAIrC,OAAO,CAACW,KAAK,CAACqB,kBAAkB,EAAE;AAClC,QAAA,IAAI,CAACX,sBAAsB,CAACd,MAAM,CAAC;AACvC;KACH;AACDC,IAAAA,aAAa,WAAbA,aAAaA,CAACT,EAAE,EAAEC,OAAO,EAAE;AACvB,MAAA,QAAQA,OAAO,CAACW,KAAK,CAAC2B,QAAQ;AAC1B,QAAA,KAAK,OAAO;UACR,OAAOvC,EAAE,CAACwC,kBAAkB;AAEhC,QAAA,KAAK,OAAO;UACR,OAAOxC,EAAE,CAACyC,sBAAsB;AAEpC,QAAA,KAAK,SAAS;UACV,OAAOzC,EAAE,CAAC0C,aAAa;AAE3B,QAAA,KAAK,cAAc;AACf,UAAA,OAAO1C,EAAE,CAAC0C,aAAa,CAACA,aAAa;AAEzC,QAAA;UACI,OAAOC,QAAQ,CAACC,aAAa,CAAC3C,OAAO,CAACW,KAAK,CAAC2B,QAAQ,CAAC;AAC7D;KACH;IACDL,oBAAoB,EAAA,SAApBA,oBAAoBA,CAAC1B,MAAM,EAAER,EAAE,EAAEC,OAAO,EAAE;AAAA,MAAA,IAAA4C,MAAA,GAAA,IAAA;AACtC,MAAA,IAAI,CAACrC,MAAM,CAACsC,8BAA8B,EAAE;AACxCtC,QAAAA,MAAM,CAACsC,8BAA8B,GAAG,UAACC,KAAK,EAAK;AAC/C,UAAA,IAAI,CAACF,MAAI,CAACG,SAAS,CAACxC,MAAM,CAAC,IAAIyC,gBAAgB,CAACzC,MAAM,CAAC,CAAC0C,gBAAgB,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;AAC/FL,YAAAA,MAAI,CAACvB,sBAAsB,CAACd,MAAM,CAAC;AACvC,WAAC,MAAM,IAAIqC,MAAI,CAACM,cAAc,CAACJ,KAAK,EAAEvC,MAAM,EAAER,EAAE,CAAC,EAAE;AAC/C6C,YAAAA,MAAI,CAAC1B,KAAK,CAACX,MAAM,EAAEP,OAAO,CAAC;AAC/B;SACH;QAEDO,MAAM,CAAC4C,aAAa,CAAChC,gBAAgB,CAAC,OAAO,EAAEZ,MAAM,CAACsC,8BAA8B,CAAC;AACzF;KACH;AACDxB,IAAAA,sBAAsB,EAAtBA,SAAAA,sBAAsBA,CAACd,MAAM,EAAE;MAC3B,IAAIA,MAAM,CAACsC,8BAA8B,EAAE;QACvCtC,MAAM,CAAC4C,aAAa,CAAC/B,mBAAmB,CAAC,OAAO,EAAEb,MAAM,CAACsC,8BAA8B,CAAC;QACxFtC,MAAM,CAACsC,8BAA8B,GAAG,IAAI;AAChD;KACH;AACDE,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACxC,MAAM,EAAE;AACd,MAAA,OAAOA,MAAM,CAACS,YAAY,KAAK,IAAI;KACtC;IACDkC,cAAc,EAAA,SAAdA,cAAcA,CAACJ,KAAK,EAAEvC,MAAM,EAAER,EAAE,EAAE;AAC9B,MAAA,OAAO,CAACA,EAAE,CAACqD,UAAU,CAACN,KAAK,CAACvC,MAAM,CAAC,IAAI,CAACR,EAAE,CAACsD,QAAQ,CAACP,KAAK,CAACvC,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC8C,QAAQ,CAACP,KAAK,CAACvC,MAAM,CAAC;AACvG;AACJ;AACJ,CAAC;;;;"}