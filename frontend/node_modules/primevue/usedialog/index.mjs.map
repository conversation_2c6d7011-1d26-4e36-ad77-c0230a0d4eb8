{"version": 3, "file": "index.mjs", "sources": ["../../src/usedialog/UseDialog.js"], "sourcesContent": ["import { inject } from 'vue';\n\nexport const PrimeVueDialogSymbol = Symbol();\n\nexport function useDialog() {\n    const PrimeVueDialog = inject(PrimeVueDialogSymbol);\n\n    if (!PrimeVueDialog) {\n        throw new Error('No PrimeVue Dialog provided!');\n    }\n\n    return PrimeVueDialog;\n}\n"], "names": ["PrimeVueDialogSymbol", "Symbol", "useDialog", "PrimeVueDialog", "inject", "Error"], "mappings": ";;AAEaA,IAAAA,oBAAoB,GAAGC,MAAM;AAEnC,SAASC,SAASA,GAAG;AACxB,EAAA,IAAMC,cAAc,GAAGC,MAAM,CAACJ,oBAAoB,CAAC;EAEnD,IAAI,CAACG,cAAc,EAAE;AACjB,IAAA,MAAM,IAAIE,KAAK,CAAC,8BAA8B,CAAC;AACnD;AAEA,EAAA,OAAOF,cAAc;AACzB;;;;"}