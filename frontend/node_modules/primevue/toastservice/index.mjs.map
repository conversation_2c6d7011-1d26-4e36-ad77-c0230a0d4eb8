{"version": 3, "file": "index.mjs", "sources": ["../../src/toastservice/ToastService.js"], "sourcesContent": ["import ToastEventBus from 'primevue/toasteventbus';\nimport { PrimeVueToastSymbol } from 'primevue/usetoast';\n\nexport default {\n    install: (app) => {\n        const ToastService = {\n            add: (message) => {\n                ToastEventBus.emit('add', message);\n            },\n            remove: (message) => {\n                ToastEventBus.emit('remove', message);\n            },\n            removeGroup: (group) => {\n                ToastEventBus.emit('remove-group', group);\n            },\n            removeAllGroups: () => {\n                ToastEventBus.emit('remove-all-groups');\n            }\n        };\n\n        app.config.globalProperties.$toast = ToastService;\n        app.provide(PrimeVueToastSymbol, ToastService);\n    }\n};\n"], "names": ["install", "app", "ToastService", "add", "message", "ToastEventBus", "emit", "remove", "removeGroup", "group", "removeAllGroups", "config", "globalProperties", "$toast", "provide", "PrimeVueToastSymbol"], "mappings": ";;;AAGA,mBAAe;AACXA,EAAAA,OAAO,EAAE,SAATA,OAAOA,CAAGC,GAAG,EAAK;AACd,IAAA,IAAMC,YAAY,GAAG;AACjBC,MAAAA,GAAG,EAAE,SAALA,GAAGA,CAAGC,OAAO,EAAK;AACdC,QAAAA,aAAa,CAACC,IAAI,CAAC,KAAK,EAAEF,OAAO,CAAC;OACrC;AACDG,MAAAA,MAAM,EAAE,SAARA,MAAMA,CAAGH,OAAO,EAAK;AACjBC,QAAAA,aAAa,CAACC,IAAI,CAAC,QAAQ,EAAEF,OAAO,CAAC;OACxC;AACDI,MAAAA,WAAW,EAAE,SAAbA,WAAWA,CAAGC,KAAK,EAAK;AACpBJ,QAAAA,aAAa,CAACC,IAAI,CAAC,cAAc,EAAEG,KAAK,CAAC;OAC5C;AACDC,MAAAA,eAAe,EAAE,SAAjBA,eAAeA,GAAQ;AACnBL,QAAAA,aAAa,CAACC,IAAI,CAAC,mBAAmB,CAAC;AAC3C;KACH;AAEDL,IAAAA,GAAG,CAACU,MAAM,CAACC,gBAAgB,CAACC,MAAM,GAAGX,YAAY;AACjDD,IAAAA,GAAG,CAACa,OAAO,CAACC,mBAAmB,EAAEb,YAAY,CAAC;AAClD;AACJ,CAAC;;;;"}