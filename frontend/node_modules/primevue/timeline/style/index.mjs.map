{"version": 3, "file": "index.mjs", "sources": ["../../../src/timeline/style/TimelineStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/timeline';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => ['p-timeline p-component', 'p-timeline-' + props.align, 'p-timeline-' + props.layout],\n    event: 'p-timeline-event',\n    eventOpposite: 'p-timeline-event-opposite',\n    eventSeparator: 'p-timeline-event-separator',\n    eventMarker: 'p-timeline-event-marker',\n    eventConnector: 'p-timeline-event-connector',\n    eventContent: 'p-timeline-event-content'\n};\n\nexport default BaseStyle.extend({\n    name: 'timeline',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "props", "align", "layout", "event", "eventOpposite", "eventSeparator", "event<PERSON><PERSON>er", "eventConnector", "eventContent", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;AAAA,IAAA,OAAO,CAAC,wBAAwB,EAAE,aAAa,GAAGA,KAAK,CAACC,KAAK,EAAE,aAAa,GAAGD,KAAK,CAACE,MAAM,CAAC;AAAA,GAAA;AAC1GC,EAAAA,KAAK,EAAE,kBAAkB;AACzBC,EAAAA,aAAa,EAAE,2BAA2B;AAC1CC,EAAAA,cAAc,EAAE,4BAA4B;AAC5CC,EAAAA,WAAW,EAAE,yBAAyB;AACtCC,EAAAA,cAAc,EAAE,4BAA4B;AAC5CC,EAAAA,YAAY,EAAE;AAClB,CAAC;AAED,oBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,UAAU;AAChBC,EAAAA,KAAK,EAALA,KAAK;AACLf,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}