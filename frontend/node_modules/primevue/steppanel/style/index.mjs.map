{"version": 3, "file": "index.mjs", "sources": ["../../../src/steppanel/style/StepPanelStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance }) => [\n        'p-steppanel',\n        {\n            'p-steppanel-active': instance.isVertical && instance.active\n        }\n    ],\n    content: 'p-steppanel-content'\n};\n\nexport default BaseStyle.extend({\n    name: 'steppanel',\n    classes\n});\n"], "names": ["classes", "root", "_ref", "instance", "isVertical", "active", "content", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAA,OAAO,CACpB,aAAa,EACb;AACI,MAAA,oBAAoB,EAAEA,QAAQ,CAACC,UAAU,IAAID,QAAQ,CAACE;AAC1D,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,OAAO,EAAE;AACb,CAAC;AAED,qBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,WAAW;AACjBT,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}