{"version": 3, "file": "index.mjs", "sources": ["../../../src/step/style/StepStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance }) => [\n        'p-step',\n        {\n            'p-step-active': instance.active,\n            'p-disabled': instance.isStepDisabled\n        }\n    ],\n    header: 'p-step-header',\n    number: 'p-step-number',\n    title: 'p-step-title'\n};\n\nexport default BaseStyle.extend({\n    name: 'step',\n    classes\n});\n"], "names": ["classes", "root", "_ref", "instance", "active", "isStepDisabled", "header", "number", "title", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAA,OAAO,CACpB,QAAQ,EACR;MACI,eAAe,EAAEA,QAAQ,CAACC,MAAM;MAChC,YAAY,EAAED,QAAQ,CAACE;AAC3B,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,MAAM,EAAE,eAAe;AACvBC,EAAAA,MAAM,EAAE,eAAe;AACvBC,EAAAA,KAAK,EAAE;AACX,CAAC;AAED,gBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,MAAM;AACZX,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}