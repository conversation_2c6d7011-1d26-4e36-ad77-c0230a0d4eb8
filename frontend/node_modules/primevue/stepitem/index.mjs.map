{"version": 3, "file": "index.mjs", "sources": ["../../src/stepitem/BaseStepItem.vue", "../../src/stepitem/StepItem.vue", "../../src/stepitem/StepItem.vue?vue&type=template&id=6d905456&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport StepItemStyle from 'primevue/stepitem/style';\n\nexport default {\n    name: 'BaseStepItem',\n    extends: BaseComponent,\n    props: {\n        value: {\n            type: [String, Number],\n            default: undefined\n        }\n    },\n    style: StepItemStyle,\n    provide() {\n        return {\n            $pcStepItem: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :data-p-active=\"isActive\" v-bind=\"ptmi('root')\">\n        <slot />\n    </div>\n</template>\n\n<script>\nimport BaseStepItem from './BaseStepItem.vue';\n\nexport default {\n    name: 'StepItem',\n    extends: BaseStepItem,\n    inheritAttrs: false,\n    inject: ['$pcStepper'],\n    computed: {\n        isActive() {\n            return this.$pcStepper?.d_value === this.value;\n        }\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :data-p-active=\"isActive\" v-bind=\"ptmi('root')\">\n        <slot />\n    </div>\n</template>\n\n<script>\nimport BaseStepItem from './BaseStepItem.vue';\n\nexport default {\n    name: 'StepItem',\n    extends: BaseStepItem,\n    inheritAttrs: false,\n    inject: ['$pcStepper'],\n    computed: {\n        isActive() {\n            return this.$pcStepper?.d_value === this.value;\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "value", "type", "String", "Number", "undefined", "style", "StepItemStyle", "provide", "$pcStepItem", "$parentInstance", "BaseStepItem", "inheritAttrs", "inject", "computed", "isActive", "_this$$pcStepper", "$pcStepper", "d_value", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "$options", "ptmi", "_renderSlot", "$slots"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,cAAc;AACpB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;MACtB,SAASC,EAAAA;AACb;GACH;AACDC,EAAAA,KAAK,EAAEC,aAAa;EACpBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,WAAW,EAAE,IAAI;AACjBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACXD,aAAe;AACXZ,EAAAA,IAAI,EAAE,UAAU;AAChB,EAAA,SAAA,EAASa,QAAY;AACrBC,EAAAA,YAAY,EAAE,KAAK;EACnBC,MAAM,EAAE,CAAC,YAAY,CAAC;AACtBC,EAAAA,QAAQ,EAAE;IACNC,QAAQ,EAAA,SAARA,QAAQA,GAAG;AAAA,MAAA,IAAAC,gBAAA;AACP,MAAA,OAAO,CAAAA,CAAAA,gBAAA,GAAI,IAAA,CAACC,UAAU,MAAAD,IAAAA,IAAAA,gBAAA,KAAfA,MAAAA,GAAAA,MAAAA,GAAAA,gBAAA,CAAiBE,aAAY,IAAI,CAACjB,KAAK;AAClD;AACJ;AACJ,CAAC;;;;EClBG,OAAAkB,SAAA,EAAA,EAAAC,kBAAA,CAEK,OAFLC,UAEK,CAAA;AAFC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;IAAW,eAAa,EAAEC,QAAQ,CAAAT;KAAUO,IAAI,CAAAG,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAC3DC,UAAO,CAAAJ,IAAA,CAAAK,MAAA,EAAA,SAAA,CAAA;;;;;;;"}