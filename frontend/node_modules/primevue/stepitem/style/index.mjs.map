{"version": 3, "file": "index.mjs", "sources": ["../../../src/stepitem/style/StepItemStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance }) => [\n        'p-stepitem',\n        {\n            'p-stepitem-active': instance.isActive\n        }\n    ]\n};\n\nexport default BaseStyle.extend({\n    name: 'stepitem',\n    classes\n});\n"], "names": ["classes", "root", "_ref", "instance", "isActive", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAA,OAAO,CACpB,YAAY,EACZ;MACI,mBAAmB,EAAEA,QAAQ,CAACC;AAClC,KAAC,CACJ;AAAA;AACL,CAAC;AAED,oBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,UAAU;AAChBP,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}