{"version": 3, "file": "index.mjs", "sources": ["../../../src/toast/style/ToastStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/toast';\nimport BaseStyle from '@primevue/core/base/style';\n\n// Position\nconst inlineStyles = {\n    root: ({ position }) => ({\n        position: 'fixed',\n        top: position === 'top-right' || position === 'top-left' || position === 'top-center' ? '20px' : position === 'center' ? '50%' : null,\n        right: (position === 'top-right' || position === 'bottom-right') && '20px',\n        bottom: (position === 'bottom-left' || position === 'bottom-right' || position === 'bottom-center') && '20px',\n        left: position === 'top-left' || position === 'bottom-left' ? '20px' : position === 'center' || position === 'top-center' || position === 'bottom-center' ? '50%' : null\n    })\n};\n\nconst classes = {\n    root: ({ props }) => ['p-toast p-component p-toast-' + props.position],\n    message: ({ props }) => [\n        'p-toast-message',\n        {\n            'p-toast-message-info': props.message.severity === 'info' || props.message.severity === undefined,\n            'p-toast-message-warn': props.message.severity === 'warn',\n            'p-toast-message-error': props.message.severity === 'error',\n            'p-toast-message-success': props.message.severity === 'success',\n            'p-toast-message-secondary': props.message.severity === 'secondary',\n            'p-toast-message-contrast': props.message.severity === 'contrast'\n        }\n    ],\n    messageContent: 'p-toast-message-content',\n    messageIcon: ({ props }) => [\n        'p-toast-message-icon',\n        {\n            [props.infoIcon]: props.message.severity === 'info',\n            [props.warnIcon]: props.message.severity === 'warn',\n            [props.errorIcon]: props.message.severity === 'error',\n            [props.successIcon]: props.message.severity === 'success'\n        }\n    ],\n    messageText: 'p-toast-message-text',\n    summary: 'p-toast-summary',\n    detail: 'p-toast-detail',\n    closeButton: 'p-toast-close-button',\n    closeIcon: 'p-toast-close-icon'\n};\n\nexport default BaseStyle.extend({\n    name: 'toast',\n    style,\n    classes,\n    inlineStyles\n});\n"], "names": ["inlineStyles", "root", "_ref", "position", "top", "right", "bottom", "left", "classes", "_ref2", "props", "message", "_ref3", "severity", "undefined", "messageContent", "messageIcon", "_ref4", "_defineProperty", "infoIcon", "warnIcon", "errorIcon", "successIcon", "messageText", "summary", "detail", "closeButton", "closeIcon", "BaseStyle", "extend", "name", "style"], "mappings": ";;;;;;;;AAGA;AACA,IAAMA,YAAY,GAAG;AACjBC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAA,OAAQ;AACrBA,MAAAA,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAED,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,UAAU,IAAIA,QAAQ,KAAK,YAAY,GAAG,MAAM,GAAGA,QAAQ,KAAK,QAAQ,GAAG,KAAK,GAAG,IAAI;MACrIE,KAAK,EAAE,CAACF,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,cAAc,KAAK,MAAM;AAC1EG,MAAAA,MAAM,EAAE,CAACH,QAAQ,KAAK,aAAa,IAAIA,QAAQ,KAAK,cAAc,IAAIA,QAAQ,KAAK,eAAe,KAAK,MAAM;MAC7GI,IAAI,EAAEJ,QAAQ,KAAK,UAAU,IAAIA,QAAQ,KAAK,aAAa,GAAG,MAAM,GAAGA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,eAAe,GAAG,KAAK,GAAG;KACvK;AAAA;AACL,CAAC;AAED,IAAMK,OAAO,GAAG;AACZP,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAQ,KAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,KAAA,CAALC,KAAK;AAAA,IAAA,OAAO,CAAC,8BAA8B,GAAGA,KAAK,CAACP,QAAQ,CAAC;AAAA,GAAA;AACtEQ,EAAAA,OAAO,EAAE,SAATA,OAAOA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKF,KAAK,GAAAE,KAAA,CAALF,KAAK;IAAA,OAAO,CACpB,iBAAiB,EACjB;AACI,MAAA,sBAAsB,EAAEA,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK,MAAM,IAAIH,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAKC,SAAS;AACjG,MAAA,sBAAsB,EAAEJ,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK,MAAM;AACzD,MAAA,uBAAuB,EAAEH,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK,OAAO;AAC3D,MAAA,yBAAyB,EAAEH,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK,SAAS;AAC/D,MAAA,2BAA2B,EAAEH,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK,WAAW;AACnE,MAAA,0BAA0B,EAAEH,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK;AAC3D,KAAC,CACJ;AAAA,GAAA;AACDE,EAAAA,cAAc,EAAE,yBAAyB;AACzCC,EAAAA,WAAW,EAAE,SAAbA,WAAWA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKP,KAAK,GAAAO,KAAA,CAALP,KAAK;IAAA,OAAO,CACxB,sBAAsB,EAAAQ,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAA,EAAA,EAEjBR,KAAK,CAACS,QAAQ,EAAGT,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK,MAAM,CAAA,EAClDH,KAAK,CAACU,QAAQ,EAAGV,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK,MAAM,CAClDH,EAAAA,KAAK,CAACW,SAAS,EAAGX,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK,OAAO,CACpDH,EAAAA,KAAK,CAACY,WAAW,EAAGZ,KAAK,CAACC,OAAO,CAACE,QAAQ,KAAK,SAAS,CAEhE,CAAA;AAAA,GAAA;AACDU,EAAAA,WAAW,EAAE,sBAAsB;AACnCC,EAAAA,OAAO,EAAE,iBAAiB;AAC1BC,EAAAA,MAAM,EAAE,gBAAgB;AACxBC,EAAAA,WAAW,EAAE,sBAAsB;AACnCC,EAAAA,SAAS,EAAE;AACf,CAAC;AAED,iBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,OAAO;AACbC,EAAAA,KAAK,EAALA,KAAK;AACLvB,EAAAA,OAAO,EAAPA,OAAO;AACPR,EAAAA,YAAY,EAAZA;AACJ,CAAC,CAAC;;;;"}