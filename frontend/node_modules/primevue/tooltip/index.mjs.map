{"version": 3, "file": "index.mjs", "sources": ["../../src/tooltip/BaseTooltip.js", "../../src/tooltip/Tooltip.js"], "sourcesContent": ["import BaseDirective from '@primevue/core/basedirective';\nimport TooltipStyle from 'primevue/tooltip/style';\n\nconst BaseTooltip = BaseDirective.extend({\n    style: TooltipStyle\n});\n\nexport default BaseTooltip;\n", "import { addClass, createElement, fadeIn, findSingle, getAttribute, getOuterHeight, getOuterWidth, getViewport, getWindowScrollLeft, getWindowScrollTop, hasClass, isExist, isTouchDevice, removeClass } from '@primeuix/utils/dom';\nimport { isEmpty } from '@primeuix/utils/object';\nimport { uuid } from '@primeuix/utils/uuid';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { ConnectedOverlayScrollHandler } from '@primevue/core/utils';\nimport BaseTooltip from './BaseTooltip';\n\nconst Tooltip = BaseTooltip.extend('tooltip', {\n    beforeMount(el, options) {\n        let target = this.getTarget(el);\n\n        target.$_ptooltipModifiers = this.getModifiers(options);\n\n        if (!options.value) return;\n        else if (typeof options.value === 'string') {\n            target.$_ptooltipValue = options.value;\n            target.$_ptooltipDisabled = false;\n            target.$_ptooltipEscape = true;\n            target.$_ptooltipClass = null;\n            target.$_ptooltipFitContent = true;\n            target.$_ptooltipIdAttr = uuid('pv_id') + '_tooltip';\n            target.$_ptooltipShowDelay = 0;\n            target.$_ptooltipHideDelay = 0;\n            target.$_ptooltipAutoHide = true;\n        } else if (typeof options.value === 'object' && options.value) {\n            if (isEmpty(options.value.value) || options.value.value.trim() === '') return;\n            else {\n                target.$_ptooltipValue = options.value.value;\n                target.$_ptooltipDisabled = !!options.value.disabled === options.value.disabled ? options.value.disabled : false;\n                target.$_ptooltipEscape = !!options.value.escape === options.value.escape ? options.value.escape : true;\n                target.$_ptooltipClass = options.value.class || '';\n                target.$_ptooltipFitContent = !!options.value.fitContent === options.value.fitContent ? options.value.fitContent : true;\n                target.$_ptooltipIdAttr = options.value.id || uuid('pv_id') + '_tooltip';\n                target.$_ptooltipShowDelay = options.value.showDelay || 0;\n                target.$_ptooltipHideDelay = options.value.hideDelay || 0;\n                target.$_ptooltipAutoHide = !!options.value.autoHide === options.value.autoHide ? options.value.autoHide : true;\n            }\n        }\n\n        target.$_ptooltipZIndex = options.instance.$primevue?.config?.zIndex?.tooltip;\n\n        this.bindEvents(target, options);\n\n        el.setAttribute('data-pd-tooltip', true);\n    },\n    updated(el, options) {\n        let target = this.getTarget(el);\n\n        target.$_ptooltipModifiers = this.getModifiers(options);\n        this.unbindEvents(target);\n\n        if (!options.value) {\n            return;\n        }\n\n        if (typeof options.value === 'string') {\n            target.$_ptooltipValue = options.value;\n            target.$_ptooltipDisabled = false;\n            target.$_ptooltipEscape = true;\n            target.$_ptooltipClass = null;\n            target.$_ptooltipIdAttr = target.$_ptooltipIdAttr || uuid('pv_id') + '_tooltip';\n            target.$_ptooltipShowDelay = 0;\n            target.$_ptooltipHideDelay = 0;\n            target.$_ptooltipAutoHide = true;\n\n            this.bindEvents(target, options);\n        } else if (typeof options.value === 'object' && options.value) {\n            if (isEmpty(options.value.value) || options.value.value.trim() === '') {\n                this.unbindEvents(target, options);\n\n                return;\n            } else {\n                target.$_ptooltipValue = options.value.value;\n                target.$_ptooltipDisabled = !!options.value.disabled === options.value.disabled ? options.value.disabled : false;\n                target.$_ptooltipEscape = !!options.value.escape === options.value.escape ? options.value.escape : true;\n                target.$_ptooltipClass = options.value.class || '';\n                target.$_ptooltipFitContent = !!options.value.fitContent === options.value.fitContent ? options.value.fitContent : true;\n                target.$_ptooltipIdAttr = options.value.id || target.$_ptooltipIdAttr || uuid('pv_id') + '_tooltip';\n                target.$_ptooltipShowDelay = options.value.showDelay || 0;\n                target.$_ptooltipHideDelay = options.value.hideDelay || 0;\n                target.$_ptooltipAutoHide = !!options.value.autoHide === options.value.autoHide ? options.value.autoHide : true;\n\n                this.bindEvents(target, options);\n            }\n        }\n    },\n    unmounted(el, options) {\n        let target = this.getTarget(el);\n\n        this.hide(el, 0);\n        this.remove(target);\n        this.unbindEvents(target, options);\n\n        if (target.$_ptooltipScrollHandler) {\n            target.$_ptooltipScrollHandler.destroy();\n            target.$_ptooltipScrollHandler = null;\n        }\n    },\n    timer: undefined,\n    methods: {\n        bindEvents(el, options) {\n            const modifiers = el.$_ptooltipModifiers;\n\n            if (modifiers.focus) {\n                el.$_ptooltipFocusEvent = (event) => this.onFocus(event, options);\n                el.$_ptooltipBlurEvent = this.onBlur.bind(this);\n\n                el.addEventListener('focus', el.$_ptooltipFocusEvent);\n                el.addEventListener('blur', el.$_ptooltipBlurEvent);\n            } else {\n                el.$_ptooltipMouseEnterEvent = (event) => this.onMouseEnter(event, options);\n                el.$_ptooltipMouseLeaveEvent = this.onMouseLeave.bind(this);\n                el.$_ptooltipClickEvent = this.onClick.bind(this);\n\n                el.addEventListener('mouseenter', el.$_ptooltipMouseEnterEvent);\n                el.addEventListener('mouseleave', el.$_ptooltipMouseLeaveEvent);\n                el.addEventListener('click', el.$_ptooltipClickEvent);\n            }\n\n            el.$_ptooltipKeydownEvent = this.onKeydown.bind(this);\n            el.addEventListener('keydown', el.$_ptooltipKeydownEvent);\n\n            el.$_pWindowResizeEvent = this.onWindowResize.bind(this, el);\n        },\n        unbindEvents(el) {\n            const modifiers = el.$_ptooltipModifiers;\n\n            if (modifiers.focus) {\n                el.removeEventListener('focus', el.$_ptooltipFocusEvent);\n                el.$_ptooltipFocusEvent = null;\n\n                el.removeEventListener('blur', el.$_ptooltipBlurEvent);\n                el.$_ptooltipBlurEvent = null;\n            } else {\n                el.removeEventListener('mouseenter', el.$_ptooltipMouseEnterEvent);\n                el.$_ptooltipMouseEnterEvent = null;\n\n                el.removeEventListener('mouseleave', el.$_ptooltipMouseLeaveEvent);\n                el.$_ptooltipMouseLeaveEvent = null;\n\n                el.removeEventListener('click', el.$_ptooltipClickEvent);\n                el.$_ptooltipClickEvent = null;\n            }\n\n            el.removeEventListener('keydown', el.$_ptooltipKeydownEvent);\n            window.removeEventListener('resize', el.$_pWindowResizeEvent);\n\n            if (el.$_ptooltipId) {\n                this.remove(el);\n            }\n        },\n        bindScrollListener(el) {\n            if (!el.$_ptooltipScrollHandler) {\n                el.$_ptooltipScrollHandler = new ConnectedOverlayScrollHandler(el, () => {\n                    this.hide(el);\n                });\n            }\n\n            el.$_ptooltipScrollHandler.bindScrollListener();\n        },\n        unbindScrollListener(el) {\n            if (el.$_ptooltipScrollHandler) {\n                el.$_ptooltipScrollHandler.unbindScrollListener();\n            }\n        },\n        onMouseEnter(event, options) {\n            const el = event.currentTarget;\n            const showDelay = el.$_ptooltipShowDelay;\n\n            this.show(el, options, showDelay);\n        },\n        onMouseLeave(event) {\n            const el = event.currentTarget;\n            const hideDelay = el.$_ptooltipHideDelay;\n            const autoHide = el.$_ptooltipAutoHide;\n\n            if (!autoHide) {\n                const valid =\n                    getAttribute(event.target, 'data-pc-name') === 'tooltip' ||\n                    getAttribute(event.target, 'data-pc-section') === 'arrow' ||\n                    getAttribute(event.target, 'data-pc-section') === 'text' ||\n                    getAttribute(event.relatedTarget, 'data-pc-name') === 'tooltip' ||\n                    getAttribute(event.relatedTarget, 'data-pc-section') === 'arrow' ||\n                    getAttribute(event.relatedTarget, 'data-pc-section') === 'text';\n\n                !valid && this.hide(el, hideDelay);\n            } else {\n                this.hide(el, hideDelay);\n            }\n        },\n        onFocus(event, options) {\n            const el = event.currentTarget;\n            const showDelay = el.$_ptooltipShowDelay;\n\n            this.show(el, options, showDelay);\n        },\n        onBlur(event) {\n            const el = event.currentTarget;\n            const hideDelay = el.$_ptooltipHideDelay;\n\n            this.hide(el, hideDelay);\n        },\n        onClick(event) {\n            const el = event.currentTarget;\n            const hideDelay = el.$_ptooltipHideDelay;\n\n            this.hide(el, hideDelay);\n        },\n        onKeydown(event) {\n            const el = event.currentTarget;\n            const hideDelay = el.$_ptooltipHideDelay;\n\n            event.code === 'Escape' && this.hide(event.currentTarget, hideDelay);\n        },\n        onWindowResize(el) {\n            if (!isTouchDevice()) {\n                this.hide(el);\n            }\n\n            window.removeEventListener('resize', el.$_pWindowResizeEvent);\n        },\n        tooltipActions(el, options) {\n            if (el.$_ptooltipDisabled || !isExist(el)) {\n                return;\n            }\n\n            let tooltipElement = this.create(el, options);\n\n            this.align(el);\n            !this.isUnstyled() && fadeIn(tooltipElement, 250);\n\n            const $this = this;\n\n            window.addEventListener('resize', el.$_pWindowResizeEvent);\n\n            tooltipElement.addEventListener('mouseleave', function onTooltipLeave() {\n                $this.hide(el);\n\n                tooltipElement.removeEventListener('mouseleave', onTooltipLeave);\n                el.removeEventListener('mouseenter', el.$_ptooltipMouseEnterEvent);\n                setTimeout(() => el.addEventListener('mouseenter', el.$_ptooltipMouseEnterEvent), 50);\n            });\n\n            this.bindScrollListener(el);\n            ZIndex.set('tooltip', tooltipElement, el.$_ptooltipZIndex);\n        },\n        show(el, options, showDelay) {\n            if (showDelay !== undefined) {\n                this.timer = setTimeout(() => this.tooltipActions(el, options), showDelay);\n            } else {\n                this.tooltipActions(el, options);\n            }\n        },\n        tooltipRemoval(el) {\n            this.remove(el);\n            this.unbindScrollListener(el);\n            window.removeEventListener('resize', el.$_pWindowResizeEvent);\n        },\n        hide(el, hideDelay) {\n            clearTimeout(this.timer);\n\n            if (hideDelay !== undefined) {\n                setTimeout(() => this.tooltipRemoval(el), hideDelay);\n            } else {\n                this.tooltipRemoval(el);\n            }\n        },\n        getTooltipElement(el) {\n            return document.getElementById(el.$_ptooltipId);\n        },\n        getArrowElement(el) {\n            let tooltipElement = this.getTooltipElement(el);\n\n            return findSingle(tooltipElement, '[data-pc-section=\"arrow\"]');\n        },\n        create(el) {\n            const modifiers = el.$_ptooltipModifiers;\n\n            const tooltipArrow = createElement('div', {\n                class: !this.isUnstyled() && this.cx('arrow'),\n                'p-bind': this.ptm('arrow', {\n                    context: modifiers\n                })\n            });\n\n            const tooltipText = createElement('div', {\n                class: !this.isUnstyled() && this.cx('text'),\n                'p-bind': this.ptm('text', {\n                    context: modifiers\n                })\n            });\n\n            if (!el.$_ptooltipEscape) {\n                tooltipText.innerHTML = el.$_ptooltipValue;\n            } else {\n                tooltipText.innerHTML = '';\n                tooltipText.appendChild(document.createTextNode(el.$_ptooltipValue));\n            }\n\n            const container = createElement(\n                'div',\n                {\n                    id: el.$_ptooltipIdAttr,\n                    role: 'tooltip',\n                    style: {\n                        display: 'inline-block',\n                        width: el.$_ptooltipFitContent ? 'fit-content' : undefined,\n                        pointerEvents: !this.isUnstyled() && el.$_ptooltipAutoHide && 'none'\n                    },\n                    class: [!this.isUnstyled() && this.cx('root'), el.$_ptooltipClass],\n                    [this.$attrSelector]: '',\n                    'p-bind': this.ptm('root', {\n                        context: modifiers\n                    })\n                },\n                tooltipArrow,\n                tooltipText\n            );\n\n            document.body.appendChild(container);\n\n            el.$_ptooltipId = container.id;\n            this.$el = container;\n\n            return container;\n        },\n        remove(el) {\n            if (el) {\n                let tooltipElement = this.getTooltipElement(el);\n\n                if (tooltipElement && tooltipElement.parentElement) {\n                    ZIndex.clear(tooltipElement);\n                    document.body.removeChild(tooltipElement);\n                }\n\n                el.$_ptooltipId = null;\n            }\n        },\n        align(el) {\n            const modifiers = el.$_ptooltipModifiers;\n\n            if (modifiers.top) {\n                this.alignTop(el);\n\n                if (this.isOutOfBounds(el)) {\n                    this.alignBottom(el);\n\n                    if (this.isOutOfBounds(el)) {\n                        this.alignTop(el);\n                    }\n                }\n            } else if (modifiers.left) {\n                this.alignLeft(el);\n\n                if (this.isOutOfBounds(el)) {\n                    this.alignRight(el);\n\n                    if (this.isOutOfBounds(el)) {\n                        this.alignTop(el);\n\n                        if (this.isOutOfBounds(el)) {\n                            this.alignBottom(el);\n\n                            if (this.isOutOfBounds(el)) {\n                                this.alignLeft(el);\n                            }\n                        }\n                    }\n                }\n            } else if (modifiers.bottom) {\n                this.alignBottom(el);\n\n                if (this.isOutOfBounds(el)) {\n                    this.alignTop(el);\n\n                    if (this.isOutOfBounds(el)) {\n                        this.alignBottom(el);\n                    }\n                }\n            } else {\n                this.alignRight(el);\n\n                if (this.isOutOfBounds(el)) {\n                    this.alignLeft(el);\n\n                    if (this.isOutOfBounds(el)) {\n                        this.alignTop(el);\n\n                        if (this.isOutOfBounds(el)) {\n                            this.alignBottom(el);\n\n                            if (this.isOutOfBounds(el)) {\n                                this.alignRight(el);\n                            }\n                        }\n                    }\n                }\n            }\n        },\n        getHostOffset(el) {\n            let offset = el.getBoundingClientRect();\n            let targetLeft = offset.left + getWindowScrollLeft();\n            let targetTop = offset.top + getWindowScrollTop();\n\n            return { left: targetLeft, top: targetTop };\n        },\n        alignRight(el) {\n            this.preAlign(el, 'right');\n            let tooltipElement = this.getTooltipElement(el);\n            let arrowElement = this.getArrowElement(el);\n            let hostOffset = this.getHostOffset(el);\n            let left = hostOffset.left + getOuterWidth(el);\n            let top = hostOffset.top + (getOuterHeight(el) - getOuterHeight(tooltipElement)) / 2;\n\n            tooltipElement.style.left = left + 'px';\n            tooltipElement.style.top = top + 'px';\n\n            arrowElement.style.top = '50%';\n            arrowElement.style.right = null;\n            arrowElement.style.bottom = null;\n            arrowElement.style.left = '0';\n        },\n        alignLeft(el) {\n            this.preAlign(el, 'left');\n            let tooltipElement = this.getTooltipElement(el);\n            let arrowElement = this.getArrowElement(el);\n            let hostOffset = this.getHostOffset(el);\n            let left = hostOffset.left - getOuterWidth(tooltipElement);\n            let top = hostOffset.top + (getOuterHeight(el) - getOuterHeight(tooltipElement)) / 2;\n\n            tooltipElement.style.left = left + 'px';\n            tooltipElement.style.top = top + 'px';\n\n            arrowElement.style.top = '50%';\n            arrowElement.style.right = '0';\n            arrowElement.style.bottom = null;\n            arrowElement.style.left = null;\n        },\n        alignTop(el) {\n            this.preAlign(el, 'top');\n            let tooltipElement = this.getTooltipElement(el);\n            let arrowElement = this.getArrowElement(el);\n            let tooltipWidth = getOuterWidth(tooltipElement);\n            let elementWidth = getOuterWidth(el);\n            let { width: viewportWidth } = getViewport();\n            let hostOffset = this.getHostOffset(el);\n            let left = hostOffset.left + (elementWidth - tooltipWidth) / 2;\n            let top = hostOffset.top - getOuterHeight(tooltipElement);\n\n            if (left < 0) {\n                left = 0;\n            } else if (left + tooltipWidth > viewportWidth) {\n                left = Math.floor(hostOffset.left + elementWidth - tooltipWidth);\n            }\n\n            tooltipElement.style.left = left + 'px';\n            tooltipElement.style.top = top + 'px';\n\n            // The center of the target relative to the tooltip\n            let elementRelativeCenter = hostOffset.left - this.getHostOffset(tooltipElement).left + elementWidth / 2;\n\n            arrowElement.style.top = null;\n            arrowElement.style.right = null;\n            arrowElement.style.bottom = '0';\n            arrowElement.style.left = elementRelativeCenter + 'px';\n        },\n        alignBottom(el) {\n            this.preAlign(el, 'bottom');\n            let tooltipElement = this.getTooltipElement(el);\n            let arrowElement = this.getArrowElement(el);\n            let tooltipWidth = getOuterWidth(tooltipElement);\n            let elementWidth = getOuterWidth(el);\n            let { width: viewportWidth } = getViewport();\n            let hostOffset = this.getHostOffset(el);\n\n            let left = hostOffset.left + (elementWidth - tooltipWidth) / 2;\n            let top = hostOffset.top + getOuterHeight(el);\n\n            if (left < 0) {\n                left = 0;\n            } else if (left + tooltipWidth > viewportWidth) {\n                left = Math.floor(hostOffset.left + elementWidth - tooltipWidth);\n            }\n\n            tooltipElement.style.left = left + 'px';\n            tooltipElement.style.top = top + 'px';\n\n            // The center of the target relative to the tooltip\n            let elementRelativeCenter = hostOffset.left - this.getHostOffset(tooltipElement).left + elementWidth / 2;\n\n            arrowElement.style.top = '0';\n            arrowElement.style.right = null;\n            arrowElement.style.bottom = null;\n            arrowElement.style.left = elementRelativeCenter + 'px';\n        },\n        preAlign(el, position) {\n            let tooltipElement = this.getTooltipElement(el);\n\n            tooltipElement.style.left = -999 + 'px';\n            tooltipElement.style.top = -999 + 'px';\n            removeClass(tooltipElement, `p-tooltip-${tooltipElement.$_ptooltipPosition}`);\n            !this.isUnstyled() && addClass(tooltipElement, `p-tooltip-${position}`);\n            tooltipElement.$_ptooltipPosition = position;\n            tooltipElement.setAttribute('data-p-position', position);\n        },\n        isOutOfBounds(el) {\n            let tooltipElement = this.getTooltipElement(el);\n            let offset = tooltipElement.getBoundingClientRect();\n            let targetTop = offset.top;\n            let targetLeft = offset.left;\n            let width = getOuterWidth(tooltipElement);\n            let height = getOuterHeight(tooltipElement);\n            let viewport = getViewport();\n\n            return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n        },\n        getTarget(el) {\n            return hasClass(el, 'p-inputwrapper') ? (findSingle(el, 'input') ?? el) : el;\n        },\n        getModifiers(options) {\n            // modifiers\n            if (options.modifiers && Object.keys(options.modifiers).length) {\n                return options.modifiers;\n            }\n\n            // arg\n            if (options.arg && typeof options.arg === 'object') {\n                return Object.entries(options.arg).reduce((acc, [key, val]) => {\n                    if (key === 'event' || key === 'position') acc[val] = true;\n\n                    return acc;\n                }, {});\n            }\n\n            return {};\n        }\n    }\n});\n\nexport default Tooltip;\n"], "names": ["BaseTooltip", "BaseDirective", "extend", "style", "TooltipStyle", "<PERSON><PERSON><PERSON>", "beforeMount", "el", "options", "_options$instance$$pr", "target", "get<PERSON><PERSON><PERSON>", "$_ptooltipModifiers", "getModifiers", "value", "$_ptooltipValue", "$_ptooltipDisabled", "$_ptooltipEscape", "$_ptooltipClass", "$_ptooltipFitContent", "$_ptooltipIdAttr", "uuid", "$_ptooltipShowDelay", "$_ptooltipHideDelay", "$_ptooltipAutoHide", "_typeof", "isEmpty", "trim", "disabled", "escape", "<PERSON><PERSON><PERSON><PERSON>", "id", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "autoHide", "$_ptooltipZIndex", "instance", "$primevue", "config", "zIndex", "tooltip", "bindEvents", "setAttribute", "updated", "unbindEvents", "unmounted", "hide", "remove", "$_ptooltipScrollHandler", "destroy", "timer", "undefined", "methods", "_this", "modifiers", "focus", "$_ptooltipFocusEvent", "event", "onFocus", "$_ptooltipBlurEvent", "onBlur", "bind", "addEventListener", "$_ptooltipMouseEnterEvent", "onMouseEnter", "$_ptooltipMouseLeaveEvent", "onMouseLeave", "$_ptooltipClickEvent", "onClick", "$_ptooltipKeydownEvent", "onKeydown", "$_pWindowResizeEvent", "onWindowResize", "removeEventListener", "window", "$_ptooltipId", "bindScrollListener", "_this2", "ConnectedOverlayScrollHandler", "unbindScrollListener", "currentTarget", "show", "valid", "getAttribute", "relatedTarget", "code", "isTouchDevice", "tooltipActions", "isExist", "tooltipElement", "create", "align", "isUnstyled", "fadeIn", "$this", "onTooltipLeave", "setTimeout", "ZIndex", "set", "_this3", "tooltipRemoval", "_this4", "clearTimeout", "getTooltipElement", "document", "getElementById", "getArrowElement", "findSingle", "tooltipArrow", "createElement", "cx", "ptm", "context", "tooltipText", "innerHTML", "append<PERSON><PERSON><PERSON>", "createTextNode", "container", "_defineProperty", "role", "display", "width", "pointerEvents", "$attrSelector", "body", "$el", "parentElement", "clear", "<PERSON><PERSON><PERSON><PERSON>", "top", "alignTop", "isOutOfBounds", "alignBottom", "left", "alignLeft", "alignRight", "bottom", "getHostOffset", "offset", "getBoundingClientRect", "targetLeft", "getWindowScrollLeft", "targetTop", "getWindowScrollTop", "preAlign", "arrowElement", "hostOffset", "getOuterWidth", "getOuterHeight", "right", "tooltipWidth", "elementWidth", "_getViewport", "getViewport", "viewportWidth", "Math", "floor", "elementRelativeCenter", "_getViewport2", "position", "removeClass", "concat", "$_ptooltipPosition", "addClass", "height", "viewport", "_findSingle", "hasClass", "Object", "keys", "length", "arg", "entries", "reduce", "acc", "_ref", "_ref2", "_slicedToArray", "key", "val"], "mappings": ";;;;;;;;AAGA,IAAMA,WAAW,GAAGC,aAAa,CAACC,MAAM,CAAC;AACrCC,EAAAA,KAAK,EAAEC;AACX,CAAC,CAAC;;;;;;;;;;;;ACEF,IAAMC,OAAO,GAAGL,WAAW,CAACE,MAAM,CAAC,SAAS,EAAE;AAC1CI,EAAAA,WAAW,WAAXA,WAAWA,CAACC,EAAE,EAAEC,OAAO,EAAE;AAAA,IAAA,IAAAC,qBAAA;AACrB,IAAA,IAAIC,MAAM,GAAG,IAAI,CAACC,SAAS,CAACJ,EAAE,CAAC;IAE/BG,MAAM,CAACE,mBAAmB,GAAG,IAAI,CAACC,YAAY,CAACL,OAAO,CAAC;AAEvD,IAAA,IAAI,CAACA,OAAO,CAACM,KAAK,EAAE,OAAO,KACtB,IAAI,OAAON,OAAO,CAACM,KAAK,KAAK,QAAQ,EAAE;AACxCJ,MAAAA,MAAM,CAACK,eAAe,GAAGP,OAAO,CAACM,KAAK;MACtCJ,MAAM,CAACM,kBAAkB,GAAG,KAAK;MACjCN,MAAM,CAACO,gBAAgB,GAAG,IAAI;MAC9BP,MAAM,CAACQ,eAAe,GAAG,IAAI;MAC7BR,MAAM,CAACS,oBAAoB,GAAG,IAAI;MAClCT,MAAM,CAACU,gBAAgB,GAAGC,IAAI,CAAC,OAAO,CAAC,GAAG,UAAU;MACpDX,MAAM,CAACY,mBAAmB,GAAG,CAAC;MAC9BZ,MAAM,CAACa,mBAAmB,GAAG,CAAC;MAC9Bb,MAAM,CAACc,kBAAkB,GAAG,IAAI;AACpC,KAAC,MAAM,IAAIC,OAAA,CAAOjB,OAAO,CAACM,KAAK,CAAA,KAAK,QAAQ,IAAIN,OAAO,CAACM,KAAK,EAAE;MAC3D,IAAIY,OAAO,CAAClB,OAAO,CAACM,KAAK,CAACA,KAAK,CAAC,IAAIN,OAAO,CAACM,KAAK,CAACA,KAAK,CAACa,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,KACzE;AACDjB,QAAAA,MAAM,CAACK,eAAe,GAAGP,OAAO,CAACM,KAAK,CAACA,KAAK;QAC5CJ,MAAM,CAACM,kBAAkB,GAAG,CAAC,CAACR,OAAO,CAACM,KAAK,CAACc,QAAQ,KAAKpB,OAAO,CAACM,KAAK,CAACc,QAAQ,GAAGpB,OAAO,CAACM,KAAK,CAACc,QAAQ,GAAG,KAAK;QAChHlB,MAAM,CAACO,gBAAgB,GAAG,CAAC,CAACT,OAAO,CAACM,KAAK,CAACe,MAAM,KAAKrB,OAAO,CAACM,KAAK,CAACe,MAAM,GAAGrB,OAAO,CAACM,KAAK,CAACe,MAAM,GAAG,IAAI;QACvGnB,MAAM,CAACQ,eAAe,GAAGV,OAAO,CAACM,KAAK,CAAA,OAAA,CAAM,IAAI,EAAE;QAClDJ,MAAM,CAACS,oBAAoB,GAAG,CAAC,CAACX,OAAO,CAACM,KAAK,CAACgB,UAAU,KAAKtB,OAAO,CAACM,KAAK,CAACgB,UAAU,GAAGtB,OAAO,CAACM,KAAK,CAACgB,UAAU,GAAG,IAAI;AACvHpB,QAAAA,MAAM,CAACU,gBAAgB,GAAGZ,OAAO,CAACM,KAAK,CAACiB,EAAE,IAAIV,IAAI,CAAC,OAAO,CAAC,GAAG,UAAU;QACxEX,MAAM,CAACY,mBAAmB,GAAGd,OAAO,CAACM,KAAK,CAACkB,SAAS,IAAI,CAAC;QACzDtB,MAAM,CAACa,mBAAmB,GAAGf,OAAO,CAACM,KAAK,CAACmB,SAAS,IAAI,CAAC;QACzDvB,MAAM,CAACc,kBAAkB,GAAG,CAAC,CAAChB,OAAO,CAACM,KAAK,CAACoB,QAAQ,KAAK1B,OAAO,CAACM,KAAK,CAACoB,QAAQ,GAAG1B,OAAO,CAACM,KAAK,CAACoB,QAAQ,GAAG,IAAI;AACnH;AACJ;AAEAxB,IAAAA,MAAM,CAACyB,gBAAgB,GAAA1B,CAAAA,qBAAA,GAAGD,OAAO,CAAC4B,QAAQ,CAACC,SAAS,MAAA5B,IAAAA,IAAAA,qBAAA,gBAAAA,qBAAA,GAA1BA,qBAAA,CAA4B6B,MAAM,MAAA,IAAA,IAAA7B,qBAAA,KAAA,MAAA,IAAA,CAAAA,qBAAA,GAAlCA,qBAAA,CAAoC8B,MAAM,MAAA9B,IAAAA,IAAAA,qBAAA,KAA1CA,MAAAA,GAAAA,MAAAA,GAAAA,qBAAA,CAA4C+B,OAAO;AAE7E,IAAA,IAAI,CAACC,UAAU,CAAC/B,MAAM,EAAEF,OAAO,CAAC;AAEhCD,IAAAA,EAAE,CAACmC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC;GAC3C;AACDC,EAAAA,OAAO,WAAPA,OAAOA,CAACpC,EAAE,EAAEC,OAAO,EAAE;AACjB,IAAA,IAAIE,MAAM,GAAG,IAAI,CAACC,SAAS,CAACJ,EAAE,CAAC;IAE/BG,MAAM,CAACE,mBAAmB,GAAG,IAAI,CAACC,YAAY,CAACL,OAAO,CAAC;AACvD,IAAA,IAAI,CAACoC,YAAY,CAAClC,MAAM,CAAC;AAEzB,IAAA,IAAI,CAACF,OAAO,CAACM,KAAK,EAAE;AAChB,MAAA;AACJ;AAEA,IAAA,IAAI,OAAON,OAAO,CAACM,KAAK,KAAK,QAAQ,EAAE;AACnCJ,MAAAA,MAAM,CAACK,eAAe,GAAGP,OAAO,CAACM,KAAK;MACtCJ,MAAM,CAACM,kBAAkB,GAAG,KAAK;MACjCN,MAAM,CAACO,gBAAgB,GAAG,IAAI;MAC9BP,MAAM,CAACQ,eAAe,GAAG,IAAI;AAC7BR,MAAAA,MAAM,CAACU,gBAAgB,GAAGV,MAAM,CAACU,gBAAgB,IAAIC,IAAI,CAAC,OAAO,CAAC,GAAG,UAAU;MAC/EX,MAAM,CAACY,mBAAmB,GAAG,CAAC;MAC9BZ,MAAM,CAACa,mBAAmB,GAAG,CAAC;MAC9Bb,MAAM,CAACc,kBAAkB,GAAG,IAAI;AAEhC,MAAA,IAAI,CAACiB,UAAU,CAAC/B,MAAM,EAAEF,OAAO,CAAC;AACpC,KAAC,MAAM,IAAIiB,OAAA,CAAOjB,OAAO,CAACM,KAAK,CAAA,KAAK,QAAQ,IAAIN,OAAO,CAACM,KAAK,EAAE;MAC3D,IAAIY,OAAO,CAAClB,OAAO,CAACM,KAAK,CAACA,KAAK,CAAC,IAAIN,OAAO,CAACM,KAAK,CAACA,KAAK,CAACa,IAAI,EAAE,KAAK,EAAE,EAAE;AACnE,QAAA,IAAI,CAACiB,YAAY,CAAClC,MAAM,EAAEF,OAAO,CAAC;AAElC,QAAA;AACJ,OAAC,MAAM;AACHE,QAAAA,MAAM,CAACK,eAAe,GAAGP,OAAO,CAACM,KAAK,CAACA,KAAK;QAC5CJ,MAAM,CAACM,kBAAkB,GAAG,CAAC,CAACR,OAAO,CAACM,KAAK,CAACc,QAAQ,KAAKpB,OAAO,CAACM,KAAK,CAACc,QAAQ,GAAGpB,OAAO,CAACM,KAAK,CAACc,QAAQ,GAAG,KAAK;QAChHlB,MAAM,CAACO,gBAAgB,GAAG,CAAC,CAACT,OAAO,CAACM,KAAK,CAACe,MAAM,KAAKrB,OAAO,CAACM,KAAK,CAACe,MAAM,GAAGrB,OAAO,CAACM,KAAK,CAACe,MAAM,GAAG,IAAI;QACvGnB,MAAM,CAACQ,eAAe,GAAGV,OAAO,CAACM,KAAK,CAAA,OAAA,CAAM,IAAI,EAAE;QAClDJ,MAAM,CAACS,oBAAoB,GAAG,CAAC,CAACX,OAAO,CAACM,KAAK,CAACgB,UAAU,KAAKtB,OAAO,CAACM,KAAK,CAACgB,UAAU,GAAGtB,OAAO,CAACM,KAAK,CAACgB,UAAU,GAAG,IAAI;AACvHpB,QAAAA,MAAM,CAACU,gBAAgB,GAAGZ,OAAO,CAACM,KAAK,CAACiB,EAAE,IAAIrB,MAAM,CAACU,gBAAgB,IAAIC,IAAI,CAAC,OAAO,CAAC,GAAG,UAAU;QACnGX,MAAM,CAACY,mBAAmB,GAAGd,OAAO,CAACM,KAAK,CAACkB,SAAS,IAAI,CAAC;QACzDtB,MAAM,CAACa,mBAAmB,GAAGf,OAAO,CAACM,KAAK,CAACmB,SAAS,IAAI,CAAC;QACzDvB,MAAM,CAACc,kBAAkB,GAAG,CAAC,CAAChB,OAAO,CAACM,KAAK,CAACoB,QAAQ,KAAK1B,OAAO,CAACM,KAAK,CAACoB,QAAQ,GAAG1B,OAAO,CAACM,KAAK,CAACoB,QAAQ,GAAG,IAAI;AAE/G,QAAA,IAAI,CAACO,UAAU,CAAC/B,MAAM,EAAEF,OAAO,CAAC;AACpC;AACJ;GACH;AACDqC,EAAAA,SAAS,WAATA,SAASA,CAACtC,EAAE,EAAEC,OAAO,EAAE;AACnB,IAAA,IAAIE,MAAM,GAAG,IAAI,CAACC,SAAS,CAACJ,EAAE,CAAC;AAE/B,IAAA,IAAI,CAACuC,IAAI,CAACvC,EAAE,EAAE,CAAC,CAAC;AAChB,IAAA,IAAI,CAACwC,MAAM,CAACrC,MAAM,CAAC;AACnB,IAAA,IAAI,CAACkC,YAAY,CAAClC,MAAM,EAAEF,OAAO,CAAC;IAElC,IAAIE,MAAM,CAACsC,uBAAuB,EAAE;AAChCtC,MAAAA,MAAM,CAACsC,uBAAuB,CAACC,OAAO,EAAE;MACxCvC,MAAM,CAACsC,uBAAuB,GAAG,IAAI;AACzC;GACH;AACDE,EAAAA,KAAK,EAAEC,SAAS;AAChBC,EAAAA,OAAO,EAAE;AACLX,IAAAA,UAAU,WAAVA,UAAUA,CAAClC,EAAE,EAAEC,OAAO,EAAE;AAAA,MAAA,IAAA6C,KAAA,GAAA,IAAA;AACpB,MAAA,IAAMC,SAAS,GAAG/C,EAAE,CAACK,mBAAmB;MAExC,IAAI0C,SAAS,CAACC,KAAK,EAAE;AACjBhD,QAAAA,EAAE,CAACiD,oBAAoB,GAAG,UAACC,KAAK,EAAA;AAAA,UAAA,OAAKJ,KAAI,CAACK,OAAO,CAACD,KAAK,EAAEjD,OAAO,CAAC;AAAA,SAAA;QACjED,EAAE,CAACoD,mBAAmB,GAAG,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;QAE/CtD,EAAE,CAACuD,gBAAgB,CAAC,OAAO,EAAEvD,EAAE,CAACiD,oBAAoB,CAAC;QACrDjD,EAAE,CAACuD,gBAAgB,CAAC,MAAM,EAAEvD,EAAE,CAACoD,mBAAmB,CAAC;AACvD,OAAC,MAAM;AACHpD,QAAAA,EAAE,CAACwD,yBAAyB,GAAG,UAACN,KAAK,EAAA;AAAA,UAAA,OAAKJ,KAAI,CAACW,YAAY,CAACP,KAAK,EAAEjD,OAAO,CAAC;AAAA,SAAA;QAC3ED,EAAE,CAAC0D,yBAAyB,GAAG,IAAI,CAACC,YAAY,CAACL,IAAI,CAAC,IAAI,CAAC;QAC3DtD,EAAE,CAAC4D,oBAAoB,GAAG,IAAI,CAACC,OAAO,CAACP,IAAI,CAAC,IAAI,CAAC;QAEjDtD,EAAE,CAACuD,gBAAgB,CAAC,YAAY,EAAEvD,EAAE,CAACwD,yBAAyB,CAAC;QAC/DxD,EAAE,CAACuD,gBAAgB,CAAC,YAAY,EAAEvD,EAAE,CAAC0D,yBAAyB,CAAC;QAC/D1D,EAAE,CAACuD,gBAAgB,CAAC,OAAO,EAAEvD,EAAE,CAAC4D,oBAAoB,CAAC;AACzD;MAEA5D,EAAE,CAAC8D,sBAAsB,GAAG,IAAI,CAACC,SAAS,CAACT,IAAI,CAAC,IAAI,CAAC;MACrDtD,EAAE,CAACuD,gBAAgB,CAAC,SAAS,EAAEvD,EAAE,CAAC8D,sBAAsB,CAAC;AAEzD9D,MAAAA,EAAE,CAACgE,oBAAoB,GAAG,IAAI,CAACC,cAAc,CAACX,IAAI,CAAC,IAAI,EAAEtD,EAAE,CAAC;KAC/D;AACDqC,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACrC,EAAE,EAAE;AACb,MAAA,IAAM+C,SAAS,GAAG/C,EAAE,CAACK,mBAAmB;MAExC,IAAI0C,SAAS,CAACC,KAAK,EAAE;QACjBhD,EAAE,CAACkE,mBAAmB,CAAC,OAAO,EAAElE,EAAE,CAACiD,oBAAoB,CAAC;QACxDjD,EAAE,CAACiD,oBAAoB,GAAG,IAAI;QAE9BjD,EAAE,CAACkE,mBAAmB,CAAC,MAAM,EAAElE,EAAE,CAACoD,mBAAmB,CAAC;QACtDpD,EAAE,CAACoD,mBAAmB,GAAG,IAAI;AACjC,OAAC,MAAM;QACHpD,EAAE,CAACkE,mBAAmB,CAAC,YAAY,EAAElE,EAAE,CAACwD,yBAAyB,CAAC;QAClExD,EAAE,CAACwD,yBAAyB,GAAG,IAAI;QAEnCxD,EAAE,CAACkE,mBAAmB,CAAC,YAAY,EAAElE,EAAE,CAAC0D,yBAAyB,CAAC;QAClE1D,EAAE,CAAC0D,yBAAyB,GAAG,IAAI;QAEnC1D,EAAE,CAACkE,mBAAmB,CAAC,OAAO,EAAElE,EAAE,CAAC4D,oBAAoB,CAAC;QACxD5D,EAAE,CAAC4D,oBAAoB,GAAG,IAAI;AAClC;MAEA5D,EAAE,CAACkE,mBAAmB,CAAC,SAAS,EAAElE,EAAE,CAAC8D,sBAAsB,CAAC;MAC5DK,MAAM,CAACD,mBAAmB,CAAC,QAAQ,EAAElE,EAAE,CAACgE,oBAAoB,CAAC;MAE7D,IAAIhE,EAAE,CAACoE,YAAY,EAAE;AACjB,QAAA,IAAI,CAAC5B,MAAM,CAACxC,EAAE,CAAC;AACnB;KACH;AACDqE,IAAAA,kBAAkB,EAAlBA,SAAAA,kBAAkBA,CAACrE,EAAE,EAAE;AAAA,MAAA,IAAAsE,MAAA,GAAA,IAAA;AACnB,MAAA,IAAI,CAACtE,EAAE,CAACyC,uBAAuB,EAAE;QAC7BzC,EAAE,CAACyC,uBAAuB,GAAG,IAAI8B,6BAA6B,CAACvE,EAAE,EAAE,YAAM;AACrEsE,UAAAA,MAAI,CAAC/B,IAAI,CAACvC,EAAE,CAAC;AACjB,SAAC,CAAC;AACN;AAEAA,MAAAA,EAAE,CAACyC,uBAAuB,CAAC4B,kBAAkB,EAAE;KAClD;AACDG,IAAAA,oBAAoB,EAApBA,SAAAA,oBAAoBA,CAACxE,EAAE,EAAE;MACrB,IAAIA,EAAE,CAACyC,uBAAuB,EAAE;AAC5BzC,QAAAA,EAAE,CAACyC,uBAAuB,CAAC+B,oBAAoB,EAAE;AACrD;KACH;AACDf,IAAAA,YAAY,WAAZA,YAAYA,CAACP,KAAK,EAAEjD,OAAO,EAAE;AACzB,MAAA,IAAMD,EAAE,GAAGkD,KAAK,CAACuB,aAAa;AAC9B,MAAA,IAAMhD,SAAS,GAAGzB,EAAE,CAACe,mBAAmB;MAExC,IAAI,CAAC2D,IAAI,CAAC1E,EAAE,EAAEC,OAAO,EAAEwB,SAAS,CAAC;KACpC;AACDkC,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACT,KAAK,EAAE;AAChB,MAAA,IAAMlD,EAAE,GAAGkD,KAAK,CAACuB,aAAa;AAC9B,MAAA,IAAM/C,SAAS,GAAG1B,EAAE,CAACgB,mBAAmB;AACxC,MAAA,IAAMW,QAAQ,GAAG3B,EAAE,CAACiB,kBAAkB;MAEtC,IAAI,CAACU,QAAQ,EAAE;AACX,QAAA,IAAMgD,KAAK,GACPC,YAAY,CAAC1B,KAAK,CAAC/C,MAAM,EAAE,cAAc,CAAC,KAAK,SAAS,IACxDyE,YAAY,CAAC1B,KAAK,CAAC/C,MAAM,EAAE,iBAAiB,CAAC,KAAK,OAAO,IACzDyE,YAAY,CAAC1B,KAAK,CAAC/C,MAAM,EAAE,iBAAiB,CAAC,KAAK,MAAM,IACxDyE,YAAY,CAAC1B,KAAK,CAAC2B,aAAa,EAAE,cAAc,CAAC,KAAK,SAAS,IAC/DD,YAAY,CAAC1B,KAAK,CAAC2B,aAAa,EAAE,iBAAiB,CAAC,KAAK,OAAO,IAChED,YAAY,CAAC1B,KAAK,CAAC2B,aAAa,EAAE,iBAAiB,CAAC,KAAK,MAAM;QAEnE,CAACF,KAAK,IAAI,IAAI,CAACpC,IAAI,CAACvC,EAAE,EAAE0B,SAAS,CAAC;AACtC,OAAC,MAAM;AACH,QAAA,IAAI,CAACa,IAAI,CAACvC,EAAE,EAAE0B,SAAS,CAAC;AAC5B;KACH;AACDyB,IAAAA,OAAO,WAAPA,OAAOA,CAACD,KAAK,EAAEjD,OAAO,EAAE;AACpB,MAAA,IAAMD,EAAE,GAAGkD,KAAK,CAACuB,aAAa;AAC9B,MAAA,IAAMhD,SAAS,GAAGzB,EAAE,CAACe,mBAAmB;MAExC,IAAI,CAAC2D,IAAI,CAAC1E,EAAE,EAAEC,OAAO,EAAEwB,SAAS,CAAC;KACpC;AACD4B,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACH,KAAK,EAAE;AACV,MAAA,IAAMlD,EAAE,GAAGkD,KAAK,CAACuB,aAAa;AAC9B,MAAA,IAAM/C,SAAS,GAAG1B,EAAE,CAACgB,mBAAmB;AAExC,MAAA,IAAI,CAACuB,IAAI,CAACvC,EAAE,EAAE0B,SAAS,CAAC;KAC3B;AACDmC,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACX,KAAK,EAAE;AACX,MAAA,IAAMlD,EAAE,GAAGkD,KAAK,CAACuB,aAAa;AAC9B,MAAA,IAAM/C,SAAS,GAAG1B,EAAE,CAACgB,mBAAmB;AAExC,MAAA,IAAI,CAACuB,IAAI,CAACvC,EAAE,EAAE0B,SAAS,CAAC;KAC3B;AACDqC,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACb,KAAK,EAAE;AACb,MAAA,IAAMlD,EAAE,GAAGkD,KAAK,CAACuB,aAAa;AAC9B,MAAA,IAAM/C,SAAS,GAAG1B,EAAE,CAACgB,mBAAmB;AAExCkC,MAAAA,KAAK,CAAC4B,IAAI,KAAK,QAAQ,IAAI,IAAI,CAACvC,IAAI,CAACW,KAAK,CAACuB,aAAa,EAAE/C,SAAS,CAAC;KACvE;AACDuC,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACjE,EAAE,EAAE;AACf,MAAA,IAAI,CAAC+E,aAAa,EAAE,EAAE;AAClB,QAAA,IAAI,CAACxC,IAAI,CAACvC,EAAE,CAAC;AACjB;MAEAmE,MAAM,CAACD,mBAAmB,CAAC,QAAQ,EAAElE,EAAE,CAACgE,oBAAoB,CAAC;KAChE;AACDgB,IAAAA,cAAc,WAAdA,cAAcA,CAAChF,EAAE,EAAEC,OAAO,EAAE;MACxB,IAAID,EAAE,CAACS,kBAAkB,IAAI,CAACwE,OAAO,CAACjF,EAAE,CAAC,EAAE;AACvC,QAAA;AACJ;MAEA,IAAIkF,cAAc,GAAG,IAAI,CAACC,MAAM,CAACnF,EAAE,EAAEC,OAAO,CAAC;AAE7C,MAAA,IAAI,CAACmF,KAAK,CAACpF,EAAE,CAAC;MACd,CAAC,IAAI,CAACqF,UAAU,EAAE,IAAIC,MAAM,CAACJ,cAAc,EAAE,GAAG,CAAC;MAEjD,IAAMK,KAAK,GAAG,IAAI;MAElBpB,MAAM,CAACZ,gBAAgB,CAAC,QAAQ,EAAEvD,EAAE,CAACgE,oBAAoB,CAAC;MAE1DkB,cAAc,CAAC3B,gBAAgB,CAAC,YAAY,EAAE,SAASiC,cAAcA,GAAG;AACpED,QAAAA,KAAK,CAAChD,IAAI,CAACvC,EAAE,CAAC;AAEdkF,QAAAA,cAAc,CAAChB,mBAAmB,CAAC,YAAY,EAAEsB,cAAc,CAAC;QAChExF,EAAE,CAACkE,mBAAmB,CAAC,YAAY,EAAElE,EAAE,CAACwD,yBAAyB,CAAC;AAClEiC,QAAAA,UAAU,CAAC,YAAA;UAAA,OAAMzF,EAAE,CAACuD,gBAAgB,CAAC,YAAY,EAAEvD,EAAE,CAACwD,yBAAyB,CAAC;AAAA,SAAA,EAAE,EAAE,CAAC;AACzF,OAAC,CAAC;AAEF,MAAA,IAAI,CAACa,kBAAkB,CAACrE,EAAE,CAAC;MAC3B0F,MAAM,CAACC,GAAG,CAAC,SAAS,EAAET,cAAc,EAAElF,EAAE,CAAC4B,gBAAgB,CAAC;KAC7D;IACD8C,IAAI,EAAA,SAAJA,IAAIA,CAAC1E,EAAE,EAAEC,OAAO,EAAEwB,SAAS,EAAE;AAAA,MAAA,IAAAmE,MAAA,GAAA,IAAA;MACzB,IAAInE,SAAS,KAAKmB,SAAS,EAAE;AACzB,QAAA,IAAI,CAACD,KAAK,GAAG8C,UAAU,CAAC,YAAA;AAAA,UAAA,OAAMG,MAAI,CAACZ,cAAc,CAAChF,EAAE,EAAEC,OAAO,CAAC;AAAA,SAAA,EAAEwB,SAAS,CAAC;AAC9E,OAAC,MAAM;AACH,QAAA,IAAI,CAACuD,cAAc,CAAChF,EAAE,EAAEC,OAAO,CAAC;AACpC;KACH;AACD4F,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC7F,EAAE,EAAE;AACf,MAAA,IAAI,CAACwC,MAAM,CAACxC,EAAE,CAAC;AACf,MAAA,IAAI,CAACwE,oBAAoB,CAACxE,EAAE,CAAC;MAC7BmE,MAAM,CAACD,mBAAmB,CAAC,QAAQ,EAAElE,EAAE,CAACgE,oBAAoB,CAAC;KAChE;AACDzB,IAAAA,IAAI,WAAJA,IAAIA,CAACvC,EAAE,EAAE0B,SAAS,EAAE;AAAA,MAAA,IAAAoE,MAAA,GAAA,IAAA;AAChBC,MAAAA,YAAY,CAAC,IAAI,CAACpD,KAAK,CAAC;MAExB,IAAIjB,SAAS,KAAKkB,SAAS,EAAE;AACzB6C,QAAAA,UAAU,CAAC,YAAA;AAAA,UAAA,OAAMK,MAAI,CAACD,cAAc,CAAC7F,EAAE,CAAC;AAAA,SAAA,EAAE0B,SAAS,CAAC;AACxD,OAAC,MAAM;AACH,QAAA,IAAI,CAACmE,cAAc,CAAC7F,EAAE,CAAC;AAC3B;KACH;AACDgG,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAAChG,EAAE,EAAE;AAClB,MAAA,OAAOiG,QAAQ,CAACC,cAAc,CAAClG,EAAE,CAACoE,YAAY,CAAC;KAClD;AACD+B,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAACnG,EAAE,EAAE;AAChB,MAAA,IAAIkF,cAAc,GAAG,IAAI,CAACc,iBAAiB,CAAChG,EAAE,CAAC;AAE/C,MAAA,OAAOoG,UAAU,CAAClB,cAAc,EAAE,2BAA2B,CAAC;KACjE;AACDC,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACnF,EAAE,EAAE;AACP,MAAA,IAAM+C,SAAS,GAAG/C,EAAE,CAACK,mBAAmB;AAExC,MAAA,IAAMgG,YAAY,GAAGC,aAAa,CAAC,KAAK,EAAE;AACtC,QAAA,OAAA,EAAO,CAAC,IAAI,CAACjB,UAAU,EAAE,IAAI,IAAI,CAACkB,EAAE,CAAC,OAAO,CAAC;AAC7C,QAAA,QAAQ,EAAE,IAAI,CAACC,GAAG,CAAC,OAAO,EAAE;AACxBC,UAAAA,OAAO,EAAE1D;SACZ;AACL,OAAC,CAAC;AAEF,MAAA,IAAM2D,WAAW,GAAGJ,aAAa,CAAC,KAAK,EAAE;AACrC,QAAA,OAAA,EAAO,CAAC,IAAI,CAACjB,UAAU,EAAE,IAAI,IAAI,CAACkB,EAAE,CAAC,MAAM,CAAC;AAC5C,QAAA,QAAQ,EAAE,IAAI,CAACC,GAAG,CAAC,MAAM,EAAE;AACvBC,UAAAA,OAAO,EAAE1D;SACZ;AACL,OAAC,CAAC;AAEF,MAAA,IAAI,CAAC/C,EAAE,CAACU,gBAAgB,EAAE;AACtBgG,QAAAA,WAAW,CAACC,SAAS,GAAG3G,EAAE,CAACQ,eAAe;AAC9C,OAAC,MAAM;QACHkG,WAAW,CAACC,SAAS,GAAG,EAAE;QAC1BD,WAAW,CAACE,WAAW,CAACX,QAAQ,CAACY,cAAc,CAAC7G,EAAE,CAACQ,eAAe,CAAC,CAAC;AACxE;MAEA,IAAMsG,SAAS,GAAGR,aAAa,CAC3B,KAAK,EAAAS,eAAA,CAAAA,eAAA,CAAA;QAEDvF,EAAE,EAAExB,EAAE,CAACa,gBAAgB;AACvBmG,QAAAA,IAAI,EAAE,SAAS;AACfpH,QAAAA,KAAK,EAAE;AACHqH,UAAAA,OAAO,EAAE,cAAc;AACvBC,UAAAA,KAAK,EAAElH,EAAE,CAACY,oBAAoB,GAAG,aAAa,GAAGgC,SAAS;UAC1DuE,aAAa,EAAE,CAAC,IAAI,CAAC9B,UAAU,EAAE,IAAIrF,EAAE,CAACiB,kBAAkB,IAAI;SACjE;AACD,QAAA,OAAA,EAAO,CAAC,CAAC,IAAI,CAACoE,UAAU,EAAE,IAAI,IAAI,CAACkB,EAAE,CAAC,MAAM,CAAC,EAAEvG,EAAE,CAACW,eAAe;AAAC,OAAA,EACjE,IAAI,CAACyG,aAAa,EAAG,EAAE,CAAA,EACxB,QAAQ,EAAE,IAAI,CAACZ,GAAG,CAAC,MAAM,EAAE;AACvBC,QAAAA,OAAO,EAAE1D;AACb,OAAC,CAAC,CAAA,EAENsD,YAAY,EACZK,WACJ,CAAC;AAEDT,MAAAA,QAAQ,CAACoB,IAAI,CAACT,WAAW,CAACE,SAAS,CAAC;AAEpC9G,MAAAA,EAAE,CAACoE,YAAY,GAAG0C,SAAS,CAACtF,EAAE;MAC9B,IAAI,CAAC8F,GAAG,GAAGR,SAAS;AAEpB,MAAA,OAAOA,SAAS;KACnB;AACDtE,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACxC,EAAE,EAAE;AACP,MAAA,IAAIA,EAAE,EAAE;AACJ,QAAA,IAAIkF,cAAc,GAAG,IAAI,CAACc,iBAAiB,CAAChG,EAAE,CAAC;AAE/C,QAAA,IAAIkF,cAAc,IAAIA,cAAc,CAACqC,aAAa,EAAE;AAChD7B,UAAAA,MAAM,CAAC8B,KAAK,CAACtC,cAAc,CAAC;AAC5Be,UAAAA,QAAQ,CAACoB,IAAI,CAACI,WAAW,CAACvC,cAAc,CAAC;AAC7C;QAEAlF,EAAE,CAACoE,YAAY,GAAG,IAAI;AAC1B;KACH;AACDgB,IAAAA,KAAK,EAALA,SAAAA,KAAKA,CAACpF,EAAE,EAAE;AACN,MAAA,IAAM+C,SAAS,GAAG/C,EAAE,CAACK,mBAAmB;MAExC,IAAI0C,SAAS,CAAC2E,GAAG,EAAE;AACf,QAAA,IAAI,CAACC,QAAQ,CAAC3H,EAAE,CAAC;AAEjB,QAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,UAAA,IAAI,CAAC6H,WAAW,CAAC7H,EAAE,CAAC;AAEpB,UAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,YAAA,IAAI,CAAC2H,QAAQ,CAAC3H,EAAE,CAAC;AACrB;AACJ;AACJ,OAAC,MAAM,IAAI+C,SAAS,CAAC+E,IAAI,EAAE;AACvB,QAAA,IAAI,CAACC,SAAS,CAAC/H,EAAE,CAAC;AAElB,QAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,UAAA,IAAI,CAACgI,UAAU,CAAChI,EAAE,CAAC;AAEnB,UAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,YAAA,IAAI,CAAC2H,QAAQ,CAAC3H,EAAE,CAAC;AAEjB,YAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,cAAA,IAAI,CAAC6H,WAAW,CAAC7H,EAAE,CAAC;AAEpB,cAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,gBAAA,IAAI,CAAC+H,SAAS,CAAC/H,EAAE,CAAC;AACtB;AACJ;AACJ;AACJ;AACJ,OAAC,MAAM,IAAI+C,SAAS,CAACkF,MAAM,EAAE;AACzB,QAAA,IAAI,CAACJ,WAAW,CAAC7H,EAAE,CAAC;AAEpB,QAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,UAAA,IAAI,CAAC2H,QAAQ,CAAC3H,EAAE,CAAC;AAEjB,UAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,YAAA,IAAI,CAAC6H,WAAW,CAAC7H,EAAE,CAAC;AACxB;AACJ;AACJ,OAAC,MAAM;AACH,QAAA,IAAI,CAACgI,UAAU,CAAChI,EAAE,CAAC;AAEnB,QAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,UAAA,IAAI,CAAC+H,SAAS,CAAC/H,EAAE,CAAC;AAElB,UAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,YAAA,IAAI,CAAC2H,QAAQ,CAAC3H,EAAE,CAAC;AAEjB,YAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,cAAA,IAAI,CAAC6H,WAAW,CAAC7H,EAAE,CAAC;AAEpB,cAAA,IAAI,IAAI,CAAC4H,aAAa,CAAC5H,EAAE,CAAC,EAAE;AACxB,gBAAA,IAAI,CAACgI,UAAU,CAAChI,EAAE,CAAC;AACvB;AACJ;AACJ;AACJ;AACJ;KACH;AACDkI,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAAClI,EAAE,EAAE;AACd,MAAA,IAAImI,MAAM,GAAGnI,EAAE,CAACoI,qBAAqB,EAAE;MACvC,IAAIC,UAAU,GAAGF,MAAM,CAACL,IAAI,GAAGQ,mBAAmB,EAAE;MACpD,IAAIC,SAAS,GAAGJ,MAAM,CAACT,GAAG,GAAGc,kBAAkB,EAAE;MAEjD,OAAO;AAAEV,QAAAA,IAAI,EAAEO,UAAU;AAAEX,QAAAA,GAAG,EAAEa;OAAW;KAC9C;AACDP,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAAChI,EAAE,EAAE;AACX,MAAA,IAAI,CAACyI,QAAQ,CAACzI,EAAE,EAAE,OAAO,CAAC;AAC1B,MAAA,IAAIkF,cAAc,GAAG,IAAI,CAACc,iBAAiB,CAAChG,EAAE,CAAC;AAC/C,MAAA,IAAI0I,YAAY,GAAG,IAAI,CAACvC,eAAe,CAACnG,EAAE,CAAC;AAC3C,MAAA,IAAI2I,UAAU,GAAG,IAAI,CAACT,aAAa,CAAClI,EAAE,CAAC;MACvC,IAAI8H,IAAI,GAAGa,UAAU,CAACb,IAAI,GAAGc,aAAa,CAAC5I,EAAE,CAAC;AAC9C,MAAA,IAAI0H,GAAG,GAAGiB,UAAU,CAACjB,GAAG,GAAG,CAACmB,cAAc,CAAC7I,EAAE,CAAC,GAAG6I,cAAc,CAAC3D,cAAc,CAAC,IAAI,CAAC;AAEpFA,MAAAA,cAAc,CAACtF,KAAK,CAACkI,IAAI,GAAGA,IAAI,GAAG,IAAI;AACvC5C,MAAAA,cAAc,CAACtF,KAAK,CAAC8H,GAAG,GAAGA,GAAG,GAAG,IAAI;AAErCgB,MAAAA,YAAY,CAAC9I,KAAK,CAAC8H,GAAG,GAAG,KAAK;AAC9BgB,MAAAA,YAAY,CAAC9I,KAAK,CAACkJ,KAAK,GAAG,IAAI;AAC/BJ,MAAAA,YAAY,CAAC9I,KAAK,CAACqI,MAAM,GAAG,IAAI;AAChCS,MAAAA,YAAY,CAAC9I,KAAK,CAACkI,IAAI,GAAG,GAAG;KAChC;AACDC,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAAC/H,EAAE,EAAE;AACV,MAAA,IAAI,CAACyI,QAAQ,CAACzI,EAAE,EAAE,MAAM,CAAC;AACzB,MAAA,IAAIkF,cAAc,GAAG,IAAI,CAACc,iBAAiB,CAAChG,EAAE,CAAC;AAC/C,MAAA,IAAI0I,YAAY,GAAG,IAAI,CAACvC,eAAe,CAACnG,EAAE,CAAC;AAC3C,MAAA,IAAI2I,UAAU,GAAG,IAAI,CAACT,aAAa,CAAClI,EAAE,CAAC;MACvC,IAAI8H,IAAI,GAAGa,UAAU,CAACb,IAAI,GAAGc,aAAa,CAAC1D,cAAc,CAAC;AAC1D,MAAA,IAAIwC,GAAG,GAAGiB,UAAU,CAACjB,GAAG,GAAG,CAACmB,cAAc,CAAC7I,EAAE,CAAC,GAAG6I,cAAc,CAAC3D,cAAc,CAAC,IAAI,CAAC;AAEpFA,MAAAA,cAAc,CAACtF,KAAK,CAACkI,IAAI,GAAGA,IAAI,GAAG,IAAI;AACvC5C,MAAAA,cAAc,CAACtF,KAAK,CAAC8H,GAAG,GAAGA,GAAG,GAAG,IAAI;AAErCgB,MAAAA,YAAY,CAAC9I,KAAK,CAAC8H,GAAG,GAAG,KAAK;AAC9BgB,MAAAA,YAAY,CAAC9I,KAAK,CAACkJ,KAAK,GAAG,GAAG;AAC9BJ,MAAAA,YAAY,CAAC9I,KAAK,CAACqI,MAAM,GAAG,IAAI;AAChCS,MAAAA,YAAY,CAAC9I,KAAK,CAACkI,IAAI,GAAG,IAAI;KACjC;AACDH,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAAC3H,EAAE,EAAE;AACT,MAAA,IAAI,CAACyI,QAAQ,CAACzI,EAAE,EAAE,KAAK,CAAC;AACxB,MAAA,IAAIkF,cAAc,GAAG,IAAI,CAACc,iBAAiB,CAAChG,EAAE,CAAC;AAC/C,MAAA,IAAI0I,YAAY,GAAG,IAAI,CAACvC,eAAe,CAACnG,EAAE,CAAC;AAC3C,MAAA,IAAI+I,YAAY,GAAGH,aAAa,CAAC1D,cAAc,CAAC;AAChD,MAAA,IAAI8D,YAAY,GAAGJ,aAAa,CAAC5I,EAAE,CAAC;AACpC,MAAA,IAAAiJ,YAAA,GAA+BC,WAAW,EAAE;QAA/BC,aAAa,GAAAF,YAAA,CAApB/B,KAAK;AACX,MAAA,IAAIyB,UAAU,GAAG,IAAI,CAACT,aAAa,CAAClI,EAAE,CAAC;MACvC,IAAI8H,IAAI,GAAGa,UAAU,CAACb,IAAI,GAAG,CAACkB,YAAY,GAAGD,YAAY,IAAI,CAAC;MAC9D,IAAIrB,GAAG,GAAGiB,UAAU,CAACjB,GAAG,GAAGmB,cAAc,CAAC3D,cAAc,CAAC;MAEzD,IAAI4C,IAAI,GAAG,CAAC,EAAE;AACVA,QAAAA,IAAI,GAAG,CAAC;AACZ,OAAC,MAAM,IAAIA,IAAI,GAAGiB,YAAY,GAAGI,aAAa,EAAE;AAC5CrB,QAAAA,IAAI,GAAGsB,IAAI,CAACC,KAAK,CAACV,UAAU,CAACb,IAAI,GAAGkB,YAAY,GAAGD,YAAY,CAAC;AACpE;AAEA7D,MAAAA,cAAc,CAACtF,KAAK,CAACkI,IAAI,GAAGA,IAAI,GAAG,IAAI;AACvC5C,MAAAA,cAAc,CAACtF,KAAK,CAAC8H,GAAG,GAAGA,GAAG,GAAG,IAAI;;AAErC;AACA,MAAA,IAAI4B,qBAAqB,GAAGX,UAAU,CAACb,IAAI,GAAG,IAAI,CAACI,aAAa,CAAChD,cAAc,CAAC,CAAC4C,IAAI,GAAGkB,YAAY,GAAG,CAAC;AAExGN,MAAAA,YAAY,CAAC9I,KAAK,CAAC8H,GAAG,GAAG,IAAI;AAC7BgB,MAAAA,YAAY,CAAC9I,KAAK,CAACkJ,KAAK,GAAG,IAAI;AAC/BJ,MAAAA,YAAY,CAAC9I,KAAK,CAACqI,MAAM,GAAG,GAAG;AAC/BS,MAAAA,YAAY,CAAC9I,KAAK,CAACkI,IAAI,GAAGwB,qBAAqB,GAAG,IAAI;KACzD;AACDzB,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAAC7H,EAAE,EAAE;AACZ,MAAA,IAAI,CAACyI,QAAQ,CAACzI,EAAE,EAAE,QAAQ,CAAC;AAC3B,MAAA,IAAIkF,cAAc,GAAG,IAAI,CAACc,iBAAiB,CAAChG,EAAE,CAAC;AAC/C,MAAA,IAAI0I,YAAY,GAAG,IAAI,CAACvC,eAAe,CAACnG,EAAE,CAAC;AAC3C,MAAA,IAAI+I,YAAY,GAAGH,aAAa,CAAC1D,cAAc,CAAC;AAChD,MAAA,IAAI8D,YAAY,GAAGJ,aAAa,CAAC5I,EAAE,CAAC;AACpC,MAAA,IAAAuJ,aAAA,GAA+BL,WAAW,EAAE;QAA/BC,aAAa,GAAAI,aAAA,CAApBrC,KAAK;AACX,MAAA,IAAIyB,UAAU,GAAG,IAAI,CAACT,aAAa,CAAClI,EAAE,CAAC;MAEvC,IAAI8H,IAAI,GAAGa,UAAU,CAACb,IAAI,GAAG,CAACkB,YAAY,GAAGD,YAAY,IAAI,CAAC;MAC9D,IAAIrB,GAAG,GAAGiB,UAAU,CAACjB,GAAG,GAAGmB,cAAc,CAAC7I,EAAE,CAAC;MAE7C,IAAI8H,IAAI,GAAG,CAAC,EAAE;AACVA,QAAAA,IAAI,GAAG,CAAC;AACZ,OAAC,MAAM,IAAIA,IAAI,GAAGiB,YAAY,GAAGI,aAAa,EAAE;AAC5CrB,QAAAA,IAAI,GAAGsB,IAAI,CAACC,KAAK,CAACV,UAAU,CAACb,IAAI,GAAGkB,YAAY,GAAGD,YAAY,CAAC;AACpE;AAEA7D,MAAAA,cAAc,CAACtF,KAAK,CAACkI,IAAI,GAAGA,IAAI,GAAG,IAAI;AACvC5C,MAAAA,cAAc,CAACtF,KAAK,CAAC8H,GAAG,GAAGA,GAAG,GAAG,IAAI;;AAErC;AACA,MAAA,IAAI4B,qBAAqB,GAAGX,UAAU,CAACb,IAAI,GAAG,IAAI,CAACI,aAAa,CAAChD,cAAc,CAAC,CAAC4C,IAAI,GAAGkB,YAAY,GAAG,CAAC;AAExGN,MAAAA,YAAY,CAAC9I,KAAK,CAAC8H,GAAG,GAAG,GAAG;AAC5BgB,MAAAA,YAAY,CAAC9I,KAAK,CAACkJ,KAAK,GAAG,IAAI;AAC/BJ,MAAAA,YAAY,CAAC9I,KAAK,CAACqI,MAAM,GAAG,IAAI;AAChCS,MAAAA,YAAY,CAAC9I,KAAK,CAACkI,IAAI,GAAGwB,qBAAqB,GAAG,IAAI;KACzD;AACDb,IAAAA,QAAQ,WAARA,QAAQA,CAACzI,EAAE,EAAEwJ,QAAQ,EAAE;AACnB,MAAA,IAAItE,cAAc,GAAG,IAAI,CAACc,iBAAiB,CAAChG,EAAE,CAAC;MAE/CkF,cAAc,CAACtF,KAAK,CAACkI,IAAI,GAAG,IAAI,GAAG,IAAI;MACvC5C,cAAc,CAACtF,KAAK,CAAC8H,GAAG,GAAG,IAAI,GAAG,IAAI;MACtC+B,WAAW,CAACvE,cAAc,EAAAwE,YAAAA,CAAAA,MAAA,CAAexE,cAAc,CAACyE,kBAAkB,CAAE,CAAC;AAC7E,MAAA,CAAC,IAAI,CAACtE,UAAU,EAAE,IAAIuE,QAAQ,CAAC1E,cAAc,EAAAwE,YAAAA,CAAAA,MAAA,CAAeF,QAAQ,CAAE,CAAC;MACvEtE,cAAc,CAACyE,kBAAkB,GAAGH,QAAQ;AAC5CtE,MAAAA,cAAc,CAAC/C,YAAY,CAAC,iBAAiB,EAAEqH,QAAQ,CAAC;KAC3D;AACD5B,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAAC5H,EAAE,EAAE;AACd,MAAA,IAAIkF,cAAc,GAAG,IAAI,CAACc,iBAAiB,CAAChG,EAAE,CAAC;AAC/C,MAAA,IAAImI,MAAM,GAAGjD,cAAc,CAACkD,qBAAqB,EAAE;AACnD,MAAA,IAAIG,SAAS,GAAGJ,MAAM,CAACT,GAAG;AAC1B,MAAA,IAAIW,UAAU,GAAGF,MAAM,CAACL,IAAI;AAC5B,MAAA,IAAIZ,KAAK,GAAG0B,aAAa,CAAC1D,cAAc,CAAC;AACzC,MAAA,IAAI2E,MAAM,GAAGhB,cAAc,CAAC3D,cAAc,CAAC;AAC3C,MAAA,IAAI4E,QAAQ,GAAGZ,WAAW,EAAE;MAE5B,OAAOb,UAAU,GAAGnB,KAAK,GAAG4C,QAAQ,CAAC5C,KAAK,IAAImB,UAAU,GAAG,CAAC,IAAIE,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAGsB,MAAM,GAAGC,QAAQ,CAACD,MAAM;KACxH;AACDzJ,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACJ,EAAE,EAAE;AAAA,MAAA,IAAA+J,WAAA;MACV,OAAOC,QAAQ,CAAChK,EAAE,EAAE,gBAAgB,CAAC,GAAA,CAAA+J,WAAA,GAAI3D,UAAU,CAACpG,EAAE,EAAE,OAAO,CAAC,MAAA+J,IAAAA,IAAAA,WAAA,cAAAA,WAAA,GAAI/J,EAAE,GAAIA,EAAE;KAC/E;AACDM,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACL,OAAO,EAAE;AAClB;AACA,MAAA,IAAIA,OAAO,CAAC8C,SAAS,IAAIkH,MAAM,CAACC,IAAI,CAACjK,OAAO,CAAC8C,SAAS,CAAC,CAACoH,MAAM,EAAE;QAC5D,OAAOlK,OAAO,CAAC8C,SAAS;AAC5B;;AAEA;MACA,IAAI9C,OAAO,CAACmK,GAAG,IAAIlJ,OAAA,CAAOjB,OAAO,CAACmK,GAAG,CAAK,KAAA,QAAQ,EAAE;AAChD,QAAA,OAAOH,MAAM,CAACI,OAAO,CAACpK,OAAO,CAACmK,GAAG,CAAC,CAACE,MAAM,CAAC,UAACC,GAAG,EAAAC,IAAA,EAAiB;AAAA,UAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA,EAAA,CAAA,CAAA;AAAdG,YAAAA,GAAG,GAAAF,KAAA,CAAA,CAAA,CAAA;AAAEG,YAAAA,GAAG,GAAAH,KAAA,CAAA,CAAA,CAAA;AACrD,UAAA,IAAIE,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,UAAU,EAAEJ,GAAG,CAACK,GAAG,CAAC,GAAG,IAAI;AAE1D,UAAA,OAAOL,GAAG;SACb,EAAE,EAAE,CAAC;AACV;AAEA,MAAA,OAAO,EAAE;AACb;AACJ;AACJ,CAAC;;;;"}