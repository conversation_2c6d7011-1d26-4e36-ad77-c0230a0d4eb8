{"version": 3, "file": "index.mjs", "sources": ["../../../src/tooltip/style/TooltipStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/tooltip';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-tooltip p-component',\n    arrow: 'p-tooltip-arrow',\n    text: 'p-tooltip-text'\n};\n\nexport default BaseStyle.extend({\n    name: 'tooltip-directive',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "arrow", "text", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,uBAAuB;AAC7BC,EAAAA,KAAK,EAAE,iBAAiB;AACxBC,EAAAA,IAAI,EAAE;AACV,CAAC;AAED,mBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,mBAAmB;AACzBC,EAAAA,KAAK,EAALA,KAAK;AACLP,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}