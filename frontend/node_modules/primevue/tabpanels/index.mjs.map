{"version": 3, "file": "index.mjs", "sources": ["../../src/tabpanels/BaseTabPanels.vue", "../../src/tabpanels/TabPanels.vue", "../../src/tabpanels/TabPanels.vue?vue&type=template&id=7bfe8426&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TabPanelsStyle from 'primevue/tabpanels/style';\n\nexport default {\n    name: 'BaseTabPanels',\n    extends: BaseComponent,\n    props: {},\n    style: TabPanelsStyle,\n    provide() {\n        return {\n            $pcTabPanels: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" role=\"presentation\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport BaseTabPanels from './BaseTabPanels.vue';\n\nexport default {\n    name: 'TabPanels',\n    extends: BaseTabPanels,\n    inheritAttrs: false\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" role=\"presentation\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport BaseTabPanels from './BaseTabPanels.vue';\n\nexport default {\n    name: 'TabPanels',\n    extends: BaseTabPanels,\n    inheritAttrs: false\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "style", "TabPanelsStyle", "provide", "$pcTabPanels", "$parentInstance", "BaseTabPanels", "inheritAttrs", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "role", "ptmi", "_renderSlot", "$slots"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,eAAe;AACrB,EAAA,SAAA,EAASC,aAAa;EACtBC,KAAK,EAAE,EAAE;AACTC,EAAAA,KAAK,EAAEC,cAAc;EACrBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,YAAY,EAAE,IAAI;AAClBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACND,aAAe;AACXP,EAAAA,IAAI,EAAE,WAAW;AACjB,EAAA,SAAA,EAASQ,QAAa;AACtBC,EAAAA,YAAY,EAAE;AAClB,CAAC;;;ECZG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAEK,OAFLC,UAEK,CAAA;AAFC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;AAAUC,IAAAA,IAAI,EAAC;KAAuBF,IAAI,CAAAG,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACrDC,UAAY,CAAAJ,IAAA,CAAAK,MAAA,EAAA,SAAA,CAAA;;;;;;;"}