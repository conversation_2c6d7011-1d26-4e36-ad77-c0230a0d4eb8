{"version": 3, "file": "index.mjs", "sources": ["../../../src/tabs/style/TabsStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/tabs';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => [\n        'p-tabs p-component',\n        {\n            'p-tabs-scrollable': props.scrollable\n        }\n    ]\n};\n\nexport default BaseStyle.extend({\n    name: 'tabs',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "props", "scrollable", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAA,OAAO,CACjB,oBAAoB,EACpB;MACI,mBAAmB,EAAEA,KAAK,CAACC;AAC/B,KAAC,CACJ;AAAA;AACL,CAAC;AAED,gBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,MAAM;AACZC,EAAAA,KAAK,EAALA,KAAK;AACLR,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}