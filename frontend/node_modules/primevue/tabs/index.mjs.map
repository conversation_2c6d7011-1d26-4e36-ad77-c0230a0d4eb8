{"version": 3, "file": "index.mjs", "sources": ["../../src/tabs/BaseTabs.vue", "../../src/tabs/Tabs.vue", "../../src/tabs/Tabs.vue?vue&type=template&id=4ffeb2f6&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TabsStyle from 'primevue/tabs/style';\n\nexport default {\n    name: 'BaseTabs',\n    extends: BaseComponent,\n    props: {\n        value: {\n            type: [String, Number],\n            default: undefined\n        },\n        lazy: {\n            type: Boolean,\n            default: false\n        },\n        scrollable: {\n            type: Boolean,\n            default: false\n        },\n        showNavigators: {\n            type: Boolean,\n            default: true\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        },\n        selectOnFocus: {\n            type: Boolean,\n            default: false\n        }\n    },\n    style: TabsStyle,\n    provide() {\n        return {\n            $pcTabs: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport BaseTabs from './BaseTabs.vue';\n\nexport default {\n    name: 'Tabs',\n    extends: BaseTabs,\n    inheritAttrs: false,\n    emits: ['update:value'],\n    data() {\n        return {\n            d_value: this.value\n        };\n    },\n    watch: {\n        value(newValue) {\n            this.d_value = newValue;\n        }\n    },\n    methods: {\n        updateValue(newValue) {\n            if (this.d_value !== newValue) {\n                this.d_value = newValue;\n                this.$emit('update:value', newValue);\n            }\n        },\n        isVertical() {\n            return this.orientation === 'vertical';\n        }\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport BaseTabs from './BaseTabs.vue';\n\nexport default {\n    name: 'Tabs',\n    extends: BaseTabs,\n    inheritAttrs: false,\n    emits: ['update:value'],\n    data() {\n        return {\n            d_value: this.value\n        };\n    },\n    watch: {\n        value(newValue) {\n            this.d_value = newValue;\n        }\n    },\n    methods: {\n        updateValue(newValue) {\n            if (this.d_value !== newValue) {\n                this.d_value = newValue;\n                this.$emit('update:value', newValue);\n            }\n        },\n        isVertical() {\n            return this.orientation === 'vertical';\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "value", "type", "String", "Number", "undefined", "lazy", "Boolean", "scrollable", "showNavigators", "tabindex", "selectOnFocus", "style", "TabsStyle", "provide", "$pcTabs", "$parentInstance", "BaseTabs", "inheritAttrs", "emits", "data", "d_value", "watch", "newValue", "methods", "updateValue", "$emit", "isVertical", "orientation", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "ptmi", "_renderSlot", "$slots"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,UAAU;AAChB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;MACtB,SAASC,EAAAA;KACZ;AACDC,IAAAA,IAAI,EAAE;AACFJ,MAAAA,IAAI,EAAEK,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRN,MAAAA,IAAI,EAAEK,OAAO;MACb,SAAS,EAAA;KACZ;AACDE,IAAAA,cAAc,EAAE;AACZP,MAAAA,IAAI,EAAEK,OAAO;MACb,SAAS,EAAA;KACZ;AACDG,IAAAA,QAAQ,EAAE;AACNR,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDO,IAAAA,aAAa,EAAE;AACXT,MAAAA,IAAI,EAAEK,OAAO;MACb,SAAS,EAAA;AACb;GACH;AACDK,EAAAA,KAAK,EAAEC,SAAS;EAChBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;AC/BD,aAAe;AACXlB,EAAAA,IAAI,EAAE,MAAM;AACZ,EAAA,SAAA,EAASmB,QAAQ;AACjBC,EAAAA,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAE,CAAC,cAAc,CAAC;EACvBC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;MACHC,OAAO,EAAE,IAAI,CAACpB;KACjB;GACJ;AACDqB,EAAAA,KAAK,EAAE;AACHrB,IAAAA,KAAK,EAALA,SAAAA,KAAKA,CAACsB,QAAQ,EAAE;MACZ,IAAI,CAACF,OAAQ,GAAEE,QAAQ;AAC3B;GACH;AACDC,EAAAA,OAAO,EAAE;AACLC,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACF,QAAQ,EAAE;AAClB,MAAA,IAAI,IAAI,CAACF,OAAQ,KAAIE,QAAQ,EAAE;QAC3B,IAAI,CAACF,OAAQ,GAAEE,QAAQ;AACvB,QAAA,IAAI,CAACG,KAAK,CAAC,cAAc,EAAEH,QAAQ,CAAC;AACxC;KACH;IACDI,UAAU,EAAA,SAAVA,UAAUA,GAAG;AACT,MAAA,OAAO,IAAI,CAACC,WAAY,KAAI,UAAU;AAC1C;AACJ;AACJ,CAAC;;;EClCG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAEK,OAFLC,UAEK,CAAA;AAFC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA;KAAkBD,IAAI,CAAAE,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACjCC,UAAY,CAAAH,IAAA,CAAAI,MAAA,EAAA,SAAA,CAAA;;;;;;;"}