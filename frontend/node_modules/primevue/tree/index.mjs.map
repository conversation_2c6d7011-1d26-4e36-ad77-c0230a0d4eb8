{"version": 3, "file": "index.mjs", "sources": ["../../src/tree/BaseTree.vue", "../../src/tree/TreeNode.vue", "../../src/tree/TreeNode.vue?vue&type=template&id=312bfd2b&lang.js", "../../src/tree/Tree.vue", "../../src/tree/Tree.vue?vue&type=template&id=02fa5d36&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TreeStyle from 'primevue/tree/style';\n\nexport default {\n    name: 'BaseTree',\n    extends: BaseComponent,\n    props: {\n        value: {\n            type: null,\n            default: null\n        },\n        expandedKeys: {\n            type: null,\n            default: null\n        },\n        selectionKeys: {\n            type: null,\n            default: null\n        },\n        selectionMode: {\n            type: String,\n            default: null\n        },\n        metaKeySelection: {\n            type: Boolean,\n            default: false\n        },\n        loading: {\n            type: Boolean,\n            default: false\n        },\n        loadingIcon: {\n            type: String,\n            default: undefined\n        },\n        loadingMode: {\n            type: String,\n            default: 'mask'\n        },\n        filter: {\n            type: Boolean,\n            default: false\n        },\n        filterBy: {\n            type: [String, Function],\n            default: 'label'\n        },\n        filterMode: {\n            type: String,\n            default: 'lenient'\n        },\n        filterPlaceholder: {\n            type: String,\n            default: null\n        },\n        filterLocale: {\n            type: String,\n            default: undefined\n        },\n        highlightOnSelect: {\n            type: Boolean,\n            default: false\n        },\n        scrollHeight: {\n            type: String,\n            default: null\n        },\n        level: {\n            type: Number,\n            default: 0\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        }\n    },\n    style: TreeStyle,\n    provide() {\n        return {\n            $pcTree: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <li\n        ref=\"currentNode\"\n        :class=\"cx('node')\"\n        role=\"treeitem\"\n        :aria-label=\"label(node)\"\n        :aria-selected=\"ariaSelected\"\n        :aria-expanded=\"expanded\"\n        :aria-setsize=\"node.children ? node.children.length : 0\"\n        :aria-posinset=\"index + 1\"\n        :aria-level=\"level\"\n        :aria-checked=\"ariaChecked\"\n        :tabindex=\"index === 0 ? 0 : -1\"\n        @keydown=\"onKeyDown\"\n        v-bind=\"getPTOptions('node')\"\n    >\n        <div :class=\"cx('nodeContent')\" @click=\"onClick\" @touchend=\"onTouchEnd\" :style=\"node.style\" v-bind=\"getPTOptions('nodeContent')\" :data-p-selected=\"checkboxMode ? checked : selected\" :data-p-selectable=\"selectable\">\n            <button v-ripple type=\"button\" :class=\"cx('nodeToggleButton')\" @click=\"toggle\" tabindex=\"-1\" :data-p-leaf=\"leaf\" v-bind=\"getPTOptions('nodeToggleButton')\">\n                <template v-if=\"node.loading && loadingMode === 'icon'\">\n                    <!-- TODO: nodetogglericon deprecated since v4.0-->\n                    <component v-if=\"templates['nodetoggleicon'] || templates['nodetogglericon']\" :is=\"templates['nodetoggleicon'] || templates['nodetogglericon']\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <SpinnerIcon v-else spin :class=\"cx('nodeToggleIcon')\" v-bind=\"getPTOptions('nodeToggleIcon')\" />\n                </template>\n                <template v-else>\n                    <!-- TODO: togglericon deprecated since v4.0-->\n                    <component v-if=\"templates['nodetoggleicon'] || templates['togglericon']\" :is=\"templates['nodetoggleicon'] || templates['togglericon']\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <component v-else-if=\"expanded\" :is=\"node.expandedIcon ? 'span' : 'ChevronDownIcon'\" :class=\"cx('nodeToggleIcon')\" v-bind=\"getPTOptions('nodeToggleIcon')\" />\n                    <component v-else :is=\"node.collapsedIcon ? 'span' : 'ChevronRightIcon'\" :class=\"cx('nodeToggleIcon')\" v-bind=\"getPTOptions('nodeToggleIcon')\" />\n                </template>\n            </button>\n            <Checkbox\n                v-if=\"checkboxMode\"\n                :defaultValue=\"checked\"\n                :binary=\"true\"\n                :indeterminate=\"partialChecked\"\n                :class=\"cx('nodeCheckbox')\"\n                :tabindex=\"-1\"\n                :unstyled=\"unstyled\"\n                :pt=\"getPTOptions('pcNodeCheckbox')\"\n                :data-p-partialchecked=\"partialChecked\"\n            >\n                <template #icon=\"slotProps\">\n                    <component v-if=\"templates['checkboxicon']\" :is=\"templates['checkboxicon']\" :checked=\"slotProps.checked\" :partialChecked=\"partialChecked\" :class=\"slotProps.class\" />\n                </template>\n            </Checkbox>\n            <component v-if=\"templates['nodeicon']\" :is=\"templates['nodeicon']\" :node=\"node\" :class=\"[cx('nodeIcon')]\" v-bind=\"getPTOptions('nodeIcon')\"></component>\n            <span v-else :class=\"[cx('nodeIcon'), node.icon]\" v-bind=\"getPTOptions('nodeIcon')\"></span>\n            <span :class=\"cx('nodeLabel')\" v-bind=\"getPTOptions('nodeLabel')\" @keydown.stop>\n                <component v-if=\"templates[node.type] || templates['default']\" :is=\"templates[node.type] || templates['default']\" :node=\"node\" :expanded=\"expanded\" :selected=\"checkboxMode ? checked : selected\" />\n                <template v-else>{{ label(node) }}</template>\n            </span>\n        </div>\n        <ul v-if=\"hasChildren && expanded\" :class=\"cx('nodeChildren')\" role=\"group\" v-bind=\"ptm('nodeChildren')\">\n            <TreeNode\n                v-for=\"childNode of node.children\"\n                :key=\"childNode.key\"\n                :node=\"childNode\"\n                :templates=\"templates\"\n                :level=\"level + 1\"\n                :loadingMode=\"loadingMode\"\n                :expandedKeys=\"expandedKeys\"\n                @node-toggle=\"onChildNodeToggle\"\n                @node-click=\"onChildNodeClick\"\n                :selectionMode=\"selectionMode\"\n                :selectionKeys=\"selectionKeys\"\n                @checkbox-change=\"propagateUp\"\n                :unstyled=\"unstyled\"\n                :pt=\"pt\"\n            />\n        </ul>\n    </li>\n</template>\n\n<script>\nimport { find, findSingle, getAttribute } from '@primeuix/utils/dom';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport CheckIcon from '@primevue/icons/check';\nimport ChevronDownIcon from '@primevue/icons/chevrondown';\nimport ChevronRightIcon from '@primevue/icons/chevronright';\nimport MinusIcon from '@primevue/icons/minus';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport Checkbox from 'primevue/checkbox';\nimport Ripple from 'primevue/ripple';\n\nexport default {\n    name: 'TreeNode',\n    hostName: 'Tree',\n    extends: BaseComponent,\n    emits: ['node-toggle', 'node-click', 'checkbox-change'],\n    props: {\n        node: {\n            type: null,\n            default: null\n        },\n        expandedKeys: {\n            type: null,\n            default: null\n        },\n        loadingMode: {\n            type: String,\n            default: 'mask'\n        },\n        selectionKeys: {\n            type: null,\n            default: null\n        },\n        selectionMode: {\n            type: String,\n            default: null\n        },\n        templates: {\n            type: null,\n            default: null\n        },\n        level: {\n            type: Number,\n            default: null\n        },\n        index: null\n    },\n    nodeTouched: false,\n    toggleClicked: false,\n    mounted() {\n        this.setAllNodesTabIndexes();\n    },\n    methods: {\n        toggle() {\n            this.$emit('node-toggle', this.node);\n            this.toggleClicked = true;\n        },\n        label(node) {\n            return typeof node.label === 'function' ? node.label() : node.label;\n        },\n        onChildNodeToggle(node) {\n            this.$emit('node-toggle', node);\n        },\n        getPTOptions(key) {\n            return this.ptm(key, {\n                context: {\n                    node: this.node,\n                    index: this.index,\n                    expanded: this.expanded,\n                    selected: this.selected,\n                    checked: this.checked,\n                    partialChecked: this.partialChecked,\n                    leaf: this.leaf\n                }\n            });\n        },\n        onClick(event) {\n            if (this.toggleClicked || getAttribute(event.target, '[data-pc-section=\"nodetogglebutton\"]') || getAttribute(event.target.parentElement, '[data-pc-section=\"nodetogglebutton\"]')) {\n                this.toggleClicked = false;\n\n                return;\n            }\n\n            if (this.isCheckboxSelectionMode()) {\n                if (this.node.selectable != false) {\n                    this.toggleCheckbox();\n                }\n            } else {\n                this.$emit('node-click', {\n                    originalEvent: event,\n                    nodeTouched: this.nodeTouched,\n                    node: this.node\n                });\n            }\n\n            this.nodeTouched = false;\n        },\n        onChildNodeClick(event) {\n            this.$emit('node-click', event);\n        },\n        onTouchEnd() {\n            this.nodeTouched = true;\n        },\n        onKeyDown(event) {\n            if (!this.isSameNode(event)) return;\n\n            switch (event.code) {\n                case 'Tab':\n                    this.onTabKey(event);\n\n                    break;\n\n                case 'ArrowDown':\n                    this.onArrowDown(event);\n\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUp(event);\n\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRight(event);\n\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeft(event);\n\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                case 'Space':\n                    this.onEnterKey(event);\n\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDown(event) {\n            const nodeElement = event.target.getAttribute('data-pc-section') === 'nodetogglebutton' ? event.target.closest('[role=\"treeitem\"]') : event.target;\n            const listElement = nodeElement.children[1];\n\n            if (listElement) {\n                this.focusRowChange(nodeElement, listElement.children[0]);\n            } else {\n                if (nodeElement.nextElementSibling) {\n                    this.focusRowChange(nodeElement, nodeElement.nextElementSibling);\n                } else {\n                    let nextSiblingAncestor = this.findNextSiblingOfAncestor(nodeElement);\n\n                    if (nextSiblingAncestor) {\n                        this.focusRowChange(nodeElement, nextSiblingAncestor);\n                    }\n                }\n            }\n\n            event.preventDefault();\n        },\n        onArrowUp(event) {\n            const nodeElement = event.target;\n\n            if (nodeElement.previousElementSibling) {\n                this.focusRowChange(nodeElement, nodeElement.previousElementSibling, this.findLastVisibleDescendant(nodeElement.previousElementSibling));\n            } else {\n                let parentNodeElement = this.getParentNodeElement(nodeElement);\n\n                if (parentNodeElement) {\n                    this.focusRowChange(nodeElement, parentNodeElement);\n                }\n            }\n\n            event.preventDefault();\n        },\n        onArrowRight(event) {\n            if (this.leaf || this.expanded) return;\n\n            event.currentTarget.tabIndex = -1;\n\n            this.$emit('node-toggle', this.node);\n            this.$nextTick(() => {\n                this.onArrowDown(event);\n            });\n        },\n        onArrowLeft(event) {\n            const togglerElement = findSingle(event.currentTarget, '[data-pc-section=\"nodetogglebutton\"]');\n\n            if (this.level === 0 && !this.expanded) {\n                return false;\n            }\n\n            if (this.expanded && !this.leaf) {\n                togglerElement.click();\n\n                return false;\n            }\n\n            const target = this.findBeforeClickableNode(event.currentTarget);\n\n            if (target) {\n                this.focusRowChange(event.currentTarget, target);\n            }\n        },\n        onEnterKey(event) {\n            this.setTabIndexForSelectionMode(event, this.nodeTouched);\n            this.onClick(event);\n\n            event.preventDefault();\n        },\n        onTabKey() {\n            this.setAllNodesTabIndexes();\n        },\n        setAllNodesTabIndexes() {\n            const nodes = find(this.$refs.currentNode.closest('[data-pc-section=\"rootchildren\"]'), '[role=\"treeitem\"]');\n\n            const hasSelectedNode = [...nodes].some((node) => node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true');\n\n            [...nodes].forEach((node) => {\n                node.tabIndex = -1;\n            });\n\n            if (hasSelectedNode) {\n                const selectedNodes = [...nodes].filter((node) => node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true');\n\n                selectedNodes[0].tabIndex = 0;\n\n                return;\n            }\n\n            [...nodes][0].tabIndex = 0;\n        },\n        setTabIndexForSelectionMode(event, nodeTouched) {\n            if (this.selectionMode !== null) {\n                const elements = [...find(this.$refs.currentNode.parentElement, '[role=\"treeitem\"]')];\n\n                event.currentTarget.tabIndex = nodeTouched === false ? -1 : 0;\n\n                if (elements.every((element) => element.tabIndex === -1)) {\n                    elements[0].tabIndex = 0;\n                }\n            }\n        },\n        focusRowChange(firstFocusableRow, currentFocusedRow, lastVisibleDescendant) {\n            firstFocusableRow.tabIndex = '-1';\n            currentFocusedRow.tabIndex = '0';\n\n            this.focusNode(lastVisibleDescendant || currentFocusedRow);\n        },\n        findBeforeClickableNode(node) {\n            const parentListElement = node.closest('ul').closest('li');\n\n            if (parentListElement) {\n                const prevNodeButton = findSingle(parentListElement, 'button');\n\n                if (prevNodeButton && prevNodeButton.style.visibility !== 'hidden') {\n                    return parentListElement;\n                }\n\n                return this.findBeforeClickableNode(node.previousElementSibling);\n            }\n\n            return null;\n        },\n        toggleCheckbox() {\n            let _selectionKeys = this.selectionKeys ? { ...this.selectionKeys } : {};\n            const _check = !this.checked;\n\n            this.propagateDown(this.node, _check, _selectionKeys);\n\n            this.$emit('checkbox-change', {\n                node: this.node,\n                check: _check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        propagateDown(node, check, selectionKeys) {\n            if (check && node.selectable != false) selectionKeys[node.key] = { checked: true, partialChecked: false };\n            else delete selectionKeys[node.key];\n\n            if (node.children && node.children.length) {\n                for (let child of node.children) {\n                    this.propagateDown(child, check, selectionKeys);\n                }\n            }\n        },\n        propagateUp(event) {\n            let check = event.check;\n            let _selectionKeys = { ...event.selectionKeys };\n            let checkedChildCount = 0;\n            let childPartialSelected = false;\n\n            for (let child of this.node.children) {\n                if (_selectionKeys[child.key] && _selectionKeys[child.key].checked) checkedChildCount++;\n                else if (_selectionKeys[child.key] && _selectionKeys[child.key].partialChecked) childPartialSelected = true;\n            }\n\n            if (check && checkedChildCount === this.node.children.length) {\n                _selectionKeys[this.node.key] = { checked: true, partialChecked: false };\n            } else {\n                if (!check) {\n                    delete _selectionKeys[this.node.key];\n                }\n\n                if (childPartialSelected || (checkedChildCount > 0 && checkedChildCount !== this.node.children.length)) _selectionKeys[this.node.key] = { checked: false, partialChecked: true };\n                else delete _selectionKeys[this.node.key];\n            }\n\n            this.$emit('checkbox-change', {\n                node: event.node,\n                check: event.check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        onChildCheckboxChange(event) {\n            this.$emit('checkbox-change', event);\n        },\n        findNextSiblingOfAncestor(nodeElement) {\n            let parentNodeElement = this.getParentNodeElement(nodeElement);\n\n            if (parentNodeElement) {\n                if (parentNodeElement.nextElementSibling) return parentNodeElement.nextElementSibling;\n                else return this.findNextSiblingOfAncestor(parentNodeElement);\n            } else {\n                return null;\n            }\n        },\n        findLastVisibleDescendant(nodeElement) {\n            const childrenListElement = nodeElement.children[1];\n\n            if (childrenListElement) {\n                const lastChildElement = childrenListElement.children[childrenListElement.children.length - 1];\n\n                return this.findLastVisibleDescendant(lastChildElement);\n            } else {\n                return nodeElement;\n            }\n        },\n        getParentNodeElement(nodeElement) {\n            const parentNodeElement = nodeElement.parentElement.parentElement;\n\n            return getAttribute(parentNodeElement, 'role') === 'treeitem' ? parentNodeElement : null;\n        },\n        focusNode(element) {\n            element.focus();\n        },\n        isCheckboxSelectionMode() {\n            return this.selectionMode === 'checkbox';\n        },\n        isSameNode(event) {\n            return event.currentTarget && (event.currentTarget.isSameNode(event.target) || event.currentTarget.isSameNode(event.target.closest('[role=\"treeitem\"]')));\n        }\n    },\n    computed: {\n        hasChildren() {\n            return this.node.children && this.node.children.length > 0;\n        },\n        expanded() {\n            return this.expandedKeys && this.expandedKeys[this.node.key] === true;\n        },\n        leaf() {\n            return this.node.leaf === false ? false : !(this.node.children && this.node.children.length);\n        },\n        selectable() {\n            return this.node.selectable === false ? false : this.selectionMode != null;\n        },\n        selected() {\n            return this.selectionMode && this.selectionKeys ? this.selectionKeys[this.node.key] === true : false;\n        },\n        checkboxMode() {\n            return this.selectionMode === 'checkbox' && this.node.selectable !== false;\n        },\n        checked() {\n            return this.selectionKeys ? this.selectionKeys[this.node.key] && this.selectionKeys[this.node.key].checked : false;\n        },\n        partialChecked() {\n            return this.selectionKeys ? this.selectionKeys[this.node.key] && this.selectionKeys[this.node.key].partialChecked : false;\n        },\n        ariaChecked() {\n            return this.selectionMode === 'single' || this.selectionMode === 'multiple' ? this.selected : undefined;\n        },\n        ariaSelected() {\n            return this.checkboxMode ? this.checked : undefined;\n        }\n    },\n    components: {\n        Checkbox,\n        ChevronDownIcon,\n        ChevronRightIcon,\n        CheckIcon,\n        MinusIcon,\n        SpinnerIcon\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <li\n        ref=\"currentNode\"\n        :class=\"cx('node')\"\n        role=\"treeitem\"\n        :aria-label=\"label(node)\"\n        :aria-selected=\"ariaSelected\"\n        :aria-expanded=\"expanded\"\n        :aria-setsize=\"node.children ? node.children.length : 0\"\n        :aria-posinset=\"index + 1\"\n        :aria-level=\"level\"\n        :aria-checked=\"ariaChecked\"\n        :tabindex=\"index === 0 ? 0 : -1\"\n        @keydown=\"onKeyDown\"\n        v-bind=\"getPTOptions('node')\"\n    >\n        <div :class=\"cx('nodeContent')\" @click=\"onClick\" @touchend=\"onTouchEnd\" :style=\"node.style\" v-bind=\"getPTOptions('nodeContent')\" :data-p-selected=\"checkboxMode ? checked : selected\" :data-p-selectable=\"selectable\">\n            <button v-ripple type=\"button\" :class=\"cx('nodeToggleButton')\" @click=\"toggle\" tabindex=\"-1\" :data-p-leaf=\"leaf\" v-bind=\"getPTOptions('nodeToggleButton')\">\n                <template v-if=\"node.loading && loadingMode === 'icon'\">\n                    <!-- TODO: nodetogglericon deprecated since v4.0-->\n                    <component v-if=\"templates['nodetoggleicon'] || templates['nodetogglericon']\" :is=\"templates['nodetoggleicon'] || templates['nodetogglericon']\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <SpinnerIcon v-else spin :class=\"cx('nodeToggleIcon')\" v-bind=\"getPTOptions('nodeToggleIcon')\" />\n                </template>\n                <template v-else>\n                    <!-- TODO: togglericon deprecated since v4.0-->\n                    <component v-if=\"templates['nodetoggleicon'] || templates['togglericon']\" :is=\"templates['nodetoggleicon'] || templates['togglericon']\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <component v-else-if=\"expanded\" :is=\"node.expandedIcon ? 'span' : 'ChevronDownIcon'\" :class=\"cx('nodeToggleIcon')\" v-bind=\"getPTOptions('nodeToggleIcon')\" />\n                    <component v-else :is=\"node.collapsedIcon ? 'span' : 'ChevronRightIcon'\" :class=\"cx('nodeToggleIcon')\" v-bind=\"getPTOptions('nodeToggleIcon')\" />\n                </template>\n            </button>\n            <Checkbox\n                v-if=\"checkboxMode\"\n                :defaultValue=\"checked\"\n                :binary=\"true\"\n                :indeterminate=\"partialChecked\"\n                :class=\"cx('nodeCheckbox')\"\n                :tabindex=\"-1\"\n                :unstyled=\"unstyled\"\n                :pt=\"getPTOptions('pcNodeCheckbox')\"\n                :data-p-partialchecked=\"partialChecked\"\n            >\n                <template #icon=\"slotProps\">\n                    <component v-if=\"templates['checkboxicon']\" :is=\"templates['checkboxicon']\" :checked=\"slotProps.checked\" :partialChecked=\"partialChecked\" :class=\"slotProps.class\" />\n                </template>\n            </Checkbox>\n            <component v-if=\"templates['nodeicon']\" :is=\"templates['nodeicon']\" :node=\"node\" :class=\"[cx('nodeIcon')]\" v-bind=\"getPTOptions('nodeIcon')\"></component>\n            <span v-else :class=\"[cx('nodeIcon'), node.icon]\" v-bind=\"getPTOptions('nodeIcon')\"></span>\n            <span :class=\"cx('nodeLabel')\" v-bind=\"getPTOptions('nodeLabel')\" @keydown.stop>\n                <component v-if=\"templates[node.type] || templates['default']\" :is=\"templates[node.type] || templates['default']\" :node=\"node\" :expanded=\"expanded\" :selected=\"checkboxMode ? checked : selected\" />\n                <template v-else>{{ label(node) }}</template>\n            </span>\n        </div>\n        <ul v-if=\"hasChildren && expanded\" :class=\"cx('nodeChildren')\" role=\"group\" v-bind=\"ptm('nodeChildren')\">\n            <TreeNode\n                v-for=\"childNode of node.children\"\n                :key=\"childNode.key\"\n                :node=\"childNode\"\n                :templates=\"templates\"\n                :level=\"level + 1\"\n                :loadingMode=\"loadingMode\"\n                :expandedKeys=\"expandedKeys\"\n                @node-toggle=\"onChildNodeToggle\"\n                @node-click=\"onChildNodeClick\"\n                :selectionMode=\"selectionMode\"\n                :selectionKeys=\"selectionKeys\"\n                @checkbox-change=\"propagateUp\"\n                :unstyled=\"unstyled\"\n                :pt=\"pt\"\n            />\n        </ul>\n    </li>\n</template>\n\n<script>\nimport { find, findSingle, getAttribute } from '@primeuix/utils/dom';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport CheckIcon from '@primevue/icons/check';\nimport ChevronDownIcon from '@primevue/icons/chevrondown';\nimport ChevronRightIcon from '@primevue/icons/chevronright';\nimport MinusIcon from '@primevue/icons/minus';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport Checkbox from 'primevue/checkbox';\nimport Ripple from 'primevue/ripple';\n\nexport default {\n    name: 'TreeNode',\n    hostName: 'Tree',\n    extends: BaseComponent,\n    emits: ['node-toggle', 'node-click', 'checkbox-change'],\n    props: {\n        node: {\n            type: null,\n            default: null\n        },\n        expandedKeys: {\n            type: null,\n            default: null\n        },\n        loadingMode: {\n            type: String,\n            default: 'mask'\n        },\n        selectionKeys: {\n            type: null,\n            default: null\n        },\n        selectionMode: {\n            type: String,\n            default: null\n        },\n        templates: {\n            type: null,\n            default: null\n        },\n        level: {\n            type: Number,\n            default: null\n        },\n        index: null\n    },\n    nodeTouched: false,\n    toggleClicked: false,\n    mounted() {\n        this.setAllNodesTabIndexes();\n    },\n    methods: {\n        toggle() {\n            this.$emit('node-toggle', this.node);\n            this.toggleClicked = true;\n        },\n        label(node) {\n            return typeof node.label === 'function' ? node.label() : node.label;\n        },\n        onChildNodeToggle(node) {\n            this.$emit('node-toggle', node);\n        },\n        getPTOptions(key) {\n            return this.ptm(key, {\n                context: {\n                    node: this.node,\n                    index: this.index,\n                    expanded: this.expanded,\n                    selected: this.selected,\n                    checked: this.checked,\n                    partialChecked: this.partialChecked,\n                    leaf: this.leaf\n                }\n            });\n        },\n        onClick(event) {\n            if (this.toggleClicked || getAttribute(event.target, '[data-pc-section=\"nodetogglebutton\"]') || getAttribute(event.target.parentElement, '[data-pc-section=\"nodetogglebutton\"]')) {\n                this.toggleClicked = false;\n\n                return;\n            }\n\n            if (this.isCheckboxSelectionMode()) {\n                if (this.node.selectable != false) {\n                    this.toggleCheckbox();\n                }\n            } else {\n                this.$emit('node-click', {\n                    originalEvent: event,\n                    nodeTouched: this.nodeTouched,\n                    node: this.node\n                });\n            }\n\n            this.nodeTouched = false;\n        },\n        onChildNodeClick(event) {\n            this.$emit('node-click', event);\n        },\n        onTouchEnd() {\n            this.nodeTouched = true;\n        },\n        onKeyDown(event) {\n            if (!this.isSameNode(event)) return;\n\n            switch (event.code) {\n                case 'Tab':\n                    this.onTabKey(event);\n\n                    break;\n\n                case 'ArrowDown':\n                    this.onArrowDown(event);\n\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUp(event);\n\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRight(event);\n\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeft(event);\n\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                case 'Space':\n                    this.onEnterKey(event);\n\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDown(event) {\n            const nodeElement = event.target.getAttribute('data-pc-section') === 'nodetogglebutton' ? event.target.closest('[role=\"treeitem\"]') : event.target;\n            const listElement = nodeElement.children[1];\n\n            if (listElement) {\n                this.focusRowChange(nodeElement, listElement.children[0]);\n            } else {\n                if (nodeElement.nextElementSibling) {\n                    this.focusRowChange(nodeElement, nodeElement.nextElementSibling);\n                } else {\n                    let nextSiblingAncestor = this.findNextSiblingOfAncestor(nodeElement);\n\n                    if (nextSiblingAncestor) {\n                        this.focusRowChange(nodeElement, nextSiblingAncestor);\n                    }\n                }\n            }\n\n            event.preventDefault();\n        },\n        onArrowUp(event) {\n            const nodeElement = event.target;\n\n            if (nodeElement.previousElementSibling) {\n                this.focusRowChange(nodeElement, nodeElement.previousElementSibling, this.findLastVisibleDescendant(nodeElement.previousElementSibling));\n            } else {\n                let parentNodeElement = this.getParentNodeElement(nodeElement);\n\n                if (parentNodeElement) {\n                    this.focusRowChange(nodeElement, parentNodeElement);\n                }\n            }\n\n            event.preventDefault();\n        },\n        onArrowRight(event) {\n            if (this.leaf || this.expanded) return;\n\n            event.currentTarget.tabIndex = -1;\n\n            this.$emit('node-toggle', this.node);\n            this.$nextTick(() => {\n                this.onArrowDown(event);\n            });\n        },\n        onArrowLeft(event) {\n            const togglerElement = findSingle(event.currentTarget, '[data-pc-section=\"nodetogglebutton\"]');\n\n            if (this.level === 0 && !this.expanded) {\n                return false;\n            }\n\n            if (this.expanded && !this.leaf) {\n                togglerElement.click();\n\n                return false;\n            }\n\n            const target = this.findBeforeClickableNode(event.currentTarget);\n\n            if (target) {\n                this.focusRowChange(event.currentTarget, target);\n            }\n        },\n        onEnterKey(event) {\n            this.setTabIndexForSelectionMode(event, this.nodeTouched);\n            this.onClick(event);\n\n            event.preventDefault();\n        },\n        onTabKey() {\n            this.setAllNodesTabIndexes();\n        },\n        setAllNodesTabIndexes() {\n            const nodes = find(this.$refs.currentNode.closest('[data-pc-section=\"rootchildren\"]'), '[role=\"treeitem\"]');\n\n            const hasSelectedNode = [...nodes].some((node) => node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true');\n\n            [...nodes].forEach((node) => {\n                node.tabIndex = -1;\n            });\n\n            if (hasSelectedNode) {\n                const selectedNodes = [...nodes].filter((node) => node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true');\n\n                selectedNodes[0].tabIndex = 0;\n\n                return;\n            }\n\n            [...nodes][0].tabIndex = 0;\n        },\n        setTabIndexForSelectionMode(event, nodeTouched) {\n            if (this.selectionMode !== null) {\n                const elements = [...find(this.$refs.currentNode.parentElement, '[role=\"treeitem\"]')];\n\n                event.currentTarget.tabIndex = nodeTouched === false ? -1 : 0;\n\n                if (elements.every((element) => element.tabIndex === -1)) {\n                    elements[0].tabIndex = 0;\n                }\n            }\n        },\n        focusRowChange(firstFocusableRow, currentFocusedRow, lastVisibleDescendant) {\n            firstFocusableRow.tabIndex = '-1';\n            currentFocusedRow.tabIndex = '0';\n\n            this.focusNode(lastVisibleDescendant || currentFocusedRow);\n        },\n        findBeforeClickableNode(node) {\n            const parentListElement = node.closest('ul').closest('li');\n\n            if (parentListElement) {\n                const prevNodeButton = findSingle(parentListElement, 'button');\n\n                if (prevNodeButton && prevNodeButton.style.visibility !== 'hidden') {\n                    return parentListElement;\n                }\n\n                return this.findBeforeClickableNode(node.previousElementSibling);\n            }\n\n            return null;\n        },\n        toggleCheckbox() {\n            let _selectionKeys = this.selectionKeys ? { ...this.selectionKeys } : {};\n            const _check = !this.checked;\n\n            this.propagateDown(this.node, _check, _selectionKeys);\n\n            this.$emit('checkbox-change', {\n                node: this.node,\n                check: _check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        propagateDown(node, check, selectionKeys) {\n            if (check && node.selectable != false) selectionKeys[node.key] = { checked: true, partialChecked: false };\n            else delete selectionKeys[node.key];\n\n            if (node.children && node.children.length) {\n                for (let child of node.children) {\n                    this.propagateDown(child, check, selectionKeys);\n                }\n            }\n        },\n        propagateUp(event) {\n            let check = event.check;\n            let _selectionKeys = { ...event.selectionKeys };\n            let checkedChildCount = 0;\n            let childPartialSelected = false;\n\n            for (let child of this.node.children) {\n                if (_selectionKeys[child.key] && _selectionKeys[child.key].checked) checkedChildCount++;\n                else if (_selectionKeys[child.key] && _selectionKeys[child.key].partialChecked) childPartialSelected = true;\n            }\n\n            if (check && checkedChildCount === this.node.children.length) {\n                _selectionKeys[this.node.key] = { checked: true, partialChecked: false };\n            } else {\n                if (!check) {\n                    delete _selectionKeys[this.node.key];\n                }\n\n                if (childPartialSelected || (checkedChildCount > 0 && checkedChildCount !== this.node.children.length)) _selectionKeys[this.node.key] = { checked: false, partialChecked: true };\n                else delete _selectionKeys[this.node.key];\n            }\n\n            this.$emit('checkbox-change', {\n                node: event.node,\n                check: event.check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        onChildCheckboxChange(event) {\n            this.$emit('checkbox-change', event);\n        },\n        findNextSiblingOfAncestor(nodeElement) {\n            let parentNodeElement = this.getParentNodeElement(nodeElement);\n\n            if (parentNodeElement) {\n                if (parentNodeElement.nextElementSibling) return parentNodeElement.nextElementSibling;\n                else return this.findNextSiblingOfAncestor(parentNodeElement);\n            } else {\n                return null;\n            }\n        },\n        findLastVisibleDescendant(nodeElement) {\n            const childrenListElement = nodeElement.children[1];\n\n            if (childrenListElement) {\n                const lastChildElement = childrenListElement.children[childrenListElement.children.length - 1];\n\n                return this.findLastVisibleDescendant(lastChildElement);\n            } else {\n                return nodeElement;\n            }\n        },\n        getParentNodeElement(nodeElement) {\n            const parentNodeElement = nodeElement.parentElement.parentElement;\n\n            return getAttribute(parentNodeElement, 'role') === 'treeitem' ? parentNodeElement : null;\n        },\n        focusNode(element) {\n            element.focus();\n        },\n        isCheckboxSelectionMode() {\n            return this.selectionMode === 'checkbox';\n        },\n        isSameNode(event) {\n            return event.currentTarget && (event.currentTarget.isSameNode(event.target) || event.currentTarget.isSameNode(event.target.closest('[role=\"treeitem\"]')));\n        }\n    },\n    computed: {\n        hasChildren() {\n            return this.node.children && this.node.children.length > 0;\n        },\n        expanded() {\n            return this.expandedKeys && this.expandedKeys[this.node.key] === true;\n        },\n        leaf() {\n            return this.node.leaf === false ? false : !(this.node.children && this.node.children.length);\n        },\n        selectable() {\n            return this.node.selectable === false ? false : this.selectionMode != null;\n        },\n        selected() {\n            return this.selectionMode && this.selectionKeys ? this.selectionKeys[this.node.key] === true : false;\n        },\n        checkboxMode() {\n            return this.selectionMode === 'checkbox' && this.node.selectable !== false;\n        },\n        checked() {\n            return this.selectionKeys ? this.selectionKeys[this.node.key] && this.selectionKeys[this.node.key].checked : false;\n        },\n        partialChecked() {\n            return this.selectionKeys ? this.selectionKeys[this.node.key] && this.selectionKeys[this.node.key].partialChecked : false;\n        },\n        ariaChecked() {\n            return this.selectionMode === 'single' || this.selectionMode === 'multiple' ? this.selected : undefined;\n        },\n        ariaSelected() {\n            return this.checkboxMode ? this.checked : undefined;\n        }\n    },\n    components: {\n        Checkbox,\n        ChevronDownIcon,\n        ChevronRightIcon,\n        CheckIcon,\n        MinusIcon,\n        SpinnerIcon\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :data-p=\"containerDataP\" v-bind=\"ptmi('root')\">\n        <template v-if=\"loading && loadingMode === 'mask'\">\n            <div :class=\"cx('mask')\" v-bind=\"ptm('mask')\">\n                <slot name=\"loadingicon\" :class=\"cx('loadingIcon')\">\n                    <i v-if=\"loadingIcon\" :class=\"[cx('loadingIcon'), 'pi-spin', loadingIcon]\" v-bind=\"ptm('loadingIcon')\" />\n                    <SpinnerIcon v-else spin :class=\"cx('loadingIcon')\" v-bind=\"ptm('loadingIcon')\" />\n                </slot>\n            </div>\n        </template>\n        <IconField v-if=\"filter\" :unstyled=\"unstyled\" :pt=\"{ ...ptm('pcFilter'), ...ptm('pcFilterContainer') }\" :class=\"cx('pcFilterContainer')\">\n            <InputText v-model=\"filterValue\" autocomplete=\"off\" :class=\"cx('pcFilterInput')\" :placeholder=\"filterPlaceholder\" :unstyled=\"unstyled\" @keyup=\"onFilterKeyup\" :pt=\"ptm('pcFilterInput')\" />\n            <InputIcon :unstyled=\"unstyled\" :pt=\"ptm('pcFilterIconContainer')\">\n                <!--TODO: searchicon deprecated since v4.0-->\n                <slot :name=\"$slots.filtericon ? 'filtericon' : 'searchicon'\" :class=\"cx('filterIcon')\">\n                    <SearchIcon :class=\"cx('filterIcon')\" v-bind=\"ptm('filterIcon')\" />\n                </slot>\n            </InputIcon>\n        </IconField>\n        <div :class=\"cx('wrapper')\" :style=\"{ maxHeight: scrollHeight }\" :data-p=\"wrapperDataP\" v-bind=\"ptm('wrapper')\">\n            <slot name=\"header\" :value=\"value\" :expandedKeys=\"expandedKeys\" :selectionKeys=\"selectionKeys\" />\n            <ul :class=\"cx('rootChildren')\" role=\"tree\" :aria-labelledby=\"ariaLabelledby\" :aria-label=\"ariaLabel\" v-bind=\"ptm('rootChildren')\">\n                <TreeNode\n                    v-for=\"(node, index) of valueToRender\"\n                    :key=\"node.key\"\n                    :node=\"node\"\n                    :templates=\"$slots\"\n                    :level=\"level + 1\"\n                    :index=\"index\"\n                    :expandedKeys=\"d_expandedKeys\"\n                    @node-toggle=\"onNodeToggle\"\n                    @node-click=\"onNodeClick\"\n                    :selectionMode=\"selectionMode\"\n                    :selectionKeys=\"selectionKeys\"\n                    @checkbox-change=\"onCheckboxChange\"\n                    :loadingMode=\"loadingMode\"\n                    :unstyled=\"unstyled\"\n                    :pt=\"pt\"\n                ></TreeNode>\n            </ul>\n            <slot name=\"footer\" :value=\"value\" :expandedKeys=\"expandedKeys\" :selectionKeys=\"selectionKeys\" />\n        </div>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { isFunction, resolveFieldData } from '@primeuix/utils/object';\nimport SearchIcon from '@primevue/icons/search';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport IconField from 'primevue/iconfield';\nimport InputIcon from 'primevue/inputicon';\nimport InputText from 'primevue/inputtext';\nimport BaseTree from './BaseTree.vue';\nimport TreeNode from './TreeNode.vue';\n\nexport default {\n    name: 'Tree',\n    extends: BaseTree,\n    inheritAttrs: false,\n    emits: ['node-expand', 'node-collapse', 'update:expandedKeys', 'update:selectionKeys', 'node-select', 'node-unselect', 'filter'],\n    data() {\n        return {\n            d_expandedKeys: this.expandedKeys || {},\n            filterValue: null\n        };\n    },\n    watch: {\n        expandedKeys(newValue) {\n            this.d_expandedKeys = newValue;\n        }\n    },\n    methods: {\n        onNodeToggle(node) {\n            const key = node.key;\n\n            if (this.d_expandedKeys[key]) {\n                delete this.d_expandedKeys[key];\n                this.$emit('node-collapse', node);\n            } else {\n                this.d_expandedKeys[key] = true;\n                this.$emit('node-expand', node);\n            }\n\n            this.d_expandedKeys = { ...this.d_expandedKeys };\n            this.$emit('update:expandedKeys', this.d_expandedKeys);\n        },\n        onNodeClick(event) {\n            if (this.selectionMode != null && event.node.selectable !== false) {\n                const metaSelection = event.nodeTouched ? false : this.metaKeySelection;\n                const _selectionKeys = metaSelection ? this.handleSelectionWithMetaKey(event) : this.handleSelectionWithoutMetaKey(event);\n\n                this.$emit('update:selectionKeys', _selectionKeys);\n            }\n        },\n        onCheckboxChange(event) {\n            this.$emit('update:selectionKeys', event.selectionKeys);\n\n            if (event.check) this.$emit('node-select', event.node);\n            else this.$emit('node-unselect', event.node);\n        },\n        handleSelectionWithMetaKey(event) {\n            const originalEvent = event.originalEvent;\n            const node = event.node;\n            const metaKey = originalEvent.metaKey || originalEvent.ctrlKey;\n            const selected = this.isNodeSelected(node);\n            let _selectionKeys;\n\n            if (selected && metaKey) {\n                if (this.isSingleSelectionMode()) {\n                    _selectionKeys = {};\n                } else {\n                    _selectionKeys = { ...this.selectionKeys };\n                    delete _selectionKeys[node.key];\n                }\n\n                this.$emit('node-unselect', node);\n            } else {\n                if (this.isSingleSelectionMode()) {\n                    _selectionKeys = {};\n                } else if (this.isMultipleSelectionMode()) {\n                    _selectionKeys = !metaKey ? {} : this.selectionKeys ? { ...this.selectionKeys } : {};\n                }\n\n                _selectionKeys[node.key] = true;\n                this.$emit('node-select', node);\n            }\n\n            return _selectionKeys;\n        },\n        handleSelectionWithoutMetaKey(event) {\n            const node = event.node;\n            const selected = this.isNodeSelected(node);\n            let _selectionKeys;\n\n            if (this.isSingleSelectionMode()) {\n                if (selected) {\n                    _selectionKeys = {};\n                    this.$emit('node-unselect', node);\n                } else {\n                    _selectionKeys = {};\n                    _selectionKeys[node.key] = true;\n                    this.$emit('node-select', node);\n                }\n            } else {\n                if (selected) {\n                    _selectionKeys = { ...this.selectionKeys };\n                    delete _selectionKeys[node.key];\n\n                    this.$emit('node-unselect', node);\n                } else {\n                    _selectionKeys = this.selectionKeys ? { ...this.selectionKeys } : {};\n                    _selectionKeys[node.key] = true;\n\n                    this.$emit('node-select', node);\n                }\n            }\n\n            return _selectionKeys;\n        },\n        isSingleSelectionMode() {\n            return this.selectionMode === 'single';\n        },\n        isMultipleSelectionMode() {\n            return this.selectionMode === 'multiple';\n        },\n        isNodeSelected(node) {\n            return this.selectionMode && this.selectionKeys ? this.selectionKeys[node.key] === true : false;\n        },\n        isChecked(node) {\n            return this.selectionKeys ? this.selectionKeys[node.key] && this.selectionKeys[node.key].checked : false;\n        },\n        isNodeLeaf(node) {\n            return node.leaf === false ? false : !(node.children && node.children.length);\n        },\n        onFilterKeyup(event) {\n            if (event.code === 'Enter' || event.code === 'NumpadEnter') {\n                event.preventDefault();\n            }\n\n            this.$emit('filter', { originalEvent: event, value: event.target.value });\n        },\n        findFilteredNodes(node, paramsWithoutNode) {\n            if (node) {\n                let matched = false;\n\n                if (node.children) {\n                    let childNodes = [...node.children];\n\n                    node.children = [];\n\n                    for (let childNode of childNodes) {\n                        let copyChildNode = { ...childNode };\n\n                        if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n                            matched = true;\n                            node.children.push(copyChildNode);\n                        }\n                    }\n                }\n\n                if (matched) {\n                    return true;\n                }\n            }\n        },\n        isFilterMatched(node, { searchFields, filterText, strict }) {\n            let matched = false;\n\n            for (let field of searchFields) {\n                let fieldValue = String(resolveFieldData(node, field)).toLocaleLowerCase(this.filterLocale);\n\n                if (fieldValue.indexOf(filterText) > -1) {\n                    matched = true;\n                }\n            }\n\n            if (!matched || (strict && !this.isNodeLeaf(node))) {\n                matched = this.findFilteredNodes(node, { searchFields, filterText, strict }) || matched;\n            }\n\n            return matched;\n        }\n    },\n    computed: {\n        filteredValue() {\n            let filteredNodes = [];\n            const searchFields = isFunction(this.filterBy) ? [this.filterBy] : this.filterBy.split(',');\n            const filterText = this.filterValue.trim().toLocaleLowerCase(this.filterLocale);\n            const strict = this.filterMode === 'strict';\n\n            for (let node of this.value) {\n                let _node = { ...node };\n                let paramsWithoutNode = { searchFields, filterText, strict };\n\n                if (\n                    (strict && (this.findFilteredNodes(_node, paramsWithoutNode) || this.isFilterMatched(_node, paramsWithoutNode))) ||\n                    (!strict && (this.isFilterMatched(_node, paramsWithoutNode) || this.findFilteredNodes(_node, paramsWithoutNode)))\n                ) {\n                    filteredNodes.push(_node);\n                }\n            }\n\n            return filteredNodes;\n        },\n        valueToRender() {\n            if (this.filterValue && this.filterValue.trim().length > 0) return this.filteredValue;\n            else return this.value;\n        },\n        containerDataP() {\n            return cn({\n                loading: this.loading,\n                scrollable: this.scrollHeight === 'flex'\n            });\n        },\n        wrapperDataP() {\n            return cn({\n                scrollable: this.scrollHeight === 'flex'\n            });\n        }\n    },\n    components: {\n        TreeNode,\n        InputText,\n        InputIcon,\n        IconField,\n        SearchIcon,\n        SpinnerIcon\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :data-p=\"containerDataP\" v-bind=\"ptmi('root')\">\n        <template v-if=\"loading && loadingMode === 'mask'\">\n            <div :class=\"cx('mask')\" v-bind=\"ptm('mask')\">\n                <slot name=\"loadingicon\" :class=\"cx('loadingIcon')\">\n                    <i v-if=\"loadingIcon\" :class=\"[cx('loadingIcon'), 'pi-spin', loadingIcon]\" v-bind=\"ptm('loadingIcon')\" />\n                    <SpinnerIcon v-else spin :class=\"cx('loadingIcon')\" v-bind=\"ptm('loadingIcon')\" />\n                </slot>\n            </div>\n        </template>\n        <IconField v-if=\"filter\" :unstyled=\"unstyled\" :pt=\"{ ...ptm('pcFilter'), ...ptm('pcFilterContainer') }\" :class=\"cx('pcFilterContainer')\">\n            <InputText v-model=\"filterValue\" autocomplete=\"off\" :class=\"cx('pcFilterInput')\" :placeholder=\"filterPlaceholder\" :unstyled=\"unstyled\" @keyup=\"onFilterKeyup\" :pt=\"ptm('pcFilterInput')\" />\n            <InputIcon :unstyled=\"unstyled\" :pt=\"ptm('pcFilterIconContainer')\">\n                <!--TODO: searchicon deprecated since v4.0-->\n                <slot :name=\"$slots.filtericon ? 'filtericon' : 'searchicon'\" :class=\"cx('filterIcon')\">\n                    <SearchIcon :class=\"cx('filterIcon')\" v-bind=\"ptm('filterIcon')\" />\n                </slot>\n            </InputIcon>\n        </IconField>\n        <div :class=\"cx('wrapper')\" :style=\"{ maxHeight: scrollHeight }\" :data-p=\"wrapperDataP\" v-bind=\"ptm('wrapper')\">\n            <slot name=\"header\" :value=\"value\" :expandedKeys=\"expandedKeys\" :selectionKeys=\"selectionKeys\" />\n            <ul :class=\"cx('rootChildren')\" role=\"tree\" :aria-labelledby=\"ariaLabelledby\" :aria-label=\"ariaLabel\" v-bind=\"ptm('rootChildren')\">\n                <TreeNode\n                    v-for=\"(node, index) of valueToRender\"\n                    :key=\"node.key\"\n                    :node=\"node\"\n                    :templates=\"$slots\"\n                    :level=\"level + 1\"\n                    :index=\"index\"\n                    :expandedKeys=\"d_expandedKeys\"\n                    @node-toggle=\"onNodeToggle\"\n                    @node-click=\"onNodeClick\"\n                    :selectionMode=\"selectionMode\"\n                    :selectionKeys=\"selectionKeys\"\n                    @checkbox-change=\"onCheckboxChange\"\n                    :loadingMode=\"loadingMode\"\n                    :unstyled=\"unstyled\"\n                    :pt=\"pt\"\n                ></TreeNode>\n            </ul>\n            <slot name=\"footer\" :value=\"value\" :expandedKeys=\"expandedKeys\" :selectionKeys=\"selectionKeys\" />\n        </div>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { isFunction, resolveFieldData } from '@primeuix/utils/object';\nimport SearchIcon from '@primevue/icons/search';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport IconField from 'primevue/iconfield';\nimport InputIcon from 'primevue/inputicon';\nimport InputText from 'primevue/inputtext';\nimport BaseTree from './BaseTree.vue';\nimport TreeNode from './TreeNode.vue';\n\nexport default {\n    name: 'Tree',\n    extends: BaseTree,\n    inheritAttrs: false,\n    emits: ['node-expand', 'node-collapse', 'update:expandedKeys', 'update:selectionKeys', 'node-select', 'node-unselect', 'filter'],\n    data() {\n        return {\n            d_expandedKeys: this.expandedKeys || {},\n            filterValue: null\n        };\n    },\n    watch: {\n        expandedKeys(newValue) {\n            this.d_expandedKeys = newValue;\n        }\n    },\n    methods: {\n        onNodeToggle(node) {\n            const key = node.key;\n\n            if (this.d_expandedKeys[key]) {\n                delete this.d_expandedKeys[key];\n                this.$emit('node-collapse', node);\n            } else {\n                this.d_expandedKeys[key] = true;\n                this.$emit('node-expand', node);\n            }\n\n            this.d_expandedKeys = { ...this.d_expandedKeys };\n            this.$emit('update:expandedKeys', this.d_expandedKeys);\n        },\n        onNodeClick(event) {\n            if (this.selectionMode != null && event.node.selectable !== false) {\n                const metaSelection = event.nodeTouched ? false : this.metaKeySelection;\n                const _selectionKeys = metaSelection ? this.handleSelectionWithMetaKey(event) : this.handleSelectionWithoutMetaKey(event);\n\n                this.$emit('update:selectionKeys', _selectionKeys);\n            }\n        },\n        onCheckboxChange(event) {\n            this.$emit('update:selectionKeys', event.selectionKeys);\n\n            if (event.check) this.$emit('node-select', event.node);\n            else this.$emit('node-unselect', event.node);\n        },\n        handleSelectionWithMetaKey(event) {\n            const originalEvent = event.originalEvent;\n            const node = event.node;\n            const metaKey = originalEvent.metaKey || originalEvent.ctrlKey;\n            const selected = this.isNodeSelected(node);\n            let _selectionKeys;\n\n            if (selected && metaKey) {\n                if (this.isSingleSelectionMode()) {\n                    _selectionKeys = {};\n                } else {\n                    _selectionKeys = { ...this.selectionKeys };\n                    delete _selectionKeys[node.key];\n                }\n\n                this.$emit('node-unselect', node);\n            } else {\n                if (this.isSingleSelectionMode()) {\n                    _selectionKeys = {};\n                } else if (this.isMultipleSelectionMode()) {\n                    _selectionKeys = !metaKey ? {} : this.selectionKeys ? { ...this.selectionKeys } : {};\n                }\n\n                _selectionKeys[node.key] = true;\n                this.$emit('node-select', node);\n            }\n\n            return _selectionKeys;\n        },\n        handleSelectionWithoutMetaKey(event) {\n            const node = event.node;\n            const selected = this.isNodeSelected(node);\n            let _selectionKeys;\n\n            if (this.isSingleSelectionMode()) {\n                if (selected) {\n                    _selectionKeys = {};\n                    this.$emit('node-unselect', node);\n                } else {\n                    _selectionKeys = {};\n                    _selectionKeys[node.key] = true;\n                    this.$emit('node-select', node);\n                }\n            } else {\n                if (selected) {\n                    _selectionKeys = { ...this.selectionKeys };\n                    delete _selectionKeys[node.key];\n\n                    this.$emit('node-unselect', node);\n                } else {\n                    _selectionKeys = this.selectionKeys ? { ...this.selectionKeys } : {};\n                    _selectionKeys[node.key] = true;\n\n                    this.$emit('node-select', node);\n                }\n            }\n\n            return _selectionKeys;\n        },\n        isSingleSelectionMode() {\n            return this.selectionMode === 'single';\n        },\n        isMultipleSelectionMode() {\n            return this.selectionMode === 'multiple';\n        },\n        isNodeSelected(node) {\n            return this.selectionMode && this.selectionKeys ? this.selectionKeys[node.key] === true : false;\n        },\n        isChecked(node) {\n            return this.selectionKeys ? this.selectionKeys[node.key] && this.selectionKeys[node.key].checked : false;\n        },\n        isNodeLeaf(node) {\n            return node.leaf === false ? false : !(node.children && node.children.length);\n        },\n        onFilterKeyup(event) {\n            if (event.code === 'Enter' || event.code === 'NumpadEnter') {\n                event.preventDefault();\n            }\n\n            this.$emit('filter', { originalEvent: event, value: event.target.value });\n        },\n        findFilteredNodes(node, paramsWithoutNode) {\n            if (node) {\n                let matched = false;\n\n                if (node.children) {\n                    let childNodes = [...node.children];\n\n                    node.children = [];\n\n                    for (let childNode of childNodes) {\n                        let copyChildNode = { ...childNode };\n\n                        if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n                            matched = true;\n                            node.children.push(copyChildNode);\n                        }\n                    }\n                }\n\n                if (matched) {\n                    return true;\n                }\n            }\n        },\n        isFilterMatched(node, { searchFields, filterText, strict }) {\n            let matched = false;\n\n            for (let field of searchFields) {\n                let fieldValue = String(resolveFieldData(node, field)).toLocaleLowerCase(this.filterLocale);\n\n                if (fieldValue.indexOf(filterText) > -1) {\n                    matched = true;\n                }\n            }\n\n            if (!matched || (strict && !this.isNodeLeaf(node))) {\n                matched = this.findFilteredNodes(node, { searchFields, filterText, strict }) || matched;\n            }\n\n            return matched;\n        }\n    },\n    computed: {\n        filteredValue() {\n            let filteredNodes = [];\n            const searchFields = isFunction(this.filterBy) ? [this.filterBy] : this.filterBy.split(',');\n            const filterText = this.filterValue.trim().toLocaleLowerCase(this.filterLocale);\n            const strict = this.filterMode === 'strict';\n\n            for (let node of this.value) {\n                let _node = { ...node };\n                let paramsWithoutNode = { searchFields, filterText, strict };\n\n                if (\n                    (strict && (this.findFilteredNodes(_node, paramsWithoutNode) || this.isFilterMatched(_node, paramsWithoutNode))) ||\n                    (!strict && (this.isFilterMatched(_node, paramsWithoutNode) || this.findFilteredNodes(_node, paramsWithoutNode)))\n                ) {\n                    filteredNodes.push(_node);\n                }\n            }\n\n            return filteredNodes;\n        },\n        valueToRender() {\n            if (this.filterValue && this.filterValue.trim().length > 0) return this.filteredValue;\n            else return this.value;\n        },\n        containerDataP() {\n            return cn({\n                loading: this.loading,\n                scrollable: this.scrollHeight === 'flex'\n            });\n        },\n        wrapperDataP() {\n            return cn({\n                scrollable: this.scrollHeight === 'flex'\n            });\n        }\n    },\n    components: {\n        TreeNode,\n        InputText,\n        InputIcon,\n        IconField,\n        SearchIcon,\n        SpinnerIcon\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "value", "type", "expandedKeys", "<PERSON><PERSON><PERSON><PERSON>", "selectionMode", "String", "metaKeySelection", "Boolean", "loading", "loadingIcon", "undefined", "loadingMode", "filter", "filterBy", "Function", "filterMode", "filterPlaceholder", "filterLocale", "highlightOnSelect", "scrollHeight", "level", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "style", "TreeStyle", "provide", "$pcTree", "$parentInstance", "hostName", "emits", "node", "templates", "index", "nodeTouched", "toggleClicked", "mounted", "setAllNodesTabIndexes", "methods", "toggle", "$emit", "label", "onChildNodeToggle", "getPTOptions", "key", "ptm", "context", "expanded", "selected", "checked", "partialChecked", "leaf", "onClick", "event", "getAttribute", "target", "parentElement", "isCheckboxSelectionMode", "selectable", "toggleCheckbox", "originalEvent", "onChildNodeClick", "onTouchEnd", "onKeyDown", "isSameNode", "code", "onTabKey", "onArrowDown", "onArrowUp", "onArrowRight", "onArrowLeft", "onEnterKey", "nodeElement", "closest", "listElement", "children", "focusRowChange", "nextElement<PERSON><PERSON>ling", "nextSiblingAncestor", "findNextSiblingOfAncestor", "preventDefault", "previousElementSibling", "findLastVisibleDescendant", "parentNodeElement", "getParentNodeElement", "_this", "currentTarget", "tabIndex", "$nextTick", "toggler<PERSON><PERSON>", "findSingle", "click", "findBeforeClickableNode", "setTabIndexForSelectionMode", "nodes", "find", "$refs", "currentNode", "hasSelectedNode", "_toConsumableArray", "some", "for<PERSON>ach", "selectedNodes", "elements", "every", "element", "firstFocusableRow", "currentFocusedRow", "lastVisibleDescendant", "focusNode", "parentListElement", "prevNodeButton", "visibility", "_selectionKeys", "_objectSpread", "_check", "propagateDown", "check", "length", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "child", "err", "e", "f", "propagateUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>ount", "childPartialSelected", "_iterator2", "_step2", "onChildCheckboxChange", "childrenListElement", "lastChildElement", "focus", "computed", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "checkboxMode", "ariaChe<PERSON>", "ariaSelected", "components", "Checkbox", "ChevronDownIcon", "ChevronRightIcon", "CheckIcon", "MinusIcon", "SpinnerIcon", "directives", "ripple", "<PERSON><PERSON><PERSON>", "_openBlock", "_createElementBlock", "_mergeProps", "ref", "_ctx", "cx", "role", "$options", "$props", "tabindex", "onKeydown", "apply", "arguments", "_createElementVNode", "onTouchend", "_withDirectives", "_Fragment", "_createBlock", "_resolveDynamicComponent", "_component_SpinnerIcon", "spin", "expandedIcon", "collapsedIcon", "_component_Checkbox", "defaultValue", "binary", "indeterminate", "unstyled", "pt", "icon", "_withCtx", "slotProps", "_normalizeClass", "_createTextVNode", "_toDisplayString", "_renderList", "childNode", "_component_TreeNode", "onNodeToggle", "onNodeClick", "onCheckboxChange", "BaseTree", "inheritAttrs", "data", "d_expandedKeys", "filterValue", "watch", "newValue", "metaSelection", "handleSelectionWithMetaKey", "handleSelectionWithoutMetaKey", "metaKey", "ctrl<PERSON>ey", "isNodeSelected", "isSingleSelectionMode", "isMultipleSelectionMode", "isChecked", "isNodeLeaf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "findFilteredNodes", "paramsWithoutNode", "matched", "childNodes", "copyChildNode", "isFilterMatched", "push", "_ref", "searchFields", "filterText", "strict", "field", "fieldValue", "resolveFieldData", "toLocaleLowerCase", "indexOf", "filteredValue", "filteredNodes", "isFunction", "split", "trim", "_iterator3", "_step3", "_node", "valueToRender", "containerDataP", "cn", "scrollable", "wrapperDataP", "TreeNode", "InputText", "InputIcon", "IconField", "SearchIcon", "ptmi", "_renderSlot", "$slots", "_component_IconField", "_createVNode", "_component_InputText", "$data", "$event", "autocomplete", "placeholder", "onKeyup", "_component_InputIcon", "filtericon", "_component_SearchIcon"], "mappings": ";;;;;;;;;;;;;;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,UAAU;AAChB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDC,IAAAA,YAAY,EAAE;AACVD,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDE,IAAAA,aAAa,EAAE;AACXF,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDG,IAAAA,aAAa,EAAE;AACXH,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,gBAAgB,EAAE;AACdL,MAAAA,IAAI,EAAEM,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,OAAO,EAAE;AACLP,MAAAA,IAAI,EAAEM,OAAO;MACb,SAAS,EAAA;KACZ;AACDE,IAAAA,WAAW,EAAE;AACTR,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAASK,EAAAA;KACZ;AACDC,IAAAA,WAAW,EAAE;AACTV,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;KACZ;AACDO,IAAAA,MAAM,EAAE;AACJX,MAAAA,IAAI,EAAEM,OAAO;MACb,SAAS,EAAA;KACZ;AACDM,IAAAA,QAAQ,EAAE;AACNZ,MAAAA,IAAI,EAAE,CAACI,MAAM,EAAES,QAAQ,CAAC;MACxB,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRd,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;KACZ;AACDW,IAAAA,iBAAiB,EAAE;AACff,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;KACZ;AACDY,IAAAA,YAAY,EAAE;AACVhB,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAASK,EAAAA;KACZ;AACDQ,IAAAA,iBAAiB,EAAE;AACfjB,MAAAA,IAAI,EAAEM,OAAO;MACb,SAAS,EAAA;KACZ;AACDY,IAAAA,YAAY,EAAE;AACVlB,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;KACZ;AACDe,IAAAA,KAAK,EAAE;AACHnB,MAAAA,IAAI,EAAEoB,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,cAAc,EAAE;AACZrB,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;KACZ;AACDkB,IAAAA,SAAS,EAAE;AACPtB,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDmB,EAAAA,KAAK,EAAEC,SAAS;EAChBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;;;;;;;;;;ACJD,eAAe;AACX/B,EAAAA,IAAI,EAAE,UAAU;AAChBgC,EAAAA,QAAQ,EAAE,MAAM;AAChB,EAAA,SAAA,EAAS/B,aAAa;AACtBgC,EAAAA,KAAK,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,iBAAiB,CAAC;AACvD/B,EAAAA,KAAK,EAAE;AACHgC,IAAAA,IAAI,EAAE;AACF9B,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDC,IAAAA,YAAY,EAAE;AACVD,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDU,IAAAA,WAAW,EAAE;AACTV,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;KACZ;AACDF,IAAAA,aAAa,EAAE;AACXF,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDG,IAAAA,aAAa,EAAE;AACXH,MAAAA,IAAI,EAAEI,MAAM;MACZ,SAAS,EAAA;KACZ;AACD2B,IAAAA,SAAS,EAAE;AACP/B,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDmB,IAAAA,KAAK,EAAE;AACHnB,MAAAA,IAAI,EAAEoB,MAAM;MACZ,SAAS,EAAA;KACZ;AACDY,IAAAA,KAAK,EAAE;GACV;AACDC,EAAAA,WAAW,EAAE,KAAK;AAClBC,EAAAA,aAAa,EAAE,KAAK;EACpBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACC,qBAAqB,EAAE;GAC/B;AACDC,EAAAA,OAAO,EAAE;IACLC,MAAM,EAAA,SAANA,MAAMA,GAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,EAAE,IAAI,CAACT,IAAI,CAAC;MACpC,IAAI,CAACI,gBAAgB,IAAI;KAC5B;AACDM,IAAAA,KAAK,EAALA,SAAAA,KAAKA,CAACV,IAAI,EAAE;AACR,MAAA,OAAO,OAAOA,IAAI,CAACU,KAAM,KAAI,UAAW,GAAEV,IAAI,CAACU,KAAK,EAAC,GAAIV,IAAI,CAACU,KAAK;KACtE;AACDC,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAACX,IAAI,EAAE;AACpB,MAAA,IAAI,CAACS,KAAK,CAAC,aAAa,EAAET,IAAI,CAAC;KAClC;AACDY,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACC,GAAG,EAAE;AACd,MAAA,OAAO,IAAI,CAACC,GAAG,CAACD,GAAG,EAAE;AACjBE,QAAAA,OAAO,EAAE;UACLf,IAAI,EAAE,IAAI,CAACA,IAAI;UACfE,KAAK,EAAE,IAAI,CAACA,KAAK;UACjBc,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBC,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBC,cAAc,EAAE,IAAI,CAACA,cAAc;UACnCC,IAAI,EAAE,IAAI,CAACA;AACf;AACJ,OAAC,CAAC;KACL;AACDC,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,KAAK,EAAE;MACX,IAAI,IAAI,CAAClB,aAAc,IAAGmB,YAAY,CAACD,KAAK,CAACE,MAAM,EAAE,sCAAsC,KAAKD,YAAY,CAACD,KAAK,CAACE,MAAM,CAACC,aAAa,EAAE,sCAAsC,CAAC,EAAE;QAC9K,IAAI,CAACrB,aAAY,GAAI,KAAK;AAE1B,QAAA;AACJ;AAEA,MAAA,IAAI,IAAI,CAACsB,uBAAuB,EAAE,EAAE;AAChC,QAAA,IAAI,IAAI,CAAC1B,IAAI,CAAC2B,UAAS,IAAK,KAAK,EAAE;UAC/B,IAAI,CAACC,cAAc,EAAE;AACzB;AACJ,OAAE,MAAK;AACH,QAAA,IAAI,CAACnB,KAAK,CAAC,YAAY,EAAE;AACrBoB,UAAAA,aAAa,EAAEP,KAAK;UACpBnB,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BH,IAAI,EAAE,IAAI,CAACA;AACf,SAAC,CAAC;AACN;MAEA,IAAI,CAACG,WAAY,GAAE,KAAK;KAC3B;AACD2B,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAACR,KAAK,EAAE;AACpB,MAAA,IAAI,CAACb,KAAK,CAAC,YAAY,EAAEa,KAAK,CAAC;KAClC;IACDS,UAAU,EAAA,SAAVA,UAAUA,GAAG;MACT,IAAI,CAAC5B,WAAU,GAAI,IAAI;KAC1B;AACD6B,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACV,KAAK,EAAE;AACb,MAAA,IAAI,CAAC,IAAI,CAACW,UAAU,CAACX,KAAK,CAAC,EAAE;MAE7B,QAAQA,KAAK,CAACY,IAAI;AACd,QAAA,KAAK,KAAK;AACN,UAAA,IAAI,CAACC,QAAQ,CAACb,KAAK,CAAC;AAEpB,UAAA;AAEJ,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACc,WAAW,CAACd,KAAK,CAAC;AAEvB,UAAA;AAEJ,QAAA,KAAK,SAAS;AACV,UAAA,IAAI,CAACe,SAAS,CAACf,KAAK,CAAC;AAErB,UAAA;AAEJ,QAAA,KAAK,YAAY;AACb,UAAA,IAAI,CAACgB,YAAY,CAAChB,KAAK,CAAC;AAExB,UAAA;AAEJ,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACiB,WAAW,CAACjB,KAAK,CAAC;AAEvB,UAAA;AAEJ,QAAA,KAAK,OAAO;AACZ,QAAA,KAAK,aAAa;AAClB,QAAA,KAAK,OAAO;AACR,UAAA,IAAI,CAACkB,UAAU,CAAClB,KAAK,CAAC;AAEtB,UAAA;AAIR;KACH;AACDc,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACd,KAAK,EAAE;MACf,IAAMmB,WAAU,GAAInB,KAAK,CAACE,MAAM,CAACD,YAAY,CAAC,iBAAiB,CAAA,KAAM,kBAAiB,GAAID,KAAK,CAACE,MAAM,CAACkB,OAAO,CAAC,mBAAmB,CAAA,GAAIpB,KAAK,CAACE,MAAM;AAClJ,MAAA,IAAMmB,WAAY,GAAEF,WAAW,CAACG,QAAQ,CAAC,CAAC,CAAC;AAE3C,MAAA,IAAID,WAAW,EAAE;QACb,IAAI,CAACE,cAAc,CAACJ,WAAW,EAAEE,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7D,OAAE,MAAK;QACH,IAAIH,WAAW,CAACK,kBAAkB,EAAE;UAChC,IAAI,CAACD,cAAc,CAACJ,WAAW,EAAEA,WAAW,CAACK,kBAAkB,CAAC;AACpE,SAAE,MAAK;AACH,UAAA,IAAIC,mBAAkB,GAAI,IAAI,CAACC,yBAAyB,CAACP,WAAW,CAAC;AAErE,UAAA,IAAIM,mBAAmB,EAAE;AACrB,YAAA,IAAI,CAACF,cAAc,CAACJ,WAAW,EAAEM,mBAAmB,CAAC;AACzD;AACJ;AACJ;MAEAzB,KAAK,CAAC2B,cAAc,EAAE;KACzB;AACDZ,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACf,KAAK,EAAE;AACb,MAAA,IAAMmB,WAAU,GAAInB,KAAK,CAACE,MAAM;MAEhC,IAAIiB,WAAW,CAACS,sBAAsB,EAAE;AACpC,QAAA,IAAI,CAACL,cAAc,CAACJ,WAAW,EAAEA,WAAW,CAACS,sBAAsB,EAAE,IAAI,CAACC,yBAAyB,CAACV,WAAW,CAACS,sBAAsB,CAAC,CAAC;AAC5I,OAAE,MAAK;AACH,QAAA,IAAIE,iBAAkB,GAAE,IAAI,CAACC,oBAAoB,CAACZ,WAAW,CAAC;AAE9D,QAAA,IAAIW,iBAAiB,EAAE;AACnB,UAAA,IAAI,CAACP,cAAc,CAACJ,WAAW,EAAEW,iBAAiB,CAAC;AACvD;AACJ;MAEA9B,KAAK,CAAC2B,cAAc,EAAE;KACzB;AACDX,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAAChB,KAAK,EAAE;AAAA,MAAA,IAAAgC,KAAA,GAAA,IAAA;AAChB,MAAA,IAAI,IAAI,CAAClC,IAAG,IAAK,IAAI,CAACJ,QAAQ,EAAE;AAEhCM,MAAAA,KAAK,CAACiC,aAAa,CAACC,QAAS,GAAE,EAAE;MAEjC,IAAI,CAAC/C,KAAK,CAAC,aAAa,EAAE,IAAI,CAACT,IAAI,CAAC;MACpC,IAAI,CAACyD,SAAS,CAAC,YAAM;AACjBH,QAAAA,KAAI,CAAClB,WAAW,CAACd,KAAK,CAAC;AAC3B,OAAC,CAAC;KACL;AACDiB,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACjB,KAAK,EAAE;MACf,IAAMoC,cAAa,GAAIC,UAAU,CAACrC,KAAK,CAACiC,aAAa,EAAE,sCAAsC,CAAC;MAE9F,IAAI,IAAI,CAAClE,KAAM,KAAI,CAAA,IAAK,CAAC,IAAI,CAAC2B,QAAQ,EAAE;AACpC,QAAA,OAAO,KAAK;AAChB;MAEA,IAAI,IAAI,CAACA,QAAO,IAAK,CAAC,IAAI,CAACI,IAAI,EAAE;QAC7BsC,cAAc,CAACE,KAAK,EAAE;AAEtB,QAAA,OAAO,KAAK;AAChB;MAEA,IAAMpC,MAAO,GAAE,IAAI,CAACqC,uBAAuB,CAACvC,KAAK,CAACiC,aAAa,CAAC;AAEhE,MAAA,IAAI/B,MAAM,EAAE;QACR,IAAI,CAACqB,cAAc,CAACvB,KAAK,CAACiC,aAAa,EAAE/B,MAAM,CAAC;AACpD;KACH;AACDgB,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAAClB,KAAK,EAAE;MACd,IAAI,CAACwC,2BAA2B,CAACxC,KAAK,EAAE,IAAI,CAACnB,WAAW,CAAC;AACzD,MAAA,IAAI,CAACkB,OAAO,CAACC,KAAK,CAAC;MAEnBA,KAAK,CAAC2B,cAAc,EAAE;KACzB;IACDd,QAAQ,EAAA,SAARA,QAAQA,GAAG;MACP,IAAI,CAAC7B,qBAAqB,EAAE;KAC/B;IACDA,qBAAqB,EAAA,SAArBA,qBAAqBA,GAAG;AACpB,MAAA,IAAMyD,KAAI,GAAIC,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,WAAW,CAACxB,OAAO,CAAC,kCAAkC,CAAC,EAAE,mBAAmB,CAAC;MAE3G,IAAMyB,eAAgB,GAAEC,oBAAA,CAAIL,KAAK,CAAEM,CAAAA,IAAI,CAAC,UAACrE,IAAI,EAAA;AAAA,QAAA,OAAKA,IAAI,CAACuB,YAAY,CAAC,eAAe,CAAE,KAAI,MAAO,IAAGvB,IAAI,CAACuB,YAAY,CAAC,cAAc,CAAA,KAAM,MAAM;OAAC,CAAA;AAEhJ6C,MAAAA,oBAAA,CAAIL,KAAK,CAAA,CAAEO,OAAO,CAAC,UAACtE,IAAI,EAAK;AACzBA,QAAAA,IAAI,CAACwD,QAAS,GAAE,EAAE;AACtB,OAAC,CAAC;AAEF,MAAA,IAAIW,eAAe,EAAE;QACjB,IAAMI,aAAc,GAAEH,oBAAA,CAAIL,KAAK,CAAElF,CAAAA,MAAM,CAAC,UAACmB,IAAI,EAAA;AAAA,UAAA,OAAKA,IAAI,CAACuB,YAAY,CAAC,eAAe,CAAE,KAAI,MAAO,IAAGvB,IAAI,CAACuB,YAAY,CAAC,cAAc,CAAA,KAAM,MAAM;SAAC,CAAA;AAEhJgD,QAAAA,aAAa,CAAC,CAAC,CAAC,CAACf,QAAS,GAAE,CAAC;AAE7B,QAAA;AACJ;MAEAY,oBAAA,CAAIL,KAAK,CAAE,CAAA,CAAC,CAAC,CAACP,QAAS,GAAE,CAAC;KAC7B;AACDM,IAAAA,2BAA2B,WAA3BA,2BAA2BA,CAACxC,KAAK,EAAEnB,WAAW,EAAE;AAC5C,MAAA,IAAI,IAAI,CAAC9B,aAAc,KAAI,IAAI,EAAE;AAC7B,QAAA,IAAMmG,QAAS,GAAAJ,oBAAA,CAAMJ,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,WAAW,CAACzC,aAAa,EAAE,mBAAmB,CAAC,CAAC;AAErFH,QAAAA,KAAK,CAACiC,aAAa,CAACC,QAAS,GAAErD,WAAU,KAAM,QAAQ,EAAG,GAAE,CAAC;AAE7D,QAAA,IAAIqE,QAAQ,CAACC,KAAK,CAAC,UAACC,OAAO,EAAA;AAAA,UAAA,OAAKA,OAAO,CAAClB,QAAS,KAAI,EAAE;AAAA,SAAA,CAAC,EAAE;AACtDgB,UAAAA,QAAQ,CAAC,CAAC,CAAC,CAAChB,QAAO,GAAI,CAAC;AAC5B;AACJ;KACH;IACDX,cAAc,EAAA,SAAdA,cAAcA,CAAC8B,iBAAiB,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAE;MACxEF,iBAAiB,CAACnB,QAAS,GAAE,IAAI;MACjCoB,iBAAiB,CAACpB,QAAS,GAAE,GAAG;AAEhC,MAAA,IAAI,CAACsB,SAAS,CAACD,yBAAyBD,iBAAiB,CAAC;KAC7D;AACDf,IAAAA,uBAAuB,EAAvBA,SAAAA,uBAAuBA,CAAC7D,IAAI,EAAE;AAC1B,MAAA,IAAM+E,iBAAkB,GAAE/E,IAAI,CAAC0C,OAAO,CAAC,IAAI,CAAC,CAACA,OAAO,CAAC,IAAI,CAAC;AAE1D,MAAA,IAAIqC,iBAAiB,EAAE;AACnB,QAAA,IAAMC,cAAa,GAAIrB,UAAU,CAACoB,iBAAiB,EAAE,QAAQ,CAAC;QAE9D,IAAIC,cAAa,IAAKA,cAAc,CAACvF,KAAK,CAACwF,UAAW,KAAI,QAAQ,EAAE;AAChE,UAAA,OAAOF,iBAAiB;AAC5B;AAEA,QAAA,OAAO,IAAI,CAAClB,uBAAuB,CAAC7D,IAAI,CAACkD,sBAAsB,CAAC;AACpE;AAEA,MAAA,OAAO,IAAI;KACd;IACDtB,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,IAAIsD,cAAa,GAAI,IAAI,CAAC9G,aAAc,GAAA+G,eAAA,CAAA,EAAA,EAAO,IAAI,CAAC/G,aAAY,CAAA,GAAM,EAAE;AACxE,MAAA,IAAMgH,MAAO,GAAE,CAAC,IAAI,CAAClE,OAAO;MAE5B,IAAI,CAACmE,aAAa,CAAC,IAAI,CAACrF,IAAI,EAAEoF,MAAM,EAAEF,cAAc,CAAC;AAErD,MAAA,IAAI,CAACzE,KAAK,CAAC,iBAAiB,EAAE;QAC1BT,IAAI,EAAE,IAAI,CAACA,IAAI;AACfsF,QAAAA,KAAK,EAAEF,MAAM;AACbhH,QAAAA,aAAa,EAAE8G;AACnB,OAAC,CAAC;KACL;IACDG,aAAa,EAAA,SAAbA,aAAaA,CAACrF,IAAI,EAAEsF,KAAK,EAAElH,aAAa,EAAE;AACtC,MAAA,IAAIkH,SAAStF,IAAI,CAAC2B,UAAS,IAAK,KAAK,EAAEvD,aAAa,CAAC4B,IAAI,CAACa,GAAG,CAAE,GAAE;AAAEK,QAAAA,OAAO,EAAE,IAAI;AAAEC,QAAAA,cAAc,EAAE;OAAO,CAAA,KACpG,OAAO/C,aAAa,CAAC4B,IAAI,CAACa,GAAG,CAAC;MAEnC,IAAIb,IAAI,CAAC4C,QAAO,IAAK5C,IAAI,CAAC4C,QAAQ,CAAC2C,MAAM,EAAE;AAAA,QAAA,IAAAC,SAAA,GAAAC,4BAAA,CACrBzF,IAAI,CAAC4C,QAAQ,CAAA;UAAA8C,KAAA;AAAA,QAAA,IAAA;UAA/B,KAAAF,SAAA,CAAAG,CAAA,EAAAD,EAAAA,CAAAA,CAAAA,KAAA,GAAAF,SAAA,CAAAI,CAAA,EAAAC,EAAAA,IAAA,GAAiC;AAAA,YAAA,IAAxBC,KAAI,GAAAJ,KAAA,CAAAzH,KAAA;YACT,IAAI,CAACoH,aAAa,CAACS,KAAK,EAAER,KAAK,EAAElH,aAAa,CAAC;AACnD;AAAA,SAAA,CAAA,OAAA2H,GAAA,EAAA;UAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA,CAAA;AAAA,SAAA,SAAA;AAAAP,UAAAA,SAAA,CAAAS,CAAA,EAAA;AAAA;AACJ;KACH;AACDC,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAAC5E,KAAK,EAAE;AACf,MAAA,IAAIgE,QAAQhE,KAAK,CAACgE,KAAK;AACvB,MAAA,IAAIJ,cAAe,GAAAC,eAAA,KAAO7D,KAAK,CAAClD,cAAe;MAC/C,IAAI+H,iBAAgB,GAAI,CAAC;MACzB,IAAIC,oBAAqB,GAAE,KAAK;MAAA,IAAAC,UAAA,GAAAZ,4BAAA,CAEd,IAAI,CAACzF,IAAI,CAAC4C,QAAQ,CAAA;QAAA0D,MAAA;AAAA,MAAA,IAAA;QAApC,KAAAD,UAAA,CAAAV,CAAA,EAAAW,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAAT,CAAA,EAAAC,EAAAA,IAAA,GAAsC;AAAA,UAAA,IAA7BC,KAAM,GAAAQ,MAAA,CAAArI,KAAA;AACX,UAAA,IAAIiH,cAAc,CAACY,KAAK,CAACjF,GAAG,KAAKqE,cAAc,CAACY,KAAK,CAACjF,GAAG,CAAC,CAACK,OAAO,EAAEiF,iBAAiB,EAAE,CAAA,KAClF,IAAIjB,cAAc,CAACY,KAAK,CAACjF,GAAG,CAAE,IAAGqE,cAAc,CAACY,KAAK,CAACjF,GAAG,CAAC,CAACM,cAAc,EAAEiF,uBAAuB,IAAI;AAC/G;AAAA,OAAA,CAAA,OAAAL,GAAA,EAAA;QAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA,CAAA;AAAA,OAAA,SAAA;AAAAM,QAAAA,UAAA,CAAAJ,CAAA,EAAA;AAAA;MAEA,IAAIX,KAAM,IAAGa,iBAAkB,KAAI,IAAI,CAACnG,IAAI,CAAC4C,QAAQ,CAAC2C,MAAM,EAAE;AAC1DL,QAAAA,cAAc,CAAC,IAAI,CAAClF,IAAI,CAACa,GAAG,CAAA,GAAI;AAAEK,UAAAA,OAAO,EAAE,IAAI;AAAEC,UAAAA,cAAc,EAAE;SAAO;AAC5E,OAAE,MAAK;QACH,IAAI,CAACmE,KAAK,EAAE;AACR,UAAA,OAAOJ,cAAc,CAAC,IAAI,CAAClF,IAAI,CAACa,GAAG,CAAC;AACxC;QAEA,IAAIuF,oBAAqB,IAAID,iBAAkB,GAAE,CAAE,IAAGA,iBAAgB,KAAM,IAAI,CAACnG,IAAI,CAAC4C,QAAQ,CAAC2C,MAAO,EAAEL,cAAc,CAAC,IAAI,CAAClF,IAAI,CAACa,GAAG,CAAE,GAAE;AAAEK,UAAAA,OAAO,EAAE,KAAK;AAAEC,UAAAA,cAAc,EAAE;SAAM,CAAA,KAC3K,OAAO+D,cAAc,CAAC,IAAI,CAAClF,IAAI,CAACa,GAAG,CAAC;AAC7C;AAEA,MAAA,IAAI,CAACJ,KAAK,CAAC,iBAAiB,EAAE;QAC1BT,IAAI,EAAEsB,KAAK,CAACtB,IAAI;QAChBsF,KAAK,EAAEhE,KAAK,CAACgE,KAAK;AAClBlH,QAAAA,aAAa,EAAE8G;AACnB,OAAC,CAAC;KACL;AACDqB,IAAAA,qBAAqB,EAArBA,SAAAA,qBAAqBA,CAACjF,KAAK,EAAE;AACzB,MAAA,IAAI,CAACb,KAAK,CAAC,iBAAiB,EAAEa,KAAK,CAAC;KACvC;AACD0B,IAAAA,yBAAyB,EAAzBA,SAAAA,yBAAyBA,CAACP,WAAW,EAAE;AACnC,MAAA,IAAIW,iBAAkB,GAAE,IAAI,CAACC,oBAAoB,CAACZ,WAAW,CAAC;AAE9D,MAAA,IAAIW,iBAAiB,EAAE;AACnB,QAAA,IAAIA,iBAAiB,CAACN,kBAAkB,EAAE,OAAOM,iBAAiB,CAACN,kBAAkB,CAAA,KAChF,OAAO,IAAI,CAACE,yBAAyB,CAACI,iBAAiB,CAAC;AACjE,OAAE,MAAK;AACH,QAAA,OAAO,IAAI;AACf;KACH;AACDD,IAAAA,yBAAyB,EAAzBA,SAAAA,yBAAyBA,CAACV,WAAW,EAAE;AACnC,MAAA,IAAM+D,mBAAkB,GAAI/D,WAAW,CAACG,QAAQ,CAAC,CAAC,CAAC;AAEnD,MAAA,IAAI4D,mBAAmB,EAAE;AACrB,QAAA,IAAMC,gBAAe,GAAID,mBAAmB,CAAC5D,QAAQ,CAAC4D,mBAAmB,CAAC5D,QAAQ,CAAC2C,MAAO,GAAE,CAAC,CAAC;AAE9F,QAAA,OAAO,IAAI,CAACpC,yBAAyB,CAACsD,gBAAgB,CAAC;AAC3D,OAAE,MAAK;AACH,QAAA,OAAOhE,WAAW;AACtB;KACH;AACDY,IAAAA,oBAAoB,EAApBA,SAAAA,oBAAoBA,CAACZ,WAAW,EAAE;AAC9B,MAAA,IAAMW,iBAAkB,GAAEX,WAAW,CAAChB,aAAa,CAACA,aAAa;MAEjE,OAAOF,YAAY,CAAC6B,iBAAiB,EAAE,MAAM,CAAE,KAAI,UAAS,GAAIA,iBAAgB,GAAI,IAAI;KAC3F;AACD0B,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACJ,OAAO,EAAE;MACfA,OAAO,CAACgC,KAAK,EAAE;KAClB;IACDhF,uBAAuB,EAAA,SAAvBA,uBAAuBA,GAAG;AACtB,MAAA,OAAO,IAAI,CAACrD,aAAc,KAAI,UAAU;KAC3C;AACD4D,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACX,KAAK,EAAE;AACd,MAAA,OAAOA,KAAK,CAACiC,aAAc,KAAIjC,KAAK,CAACiC,aAAa,CAACtB,UAAU,CAACX,KAAK,CAACE,MAAM,KAAKF,KAAK,CAACiC,aAAa,CAACtB,UAAU,CAACX,KAAK,CAACE,MAAM,CAACkB,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAC7J;GACH;AACDiE,EAAAA,QAAQ,EAAE;IACNC,WAAW,EAAA,SAAXA,WAAWA,GAAG;AACV,MAAA,OAAO,IAAI,CAAC5G,IAAI,CAAC4C,QAAO,IAAK,IAAI,CAAC5C,IAAI,CAAC4C,QAAQ,CAAC2C,MAAO,GAAE,CAAC;KAC7D;IACDvE,QAAQ,EAAA,SAARA,QAAQA,GAAG;AACP,MAAA,OAAO,IAAI,CAAC7C,YAAW,IAAK,IAAI,CAACA,YAAY,CAAC,IAAI,CAAC6B,IAAI,CAACa,GAAG,CAAE,KAAI,IAAI;KACxE;IACDO,IAAI,EAAA,SAAJA,IAAIA,GAAG;MACH,OAAO,IAAI,CAACpB,IAAI,CAACoB,IAAG,KAAM,QAAQ,KAAM,GAAE,EAAE,IAAI,CAACpB,IAAI,CAAC4C,QAAS,IAAG,IAAI,CAAC5C,IAAI,CAAC4C,QAAQ,CAAC2C,MAAM,CAAC;KAC/F;IACD5D,UAAU,EAAA,SAAVA,UAAUA,GAAG;AACT,MAAA,OAAO,IAAI,CAAC3B,IAAI,CAAC2B,UAAW,KAAI,KAAI,GAAI,KAAI,GAAI,IAAI,CAACtD,aAAY,IAAK,IAAI;KAC7E;IACD4C,QAAQ,EAAA,SAARA,QAAQA,GAAG;MACP,OAAO,IAAI,CAAC5C,aAAY,IAAK,IAAI,CAACD,aAAc,GAAE,IAAI,CAACA,aAAa,CAAC,IAAI,CAAC4B,IAAI,CAACa,GAAG,MAAM,IAAK,GAAE,KAAK;KACvG;IACDgG,YAAY,EAAA,SAAZA,YAAYA,GAAG;AACX,MAAA,OAAO,IAAI,CAACxI,aAAY,KAAM,UAAS,IAAK,IAAI,CAAC2B,IAAI,CAAC2B,UAAS,KAAM,KAAK;KAC7E;IACDT,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,MAAA,OAAO,IAAI,CAAC9C,aAAY,GAAI,IAAI,CAACA,aAAa,CAAC,IAAI,CAAC4B,IAAI,CAACa,GAAG,CAAE,IAAG,IAAI,CAACzC,aAAa,CAAC,IAAI,CAAC4B,IAAI,CAACa,GAAG,CAAC,CAACK,OAAM,GAAI,KAAK;KACrH;IACDC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,OAAO,IAAI,CAAC/C,aAAY,GAAI,IAAI,CAACA,aAAa,CAAC,IAAI,CAAC4B,IAAI,CAACa,GAAG,KAAK,IAAI,CAACzC,aAAa,CAAC,IAAI,CAAC4B,IAAI,CAACa,GAAG,CAAC,CAACM,cAAe,GAAE,KAAK;KAC5H;IACD2F,WAAW,EAAA,SAAXA,WAAWA,GAAG;AACV,MAAA,OAAO,IAAI,CAACzI,aAAc,KAAI,YAAY,IAAI,CAACA,kBAAkB,UAAW,GAAE,IAAI,CAAC4C,WAAWtC,SAAS;KAC1G;IACDoI,YAAY,EAAA,SAAZA,YAAYA,GAAG;MACX,OAAO,IAAI,CAACF,YAAW,GAAI,IAAI,CAAC3F,UAAUvC,SAAS;AACvD;GACH;AACDqI,EAAAA,UAAU,EAAE;AACRC,IAAAA,QAAQ,EAARA,QAAQ;AACRC,IAAAA,eAAe,EAAfA,eAAe;AACfC,IAAAA,gBAAgB,EAAhBA,gBAAgB;AAChBC,IAAAA,SAAS,EAATA,SAAS;AACTC,IAAAA,SAAS,EAATA,SAAS;AACTC,IAAAA,WAAU,EAAVA;GACH;AACDC,EAAAA,UAAU,EAAE;AACRC,IAAAA,MAAM,EAAEC;AACZ;AACJ,CAAC;;;;;;;;;;ECvdG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAqEI,MArEJC,UAqEI,CAAA;AApEAC,IAAAA,GAAG,EAAC,aAAY;AACf,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;AACVC,IAAAA,IAAI,EAAC,UAAS;IACb,YAAU,EAAEC,QAAK,CAAAvH,KAAA,CAACwH,MAAI,CAAAlI,IAAA,CAAA;IACtB,eAAa,EAAEiI,QAAY,CAAAlB,YAAA;IAC3B,eAAa,EAAEkB,QAAQ,CAAAjH,QAAA;AACvB,IAAA,cAAY,EAAEkH,WAAI,CAACtF,QAAO,GAAIsF,MAAI,CAAAlI,IAAA,CAAC4C,QAAQ,CAAC2C,MAAO,GAAA,CAAA;AACnD,IAAA,eAAa,EAAE2C,MAAM,CAAAhI,KAAA,GAAA,CAAA;IACrB,YAAU,EAAEgI,MAAK,CAAA7I,KAAA;IACjB,cAAY,EAAE4I,QAAW,CAAAnB,WAAA;IACzBqB,QAAQ,EAAED,MAAM,CAAAhI,KAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA;IAChBkI,SAAO;aAAEH,QAAS,CAAAjG,SAAA,IAAAiG,QAAA,CAAAjG,SAAA,CAAAqG,KAAA,CAAAJ,QAAA,EAAAK,SAAA,CAAA;KAAA;KACXL,QAAY,CAAArH,YAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAEpB2H,kBAAA,CAmCK,OAnCLX,UAmCK,CAAA;AAnCC,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,aAAA,CAAA;IAAkB1G,OAAK;aAAE4G,QAAO,CAAA5G,OAAA,IAAA4G,QAAA,CAAA5G,OAAA,CAAAgH,KAAA,CAAAJ,QAAA,EAAAK,SAAA,CAAA;AAAA,KAAA,CAAA;IAAGE,UAAQ;aAAEP,QAAU,CAAAlG,UAAA,IAAAkG,QAAA,CAAAlG,UAAA,CAAAsG,KAAA,CAAAJ,QAAA,EAAAK,SAAA,CAAA;AAAA,KAAA,CAAA;AAAG7I,IAAAA,KAAK,EAAEyI,MAAI,CAAAlI,IAAA,CAACP;KAAewI,QAAY,CAAArH,YAAA,CAAA,aAAA,CAAA,EAAA;IAAkB,iBAAe,EAAEqH,QAAA,CAAApB,eAAeoB,QAAA,CAAA/G,OAAM,GAAI+G,QAAQ,CAAAhH,QAAA;IAAG,mBAAiB,EAAEgH,QAAU,CAAAtG;OAChN8G,cAAA,EAAAf,SAAA,EAAA,EAAAC,kBAAA,CAYQ,UAZRC,UAYQ,CAAA;AAZS1J,IAAAA,IAAI,EAAC,QAAO;AAAG,IAAA,OAAA,EAAO4J,IAAE,CAAAC,EAAA,CAAA,kBAAA,CAAA;IAAuB1G,OAAK;aAAE4G,QAAM,CAAAzH,MAAA,IAAAyH,QAAA,CAAAzH,MAAA,CAAA6H,KAAA,CAAAJ,QAAA,EAAAK,SAAA,CAAA;AAAA,KAAA,CAAA;AAAEH,IAAAA,QAAQ,EAAC,IAAK;IAAC,aAAW,EAAEF,QAAI,CAAA7G;KAAU6G,QAAY,CAAArH,YAAA,CAAA,kBAAA,CAAA,CAAA,EAAA,CACjHsH,MAAI,CAAAlI,IAAA,CAACvB,OAAQ,IAAGyJ,MAAY,CAAAtJ,WAAA,KAAA,MAAA,iBAA5C+I,kBAIU,CAAAe,QAAA,EAAA;AAAA7H,IAAAA,GAAA,EAAA;AAAA,GAAA,EAAA,CAFWqH,MAAA,CAAAjI,SAAS,sBAAsBiI,MAAS,CAAAjI,SAAA,CAAA,iBAAA,CAAA,iBAAzD0I,WAAiN,CAAAC,uBAAA,CAA9HV,MAAS,CAAAjI,SAAA,CAAA,gBAAA,CAAA,IAAsBiI,MAAS,CAAAjI,SAAA,CAAA,iBAAA,CAAA,CAAA,EAAA;;IAAsBD,IAAI,EAAEkI,MAAI,CAAAlI,IAAA;IAAGgB,QAAQ,EAAEiH,QAAQ,CAAAjH,QAAA;AAAG,IAAA,OAAA,iBAAO8G,IAAE,CAAAC,EAAA,CAAA,gBAAA,CAAA;iDAC5LL,SAAA,EAAA,EAAAiB,WAAA,CAAgGE,wBAAhGjB,UAAgG,CAAA;;AAA5EkB,IAAAA,IAAG,EAAH,EAAG;AAAG,IAAA,OAAA,EAAOhB,IAAE,CAAAC,EAAA,CAAA,gBAAA;KAA4BE,QAAY,CAAArH,YAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA,wBAE/E+G,kBAKU,CAAAe,QAAA,EAAA;AAAA7H,IAAAA,GAAA,EAAA;AAAA,GAAA,EAAA,CAHWqH,MAAA,CAAAjI,SAAS,sBAAsBiI,MAAS,CAAAjI,SAAA,CAAA,aAAA,CAAA,iBAAzD0I,WAAyM,CAAAC,uBAAA,CAA1HV,MAAS,CAAAjI,SAAA,CAAA,gBAAA,CAAA,IAAsBiI,MAAS,CAAAjI,SAAA,CAAA,aAAA,CAAA,CAAA,EAAA;;IAAkBD,IAAI,EAAEkI,MAAI,CAAAlI,IAAA;IAAGgB,QAAQ,EAAEiH,QAAQ,CAAAjH,QAAA;AAAG,IAAA,OAAA,iBAAO8G,IAAE,CAAAC,EAAA,CAAA,gBAAA,CAAA;gDAC9JE,QAAQ,CAAAjH,QAAA,IAA9B0G,SAAA,EAAA,EAAAiB,WAAA,CAA4JC,uBAAvH,CAAAV,MAAA,CAAAlI,IAAI,CAAC+I,4CAA1CnB,UAA4J,CAAA;;AAAtE,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,gBAAA;KAA4BE,QAAY,CAAArH,YAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,KACvI8G,SAAA,EAAA,EAAAiB,WAAA,CAAgJC,uBAAzH,CAAAV,MAAA,CAAAlI,IAAI,CAACgJ,aAAY,iCAAxCpB,UAAgJ,CAAA;;AAAtE,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,gBAAA;KAA4BE,QAAY,CAAArH,YAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA,sDAIzHqH,QAAY,CAAApB,YAAA,iBADtB8B,WAcU,CAAAM,mBAAA,EAAA;;IAZLC,YAAY,EAAEjB,QAAO,CAAA/G,OAAA;AACrBiI,IAAAA,MAAM,EAAE,IAAI;IACZC,aAAa,EAAEnB,QAAc,CAAA9G,cAAA;IAC7B,wBAAO2G,IAAE,CAAAC,EAAA,CAAA,cAAA,CAAA,CAAA;IACTI,QAAQ,EAAE,EAAE;IACZkB,QAAQ,EAAEvB,IAAQ,CAAAuB,QAAA;AAClBC,IAAAA,EAAE,EAAErB,QAAY,CAAArH,YAAA,CAAA,gBAAA,CAAA;IAChB,uBAAqB,EAAEqH,QAAc,CAAA9G;;AAE3BoI,IAAAA,IAAI,EAAAC,OAAA,CACX,UAAoKC,SAD9I,EAAA;MAAA,OAAA,CACLvB,MAAS,CAAAjI,SAAA,CAAA,cAAA,CAAA,IAA1ByH,SAAA,EAAA,EAAAiB,WAAA,CAAoKC,wBAAnHV,MAAS,CAAAjI,SAAA,CAAA,cAAA,CAAA,CAAA,EAAA;;QAAmBiB,OAAO,EAAEuI,SAAS,CAACvI,OAAO;QAAGC,cAAc,EAAE8G,QAAc,CAAA9G,cAAA;QAAG,OAAKuI,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;gIAGxJvB,MAAS,CAAAjI,SAAA,CAAA,UAAA,CAAA,iBAA1B0I,WAAwJ,CAAAC,uBAAA,CAA3GV,MAAS,CAAAjI,SAAA,CAAA,UAAA,CAAA,CAAA,EAAtD2H,UAAwJ,CAAA;;IAAnF5H,IAAI,EAAEkI,MAAI,CAAAlI,IAAA;AAAG,IAAA,OAAA,GAAQ8H,IAAE,CAAAC,EAAA,CAAA,UAAA,CAAA;KAAuBE,QAAY,CAAArH,YAAA,CAAA,UAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA,KAC/H8G,SAAA,EAAA,EAAAC,kBAAA,CAA0F,QAA1FC,UAA0F,CAAA;;AAA5E,IAAA,OAAA,EAAQ,CAAAE,IAAA,CAAAC,EAAE,CAAc,UAAA,CAAA,EAAAG,MAAA,CAAAlI,IAAI,CAACuJ,IAAI;KAAWtB,QAAY,CAAArH,YAAA,CAAA,UAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,CAAA,EACtE2H,kBAAA,CAGM,QAHNX,UAGM,CAAA;AAHC,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,WAAA;KAAuBE,QAAY,CAAArH,YAAA,CAAA,WAAA,CAAA,EAAA;IAAgBwH,SAAO,0CAAR,YAAa,EAAA,EAAA,CAAA,MAAA,CAAA,CAAA;OAC1DF,MAAA,CAAAjI,SAAS,CAACiI,MAAA,CAAAlI,IAAI,CAAC9B,IAAI,KAAKgK,MAAS,CAAAjI,SAAA,CAAA,SAAA,CAAA,IAAlDyH,SAAA,EAAA,EAAAiB,WAAA,CAAmMC,wBAA/HV,MAAS,CAAAjI,SAAA,CAACiI,WAAI,CAAChK,IAAI,KAAKgK,MAAS,CAAAjI,SAAA,CAAA,SAAA,CAAA,CAAA,EAAA;;IAAcD,IAAI,EAAEkI,MAAI,CAAAlI,IAAA;IAAGgB,QAAQ,EAAEiH,QAAQ,CAAAjH,QAAA;IAAGC,QAAQ,EAAEgH,QAAA,CAAApB,eAAeoB,QAAA,CAAA/G,OAAM,GAAI+G,QAAQ,CAAAhH;iEAChM0G,kBAA4C,CAAAe,QAAA,EAAA;AAAA7H,IAAAA,GAAA,EAAA;GAAA,EAAA,CAAxB8I,eAAA,CAAAC,eAAA,CAAA3B,QAAA,CAAAvH,KAAK,CAACwH,MAAI,CAAAlI,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,mCAG5BiI,QAAA,CAAArB,WAAU,IAAKqB,QAAQ,CAAAjH,QAAA,IAAjC0G,SAAA,EAAA,EAAAC,kBAAA,CAiBI,MAjBJC,UAiBI,CAAA;;AAjBgC,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,cAAA,CAAA;AAAkBC,IAAAA,IAAI,EAAC;KAAgBF,IAAG,CAAAhH,GAAA,CAAA,cAAA,CAAA,CAAA,EAAA,EACnF4G,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAeCe,QAduB,EAAA,IAAA,EAAAmB,UAAA,CAAA3B,MAAA,CAAAlI,IAAI,CAAC4C,QAAQ,YAA1BkH,SAAU,EAAA;wBADrBnB,WAeC,CAAAoB,mBAAA,EAAA;MAbIlJ,GAAG,EAAEiJ,SAAS,CAACjJ,GAAG;AAClBb,MAAAA,IAAI,EAAE8J,SAAS;MACf7J,SAAS,EAAEiI,MAAS,CAAAjI,SAAA;AACpBZ,MAAAA,KAAK,EAAE6I,MAAI,CAAA7I,KAAA,GAAA,CAAA;MACXT,WAAW,EAAEsJ,MAAW,CAAAtJ,WAAA;MACxBT,YAAY,EAAE+J,MAAY,CAAA/J,YAAA;MAC1B6L,YAAW,EAAE/B,QAAiB,CAAAtH,iBAAA;MAC9BsJ,WAAU,EAAEhC,QAAgB,CAAAnG,gBAAA;MAC5BzD,aAAa,EAAE6J,MAAa,CAAA7J,aAAA;MAC5BD,aAAa,EAAE8J,MAAa,CAAA9J,aAAA;MAC5B8L,gBAAe,EAAEjC,QAAW,CAAA/B,WAAA;MAC5BmD,QAAQ,EAAEvB,IAAQ,CAAAuB,QAAA;MAClBC,EAAE,EAAExB,IAAE,CAAAwB;;;;;;;;;;;;;;;;;;;;ACXvB,aAAe;AACXxL,EAAAA,IAAI,EAAE,MAAM;AACZ,EAAA,SAAA,EAASqM,QAAQ;AACjBC,EAAAA,YAAY,EAAE,KAAK;AACnBrK,EAAAA,KAAK,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,CAAC;EAChIsK,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHC,MAAAA,cAAc,EAAE,IAAI,CAACnM,YAAW,IAAK,EAAE;AACvCoM,MAAAA,WAAW,EAAE;KAChB;GACJ;AACDC,EAAAA,KAAK,EAAE;AACHrM,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACsM,QAAQ,EAAE;MACnB,IAAI,CAACH,cAAa,GAAIG,QAAQ;AAClC;GACH;AACDlK,EAAAA,OAAO,EAAE;AACLyJ,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAAChK,IAAI,EAAE;AACf,MAAA,IAAMa,GAAE,GAAIb,IAAI,CAACa,GAAG;AAEpB,MAAA,IAAI,IAAI,CAACyJ,cAAc,CAACzJ,GAAG,CAAC,EAAE;AAC1B,QAAA,OAAO,IAAI,CAACyJ,cAAc,CAACzJ,GAAG,CAAC;AAC/B,QAAA,IAAI,CAACJ,KAAK,CAAC,eAAe,EAAET,IAAI,CAAC;AACrC,OAAE,MAAK;AACH,QAAA,IAAI,CAACsK,cAAc,CAACzJ,GAAG,CAAA,GAAI,IAAI;AAC/B,QAAA,IAAI,CAACJ,KAAK,CAAC,aAAa,EAAET,IAAI,CAAC;AACnC;MAEA,IAAI,CAACsK,cAAa,GAAAnF,eAAA,KAAS,IAAI,CAACmF,eAAgB;MAChD,IAAI,CAAC7J,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC6J,cAAc,CAAC;KACzD;AACDL,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAAC3I,KAAK,EAAE;AACf,MAAA,IAAI,IAAI,CAACjD,aAAY,IAAK,IAAG,IAAKiD,KAAK,CAACtB,IAAI,CAAC2B,UAAS,KAAM,KAAK,EAAE;QAC/D,IAAM+I,aAAc,GAAEpJ,KAAK,CAACnB,WAAU,GAAI,KAAI,GAAI,IAAI,CAAC5B,gBAAgB;AACvE,QAAA,IAAM2G,cAAe,GAAEwF,aAAY,GAAI,IAAI,CAACC,0BAA0B,CAACrJ,KAAK,IAAI,IAAI,CAACsJ,6BAA6B,CAACtJ,KAAK,CAAC;AAEzH,QAAA,IAAI,CAACb,KAAK,CAAC,sBAAsB,EAAEyE,cAAc,CAAC;AACtD;KACH;AACDgF,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAC5I,KAAK,EAAE;MACpB,IAAI,CAACb,KAAK,CAAC,sBAAsB,EAAEa,KAAK,CAAClD,aAAa,CAAC;MAEvD,IAAIkD,KAAK,CAACgE,KAAK,EAAE,IAAI,CAAC7E,KAAK,CAAC,aAAa,EAAEa,KAAK,CAACtB,IAAI,CAAC,CAAA,KACjD,IAAI,CAACS,KAAK,CAAC,eAAe,EAAEa,KAAK,CAACtB,IAAI,CAAC;KAC/C;AACD2K,IAAAA,0BAA0B,EAA1BA,SAAAA,0BAA0BA,CAACrJ,KAAK,EAAE;AAC9B,MAAA,IAAMO,aAAc,GAAEP,KAAK,CAACO,aAAa;AACzC,MAAA,IAAM7B,IAAK,GAAEsB,KAAK,CAACtB,IAAI;MACvB,IAAM6K,OAAQ,GAAEhJ,aAAa,CAACgJ,OAAQ,IAAGhJ,aAAa,CAACiJ,OAAO;AAC9D,MAAA,IAAM7J,QAAS,GAAE,IAAI,CAAC8J,cAAc,CAAC/K,IAAI,CAAC;AAC1C,MAAA,IAAIkF,cAAc;MAElB,IAAIjE,YAAY4J,OAAO,EAAE;AACrB,QAAA,IAAI,IAAI,CAACG,qBAAqB,EAAE,EAAE;UAC9B9F,cAAe,GAAE,EAAE;AACvB,SAAE,MAAK;AACHA,UAAAA,cAAa,GAAAC,eAAA,CAAA,EAAA,EAAS,IAAI,CAAC/G,cAAe;AAC1C,UAAA,OAAO8G,cAAc,CAAClF,IAAI,CAACa,GAAG,CAAC;AACnC;AAEA,QAAA,IAAI,CAACJ,KAAK,CAAC,eAAe,EAAET,IAAI,CAAC;AACrC,OAAE,MAAK;AACH,QAAA,IAAI,IAAI,CAACgL,qBAAqB,EAAE,EAAE;UAC9B9F,cAAe,GAAE,EAAE;AACvB,SAAA,MAAO,IAAI,IAAI,CAAC+F,uBAAuB,EAAE,EAAE;AACvC/F,UAAAA,cAAa,GAAI,CAAC2F,OAAM,GAAI,EAAC,GAAI,IAAI,CAACzM,aAAY,GAAA+G,eAAA,CAAS,EAAA,EAAA,IAAI,CAAC/G,aAAY,CAAA,GAAM,EAAE;AACxF;AAEA8G,QAAAA,cAAc,CAAClF,IAAI,CAACa,GAAG,CAAA,GAAI,IAAI;AAC/B,QAAA,IAAI,CAACJ,KAAK,CAAC,aAAa,EAAET,IAAI,CAAC;AACnC;AAEA,MAAA,OAAOkF,cAAc;KACxB;AACD0F,IAAAA,6BAA6B,EAA7BA,SAAAA,6BAA6BA,CAACtJ,KAAK,EAAE;AACjC,MAAA,IAAMtB,IAAK,GAAEsB,KAAK,CAACtB,IAAI;AACvB,MAAA,IAAMiB,QAAS,GAAE,IAAI,CAAC8J,cAAc,CAAC/K,IAAI,CAAC;AAC1C,MAAA,IAAIkF,cAAc;AAElB,MAAA,IAAI,IAAI,CAAC8F,qBAAqB,EAAE,EAAE;AAC9B,QAAA,IAAI/J,QAAQ,EAAE;UACViE,cAAe,GAAE,EAAE;AACnB,UAAA,IAAI,CAACzE,KAAK,CAAC,eAAe,EAAET,IAAI,CAAC;AACrC,SAAE,MAAK;UACHkF,cAAe,GAAE,EAAE;AACnBA,UAAAA,cAAc,CAAClF,IAAI,CAACa,GAAG,CAAA,GAAI,IAAI;AAC/B,UAAA,IAAI,CAACJ,KAAK,CAAC,aAAa,EAAET,IAAI,CAAC;AACnC;AACJ,OAAE,MAAK;AACH,QAAA,IAAIiB,QAAQ,EAAE;AACViE,UAAAA,cAAa,GAAAC,eAAA,CAAA,EAAA,EAAS,IAAI,CAAC/G,cAAe;AAC1C,UAAA,OAAO8G,cAAc,CAAClF,IAAI,CAACa,GAAG,CAAC;AAE/B,UAAA,IAAI,CAACJ,KAAK,CAAC,eAAe,EAAET,IAAI,CAAC;AACrC,SAAE,MAAK;AACHkF,UAAAA,cAAa,GAAI,IAAI,CAAC9G,aAAY,GAAA+G,eAAA,CAAS,EAAA,EAAA,IAAI,CAAC/G,aAAc,CAAI,GAAA,EAAE;AACpE8G,UAAAA,cAAc,CAAClF,IAAI,CAACa,GAAG,CAAA,GAAI,IAAI;AAE/B,UAAA,IAAI,CAACJ,KAAK,CAAC,aAAa,EAAET,IAAI,CAAC;AACnC;AACJ;AAEA,MAAA,OAAOkF,cAAc;KACxB;IACD8F,qBAAqB,EAAA,SAArBA,qBAAqBA,GAAG;AACpB,MAAA,OAAO,IAAI,CAAC3M,kBAAkB,QAAQ;KACzC;IACD4M,uBAAuB,EAAA,SAAvBA,uBAAuBA,GAAG;AACtB,MAAA,OAAO,IAAI,CAAC5M,aAAc,KAAI,UAAU;KAC3C;AACD0M,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC/K,IAAI,EAAE;AACjB,MAAA,OAAO,IAAI,CAAC3B,iBAAiB,IAAI,CAACD,aAAc,GAAE,IAAI,CAACA,aAAa,CAAC4B,IAAI,CAACa,GAAG,CAAA,KAAM,IAAK,GAAE,KAAK;KAClG;AACDqK,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAAClL,IAAI,EAAE;MACZ,OAAO,IAAI,CAAC5B,gBAAgB,IAAI,CAACA,aAAa,CAAC4B,IAAI,CAACa,GAAG,CAAE,IAAG,IAAI,CAACzC,aAAa,CAAC4B,IAAI,CAACa,GAAG,CAAC,CAACK,OAAM,GAAI,KAAK;KAC3G;AACDiK,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACnL,IAAI,EAAE;AACb,MAAA,OAAOA,IAAI,CAACoB,IAAK,KAAI,QAAQ,KAAI,GAAI,EAAEpB,IAAI,CAAC4C,QAAS,IAAG5C,IAAI,CAAC4C,QAAQ,CAAC2C,MAAM,CAAC;KAChF;AACD6F,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAAC9J,KAAK,EAAE;MACjB,IAAIA,KAAK,CAACY,SAAS,OAAQ,IAAGZ,KAAK,CAACY,IAAG,KAAM,aAAa,EAAE;QACxDZ,KAAK,CAAC2B,cAAc,EAAE;AAC1B;AAEA,MAAA,IAAI,CAACxC,KAAK,CAAC,QAAQ,EAAE;AAAEoB,QAAAA,aAAa,EAAEP,KAAK;AAAErD,QAAAA,KAAK,EAAEqD,KAAK,CAACE,MAAM,CAACvD;AAAM,OAAC,CAAC;KAC5E;AACDoN,IAAAA,iBAAiB,WAAjBA,iBAAiBA,CAACrL,IAAI,EAAEsL,iBAAiB,EAAE;AACvC,MAAA,IAAItL,IAAI,EAAE;QACN,IAAIuL,OAAM,GAAI,KAAK;QAEnB,IAAIvL,IAAI,CAAC4C,QAAQ,EAAE;AACf,UAAA,IAAI4I,UAAS,GAAApH,kBAAA,CAAQpE,IAAI,CAAC4C,QAAQ,CAAC;UAEnC5C,IAAI,CAAC4C,QAAS,GAAE,EAAE;AAAA,UAAA,IAAA4C,SAAA,GAAAC,0BAAA,CAEI+F,UAAU,CAAA;YAAA9F,KAAA;AAAA,UAAA,IAAA;YAAhC,KAAAF,SAAA,CAAAG,CAAA,EAAAD,EAAAA,CAAAA,CAAAA,KAAA,GAAAF,SAAA,CAAAI,CAAA,EAAAC,EAAAA,IAAA,GAAkC;AAAA,cAAA,IAAzBiE,SAAQ,GAAApE,KAAA,CAAAzH,KAAA;AACb,cAAA,IAAIwN,aAAc,GAAAtG,eAAA,CAAA,EAAA,EAAO2E,UAAW;cAEpC,IAAI,IAAI,CAAC4B,eAAe,CAACD,aAAa,EAAEH,iBAAiB,CAAC,EAAE;AACxDC,gBAAAA,UAAU,IAAI;AACdvL,gBAAAA,IAAI,CAAC4C,QAAQ,CAAC+I,IAAI,CAACF,aAAa,CAAC;AACrC;AACJ;AAAA,WAAA,CAAA,OAAA1F,GAAA,EAAA;YAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA,CAAA;AAAA,WAAA,SAAA;AAAAP,YAAAA,SAAA,CAAAS,CAAA,EAAA;AAAA;AACJ;AAEA,QAAA,IAAIsF,OAAO,EAAE;AACT,UAAA,OAAO,IAAI;AACf;AACJ;KACH;AACDG,IAAAA,eAAe,WAAfA,eAAeA,CAAC1L,IAAI,EAAA4L,IAAA,EAAwC;AAAA,MAAA,IAApCC,YAAY,GAAAD,IAAA,CAAZC,YAAY;QAAEC,UAAU,GAAAF,IAAA,CAAVE,UAAU;QAAEC,MAAO,GAAAH,IAAA,CAAPG,MAAO;MACrD,IAAIR,OAAM,GAAI,KAAK;AAAA,MAAA,IAAAlF,UAAA,GAAAZ,0BAAA,CAEDoG,YAAY,CAAA;QAAAvF,MAAA;AAAA,MAAA,IAAA;QAA9B,KAAAD,UAAA,CAAAV,CAAA,EAAAW,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAAT,CAAA,EAAAC,EAAAA,IAAA,GAAgC;AAAA,UAAA,IAAvBmG,KAAM,GAAA1F,MAAA,CAAArI,KAAA;AACX,UAAA,IAAIgO,aAAa3N,MAAM,CAAC4N,gBAAgB,CAAClM,IAAI,EAAEgM,KAAK,CAAC,CAAC,CAACG,iBAAiB,CAAC,IAAI,CAACjN,YAAY,CAAC;UAE3F,IAAI+M,UAAU,CAACG,OAAO,CAACN,UAAU,CAAA,GAAI,CAAC,CAAC,EAAE;AACrCP,YAAAA,UAAU,IAAI;AAClB;AACJ;AAAA,OAAA,CAAA,OAAAxF,GAAA,EAAA;QAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA,CAAA;AAAA,OAAA,SAAA;AAAAM,QAAAA,UAAA,CAAAJ,CAAA,EAAA;AAAA;AAEA,MAAA,IAAI,CAACsF,OAAM,IAAMQ,MAAK,IAAK,CAAC,IAAI,CAACZ,UAAU,CAACnL,IAAI,CAAE,EAAE;AAChDuL,QAAAA,UAAU,IAAI,CAACF,iBAAiB,CAACrL,IAAI,EAAE;AAAE6L,UAAAA,YAAY,EAAZA,YAAY;AAAEC,UAAAA,UAAU,EAAVA,UAAU;AAAEC,UAAAA,QAAAA;SAAQ,CAAA,IAAKR,OAAO;AAC3F;AAEA,MAAA,OAAOA,OAAO;AAClB;GACH;AACD5E,EAAAA,QAAQ,EAAE;IACN0F,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,IAAIC,aAAc,GAAE,EAAE;MACtB,IAAMT,YAAW,GAAIU,UAAU,CAAC,IAAI,CAACzN,QAAQ,CAAE,GAAE,CAAC,IAAI,CAACA,QAAQ,CAAE,GAAE,IAAI,CAACA,QAAQ,CAAC0N,KAAK,CAAC,GAAG,CAAC;AAC3F,MAAA,IAAMV,UAAS,GAAI,IAAI,CAACvB,WAAW,CAACkC,IAAI,EAAE,CAACN,iBAAiB,CAAC,IAAI,CAACjN,YAAY,CAAC;AAC/E,MAAA,IAAM6M,MAAK,GAAI,IAAI,CAAC/M,eAAe,QAAQ;AAAA,MAAA,IAAA0N,UAAA,GAAAjH,0BAAA,CAE1B,IAAI,CAACxH,KAAK,CAAA;QAAA0O,MAAA;AAAA,MAAA,IAAA;QAA3B,KAAAD,UAAA,CAAA/G,CAAA,EAAAgH,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAA9G,CAAA,EAAAC,EAAAA,IAAA,GAA6B;AAAA,UAAA,IAApB7F,IAAK,GAAA2M,MAAA,CAAA1O,KAAA;AACV,UAAA,IAAI2O,KAAM,GAAAzH,eAAA,CAAA,EAAA,EAAOnF,KAAM;AACvB,UAAA,IAAIsL,oBAAoB;AAAEO,YAAAA,YAAY,EAAZA,YAAY;AAAEC,YAAAA,UAAU,EAAVA,UAAU;AAAEC,YAAAA,MAAK,EAALA;WAAQ;AAE5D,UAAA,IACKA,MAAO,KAAI,IAAI,CAACV,iBAAiB,CAACuB,KAAK,EAAEtB,iBAAiB,KAAK,IAAI,CAACI,eAAe,CAACkB,KAAK,EAAEtB,iBAAiB,CAAC,CAAC,IAC9G,CAACS,MAAK,KAAM,IAAI,CAACL,eAAe,CAACkB,KAAK,EAAEtB,iBAAiB,CAAA,IAAK,IAAI,CAACD,iBAAiB,CAACuB,KAAK,EAAEtB,iBAAiB,CAAC,CAAC,EAClH;AACEgB,YAAAA,aAAa,CAACX,IAAI,CAACiB,KAAK,CAAC;AAC7B;AACJ;AAAA,OAAA,CAAA,OAAA7G,GAAA,EAAA;QAAA2G,UAAA,CAAA1G,CAAA,CAAAD,GAAA,CAAA;AAAA,OAAA,SAAA;AAAA2G,QAAAA,UAAA,CAAAzG,CAAA,EAAA;AAAA;AAEA,MAAA,OAAOqG,aAAa;KACvB;IACDO,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,IAAI,IAAI,CAACtC,WAAU,IAAK,IAAI,CAACA,WAAW,CAACkC,IAAI,EAAE,CAAClH,MAAK,GAAI,CAAC,EAAE,OAAO,IAAI,CAAC8G,aAAa,CAAA,KAChF,OAAO,IAAI,CAACpO,KAAK;KACzB;IACD6O,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,OAAOC,EAAE,CAAC;QACNtO,OAAO,EAAE,IAAI,CAACA,OAAO;AACrBuO,QAAAA,UAAU,EAAE,IAAI,CAAC5N,YAAa,KAAI;AACtC,OAAC,CAAC;KACL;IACD6N,YAAY,EAAA,SAAZA,YAAYA,GAAG;AACX,MAAA,OAAOF,EAAE,CAAC;AACNC,QAAAA,UAAU,EAAE,IAAI,CAAC5N,YAAa,KAAI;AACtC,OAAC,CAAC;AACN;GACH;AACD4H,EAAAA,UAAU,EAAE;AACRkG,IAAAA,QAAQ,EAARA,QAAQ;AACRC,IAAAA,SAAS,EAATA,SAAS;AACTC,IAAAA,SAAS,EAATA,SAAS;AACTC,IAAAA,SAAS,EAATA,SAAS;AACTC,IAAAA,UAAU,EAAVA,UAAU;AACVhG,IAAAA,WAAU,EAAVA;AACJ;AACJ,CAAC;;;;;;;;;;;;;;;;;;EC5QG,OAAAI,SAAA,EAAA,EAAAC,kBAAA,CAyCK,OAzCLC,UAyCK,CAAA;AAzCC,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;IAAW,QAAM,EAAEE,QAAc,CAAA6E;KAAUhF,IAAI,CAAAyF,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAC1CzF,IAAA,CAAArJ,OAAM,IAAKqJ,+BACvBJ,SAAA,EAAA,EAAAC,kBAAA,CAKK,OALLC,UAKK,CAAA;;AALC,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,MAAA;KAAkBD,IAAG,CAAAhH,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAChC0M,UAGM,CAAA1F,IAAA,CAAA2F,MAAA,EAAA,aAAA,EAAA;AAHoB,IAAA,OAAA,iBAAO3F,IAAE,CAAAC,EAAA,CAAA,aAAA,CAAA;KAAnC,YAAA;AAAA,IAAA,OAGM,CAFOD,IAAW,CAAApJ,WAAA,IAApBgJ,SAAA,EAAA,EAAAC,kBAAA,CAAwG,KAAxGC,UAAwG,CAAA;;AAAjF,MAAA,OAAA,EAAK,CAAGE,IAAE,CAAAC,EAAA,CAAA,aAAA,CAAA,EAAA,SAAA,EAA4BD,IAAW,CAAApJ,WAAA;OAAWoJ,IAAG,CAAAhH,GAAA,CAAA,aAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,KACtF4G,SAAA,EAAA,EAAAiB,WAAA,CAAiFE,wBAAjFjB,UAAiF,CAAA;;AAA7DkB,MAAAA,IAAK,EAAL,EAAK;AAAC,MAAA,OAAA,EAAOhB,IAAE,CAAAC,EAAA,CAAA,aAAA;OAAyBD,IAAG,CAAAhH,GAAA,CAAA,aAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA;4CAI1DgH,IAAM,CAAAjJ,MAAA,iBAAvB8J,WAQW,CAAA+E,oBAAA,EAAA;;IARerE,QAAQ,EAAEvB,IAAQ,CAAAuB,QAAA;AAAGC,IAAAA,EAAE,EAAAnE,aAAA,CAAAA,aAAA,CAAO2C,EAAAA,EAAAA,IAAG,CAAAhH,GAAA,CAAA,UAAA,CAAA,GAAiBgH,IAAG,CAAAhH,GAAA,CAAA,mBAAA,CAAA,CAAA;AAA0B,IAAA,OAAA,iBAAOgH,IAAE,CAAAC,EAAA,CAAA,mBAAA,CAAA;;uBAC9G,YAAA;AAAA,MAAA,OAA0L,CAA1L4F,WAA0L,CAAAC,oBAAA,EAAA;oBAAtKC,KAAW,CAAAtD,WAAA;;iBAAXsD,KAAW,CAAAtD,WAAA,GAAAuD,MAAA;AAAA,SAAA,CAAA;AAAEC,QAAAA,YAAY,EAAC,KAAI;QAAG,wBAAOjG,IAAE,CAAAC,EAAA,CAAA,eAAA,CAAA,CAAA;QAAoBiG,WAAW,EAAElG,IAAiB,CAAA7I,iBAAA;QAAGoK,QAAQ,EAAEvB,IAAQ,CAAAuB,QAAA;QAAG4E,OAAK,EAAEhG,QAAa,CAAAmD,aAAA;AAAG9B,QAAAA,EAAE,EAAExB,IAAG,CAAAhH,GAAA,CAAA,eAAA;wFACtK6M,WAKW,CAAAO,oBAAA,EAAA;QALC7E,QAAQ,EAAEvB,IAAQ,CAAAuB,QAAA;AAAGC,QAAAA,EAAE,EAAExB,IAAG,CAAAhH,GAAA,CAAA,uBAAA;;2BAEpC,YAAA;AAAA,UAAA,OAEM,CAFN0M,UAEM,CAAA1F,IAAA,CAAA2F,MAAA,EAFO3F,IAAM,CAAA2F,MAAA,CAACU,UAAS,GAAA,YAAA,GAAA,YAAA,EAAA;AAAkC,YAAA,OAAA,iBAAOrG,IAAE,CAAAC,EAAA,CAAA,YAAA,CAAA;aAAxE,YAAA;AAAA,YAAA,OAEM,CADF4F,WAAA,CAAkES,uBAAlExG,UAAkE,CAAA;AAArD,cAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,YAAA;eAAwBD,IAAG,CAAAhH,GAAA,CAAA,YAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;sEAI7DyH,kBAAA,CAsBK,OAtBLX,UAsBK,CAAA;AAtBC,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,SAAA,CAAA;AAActI,IAAAA,KAAK;iBAAeqI,IAAa,CAAA1I;KAAA;IAAI,QAAM,EAAE6I,QAAY,CAAAgF;KAAUnF,IAAG,CAAAhH,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAC/F0M,UAAgG,CAAA1F,IAAA,CAAA2F,MAAA,EAAA,QAAA,EAAA;IAA3ExP,KAAK,EAAE6J,IAAK,CAAA7J,KAAA;IAAGE,YAAY,EAAE2J,IAAY,CAAA3J,YAAA;IAAGC,aAAa,EAAE0J,IAAa,CAAA1J;MAC7FmK,kBAAA,CAkBI,MAlBJX,UAkBI,CAAA;AAlBC,IAAA,OAAA,EAAOE,IAAE,CAAAC,EAAA,CAAA,cAAA,CAAA;AAAkBC,IAAAA,IAAI,EAAC,MAAO;IAAC,iBAAe,EAAEF,IAAc,CAAAvI,cAAA;IAAG,YAAU,EAAEuI,IAAS,CAAAtI;KAAUsI,IAAG,CAAAhH,GAAA,CAAA,cAAA,CAAA,CAAA,EAAA,EAC7G4G,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAgBWe,QAfiB,EAAA,IAAA,EAAAmB,UAAA,CAAA5B,QAAA,CAAA4E,aAAa,EAA7B,UAAA7M,IAAI,EAAEE,KAAK,EAAA;wBADvByI,WAgBW,CAAAoB,mBAAA,EAAA;MAdNlJ,GAAG,EAAEb,IAAI,CAACa,GAAG;AACbb,MAAAA,IAAI,EAAEA,IAAI;MACVC,SAAS,EAAE6H,IAAM,CAAA2F,MAAA;AACjBpO,MAAAA,KAAK,EAAEyI,IAAI,CAAAzI,KAAA,GAAA,CAAA;AACXa,MAAAA,KAAK,EAAEA,KAAK;MACZ/B,YAAY,EAAE0P,KAAc,CAAAvD,cAAA;MAC5BN,YAAW,EAAE/B,QAAY,CAAA+B,YAAA;MACzBC,WAAU,EAAEhC,QAAW,CAAAgC,WAAA;MACvB5L,aAAa,EAAEyJ,IAAa,CAAAzJ,aAAA;MAC5BD,aAAa,EAAE0J,IAAa,CAAA1J,aAAA;MAC5B8L,gBAAe,EAAEjC,QAAgB,CAAAiC,gBAAA;MACjCtL,WAAW,EAAEkJ,IAAW,CAAAlJ,WAAA;MACxByK,QAAQ,EAAEvB,IAAQ,CAAAuB,QAAA;MAClBC,EAAE,EAAExB,IAAE,CAAAwB;;+BAGfkE,UAAgG,CAAA1F,IAAA,CAAA2F,MAAA,EAAA,QAAA,EAAA;IAA3ExP,KAAK,EAAE6J,IAAK,CAAA7J,KAAA;IAAGE,YAAY,EAAE2J,IAAY,CAAA3J,YAAA;IAAGC,aAAa,EAAE0J,IAAa,CAAA1J;;;;;;;;"}