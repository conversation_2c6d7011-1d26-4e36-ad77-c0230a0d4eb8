{"version": 3, "file": "index.mjs", "sources": ["../../../src/tree/style/TreeStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/tree';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => [\n        'p-tree p-component',\n        {\n            'p-tree-selectable': props.selectionMode != null,\n            'p-tree-loading': props.loading,\n            'p-tree-flex-scrollable': props.scrollHeight === 'flex'\n        }\n    ],\n    mask: 'p-tree-mask p-overlay-mask',\n    loadingIcon: 'p-tree-loading-icon',\n    pcFilterContainer: 'p-tree-filter',\n    pcFilterInput: 'p-tree-filter-input',\n    wrapper: 'p-tree-root', //TODO: discuss\n    rootChildren: 'p-tree-root-children',\n    node: ({ instance }) => ['p-tree-node', { 'p-tree-node-leaf': instance.leaf }],\n    nodeContent: ({ instance }) => [\n        'p-tree-node-content',\n        instance.node.styleClass,\n        {\n            'p-tree-node-selectable': instance.selectable,\n            'p-tree-node-selected': instance.checkboxMode && instance.$parentInstance.highlightOnSelect ? instance.checked : instance.selected\n        }\n    ],\n    nodeToggleButton: 'p-tree-node-toggle-button',\n    nodeToggleIcon: 'p-tree-node-toggle-icon',\n    nodeCheckbox: 'p-tree-node-checkbox',\n    nodeIcon: 'p-tree-node-icon',\n    nodeLabel: 'p-tree-node-label',\n    nodeChildren: 'p-tree-node-children'\n};\n\nexport default BaseStyle.extend({\n    name: 'tree',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "props", "selectionMode", "loading", "scrollHeight", "mask", "loadingIcon", "pc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcFilterInput", "wrapper", "rootChildren", "node", "_ref2", "instance", "leaf", "nodeContent", "_ref3", "styleClass", "selectable", "checkboxMode", "$parentInstance", "highlightOnSelect", "checked", "selected", "nodeToggleButton", "nodeToggleIcon", "nodeCheckbox", "nodeIcon", "nodeLabel", "nodeChildren", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAA,OAAO,CACjB,oBAAoB,EACpB;AACI,MAAA,mBAAmB,EAAEA,KAAK,CAACC,aAAa,IAAI,IAAI;MAChD,gBAAgB,EAAED,KAAK,CAACE,OAAO;AAC/B,MAAA,wBAAwB,EAAEF,KAAK,CAACG,YAAY,KAAK;AACrD,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,IAAI,EAAE,4BAA4B;AAClCC,EAAAA,WAAW,EAAE,qBAAqB;AAClCC,EAAAA,iBAAiB,EAAE,eAAe;AAClCC,EAAAA,aAAa,EAAE,qBAAqB;AACpCC,EAAAA,OAAO,EAAE,aAAa;AAAE;AACxBC,EAAAA,YAAY,EAAE,sBAAsB;AACpCC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,KAAA,CAARC,QAAQ;IAAA,OAAO,CAAC,aAAa,EAAE;MAAE,kBAAkB,EAAEA,QAAQ,CAACC;AAAK,KAAC,CAAC;AAAA,GAAA;AAC9EC,EAAAA,WAAW,EAAE,SAAbA,WAAWA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKH,QAAQ,GAAAG,KAAA,CAARH,QAAQ;IAAA,OAAO,CAC3B,qBAAqB,EACrBA,QAAQ,CAACF,IAAI,CAACM,UAAU,EACxB;MACI,wBAAwB,EAAEJ,QAAQ,CAACK,UAAU;AAC7C,MAAA,sBAAsB,EAAEL,QAAQ,CAACM,YAAY,IAAIN,QAAQ,CAACO,eAAe,CAACC,iBAAiB,GAAGR,QAAQ,CAACS,OAAO,GAAGT,QAAQ,CAACU;AAC9H,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,gBAAgB,EAAE,2BAA2B;AAC7CC,EAAAA,cAAc,EAAE,yBAAyB;AACzCC,EAAAA,YAAY,EAAE,sBAAsB;AACpCC,EAAAA,QAAQ,EAAE,kBAAkB;AAC5BC,EAAAA,SAAS,EAAE,mBAAmB;AAC9BC,EAAAA,YAAY,EAAE;AAClB,CAAC;AAED,gBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,MAAM;AACZC,EAAAA,KAAK,EAALA,KAAK;AACLnC,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}