{"version": 3, "file": "index.mjs", "sources": ["../../src/tabpanel/BaseTabPanel.vue", "../../src/tabpanel/TabPanel.vue", "../../src/tabpanel/TabPanel.vue?vue&type=template&id=8c0752ba&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TabPanelStyle from 'primevue/tabpanel/style';\n\nexport default {\n    name: 'BaseTabPanel',\n    extends: BaseComponent,\n    props: {\n        // in Tabs\n        value: {\n            type: [String, Number],\n            default: undefined\n        },\n        as: {\n            type: [String, Object],\n            default: 'DIV'\n        },\n        asChild: {\n            type: Boolean,\n            default: false\n        },\n        // in TabView\n        header: null,\n        headerStyle: null,\n        headerClass: null,\n        headerProps: null,\n        headerActionProps: null,\n        contentStyle: null,\n        contentClass: null,\n        contentProps: null,\n        disabled: Boolean\n    },\n    style: TabPanelStyle,\n    provide() {\n        return {\n            $pcTabPanel: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <slot v-if=\"!$pcTabs\"></slot>\n    <template v-else>\n        <template v-if=\"!asChild\">\n            <component v-if=\"$pcTabs?.lazy ? active : true\" v-show=\"$pcTabs?.lazy ? true : active\" :is=\"as\" :class=\"cx('root')\" v-bind=\"attrs\">\n                <slot></slot>\n            </component>\n        </template>\n\n        <slot v-else :class=\"cx('root')\" :active=\"active\" :a11yAttrs=\"a11yAttrs\"></slot>\n    </template>\n</template>\n\n<script>\nimport { equals } from '@primeuix/utils/object';\nimport { mergeProps } from 'vue';\nimport BaseTabPanel from './BaseTabPanel.vue';\n\nexport default {\n    name: 'TabPanel',\n    extends: BaseTabPanel,\n    inheritAttrs: false,\n    inject: ['$pcTabs'],\n    computed: {\n        active() {\n            return equals(this.$pcTabs?.d_value, this.value);\n        },\n        id() {\n            return `${this.$pcTabs?.$id}_tabpanel_${this.value}`;\n        },\n        ariaLabelledby() {\n            return `${this.$pcTabs?.$id}_tab_${this.value}`;\n        },\n        attrs() {\n            return mergeProps(this.a11yAttrs, this.ptmi('root', this.ptParams));\n        },\n        a11yAttrs() {\n            return {\n                id: this.id,\n                tabindex: this.$pcTabs?.tabindex,\n                role: 'tabpanel',\n                'aria-labelledby': this.ariaLabelledby,\n                'data-pc-name': 'tabpanel',\n                'data-p-active': this.active\n            };\n        },\n        ptParams() {\n            return {\n                context: {\n                    active: this.active\n                }\n            };\n        }\n    }\n};\n</script>\n", "<template>\n    <slot v-if=\"!$pcTabs\"></slot>\n    <template v-else>\n        <template v-if=\"!asChild\">\n            <component v-if=\"$pcTabs?.lazy ? active : true\" v-show=\"$pcTabs?.lazy ? true : active\" :is=\"as\" :class=\"cx('root')\" v-bind=\"attrs\">\n                <slot></slot>\n            </component>\n        </template>\n\n        <slot v-else :class=\"cx('root')\" :active=\"active\" :a11yAttrs=\"a11yAttrs\"></slot>\n    </template>\n</template>\n\n<script>\nimport { equals } from '@primeuix/utils/object';\nimport { mergeProps } from 'vue';\nimport BaseTabPanel from './BaseTabPanel.vue';\n\nexport default {\n    name: 'TabPanel',\n    extends: BaseTabPanel,\n    inheritAttrs: false,\n    inject: ['$pcTabs'],\n    computed: {\n        active() {\n            return equals(this.$pcTabs?.d_value, this.value);\n        },\n        id() {\n            return `${this.$pcTabs?.$id}_tabpanel_${this.value}`;\n        },\n        ariaLabelledby() {\n            return `${this.$pcTabs?.$id}_tab_${this.value}`;\n        },\n        attrs() {\n            return mergeProps(this.a11yAttrs, this.ptmi('root', this.ptParams));\n        },\n        a11yAttrs() {\n            return {\n                id: this.id,\n                tabindex: this.$pcTabs?.tabindex,\n                role: 'tabpanel',\n                'aria-labelledby': this.ariaLabelledby,\n                'data-pc-name': 'tabpanel',\n                'data-p-active': this.active\n            };\n        },\n        ptParams() {\n            return {\n                context: {\n                    active: this.active\n                }\n            };\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "value", "type", "String", "Number", "undefined", "as", "Object", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "header", "headerStyle", "headerClass", "headerProps", "headerActionProps", "contentStyle", "contentClass", "contentProps", "disabled", "style", "TabPanelStyle", "provide", "$pcTabPanel", "$parentInstance", "BaseTabPanel", "inheritAttrs", "inject", "computed", "active", "_this$$pcTabs", "equals", "$pcTabs", "d_value", "id", "_this$$pcTabs2", "concat", "$id", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_this$$pcTabs3", "attrs", "mergeProps", "a11yAttrs", "ptmi", "ptParams", "_this$$pcTabs4", "tabindex", "role", "context", "$options", "_renderSlot", "_ctx", "$slots", "key", "_createElementBlock", "_Fragment", "_$options$$pcTabs", "lazy", "_createBlock", "_resolveDynamicComponent", "_mergeProps", "cx", "_$options$$pcTabs2"], "mappings": ";;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,cAAc;AACpB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACH;AACAC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;MACtB,SAASC,EAAAA;KACZ;AACDC,IAAAA,EAAE,EAAE;AACAJ,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEI,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDC,IAAAA,OAAO,EAAE;AACLN,MAAAA,IAAI,EAAEO,OAAO;MACb,SAAS,EAAA;KACZ;AACD;AACAC,IAAAA,MAAM,EAAE,IAAI;AACZC,IAAAA,WAAW,EAAE,IAAI;AACjBC,IAAAA,WAAW,EAAE,IAAI;AACjBC,IAAAA,WAAW,EAAE,IAAI;AACjBC,IAAAA,iBAAiB,EAAE,IAAI;AACvBC,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,QAAQ,EAAET;GACb;AACDU,EAAAA,KAAK,EAAEC,aAAa;EACpBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,WAAW,EAAE,IAAI;AACjBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACrBD,aAAe;AACXzB,EAAAA,IAAI,EAAE,UAAU;AAChB,EAAA,SAAA,EAAS0B,QAAY;AACrBC,EAAAA,YAAY,EAAE,KAAK;EACnBC,MAAM,EAAE,CAAC,SAAS,CAAC;AACnBC,EAAAA,QAAQ,EAAE;IACNC,MAAM,EAAA,SAANA,MAAMA,GAAG;AAAA,MAAA,IAAAC,aAAA;AACL,MAAA,OAAOC,MAAM,CAAAD,CAAAA,aAAA,GAAC,IAAI,CAACE,OAAO,MAAA,IAAA,IAAAF,aAAA,KAAA,MAAA,GAAA,MAAA,GAAZA,aAAA,CAAcG,OAAO,EAAE,IAAI,CAAC/B,KAAK,CAAC;KACnD;IACDgC,EAAE,EAAA,SAAFA,EAAEA,GAAG;AAAA,MAAA,IAAAC,cAAA;AACD,MAAA,OAAA,EAAA,CAAAC,MAAA,CAAAD,CAAAA,cAAA,GAAU,IAAI,CAACH,OAAO,MAAAG,IAAAA,IAAAA,cAAA,KAAZA,MAAAA,GAAAA,MAAAA,GAAAA,cAAA,CAAcE,GAAG,EAAA,YAAA,CAAA,CAAAD,MAAA,CAAa,IAAI,CAAClC,KAAK,CAAA;KACrD;IACDoC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AAAA,MAAA,IAAAC,cAAA;AACb,MAAA,OAAA,EAAA,CAAAH,MAAA,CAAAG,CAAAA,cAAA,GAAU,IAAI,CAACP,OAAO,MAAAO,IAAAA,IAAAA,cAAA,KAAZA,MAAAA,GAAAA,MAAAA,GAAAA,cAAA,CAAcF,GAAG,EAAA,OAAA,CAAA,CAAAD,MAAA,CAAQ,IAAI,CAAClC,KAAK,CAAA;KAChD;IACDsC,KAAK,EAAA,SAALA,KAAKA,GAAG;AACJ,MAAA,OAAOC,UAAU,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,IAAI,CAAC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;KACtE;IACDF,SAAS,EAAA,SAATA,SAASA,GAAG;AAAA,MAAA,IAAAG,cAAA;MACR,OAAO;QACHX,EAAE,EAAE,IAAI,CAACA,EAAE;QACXY,QAAQ,EAAA,CAAAD,cAAA,GAAE,IAAI,CAACb,OAAO,MAAA,IAAA,IAAAa,cAAA,KAAA,MAAA,GAAA,MAAA,GAAZA,cAAA,CAAcC,QAAQ;AAChCC,QAAAA,IAAI,EAAE,UAAU;QAChB,iBAAiB,EAAE,IAAI,CAACT,cAAc;AACtC,QAAA,cAAc,EAAE,UAAU;QAC1B,eAAe,EAAE,IAAI,CAACT;OACzB;KACJ;IACDe,QAAQ,EAAA,SAARA,QAAQA,GAAG;MACP,OAAO;AACHI,QAAAA,OAAO,EAAE;UACLnB,MAAM,EAAE,IAAI,CAACA;AACjB;OACH;AACL;AACJ;AACJ,CAAC;;;;UCrDgBoB,QAAO,CAAAjB,OAAA,GAApBkB,UAA4B,CAAAC,IAAA,CAAAC,MAAA,EAAA,SAAA,EAAA;AAAAC,IAAAA,GAAA,EAAA;GAAA,CAAA,iBAC5BC,kBAQU,CAAAC,QAAA,EAAA;AAAAF,IAAAA,GAAA,EAAA;AAAA,GAAA,EAAA,EAPWF,IAAO,CAAA1C,OAAA,iBAAxB6C,kBAIU,CAAAC,QAAA,EAAA;AAAAF,IAAAA,GAAA,EAAA;AAAA,GAAA,EAAA,EAHW,CAAAG,iBAAA,GAAAP,QAAO,CAAAjB,OAAA,MAAA,IAAA,IAAAwB,iBAAA,KAAPA,MAAAA,IAAAA,iBAAA,CAASC,OAAOR,QAAO,CAAApB,MAAA,GAAA,IAAA,iCAAxC6B,WAEW,CAAAC,uBAAA,CAFiFR,IAAE,CAAA5C,EAAA,CAAA,EAA9FqD,UAEW,CAAA;;AAFsF,IAAA,OAAA,EAAOT,IAAE,CAAAU,EAAA,CAAA,MAAA;KAAkBZ,QAAK,CAAAT,KAAA,CAAA,EAAA;uBAC7H,YAAA;MAAA,OAAY,CAAZU,UAAY,CAAAC,IAAA,CAAAC,MAAA,EAAA,SAAA,CAAA;;;+BADwC,CAAAU,kBAAA,GAAAb,QAAO,CAAAjB,OAAA,MAAA,IAAA,IAAA8B,kBAAA,KAAPA,MAAAA,IAAAA,kBAAA,CAASL,IAAG,GAAA,IAAA,GAAWR,QAAM,CAAApB,MAAA,CAAA,2CAKzFqB,UAA+E,CAAAC,IAAA,CAAAC,MAAA,EAAA,SAAA,EAAA;;IAAjE,wBAAOD,IAAE,CAAAU,EAAA,CAAA,MAAA,CAAA,CAAA;IAAWhC,MAAM,EAAEoB,QAAM,CAAApB,MAAA;IAAGa,SAAS,EAAEO,QAAS,CAAAP;;;;;;;;"}