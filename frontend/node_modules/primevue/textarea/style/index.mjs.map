{"version": 3, "file": "index.mjs", "sources": ["../../../src/textarea/style/TextareaStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/textarea';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-textarea p-component',\n        {\n            'p-filled': instance.$filled,\n            'p-textarea-resizable ': props.autoResize,\n            'p-textarea-sm p-inputfield-sm': props.size === 'small',\n            'p-textarea-lg p-inputfield-lg': props.size === 'large',\n            'p-invalid': instance.$invalid,\n            'p-variant-filled': instance.$variant === 'filled',\n            'p-textarea-fluid': instance.$fluid\n        }\n    ]\n};\n\nexport default BaseStyle.extend({\n    name: 'textarea',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "instance", "props", "$filled", "autoResize", "size", "$invalid", "$variant", "$fluid", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAA,OAAO,CAC3B,wBAAwB,EACxB;MACI,UAAU,EAAED,QAAQ,CAACE,OAAO;MAC5B,uBAAuB,EAAED,KAAK,CAACE,UAAU;AACzC,MAAA,+BAA+B,EAAEF,KAAK,CAACG,IAAI,KAAK,OAAO;AACvD,MAAA,+BAA+B,EAAEH,KAAK,CAACG,IAAI,KAAK,OAAO;MACvD,WAAW,EAAEJ,QAAQ,CAACK,QAAQ;AAC9B,MAAA,kBAAkB,EAAEL,QAAQ,CAACM,QAAQ,KAAK,QAAQ;MAClD,kBAAkB,EAAEN,QAAQ,CAACO;AACjC,KAAC,CACJ;AAAA;AACL,CAAC;AAED,oBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,UAAU;AAChBC,EAAAA,KAAK,EAALA,KAAK;AACLd,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}