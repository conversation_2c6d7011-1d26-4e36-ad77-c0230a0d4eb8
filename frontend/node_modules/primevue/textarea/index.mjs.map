{"version": 3, "file": "index.mjs", "sources": ["../../src/textarea/BaseTextarea.vue", "../../src/textarea/Textarea.vue", "../../src/textarea/Textarea.vue?vue&type=template&id=f8add878&lang.js"], "sourcesContent": ["<script>\nimport BaseInput from '@primevue/core/baseinput';\nimport TextareaStyle from 'primevue/textarea/style';\n\nexport default {\n    name: 'BaseTextarea',\n    extends: BaseInput,\n    props: {\n        autoResize: Boolean\n    },\n    style: TextareaStyle,\n    provide() {\n        return {\n            $pcTextarea: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <textarea :class=\"cx('root')\" :value=\"d_value\" :name=\"name\" :disabled=\"disabled\" :aria-invalid=\"invalid || undefined\" :data-p=\"dataP\" @input=\"onInput\" v-bind=\"attrs\"></textarea>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { mergeProps } from 'vue';\nimport BaseTextarea from './BaseTextarea.vue';\n\nexport default {\n    name: 'Textarea',\n    extends: BaseTextarea,\n    inheritAttrs: false,\n    observer: null,\n    mounted() {\n        if (this.autoResize) {\n            this.observer = new ResizeObserver(() => {\n                // Firefox has issues without the requestAnimationFrame - ResizeObserver loop completed with undelivered notifications.\n                requestAnimationFrame(() => {\n                    this.resize();\n                });\n            });\n            this.observer.observe(this.$el);\n        }\n    },\n    updated() {\n        if (this.autoResize) {\n            this.resize();\n        }\n    },\n    beforeUnmount() {\n        if (this.observer) {\n            this.observer.disconnect();\n        }\n    },\n    methods: {\n        resize() {\n            if (!this.$el.offsetParent) return;\n\n            this.$el.style.height = 'auto';\n            this.$el.style.height = this.$el.scrollHeight + 'px';\n\n            if (parseFloat(this.$el.style.height) >= parseFloat(this.$el.style.maxHeight)) {\n                this.$el.style.overflowY = 'scroll';\n                this.$el.style.height = this.$el.style.maxHeight;\n            } else {\n                this.$el.style.overflow = 'hidden';\n            }\n        },\n        onInput(event) {\n            if (this.autoResize) {\n                this.resize();\n            }\n\n            this.writeValue(event.target.value, event);\n        }\n    },\n    computed: {\n        attrs() {\n            return mergeProps(\n                this.ptmi('root', {\n                    context: {\n                        filled: this.$filled,\n                        disabled: this.disabled\n                    }\n                }),\n                this.formField\n            );\n        },\n        dataP() {\n            return cn({\n                invalid: this.$invalid,\n                fluid: this.$fluid,\n                filled: this.$variant === 'filled',\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n", "<template>\n    <textarea :class=\"cx('root')\" :value=\"d_value\" :name=\"name\" :disabled=\"disabled\" :aria-invalid=\"invalid || undefined\" :data-p=\"dataP\" @input=\"onInput\" v-bind=\"attrs\"></textarea>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { mergeProps } from 'vue';\nimport BaseTextarea from './BaseTextarea.vue';\n\nexport default {\n    name: 'Textarea',\n    extends: BaseTextarea,\n    inheritAttrs: false,\n    observer: null,\n    mounted() {\n        if (this.autoResize) {\n            this.observer = new ResizeObserver(() => {\n                // Firefox has issues without the requestAnimationFrame - ResizeObserver loop completed with undelivered notifications.\n                requestAnimationFrame(() => {\n                    this.resize();\n                });\n            });\n            this.observer.observe(this.$el);\n        }\n    },\n    updated() {\n        if (this.autoResize) {\n            this.resize();\n        }\n    },\n    beforeUnmount() {\n        if (this.observer) {\n            this.observer.disconnect();\n        }\n    },\n    methods: {\n        resize() {\n            if (!this.$el.offsetParent) return;\n\n            this.$el.style.height = 'auto';\n            this.$el.style.height = this.$el.scrollHeight + 'px';\n\n            if (parseFloat(this.$el.style.height) >= parseFloat(this.$el.style.maxHeight)) {\n                this.$el.style.overflowY = 'scroll';\n                this.$el.style.height = this.$el.style.maxHeight;\n            } else {\n                this.$el.style.overflow = 'hidden';\n            }\n        },\n        onInput(event) {\n            if (this.autoResize) {\n                this.resize();\n            }\n\n            this.writeValue(event.target.value, event);\n        }\n    },\n    computed: {\n        attrs() {\n            return mergeProps(\n                this.ptmi('root', {\n                    context: {\n                        filled: this.$filled,\n                        disabled: this.disabled\n                    }\n                }),\n                this.formField\n            );\n        },\n        dataP() {\n            return cn({\n                invalid: this.$invalid,\n                fluid: this.$fluid,\n                filled: this.$variant === 'filled',\n                [this.size]: this.size\n            });\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseInput", "props", "autoResize", "Boolean", "style", "TextareaStyle", "provide", "$pcTextarea", "$parentInstance", "BaseTextarea", "inheritAttrs", "observer", "mounted", "_this", "ResizeObserver", "requestAnimationFrame", "resize", "observe", "$el", "updated", "beforeUnmount", "disconnect", "methods", "offsetParent", "height", "scrollHeight", "parseFloat", "maxHeight", "overflowY", "overflow", "onInput", "event", "writeValue", "target", "value", "computed", "attrs", "mergeProps", "ptmi", "context", "filled", "$filled", "disabled", "formField", "dataP", "cn", "_defineProperty", "invalid", "$invalid", "fluid", "$fluid", "$variant", "size", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "d_value", "undefined", "$options", "apply", "arguments", "_hoisted_1"], "mappings": ";;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,cAAc;AACpB,EAAA,SAAA,EAASC,SAAS;AAClBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,UAAU,EAAEC;GACf;AACDC,EAAAA,KAAK,EAAEC,aAAa;EACpBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,WAAW,EAAE,IAAI;AACjBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;ACRD,aAAe;AACXT,EAAAA,IAAI,EAAE,UAAU;AAChB,EAAA,SAAA,EAASU,QAAY;AACrBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AAAA,IAAA,IAAAC,KAAA,GAAA,IAAA;IACN,IAAI,IAAI,CAACX,UAAU,EAAE;AACjB,MAAA,IAAI,CAACS,QAAO,GAAI,IAAIG,cAAc,CAAC,YAAM;AACrC;AACAC,QAAAA,qBAAqB,CAAC,YAAM;UACxBF,KAAI,CAACG,MAAM,EAAE;AACjB,SAAC,CAAC;AACN,OAAC,CAAC;MACF,IAAI,CAACL,QAAQ,CAACM,OAAO,CAAC,IAAI,CAACC,GAAG,CAAC;AACnC;GACH;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,IAAI,CAACjB,UAAU,EAAE;MACjB,IAAI,CAACc,MAAM,EAAE;AACjB;GACH;EACDI,aAAa,EAAA,SAAbA,aAAaA,GAAG;IACZ,IAAI,IAAI,CAACT,QAAQ,EAAE;AACf,MAAA,IAAI,CAACA,QAAQ,CAACU,UAAU,EAAE;AAC9B;GACH;AACDC,EAAAA,OAAO,EAAE;IACLN,MAAM,EAAA,SAANA,MAAMA,GAAG;AACL,MAAA,IAAI,CAAC,IAAI,CAACE,GAAG,CAACK,YAAY,EAAE;AAE5B,MAAA,IAAI,CAACL,GAAG,CAACd,KAAK,CAACoB,MAAK,GAAI,MAAM;AAC9B,MAAA,IAAI,CAACN,GAAG,CAACd,KAAK,CAACoB,MAAO,GAAE,IAAI,CAACN,GAAG,CAACO,eAAe,IAAI;MAEpD,IAAIC,UAAU,CAAC,IAAI,CAACR,GAAG,CAACd,KAAK,CAACoB,MAAM,CAAE,IAAGE,UAAU,CAAC,IAAI,CAACR,GAAG,CAACd,KAAK,CAACuB,SAAS,CAAC,EAAE;AAC3E,QAAA,IAAI,CAACT,GAAG,CAACd,KAAK,CAACwB,SAAU,GAAE,QAAQ;AACnC,QAAA,IAAI,CAACV,GAAG,CAACd,KAAK,CAACoB,SAAS,IAAI,CAACN,GAAG,CAACd,KAAK,CAACuB,SAAS;AACpD,OAAE,MAAK;AACH,QAAA,IAAI,CAACT,GAAG,CAACd,KAAK,CAACyB,QAAS,GAAE,QAAQ;AACtC;KACH;AACDC,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,KAAK,EAAE;MACX,IAAI,IAAI,CAAC7B,UAAU,EAAE;QACjB,IAAI,CAACc,MAAM,EAAE;AACjB;MAEA,IAAI,CAACgB,UAAU,CAACD,KAAK,CAACE,MAAM,CAACC,KAAK,EAAEH,KAAK,CAAC;AAC9C;GACH;AACDI,EAAAA,QAAQ,EAAE;IACNC,KAAK,EAAA,SAALA,KAAKA,GAAG;AACJ,MAAA,OAAOC,UAAU,CACb,IAAI,CAACC,IAAI,CAAC,MAAM,EAAE;AACdC,QAAAA,OAAO,EAAE;UACLC,MAAM,EAAE,IAAI,CAACC,OAAO;UACpBC,QAAQ,EAAE,IAAI,CAACA;AACnB;AACJ,OAAC,CAAC,EACF,IAAI,CAACC,SACT,CAAC;KACJ;IACDC,KAAK,EAAA,SAALA,KAAKA,GAAG;MACJ,OAAOC,EAAE,CAAAC,eAAA,CAAA;QACLC,OAAO,EAAE,IAAI,CAACC,QAAQ;QACtBC,KAAK,EAAE,IAAI,CAACC,MAAM;AAClBV,QAAAA,MAAM,EAAE,IAAI,CAACW,QAAS,KAAI;OACzB,EAAA,IAAI,CAACC,IAAI,EAAG,IAAI,CAACA,IAAG,CACxB,CAAC;AACN;AACJ;AACJ,CAAC;;;;EC7EG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAgL,YAAhLC,UAAgL,CAAA;AAArK,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;IAAWvB,KAAK,EAAEsB,IAAO,CAAAE,OAAA;IAAG3D,IAAI,EAAEyD,IAAI,CAAAzD,IAAA;IAAG2C,QAAQ,EAAEc,IAAQ,CAAAd,QAAA;AAAG,IAAA,cAAY,EAAEc,IAAM,CAAAT,OAAA,IAAKY,SAAS;IAAG,QAAM,EAAEC,QAAK,CAAAhB,KAAA;IAAGd,OAAK;aAAE8B,QAAO,CAAA9B,OAAA,IAAA8B,QAAA,CAAA9B,OAAA,CAAA+B,KAAA,CAAAD,QAAA,EAAAE,SAAA,CAAA;KAAA;KAAUF,QAAK,CAAAxB,KAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA2B,UAAA,CAAA;;;;;;;"}