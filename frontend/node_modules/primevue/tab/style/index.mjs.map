{"version": 3, "file": "index.mjs", "sources": ["../../../src/tab/style/TabStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-tab',\n        {\n            'p-tab-active': instance.active,\n            'p-disabled': props.disabled\n        }\n    ]\n};\n\nexport default BaseStyle.extend({\n    name: 'tab',\n    classes\n});\n"], "names": ["classes", "root", "_ref", "instance", "props", "active", "disabled", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAA,OAAO,CAC3B,OAAO,EACP;MACI,cAAc,EAAED,QAAQ,CAACE,MAAM;MAC/B,YAAY,EAAED,KAAK,CAACE;AACxB,KAAC,CACJ;AAAA;AACL,CAAC;AAED,eAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,KAAK;AACXT,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}