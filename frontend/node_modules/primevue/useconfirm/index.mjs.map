{"version": 3, "file": "index.mjs", "sources": ["../../src/useconfirm/UseConfirm.js"], "sourcesContent": ["import { inject } from 'vue';\n\nexport const PrimeVueConfirmSymbol = Symbol();\n\nexport function useConfirm() {\n    const PrimeVueConfirm = inject(PrimeVueConfirmSymbol);\n\n    if (!PrimeVueConfirm) {\n        throw new Error('No PrimeVue Confirmation provided!');\n    }\n\n    return PrimeVueConfirm;\n}\n"], "names": ["PrimeVueConfirmSymbol", "Symbol", "useConfirm", "PrimeVueConfirm", "inject", "Error"], "mappings": ";;AAEaA,IAAAA,qBAAqB,GAAGC,MAAM;AAEpC,SAASC,UAAUA,GAAG;AACzB,EAAA,IAAMC,eAAe,GAAGC,MAAM,CAACJ,qBAAqB,CAAC;EAErD,IAAI,CAACG,eAAe,EAAE;AAClB,IAAA,MAAM,IAAIE,KAAK,CAAC,oCAAoC,CAAC;AACzD;AAEA,EAAA,OAAOF,eAAe;AAC1B;;;;"}