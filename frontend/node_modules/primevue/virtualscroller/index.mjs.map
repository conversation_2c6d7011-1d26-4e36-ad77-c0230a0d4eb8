{"version": 3, "file": "index.mjs", "sources": ["../../src/virtualscroller/BaseVirtualScroller.vue", "../../src/virtualscroller/VirtualScroller.vue", "../../src/virtualscroller/VirtualScroller.vue?vue&type=template&id=1c88b296&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport VirtualScrollerStyle from 'primevue/virtualscroller/style';\n\nexport default {\n    name: 'BaseVirtualScroller',\n    extends: BaseComponent,\n    props: {\n        id: {\n            type: String,\n            default: null\n        },\n        style: null,\n        class: null,\n        items: {\n            type: Array,\n            default: null\n        },\n        itemSize: {\n            type: [Number, Array],\n            default: 0\n        },\n        scrollHeight: null,\n        scrollWidth: null,\n        orientation: {\n            type: String,\n            default: 'vertical'\n        },\n        numToleratedItems: {\n            type: Number,\n            default: null\n        },\n        delay: {\n            type: Number,\n            default: 0\n        },\n        resizeDelay: {\n            type: Number,\n            default: 10\n        },\n        lazy: {\n            type: Boolean,\n            default: false\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        },\n        loaderDisabled: {\n            type: Boolean,\n            default: false\n        },\n        columns: {\n            type: Array,\n            default: null\n        },\n        loading: {\n            type: Boolean,\n            default: false\n        },\n        showSpacer: {\n            type: Boolean,\n            default: true\n        },\n        showLoader: {\n            type: <PERSON><PERSON>an,\n            default: false\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        },\n        inline: {\n            type: Boolean,\n            default: false\n        },\n        step: {\n            type: Number,\n            default: 0\n        },\n        appendOnly: {\n            type: Boolean,\n            default: false\n        },\n        autoSize: {\n            type: Boolean,\n            default: false\n        }\n    },\n    style: VirtualScrollerStyle,\n    provide() {\n        return {\n            $pcVirtualScroller: this,\n            $parentInstance: this\n        };\n    },\n    beforeMount() {\n        VirtualScrollerStyle.loadCSS({ nonce: this.$primevueConfig?.csp?.nonce });\n    }\n};\n</script>\n", "<template>\n    <template v-if=\"!disabled\">\n        <div :ref=\"elementRef\" :class=\"containerClass\" :tabindex=\"tabindex\" :style=\"style\" @scroll=\"onScroll\" v-bind=\"ptmi('root')\">\n            <slot\n                name=\"content\"\n                :styleClass=\"contentClass\"\n                :items=\"loadedItems\"\n                :getItemOptions=\"getOptions\"\n                :loading=\"d_loading\"\n                :getLoaderOptions=\"getLoaderOptions\"\n                :itemSize=\"itemSize\"\n                :rows=\"loadedRows\"\n                :columns=\"loadedColumns\"\n                :contentRef=\"contentRef\"\n                :spacerStyle=\"spacerStyle\"\n                :contentStyle=\"contentStyle\"\n                :vertical=\"isVertical()\"\n                :horizontal=\"isHorizontal()\"\n                :both=\"isBoth()\"\n            >\n                <div :ref=\"contentRef\" :class=\"contentClass\" :style=\"contentStyle\" v-bind=\"ptm('content')\">\n                    <template v-for=\"(item, index) of loadedItems\" :key=\"index\">\n                        <slot name=\"item\" :item=\"item\" :options=\"getOptions(index)\"></slot>\n                    </template>\n                </div>\n            </slot>\n            <div v-if=\"showSpacer\" class=\"p-virtualscroller-spacer\" :style=\"spacerStyle\" v-bind=\"ptm('spacer')\"></div>\n            <div v-if=\"!loaderDisabled && showLoader && d_loading\" :class=\"loaderClass\" v-bind=\"ptm('loader')\">\n                <template v-if=\"$slots && $slots.loader\">\n                    <template v-for=\"(_, index) of loaderArr\" :key=\"index\">\n                        <slot name=\"loader\" :options=\"getLoaderOptions(index, isBoth() && { numCols: d_numItemsInViewport.cols })\"></slot>\n                    </template>\n                </template>\n                <slot name=\"loadingicon\">\n                    <SpinnerIcon spin class=\"p-virtualscroller-loading-icon\" v-bind=\"ptm('loadingIcon')\" />\n                </slot>\n            </div>\n        </div>\n    </template>\n    <template v-else>\n        <slot></slot>\n        <slot name=\"content\" :items=\"items\" :rows=\"items\" :columns=\"loadedColumns\"></slot>\n    </template>\n</template>\n\n<script>\nimport { findSingle, getHeight, getWidth, isVisible } from '@primeuix/utils/dom';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport BaseVirtualScroller from './BaseVirtualScroller.vue';\n\nexport default {\n    name: 'VirtualScroller',\n    extends: BaseVirtualScroller,\n    inheritAttrs: false,\n    emits: ['update:numToleratedItems', 'scroll', 'scroll-index-change', 'lazy-load'],\n    data() {\n        const both = this.isBoth();\n\n        return {\n            first: both ? { rows: 0, cols: 0 } : 0,\n            last: both ? { rows: 0, cols: 0 } : 0,\n            page: both ? { rows: 0, cols: 0 } : 0,\n            numItemsInViewport: both ? { rows: 0, cols: 0 } : 0,\n            lastScrollPos: both ? { top: 0, left: 0 } : 0,\n            d_numToleratedItems: this.numToleratedItems,\n            d_loading: this.loading,\n            loaderArr: [],\n            spacerStyle: {},\n            contentStyle: {}\n        };\n    },\n    element: null,\n    content: null,\n    lastScrollPos: null,\n    scrollTimeout: null,\n    resizeTimeout: null,\n    defaultWidth: 0,\n    defaultHeight: 0,\n    defaultContentWidth: 0,\n    defaultContentHeight: 0,\n    isRangeChanged: false,\n    lazyLoadState: {},\n    resizeListener: null,\n    resizeObserver: null,\n    initialized: false,\n    watch: {\n        numToleratedItems(newValue) {\n            this.d_numToleratedItems = newValue;\n        },\n        loading(newValue, oldValue) {\n            if (this.lazy && newValue !== oldValue && newValue !== this.d_loading) {\n                this.d_loading = newValue;\n            }\n        },\n        items: {\n            handler(newValue, oldValue) {\n                if (!oldValue || oldValue.length !== (newValue || []).length) {\n                    this.init();\n                    this.calculateAutoSize();\n                }\n            },\n            deep: true\n        },\n        itemSize() {\n            this.init();\n            this.calculateAutoSize();\n        },\n        orientation() {\n            this.lastScrollPos = this.isBoth() ? { top: 0, left: 0 } : 0;\n        },\n        scrollHeight() {\n            this.init();\n            this.calculateAutoSize();\n        },\n        scrollWidth() {\n            this.init();\n            this.calculateAutoSize();\n        }\n    },\n    mounted() {\n        this.viewInit();\n\n        this.lastScrollPos = this.isBoth() ? { top: 0, left: 0 } : 0;\n        this.lazyLoadState = this.lazyLoadState || {};\n    },\n    updated() {\n        !this.initialized && this.viewInit();\n    },\n    unmounted() {\n        this.unbindResizeListener();\n\n        this.initialized = false;\n    },\n    methods: {\n        viewInit() {\n            if (isVisible(this.element)) {\n                this.setContentEl(this.content);\n                this.init();\n                this.calculateAutoSize();\n\n                this.defaultWidth = getWidth(this.element);\n                this.defaultHeight = getHeight(this.element);\n                this.defaultContentWidth = getWidth(this.content);\n                this.defaultContentHeight = getHeight(this.content);\n                this.initialized = true;\n            }\n\n            if (this.element) {\n                this.bindResizeListener();\n            }\n        },\n        init() {\n            if (!this.disabled) {\n                this.setSize();\n                this.calculateOptions();\n                this.setSpacerSize();\n            }\n        },\n        isVertical() {\n            return this.orientation === 'vertical';\n        },\n        isHorizontal() {\n            return this.orientation === 'horizontal';\n        },\n        isBoth() {\n            return this.orientation === 'both';\n        },\n        scrollTo(options) {\n            //this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n            this.element && this.element.scrollTo(options);\n        },\n        scrollToIndex(index, behavior = 'auto') {\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const valid = both ? index.every((i) => i > -1) : index > -1;\n\n            if (valid) {\n                const first = this.first;\n                const { scrollTop = 0, scrollLeft = 0 } = this.element;\n                const { numToleratedItems } = this.calculateNumItems();\n                const contentPos = this.getContentPosition();\n                const itemSize = this.itemSize;\n                const calculateFirst = (_index = 0, _numT) => (_index <= _numT ? 0 : _index);\n                const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n                const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n                let newFirst = both ? { rows: 0, cols: 0 } : 0;\n                let isRangeChanged = false,\n                    isScrollChanged = false;\n\n                if (both) {\n                    newFirst = { rows: calculateFirst(index[0], numToleratedItems[0]), cols: calculateFirst(index[1], numToleratedItems[1]) };\n                    scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPos.left), calculateCoord(newFirst.rows, itemSize[0], contentPos.top));\n                    isScrollChanged = this.lastScrollPos.top !== scrollTop || this.lastScrollPos.left !== scrollLeft;\n                    isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols;\n                } else {\n                    newFirst = calculateFirst(index, numToleratedItems);\n                    horizontal ? scrollTo(calculateCoord(newFirst, itemSize, contentPos.left), scrollTop) : scrollTo(scrollLeft, calculateCoord(newFirst, itemSize, contentPos.top));\n                    isScrollChanged = this.lastScrollPos !== (horizontal ? scrollLeft : scrollTop);\n                    isRangeChanged = newFirst !== first;\n                }\n\n                this.isRangeChanged = isRangeChanged;\n                isScrollChanged && (this.first = newFirst);\n            }\n        },\n        scrollInView(index, to, behavior = 'auto') {\n            if (to) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const valid = both ? index.every((i) => i > -1) : index > -1;\n\n                if (valid) {\n                    const { first, viewport } = this.getRenderedRange();\n                    const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n                    const isToStart = to === 'to-start';\n                    const isToEnd = to === 'to-end';\n\n                    if (isToStart) {\n                        if (both) {\n                            if (viewport.first.rows - first.rows > index[0]) {\n                                scrollTo(viewport.first.cols * this.itemSize[1], (viewport.first.rows - 1) * this.itemSize[0]);\n                            } else if (viewport.first.cols - first.cols > index[1]) {\n                                scrollTo((viewport.first.cols - 1) * this.itemSize[1], viewport.first.rows * this.itemSize[0]);\n                            }\n                        } else {\n                            if (viewport.first - first > index) {\n                                const pos = (viewport.first - 1) * this.itemSize;\n\n                                horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                            }\n                        }\n                    } else if (isToEnd) {\n                        if (both) {\n                            if (viewport.last.rows - first.rows <= index[0] + 1) {\n                                scrollTo(viewport.first.cols * this.itemSize[1], (viewport.first.rows + 1) * this.itemSize[0]);\n                            } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n                                scrollTo((viewport.first.cols + 1) * this.itemSize[1], viewport.first.rows * this.itemSize[0]);\n                            }\n                        } else {\n                            if (viewport.last - first <= index + 1) {\n                                const pos = (viewport.first + 1) * this.itemSize;\n\n                                horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                            }\n                        }\n                    }\n                }\n            } else {\n                this.scrollToIndex(index, behavior);\n            }\n        },\n        getRenderedRange() {\n            const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n            let firstInViewport = this.first;\n            let lastInViewport = 0;\n\n            if (this.element) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const { scrollTop, scrollLeft } = this.element;\n\n                if (both) {\n                    firstInViewport = { rows: calculateFirstInViewport(scrollTop, this.itemSize[0]), cols: calculateFirstInViewport(scrollLeft, this.itemSize[1]) };\n                    lastInViewport = { rows: firstInViewport.rows + this.numItemsInViewport.rows, cols: firstInViewport.cols + this.numItemsInViewport.cols };\n                } else {\n                    const scrollPos = horizontal ? scrollLeft : scrollTop;\n\n                    firstInViewport = calculateFirstInViewport(scrollPos, this.itemSize);\n                    lastInViewport = firstInViewport + this.numItemsInViewport;\n                }\n            }\n\n            return {\n                first: this.first,\n                last: this.last,\n                viewport: {\n                    first: firstInViewport,\n                    last: lastInViewport\n                }\n            };\n        },\n        calculateNumItems() {\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const itemSize = this.itemSize;\n            const contentPos = this.getContentPosition();\n            const contentWidth = this.element ? this.element.offsetWidth - contentPos.left : 0;\n            const contentHeight = this.element ? this.element.offsetHeight - contentPos.top : 0;\n            const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n            const calculateNumToleratedItems = (_numItems) => Math.ceil(_numItems / 2);\n            const numItemsInViewport = both\n                ? { rows: calculateNumItemsInViewport(contentHeight, itemSize[0]), cols: calculateNumItemsInViewport(contentWidth, itemSize[1]) }\n                : calculateNumItemsInViewport(horizontal ? contentWidth : contentHeight, itemSize);\n\n            const numToleratedItems = this.d_numToleratedItems || (both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n\n            return { numItemsInViewport, numToleratedItems };\n        },\n        calculateOptions() {\n            const both = this.isBoth();\n            const first = this.first;\n            const { numItemsInViewport, numToleratedItems } = this.calculateNumItems();\n            const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n            const last = both\n                ? { rows: calculateLast(first.rows, numItemsInViewport.rows, numToleratedItems[0]), cols: calculateLast(first.cols, numItemsInViewport.cols, numToleratedItems[1], true) }\n                : calculateLast(first, numItemsInViewport, numToleratedItems);\n\n            this.last = last;\n            this.numItemsInViewport = numItemsInViewport;\n            this.d_numToleratedItems = numToleratedItems;\n            this.$emit('update:numToleratedItems', this.d_numToleratedItems);\n\n            if (this.showLoader) {\n                this.loaderArr = both ? Array.from({ length: numItemsInViewport.rows }).map(() => Array.from({ length: numItemsInViewport.cols })) : Array.from({ length: numItemsInViewport });\n            }\n\n            if (this.lazy) {\n                Promise.resolve().then(() => {\n                    this.lazyLoadState = {\n                        first: this.step ? (both ? { rows: 0, cols: first.cols } : 0) : first,\n                        last: Math.min(this.step ? this.step : last, this.items?.length || 0)\n                    };\n\n                    this.$emit('lazy-load', this.lazyLoadState);\n                });\n            }\n        },\n        calculateAutoSize() {\n            if (this.autoSize && !this.d_loading) {\n                Promise.resolve().then(() => {\n                    if (this.content) {\n                        const both = this.isBoth();\n                        const horizontal = this.isHorizontal();\n                        const vertical = this.isVertical();\n\n                        this.content.style.minHeight = this.content.style.minWidth = 'auto';\n                        this.content.style.position = 'relative';\n                        this.element.style.contain = 'none';\n\n                        /*const [contentWidth, contentHeight] = [getWidth(this.content), getHeight(this.content)];\n\n                        contentWidth !== this.defaultContentWidth && (this.element.style.width = '');\n                        contentHeight !== this.defaultContentHeight && (this.element.style.height = '');*/\n\n                        const [width, height] = [getWidth(this.element), getHeight(this.element)];\n\n                        (both || horizontal) && (this.element.style.width = width < this.defaultWidth ? width + 'px' : this.scrollWidth || this.defaultWidth + 'px');\n                        (both || vertical) && (this.element.style.height = height < this.defaultHeight ? height + 'px' : this.scrollHeight || this.defaultHeight + 'px');\n\n                        this.content.style.minHeight = this.content.style.minWidth = '';\n                        this.content.style.position = '';\n                        this.element.style.contain = '';\n                    }\n                });\n            }\n        },\n        getLast(last = 0, isCols) {\n            return this.items ? Math.min(isCols ? (this.columns || this.items[0])?.length || 0 : this.items?.length || 0, last) : 0;\n        },\n        getContentPosition() {\n            if (this.content) {\n                const style = getComputedStyle(this.content);\n                const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n                const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n                const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n                const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n\n                return { left, right, top, bottom, x: left + right, y: top + bottom };\n            }\n\n            return { left: 0, right: 0, top: 0, bottom: 0, x: 0, y: 0 };\n        },\n        setSize() {\n            if (this.element) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const parentElement = this.element.parentElement;\n                const width = this.scrollWidth || `${this.element.offsetWidth || parentElement.offsetWidth}px`;\n                const height = this.scrollHeight || `${this.element.offsetHeight || parentElement.offsetHeight}px`;\n                const setProp = (_name, _value) => (this.element.style[_name] = _value);\n\n                if (both || horizontal) {\n                    setProp('height', height);\n                    setProp('width', width);\n                } else {\n                    setProp('height', height);\n                }\n            }\n        },\n        setSpacerSize() {\n            const items = this.items;\n\n            if (items) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const contentPos = this.getContentPosition();\n                const setProp = (_name, _value, _size, _cpos = 0) => (this.spacerStyle = { ...this.spacerStyle, ...{ [`${_name}`]: (_value || []).length * _size + _cpos + 'px' } });\n\n                if (both) {\n                    setProp('height', items, this.itemSize[0], contentPos.y);\n                    setProp('width', this.columns || items[1], this.itemSize[1], contentPos.x);\n                } else {\n                    horizontal ? setProp('width', this.columns || items, this.itemSize, contentPos.x) : setProp('height', items, this.itemSize, contentPos.y);\n                }\n            }\n        },\n        setContentPosition(pos) {\n            if (this.content && !this.appendOnly) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const first = pos ? pos.first : this.first;\n                const calculateTranslateVal = (_first, _size) => _first * _size;\n                const setTransform = (_x = 0, _y = 0) => (this.contentStyle = { ...this.contentStyle, ...{ transform: `translate3d(${_x}px, ${_y}px, 0)` } });\n\n                if (both) {\n                    setTransform(calculateTranslateVal(first.cols, this.itemSize[1]), calculateTranslateVal(first.rows, this.itemSize[0]));\n                } else {\n                    const translateVal = calculateTranslateVal(first, this.itemSize);\n\n                    horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n                }\n            }\n        },\n        onScrollPositionChange(event) {\n            const target = event.target;\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const contentPos = this.getContentPosition();\n            const calculateScrollPos = (_pos, _cpos) => (_pos ? (_pos > _cpos ? _pos - _cpos : _pos) : 0);\n            const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n            const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n                return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n            };\n\n            const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight, _isCols) => {\n                if (_currentIndex <= _numT) return 0;\n                const firstValue = Math.max(0, _isScrollDownOrRight ? (_currentIndex < _triggerIndex ? _first : _currentIndex - _numT) : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n                const maxFirst = this.getLast(firstValue, _isCols);\n                if (firstValue > maxFirst) return maxFirst - _num;\n                else return firstValue;\n            };\n\n            const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols) => {\n                let lastValue = _first + _num + 2 * _numT;\n\n                if (_currentIndex >= _numT) {\n                    lastValue += _numT + 1;\n                }\n\n                return this.getLast(lastValue, _isCols);\n            };\n\n            const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n            const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n\n            let newFirst = both ? { rows: 0, cols: 0 } : 0;\n            let newLast = this.last;\n            let isRangeChanged = false;\n            let newScrollPos = this.lastScrollPos;\n\n            if (both) {\n                const isScrollDown = this.lastScrollPos.top <= scrollTop;\n                const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n\n                if (!this.appendOnly || (this.appendOnly && (isScrollDown || isScrollRight))) {\n                    const currentIndex = { rows: calculateCurrentIndex(scrollTop, this.itemSize[0]), cols: calculateCurrentIndex(scrollLeft, this.itemSize[1]) };\n                    const triggerIndex = {\n                        rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                        cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                    };\n\n                    newFirst = {\n                        rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                        cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight, true)\n                    };\n                    newLast = {\n                        rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n                        cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n                    };\n\n                    isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n                    newScrollPos = { top: scrollTop, left: scrollLeft };\n                }\n            } else {\n                const scrollPos = horizontal ? scrollLeft : scrollTop;\n                const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n\n                if (!this.appendOnly || (this.appendOnly && isScrollDownOrRight)) {\n                    const currentIndex = calculateCurrentIndex(scrollPos, this.itemSize);\n                    const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n\n                    newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                    newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n                    isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n                    newScrollPos = scrollPos;\n                }\n            }\n\n            return {\n                first: newFirst,\n                last: newLast,\n                isRangeChanged,\n                scrollPos: newScrollPos\n            };\n        },\n        onScrollChange(event) {\n            const { first, last, isRangeChanged, scrollPos } = this.onScrollPositionChange(event);\n\n            if (isRangeChanged) {\n                const newState = { first, last };\n\n                this.setContentPosition(newState);\n\n                this.first = first;\n                this.last = last;\n                this.lastScrollPos = scrollPos;\n\n                this.$emit('scroll-index-change', newState);\n\n                if (this.lazy && this.isPageChanged(first)) {\n                    const lazyLoadState = {\n                        first: this.step ? Math.min(this.getPageByFirst(first) * this.step, (this.items?.length || 0) - this.step) : first,\n                        last: Math.min(this.step ? (this.getPageByFirst(first) + 1) * this.step : last, this.items?.length || 0)\n                    };\n                    const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n\n                    isLazyStateChanged && this.$emit('lazy-load', lazyLoadState);\n                    this.lazyLoadState = lazyLoadState;\n                }\n            }\n        },\n        onScroll(event) {\n            this.$emit('scroll', event);\n\n            if (this.delay) {\n                if (this.scrollTimeout) {\n                    clearTimeout(this.scrollTimeout);\n                }\n\n                if (this.isPageChanged()) {\n                    if (!this.d_loading && this.showLoader) {\n                        const { isRangeChanged } = this.onScrollPositionChange(event);\n                        const changed = isRangeChanged || (this.step ? this.isPageChanged() : false);\n\n                        changed && (this.d_loading = true);\n                    }\n\n                    this.scrollTimeout = setTimeout(() => {\n                        this.onScrollChange(event);\n\n                        if (this.d_loading && this.showLoader && (!this.lazy || this.loading === undefined)) {\n                            this.d_loading = false;\n                            this.page = this.getPageByFirst();\n                        }\n                    }, this.delay);\n                }\n            } else {\n                this.onScrollChange(event);\n            }\n        },\n        onResize() {\n            if (this.resizeTimeout) {\n                clearTimeout(this.resizeTimeout);\n            }\n\n            this.resizeTimeout = setTimeout(() => {\n                if (isVisible(this.element)) {\n                    const both = this.isBoth();\n                    const vertical = this.isVertical();\n                    const horizontal = this.isHorizontal();\n                    const [width, height] = [getWidth(this.element), getHeight(this.element)];\n                    const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n                    const reinit = both ? isDiffWidth || isDiffHeight : horizontal ? isDiffWidth : vertical ? isDiffHeight : false;\n\n                    if (reinit) {\n                        this.d_numToleratedItems = this.numToleratedItems;\n                        this.defaultWidth = width;\n                        this.defaultHeight = height;\n                        this.defaultContentWidth = getWidth(this.content);\n                        this.defaultContentHeight = getHeight(this.content);\n\n                        this.init();\n                    }\n                }\n            }, this.resizeDelay);\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = this.onResize.bind(this);\n\n                window.addEventListener('resize', this.resizeListener);\n                window.addEventListener('orientationchange', this.resizeListener);\n\n                this.resizeObserver = new ResizeObserver(() => {\n                    this.onResize();\n                });\n                this.resizeObserver.observe(this.element);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                window.removeEventListener('orientationchange', this.resizeListener);\n                this.resizeListener = null;\n            }\n\n            if (this.resizeObserver) {\n                this.resizeObserver.disconnect();\n                this.resizeObserver = null;\n            }\n        },\n        getOptions(renderedIndex) {\n            const count = (this.items || []).length;\n            const index = this.isBoth() ? this.first.rows + renderedIndex : this.first + renderedIndex;\n\n            return {\n                index,\n                count,\n                first: index === 0,\n                last: index === count - 1,\n                even: index % 2 === 0,\n                odd: index % 2 !== 0\n            };\n        },\n        getLoaderOptions(index, extOptions) {\n            let count = this.loaderArr.length;\n\n            return {\n                index,\n                count,\n                first: index === 0,\n                last: index === count - 1,\n                even: index % 2 === 0,\n                odd: index % 2 !== 0,\n                ...extOptions\n            };\n        },\n        getPageByFirst(first) {\n            return Math.floor(((first ?? this.first) + this.d_numToleratedItems * 4) / (this.step || 1));\n        },\n        isPageChanged(first) {\n            return this.step && !this.lazy ? this.page !== this.getPageByFirst(first ?? this.first) : true;\n        },\n        setContentEl(el) {\n            this.content = el || this.content || findSingle(this.element, '[data-pc-section=\"content\"]');\n        },\n        elementRef(el) {\n            this.element = el;\n        },\n        contentRef(el) {\n            this.content = el;\n        }\n    },\n    computed: {\n        containerClass() {\n            return [\n                'p-virtualscroller',\n                this.class,\n                {\n                    'p-virtualscroller-inline': this.inline,\n                    'p-virtualscroller-both p-both-scroll': this.isBoth(),\n                    'p-virtualscroller-horizontal p-horizontal-scroll': this.isHorizontal()\n                }\n            ];\n        },\n        contentClass() {\n            return [\n                'p-virtualscroller-content',\n                {\n                    'p-virtualscroller-loading': this.d_loading\n                }\n            ];\n        },\n        loaderClass() {\n            return [\n                'p-virtualscroller-loader',\n                {\n                    'p-virtualscroller-loader-mask': !this.$slots.loader\n                }\n            ];\n        },\n        loadedItems() {\n            if (this.items && !this.d_loading) {\n                if (this.isBoth()) return this.items.slice(this.appendOnly ? 0 : this.first.rows, this.last.rows).map((item) => (this.columns ? item : item.slice(this.appendOnly ? 0 : this.first.cols, this.last.cols)));\n                else if (this.isHorizontal() && this.columns) return this.items;\n                else return this.items.slice(this.appendOnly ? 0 : this.first, this.last);\n            }\n\n            return [];\n        },\n        loadedRows() {\n            return this.d_loading ? (this.loaderDisabled ? this.loaderArr : []) : this.loadedItems;\n        },\n        loadedColumns() {\n            if (this.columns) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n\n                if (both || horizontal) {\n                    return this.d_loading && this.loaderDisabled ? (both ? this.loaderArr[0] : this.loaderArr) : this.columns.slice(both ? this.first.cols : this.first, both ? this.last.cols : this.last);\n                }\n            }\n\n            return this.columns;\n        }\n    },\n    components: {\n        SpinnerIcon: SpinnerIcon\n    }\n};\n</script>\n", "<template>\n    <template v-if=\"!disabled\">\n        <div :ref=\"elementRef\" :class=\"containerClass\" :tabindex=\"tabindex\" :style=\"style\" @scroll=\"onScroll\" v-bind=\"ptmi('root')\">\n            <slot\n                name=\"content\"\n                :styleClass=\"contentClass\"\n                :items=\"loadedItems\"\n                :getItemOptions=\"getOptions\"\n                :loading=\"d_loading\"\n                :getLoaderOptions=\"getLoaderOptions\"\n                :itemSize=\"itemSize\"\n                :rows=\"loadedRows\"\n                :columns=\"loadedColumns\"\n                :contentRef=\"contentRef\"\n                :spacerStyle=\"spacerStyle\"\n                :contentStyle=\"contentStyle\"\n                :vertical=\"isVertical()\"\n                :horizontal=\"isHorizontal()\"\n                :both=\"isBoth()\"\n            >\n                <div :ref=\"contentRef\" :class=\"contentClass\" :style=\"contentStyle\" v-bind=\"ptm('content')\">\n                    <template v-for=\"(item, index) of loadedItems\" :key=\"index\">\n                        <slot name=\"item\" :item=\"item\" :options=\"getOptions(index)\"></slot>\n                    </template>\n                </div>\n            </slot>\n            <div v-if=\"showSpacer\" class=\"p-virtualscroller-spacer\" :style=\"spacerStyle\" v-bind=\"ptm('spacer')\"></div>\n            <div v-if=\"!loaderDisabled && showLoader && d_loading\" :class=\"loaderClass\" v-bind=\"ptm('loader')\">\n                <template v-if=\"$slots && $slots.loader\">\n                    <template v-for=\"(_, index) of loaderArr\" :key=\"index\">\n                        <slot name=\"loader\" :options=\"getLoaderOptions(index, isBoth() && { numCols: d_numItemsInViewport.cols })\"></slot>\n                    </template>\n                </template>\n                <slot name=\"loadingicon\">\n                    <SpinnerIcon spin class=\"p-virtualscroller-loading-icon\" v-bind=\"ptm('loadingIcon')\" />\n                </slot>\n            </div>\n        </div>\n    </template>\n    <template v-else>\n        <slot></slot>\n        <slot name=\"content\" :items=\"items\" :rows=\"items\" :columns=\"loadedColumns\"></slot>\n    </template>\n</template>\n\n<script>\nimport { findSingle, getHeight, getWidth, isVisible } from '@primeuix/utils/dom';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport BaseVirtualScroller from './BaseVirtualScroller.vue';\n\nexport default {\n    name: 'VirtualScroller',\n    extends: BaseVirtualScroller,\n    inheritAttrs: false,\n    emits: ['update:numToleratedItems', 'scroll', 'scroll-index-change', 'lazy-load'],\n    data() {\n        const both = this.isBoth();\n\n        return {\n            first: both ? { rows: 0, cols: 0 } : 0,\n            last: both ? { rows: 0, cols: 0 } : 0,\n            page: both ? { rows: 0, cols: 0 } : 0,\n            numItemsInViewport: both ? { rows: 0, cols: 0 } : 0,\n            lastScrollPos: both ? { top: 0, left: 0 } : 0,\n            d_numToleratedItems: this.numToleratedItems,\n            d_loading: this.loading,\n            loaderArr: [],\n            spacerStyle: {},\n            contentStyle: {}\n        };\n    },\n    element: null,\n    content: null,\n    lastScrollPos: null,\n    scrollTimeout: null,\n    resizeTimeout: null,\n    defaultWidth: 0,\n    defaultHeight: 0,\n    defaultContentWidth: 0,\n    defaultContentHeight: 0,\n    isRangeChanged: false,\n    lazyLoadState: {},\n    resizeListener: null,\n    resizeObserver: null,\n    initialized: false,\n    watch: {\n        numToleratedItems(newValue) {\n            this.d_numToleratedItems = newValue;\n        },\n        loading(newValue, oldValue) {\n            if (this.lazy && newValue !== oldValue && newValue !== this.d_loading) {\n                this.d_loading = newValue;\n            }\n        },\n        items: {\n            handler(newValue, oldValue) {\n                if (!oldValue || oldValue.length !== (newValue || []).length) {\n                    this.init();\n                    this.calculateAutoSize();\n                }\n            },\n            deep: true\n        },\n        itemSize() {\n            this.init();\n            this.calculateAutoSize();\n        },\n        orientation() {\n            this.lastScrollPos = this.isBoth() ? { top: 0, left: 0 } : 0;\n        },\n        scrollHeight() {\n            this.init();\n            this.calculateAutoSize();\n        },\n        scrollWidth() {\n            this.init();\n            this.calculateAutoSize();\n        }\n    },\n    mounted() {\n        this.viewInit();\n\n        this.lastScrollPos = this.isBoth() ? { top: 0, left: 0 } : 0;\n        this.lazyLoadState = this.lazyLoadState || {};\n    },\n    updated() {\n        !this.initialized && this.viewInit();\n    },\n    unmounted() {\n        this.unbindResizeListener();\n\n        this.initialized = false;\n    },\n    methods: {\n        viewInit() {\n            if (isVisible(this.element)) {\n                this.setContentEl(this.content);\n                this.init();\n                this.calculateAutoSize();\n\n                this.defaultWidth = getWidth(this.element);\n                this.defaultHeight = getHeight(this.element);\n                this.defaultContentWidth = getWidth(this.content);\n                this.defaultContentHeight = getHeight(this.content);\n                this.initialized = true;\n            }\n\n            if (this.element) {\n                this.bindResizeListener();\n            }\n        },\n        init() {\n            if (!this.disabled) {\n                this.setSize();\n                this.calculateOptions();\n                this.setSpacerSize();\n            }\n        },\n        isVertical() {\n            return this.orientation === 'vertical';\n        },\n        isHorizontal() {\n            return this.orientation === 'horizontal';\n        },\n        isBoth() {\n            return this.orientation === 'both';\n        },\n        scrollTo(options) {\n            //this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n            this.element && this.element.scrollTo(options);\n        },\n        scrollToIndex(index, behavior = 'auto') {\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const valid = both ? index.every((i) => i > -1) : index > -1;\n\n            if (valid) {\n                const first = this.first;\n                const { scrollTop = 0, scrollLeft = 0 } = this.element;\n                const { numToleratedItems } = this.calculateNumItems();\n                const contentPos = this.getContentPosition();\n                const itemSize = this.itemSize;\n                const calculateFirst = (_index = 0, _numT) => (_index <= _numT ? 0 : _index);\n                const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n                const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n                let newFirst = both ? { rows: 0, cols: 0 } : 0;\n                let isRangeChanged = false,\n                    isScrollChanged = false;\n\n                if (both) {\n                    newFirst = { rows: calculateFirst(index[0], numToleratedItems[0]), cols: calculateFirst(index[1], numToleratedItems[1]) };\n                    scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPos.left), calculateCoord(newFirst.rows, itemSize[0], contentPos.top));\n                    isScrollChanged = this.lastScrollPos.top !== scrollTop || this.lastScrollPos.left !== scrollLeft;\n                    isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols;\n                } else {\n                    newFirst = calculateFirst(index, numToleratedItems);\n                    horizontal ? scrollTo(calculateCoord(newFirst, itemSize, contentPos.left), scrollTop) : scrollTo(scrollLeft, calculateCoord(newFirst, itemSize, contentPos.top));\n                    isScrollChanged = this.lastScrollPos !== (horizontal ? scrollLeft : scrollTop);\n                    isRangeChanged = newFirst !== first;\n                }\n\n                this.isRangeChanged = isRangeChanged;\n                isScrollChanged && (this.first = newFirst);\n            }\n        },\n        scrollInView(index, to, behavior = 'auto') {\n            if (to) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const valid = both ? index.every((i) => i > -1) : index > -1;\n\n                if (valid) {\n                    const { first, viewport } = this.getRenderedRange();\n                    const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n                    const isToStart = to === 'to-start';\n                    const isToEnd = to === 'to-end';\n\n                    if (isToStart) {\n                        if (both) {\n                            if (viewport.first.rows - first.rows > index[0]) {\n                                scrollTo(viewport.first.cols * this.itemSize[1], (viewport.first.rows - 1) * this.itemSize[0]);\n                            } else if (viewport.first.cols - first.cols > index[1]) {\n                                scrollTo((viewport.first.cols - 1) * this.itemSize[1], viewport.first.rows * this.itemSize[0]);\n                            }\n                        } else {\n                            if (viewport.first - first > index) {\n                                const pos = (viewport.first - 1) * this.itemSize;\n\n                                horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                            }\n                        }\n                    } else if (isToEnd) {\n                        if (both) {\n                            if (viewport.last.rows - first.rows <= index[0] + 1) {\n                                scrollTo(viewport.first.cols * this.itemSize[1], (viewport.first.rows + 1) * this.itemSize[0]);\n                            } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n                                scrollTo((viewport.first.cols + 1) * this.itemSize[1], viewport.first.rows * this.itemSize[0]);\n                            }\n                        } else {\n                            if (viewport.last - first <= index + 1) {\n                                const pos = (viewport.first + 1) * this.itemSize;\n\n                                horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                            }\n                        }\n                    }\n                }\n            } else {\n                this.scrollToIndex(index, behavior);\n            }\n        },\n        getRenderedRange() {\n            const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n            let firstInViewport = this.first;\n            let lastInViewport = 0;\n\n            if (this.element) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const { scrollTop, scrollLeft } = this.element;\n\n                if (both) {\n                    firstInViewport = { rows: calculateFirstInViewport(scrollTop, this.itemSize[0]), cols: calculateFirstInViewport(scrollLeft, this.itemSize[1]) };\n                    lastInViewport = { rows: firstInViewport.rows + this.numItemsInViewport.rows, cols: firstInViewport.cols + this.numItemsInViewport.cols };\n                } else {\n                    const scrollPos = horizontal ? scrollLeft : scrollTop;\n\n                    firstInViewport = calculateFirstInViewport(scrollPos, this.itemSize);\n                    lastInViewport = firstInViewport + this.numItemsInViewport;\n                }\n            }\n\n            return {\n                first: this.first,\n                last: this.last,\n                viewport: {\n                    first: firstInViewport,\n                    last: lastInViewport\n                }\n            };\n        },\n        calculateNumItems() {\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const itemSize = this.itemSize;\n            const contentPos = this.getContentPosition();\n            const contentWidth = this.element ? this.element.offsetWidth - contentPos.left : 0;\n            const contentHeight = this.element ? this.element.offsetHeight - contentPos.top : 0;\n            const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n            const calculateNumToleratedItems = (_numItems) => Math.ceil(_numItems / 2);\n            const numItemsInViewport = both\n                ? { rows: calculateNumItemsInViewport(contentHeight, itemSize[0]), cols: calculateNumItemsInViewport(contentWidth, itemSize[1]) }\n                : calculateNumItemsInViewport(horizontal ? contentWidth : contentHeight, itemSize);\n\n            const numToleratedItems = this.d_numToleratedItems || (both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n\n            return { numItemsInViewport, numToleratedItems };\n        },\n        calculateOptions() {\n            const both = this.isBoth();\n            const first = this.first;\n            const { numItemsInViewport, numToleratedItems } = this.calculateNumItems();\n            const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n            const last = both\n                ? { rows: calculateLast(first.rows, numItemsInViewport.rows, numToleratedItems[0]), cols: calculateLast(first.cols, numItemsInViewport.cols, numToleratedItems[1], true) }\n                : calculateLast(first, numItemsInViewport, numToleratedItems);\n\n            this.last = last;\n            this.numItemsInViewport = numItemsInViewport;\n            this.d_numToleratedItems = numToleratedItems;\n            this.$emit('update:numToleratedItems', this.d_numToleratedItems);\n\n            if (this.showLoader) {\n                this.loaderArr = both ? Array.from({ length: numItemsInViewport.rows }).map(() => Array.from({ length: numItemsInViewport.cols })) : Array.from({ length: numItemsInViewport });\n            }\n\n            if (this.lazy) {\n                Promise.resolve().then(() => {\n                    this.lazyLoadState = {\n                        first: this.step ? (both ? { rows: 0, cols: first.cols } : 0) : first,\n                        last: Math.min(this.step ? this.step : last, this.items?.length || 0)\n                    };\n\n                    this.$emit('lazy-load', this.lazyLoadState);\n                });\n            }\n        },\n        calculateAutoSize() {\n            if (this.autoSize && !this.d_loading) {\n                Promise.resolve().then(() => {\n                    if (this.content) {\n                        const both = this.isBoth();\n                        const horizontal = this.isHorizontal();\n                        const vertical = this.isVertical();\n\n                        this.content.style.minHeight = this.content.style.minWidth = 'auto';\n                        this.content.style.position = 'relative';\n                        this.element.style.contain = 'none';\n\n                        /*const [contentWidth, contentHeight] = [getWidth(this.content), getHeight(this.content)];\n\n                        contentWidth !== this.defaultContentWidth && (this.element.style.width = '');\n                        contentHeight !== this.defaultContentHeight && (this.element.style.height = '');*/\n\n                        const [width, height] = [getWidth(this.element), getHeight(this.element)];\n\n                        (both || horizontal) && (this.element.style.width = width < this.defaultWidth ? width + 'px' : this.scrollWidth || this.defaultWidth + 'px');\n                        (both || vertical) && (this.element.style.height = height < this.defaultHeight ? height + 'px' : this.scrollHeight || this.defaultHeight + 'px');\n\n                        this.content.style.minHeight = this.content.style.minWidth = '';\n                        this.content.style.position = '';\n                        this.element.style.contain = '';\n                    }\n                });\n            }\n        },\n        getLast(last = 0, isCols) {\n            return this.items ? Math.min(isCols ? (this.columns || this.items[0])?.length || 0 : this.items?.length || 0, last) : 0;\n        },\n        getContentPosition() {\n            if (this.content) {\n                const style = getComputedStyle(this.content);\n                const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n                const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n                const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n                const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n\n                return { left, right, top, bottom, x: left + right, y: top + bottom };\n            }\n\n            return { left: 0, right: 0, top: 0, bottom: 0, x: 0, y: 0 };\n        },\n        setSize() {\n            if (this.element) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const parentElement = this.element.parentElement;\n                const width = this.scrollWidth || `${this.element.offsetWidth || parentElement.offsetWidth}px`;\n                const height = this.scrollHeight || `${this.element.offsetHeight || parentElement.offsetHeight}px`;\n                const setProp = (_name, _value) => (this.element.style[_name] = _value);\n\n                if (both || horizontal) {\n                    setProp('height', height);\n                    setProp('width', width);\n                } else {\n                    setProp('height', height);\n                }\n            }\n        },\n        setSpacerSize() {\n            const items = this.items;\n\n            if (items) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const contentPos = this.getContentPosition();\n                const setProp = (_name, _value, _size, _cpos = 0) => (this.spacerStyle = { ...this.spacerStyle, ...{ [`${_name}`]: (_value || []).length * _size + _cpos + 'px' } });\n\n                if (both) {\n                    setProp('height', items, this.itemSize[0], contentPos.y);\n                    setProp('width', this.columns || items[1], this.itemSize[1], contentPos.x);\n                } else {\n                    horizontal ? setProp('width', this.columns || items, this.itemSize, contentPos.x) : setProp('height', items, this.itemSize, contentPos.y);\n                }\n            }\n        },\n        setContentPosition(pos) {\n            if (this.content && !this.appendOnly) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n                const first = pos ? pos.first : this.first;\n                const calculateTranslateVal = (_first, _size) => _first * _size;\n                const setTransform = (_x = 0, _y = 0) => (this.contentStyle = { ...this.contentStyle, ...{ transform: `translate3d(${_x}px, ${_y}px, 0)` } });\n\n                if (both) {\n                    setTransform(calculateTranslateVal(first.cols, this.itemSize[1]), calculateTranslateVal(first.rows, this.itemSize[0]));\n                } else {\n                    const translateVal = calculateTranslateVal(first, this.itemSize);\n\n                    horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n                }\n            }\n        },\n        onScrollPositionChange(event) {\n            const target = event.target;\n            const both = this.isBoth();\n            const horizontal = this.isHorizontal();\n            const contentPos = this.getContentPosition();\n            const calculateScrollPos = (_pos, _cpos) => (_pos ? (_pos > _cpos ? _pos - _cpos : _pos) : 0);\n            const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n            const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n                return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n            };\n\n            const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight, _isCols) => {\n                if (_currentIndex <= _numT) return 0;\n                const firstValue = Math.max(0, _isScrollDownOrRight ? (_currentIndex < _triggerIndex ? _first : _currentIndex - _numT) : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n                const maxFirst = this.getLast(firstValue, _isCols);\n                if (firstValue > maxFirst) return maxFirst - _num;\n                else return firstValue;\n            };\n\n            const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols) => {\n                let lastValue = _first + _num + 2 * _numT;\n\n                if (_currentIndex >= _numT) {\n                    lastValue += _numT + 1;\n                }\n\n                return this.getLast(lastValue, _isCols);\n            };\n\n            const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n            const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n\n            let newFirst = both ? { rows: 0, cols: 0 } : 0;\n            let newLast = this.last;\n            let isRangeChanged = false;\n            let newScrollPos = this.lastScrollPos;\n\n            if (both) {\n                const isScrollDown = this.lastScrollPos.top <= scrollTop;\n                const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n\n                if (!this.appendOnly || (this.appendOnly && (isScrollDown || isScrollRight))) {\n                    const currentIndex = { rows: calculateCurrentIndex(scrollTop, this.itemSize[0]), cols: calculateCurrentIndex(scrollLeft, this.itemSize[1]) };\n                    const triggerIndex = {\n                        rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                        cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                    };\n\n                    newFirst = {\n                        rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                        cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight, true)\n                    };\n                    newLast = {\n                        rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n                        cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n                    };\n\n                    isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n                    newScrollPos = { top: scrollTop, left: scrollLeft };\n                }\n            } else {\n                const scrollPos = horizontal ? scrollLeft : scrollTop;\n                const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n\n                if (!this.appendOnly || (this.appendOnly && isScrollDownOrRight)) {\n                    const currentIndex = calculateCurrentIndex(scrollPos, this.itemSize);\n                    const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n\n                    newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                    newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n                    isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n                    newScrollPos = scrollPos;\n                }\n            }\n\n            return {\n                first: newFirst,\n                last: newLast,\n                isRangeChanged,\n                scrollPos: newScrollPos\n            };\n        },\n        onScrollChange(event) {\n            const { first, last, isRangeChanged, scrollPos } = this.onScrollPositionChange(event);\n\n            if (isRangeChanged) {\n                const newState = { first, last };\n\n                this.setContentPosition(newState);\n\n                this.first = first;\n                this.last = last;\n                this.lastScrollPos = scrollPos;\n\n                this.$emit('scroll-index-change', newState);\n\n                if (this.lazy && this.isPageChanged(first)) {\n                    const lazyLoadState = {\n                        first: this.step ? Math.min(this.getPageByFirst(first) * this.step, (this.items?.length || 0) - this.step) : first,\n                        last: Math.min(this.step ? (this.getPageByFirst(first) + 1) * this.step : last, this.items?.length || 0)\n                    };\n                    const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n\n                    isLazyStateChanged && this.$emit('lazy-load', lazyLoadState);\n                    this.lazyLoadState = lazyLoadState;\n                }\n            }\n        },\n        onScroll(event) {\n            this.$emit('scroll', event);\n\n            if (this.delay) {\n                if (this.scrollTimeout) {\n                    clearTimeout(this.scrollTimeout);\n                }\n\n                if (this.isPageChanged()) {\n                    if (!this.d_loading && this.showLoader) {\n                        const { isRangeChanged } = this.onScrollPositionChange(event);\n                        const changed = isRangeChanged || (this.step ? this.isPageChanged() : false);\n\n                        changed && (this.d_loading = true);\n                    }\n\n                    this.scrollTimeout = setTimeout(() => {\n                        this.onScrollChange(event);\n\n                        if (this.d_loading && this.showLoader && (!this.lazy || this.loading === undefined)) {\n                            this.d_loading = false;\n                            this.page = this.getPageByFirst();\n                        }\n                    }, this.delay);\n                }\n            } else {\n                this.onScrollChange(event);\n            }\n        },\n        onResize() {\n            if (this.resizeTimeout) {\n                clearTimeout(this.resizeTimeout);\n            }\n\n            this.resizeTimeout = setTimeout(() => {\n                if (isVisible(this.element)) {\n                    const both = this.isBoth();\n                    const vertical = this.isVertical();\n                    const horizontal = this.isHorizontal();\n                    const [width, height] = [getWidth(this.element), getHeight(this.element)];\n                    const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n                    const reinit = both ? isDiffWidth || isDiffHeight : horizontal ? isDiffWidth : vertical ? isDiffHeight : false;\n\n                    if (reinit) {\n                        this.d_numToleratedItems = this.numToleratedItems;\n                        this.defaultWidth = width;\n                        this.defaultHeight = height;\n                        this.defaultContentWidth = getWidth(this.content);\n                        this.defaultContentHeight = getHeight(this.content);\n\n                        this.init();\n                    }\n                }\n            }, this.resizeDelay);\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = this.onResize.bind(this);\n\n                window.addEventListener('resize', this.resizeListener);\n                window.addEventListener('orientationchange', this.resizeListener);\n\n                this.resizeObserver = new ResizeObserver(() => {\n                    this.onResize();\n                });\n                this.resizeObserver.observe(this.element);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                window.removeEventListener('orientationchange', this.resizeListener);\n                this.resizeListener = null;\n            }\n\n            if (this.resizeObserver) {\n                this.resizeObserver.disconnect();\n                this.resizeObserver = null;\n            }\n        },\n        getOptions(renderedIndex) {\n            const count = (this.items || []).length;\n            const index = this.isBoth() ? this.first.rows + renderedIndex : this.first + renderedIndex;\n\n            return {\n                index,\n                count,\n                first: index === 0,\n                last: index === count - 1,\n                even: index % 2 === 0,\n                odd: index % 2 !== 0\n            };\n        },\n        getLoaderOptions(index, extOptions) {\n            let count = this.loaderArr.length;\n\n            return {\n                index,\n                count,\n                first: index === 0,\n                last: index === count - 1,\n                even: index % 2 === 0,\n                odd: index % 2 !== 0,\n                ...extOptions\n            };\n        },\n        getPageByFirst(first) {\n            return Math.floor(((first ?? this.first) + this.d_numToleratedItems * 4) / (this.step || 1));\n        },\n        isPageChanged(first) {\n            return this.step && !this.lazy ? this.page !== this.getPageByFirst(first ?? this.first) : true;\n        },\n        setContentEl(el) {\n            this.content = el || this.content || findSingle(this.element, '[data-pc-section=\"content\"]');\n        },\n        elementRef(el) {\n            this.element = el;\n        },\n        contentRef(el) {\n            this.content = el;\n        }\n    },\n    computed: {\n        containerClass() {\n            return [\n                'p-virtualscroller',\n                this.class,\n                {\n                    'p-virtualscroller-inline': this.inline,\n                    'p-virtualscroller-both p-both-scroll': this.isBoth(),\n                    'p-virtualscroller-horizontal p-horizontal-scroll': this.isHorizontal()\n                }\n            ];\n        },\n        contentClass() {\n            return [\n                'p-virtualscroller-content',\n                {\n                    'p-virtualscroller-loading': this.d_loading\n                }\n            ];\n        },\n        loaderClass() {\n            return [\n                'p-virtualscroller-loader',\n                {\n                    'p-virtualscroller-loader-mask': !this.$slots.loader\n                }\n            ];\n        },\n        loadedItems() {\n            if (this.items && !this.d_loading) {\n                if (this.isBoth()) return this.items.slice(this.appendOnly ? 0 : this.first.rows, this.last.rows).map((item) => (this.columns ? item : item.slice(this.appendOnly ? 0 : this.first.cols, this.last.cols)));\n                else if (this.isHorizontal() && this.columns) return this.items;\n                else return this.items.slice(this.appendOnly ? 0 : this.first, this.last);\n            }\n\n            return [];\n        },\n        loadedRows() {\n            return this.d_loading ? (this.loaderDisabled ? this.loaderArr : []) : this.loadedItems;\n        },\n        loadedColumns() {\n            if (this.columns) {\n                const both = this.isBoth();\n                const horizontal = this.isHorizontal();\n\n                if (both || horizontal) {\n                    return this.d_loading && this.loaderDisabled ? (both ? this.loaderArr[0] : this.loaderArr) : this.columns.slice(both ? this.first.cols : this.first, both ? this.last.cols : this.last);\n                }\n            }\n\n            return this.columns;\n        }\n    },\n    components: {\n        SpinnerIcon: SpinnerIcon\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "id", "type", "String", "style", "items", "Array", "itemSize", "Number", "scrollHeight", "scrollWidth", "orientation", "numToleratedItems", "delay", "resizeDelay", "lazy", "Boolean", "disabled", "loaderDisabled", "columns", "loading", "showSpacer", "<PERSON><PERSON><PERSON><PERSON>", "tabindex", "inline", "step", "appendOnly", "autoSize", "VirtualScrollerStyle", "provide", "$pcVirtualScroller", "$parentInstance", "beforeMount", "_this$$primevueConfig", "loadCSS", "nonce", "$primevueConfig", "csp", "BaseVirtualScroller", "inheritAttrs", "emits", "data", "both", "isBoth", "first", "rows", "cols", "last", "page", "numItemsInViewport", "lastScrollPos", "top", "left", "d_numToleratedItems", "d_loading", "loaderArr", "spacerStyle", "contentStyle", "element", "content", "scrollTimeout", "resizeTimeout", "defaultWidth", "defaultHeight", "defaultContentWidth", "defaultContentHeight", "isRangeChanged", "lazyLoadState", "resizeListener", "resizeObserver", "initialized", "watch", "newValue", "oldValue", "handler", "length", "init", "calculateAutoSize", "deep", "mounted", "viewInit", "updated", "unmounted", "unbindResizeListener", "methods", "isVisible", "setContentEl", "getWidth", "getHeight", "bindResizeListener", "setSize", "calculateOptions", "setSpacerSize", "isVertical", "isHorizontal", "scrollTo", "options", "scrollToIndex", "index", "_this", "behavior", "arguments", "undefined", "horizontal", "valid", "every", "i", "_this$element", "_this$element$scrollT", "scrollTop", "_this$element$scrollL", "scrollLeft", "_this$calculateNumIte", "calculateNumItems", "contentPos", "getContentPosition", "calculateFirst", "_index", "_numT", "calculateCoord", "_first", "_size", "_cpos", "newFirst", "isScrollChanged", "scrollInView", "to", "_this2", "_this$getRenderedRang", "getRenderedRange", "viewport", "isToStart", "isToEnd", "pos", "calculateFirstInViewport", "_pos", "Math", "floor", "firstInViewport", "lastInViewport", "_this$element2", "scrollPos", "contentWidth", "offsetWidth", "contentHeight", "offsetHeight", "calculateNumItemsInViewport", "_contentSize", "_itemSize", "ceil", "calculateNumToleratedItems", "_numItems", "_this3", "_this$calculateNumIte2", "calculateLast", "_num", "_isCols", "getLast", "$emit", "from", "map", "Promise", "resolve", "then", "_this3$items", "min", "_this4", "vertical", "minHeight", "min<PERSON><PERSON><PERSON>", "position", "contain", "_ref", "width", "height", "_ref2", "_this$items", "isCols", "getComputedStyle", "parseFloat", "paddingLeft", "max", "right", "paddingRight", "paddingTop", "bottom", "paddingBottom", "x", "y", "_this5", "parentElement", "concat", "setProp", "_name", "_value", "_this6", "_objectSpread", "_defineProperty", "setContentPosition", "_this7", "calculateTranslateVal", "setTransform", "_x", "_y", "transform", "translateVal", "onScrollPositionChange", "event", "_this8", "target", "calculateScrollPos", "calculateCurrentIndex", "calculateTriggerIndex", "_currentIndex", "_last", "_isScrollDownOrRight", "_triggerIndex", "firstValue", "max<PERSON><PERSON><PERSON>", "lastValue", "newLast", "newScrollPos", "isScrollDown", "isScrollRight", "currentIndex", "triggerIndex", "isScrollDownOrRight", "onScrollChange", "_this$onScrollPositio", "newState", "isPageChanged", "_this$items2", "_this$items3", "getPageByFirst", "isLazyStateChanged", "onScroll", "_this9", "clearTimeout", "_this$onScrollPositio2", "changed", "setTimeout", "onResize", "_this0", "_ref3", "isDiffWidth", "isDiffHeight", "reinit", "_this1", "bind", "window", "addEventListener", "ResizeObserver", "observe", "removeEventListener", "disconnect", "getOptions", "renderedIndex", "count", "even", "odd", "getLoaderOptions", "extOptions", "el", "findSingle", "elementRef", "contentRef", "computed", "containerClass", "contentClass", "loaderClass", "$slots", "loader", "loadedItems", "_this10", "slice", "item", "loadedRows", "loadedColumns", "components", "SpinnerIcon", "_ctx", "_openBlock", "_createElementBlock", "_mergeProps", "ref", "$options", "apply", "ptmi", "_renderSlot", "styleClass", "getItemOptions", "$data", "_createElementVNode", "ptm", "_Fragment", "_renderList", "key", "_", "numCols", "d_numItemsInViewport", "_createVNode", "_component_SpinnerIcon", "spin"], "mappings": ";;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,qBAAqB;AAC3B,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,EAAE,EAAE;AACAC,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,KAAK,EAAE,IAAI;AACX,IAAA,OAAA,EAAO,IAAI;AACXC,IAAAA,KAAK,EAAE;AACHH,MAAAA,IAAI,EAAEI,KAAK;MACX,SAAS,EAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNL,MAAAA,IAAI,EAAE,CAACM,MAAM,EAAEF,KAAK,CAAC;MACrB,SAAS,EAAA;KACZ;AACDG,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,WAAW,EAAE,IAAI;AACjBC,IAAAA,WAAW,EAAE;AACTT,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDS,IAAAA,iBAAiB,EAAE;AACfV,MAAAA,IAAI,EAAEM,MAAM;MACZ,SAAS,EAAA;KACZ;AACDK,IAAAA,KAAK,EAAE;AACHX,MAAAA,IAAI,EAAEM,MAAM;MACZ,SAAS,EAAA;KACZ;AACDM,IAAAA,WAAW,EAAE;AACTZ,MAAAA,IAAI,EAAEM,MAAM;MACZ,SAAS,EAAA;KACZ;AACDO,IAAAA,IAAI,EAAE;AACFb,MAAAA,IAAI,EAAEc,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNf,MAAAA,IAAI,EAAEc,OAAO;MACb,SAAS,EAAA;KACZ;AACDE,IAAAA,cAAc,EAAE;AACZhB,MAAAA,IAAI,EAAEc,OAAO;MACb,SAAS,EAAA;KACZ;AACDG,IAAAA,OAAO,EAAE;AACLjB,MAAAA,IAAI,EAAEI,KAAK;MACX,SAAS,EAAA;KACZ;AACDc,IAAAA,OAAO,EAAE;AACLlB,MAAAA,IAAI,EAAEc,OAAO;MACb,SAAS,EAAA;KACZ;AACDK,IAAAA,UAAU,EAAE;AACRnB,MAAAA,IAAI,EAAEc,OAAO;MACb,SAAS,EAAA;KACZ;AACDM,IAAAA,UAAU,EAAE;AACRpB,MAAAA,IAAI,EAAEc,OAAO;MACb,SAAS,EAAA;KACZ;AACDO,IAAAA,QAAQ,EAAE;AACNrB,MAAAA,IAAI,EAAEM,MAAM;MACZ,SAAS,EAAA;KACZ;AACDgB,IAAAA,MAAM,EAAE;AACJtB,MAAAA,IAAI,EAAEc,OAAO;MACb,SAAS,EAAA;KACZ;AACDS,IAAAA,IAAI,EAAE;AACFvB,MAAAA,IAAI,EAAEM,MAAM;MACZ,SAAS,EAAA;KACZ;AACDkB,IAAAA,UAAU,EAAE;AACRxB,MAAAA,IAAI,EAAEc,OAAO;MACb,SAAS,EAAA;KACZ;AACDW,IAAAA,QAAQ,EAAE;AACNzB,MAAAA,IAAI,EAAEc,OAAO;MACb,SAAS,EAAA;AACb;GACH;AACDZ,EAAAA,KAAK,EAAEwB,oBAAoB;EAC3BC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,kBAAkB,EAAE,IAAI;AACxBC,MAAAA,eAAe,EAAE;KACpB;GACJ;EACDC,WAAW,EAAA,SAAXA,WAAWA,GAAG;AAAA,IAAA,IAAAC,qBAAA;IACVL,oBAAoB,CAACM,OAAO,CAAC;AAAEC,MAAAA,KAAK,GAAAF,qBAAA,GAAE,IAAI,CAACG,eAAe,cAAAH,qBAAA,KAAA,MAAA,IAAA,CAAAA,qBAAA,GAApBA,qBAAA,CAAsBI,GAAG,cAAAJ,qBAAA,KAAA,MAAA,GAAA,MAAA,GAAzBA,qBAAA,CAA2BE;AAAM,KAAC,CAAC;AAC7E;AACJ,CAAC;;;;;;;;ACjDD,aAAe;AACXrC,EAAAA,IAAI,EAAE,iBAAiB;AACvB,EAAA,SAAA,EAASwC,QAAmB;AAC5BC,EAAAA,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAE,CAAC,0BAA0B,EAAE,QAAQ,EAAE,qBAAqB,EAAE,WAAW,CAAC;EACjFC,IAAI,EAAA,SAAJA,IAAIA,GAAG;AACH,IAAA,IAAMC,OAAO,IAAI,CAACC,MAAM,EAAE;IAE1B,OAAO;MACHC,KAAK,EAAEF,IAAG,GAAI;AAAEG,QAAAA,IAAI,EAAE,CAAC;AAAEC,QAAAA,IAAI,EAAE;AAAE,OAAE,GAAE,CAAC;MACtCC,IAAI,EAAEL,IAAG,GAAI;AAAEG,QAAAA,IAAI,EAAE,CAAC;AAAEC,QAAAA,IAAI,EAAE;AAAE,OAAE,GAAE,CAAC;MACrCE,IAAI,EAAEN,IAAG,GAAI;AAAEG,QAAAA,IAAI,EAAE,CAAC;AAAEC,QAAAA,IAAI,EAAE;AAAE,OAAE,GAAE,CAAC;MACrCG,kBAAkB,EAAEP,OAAO;AAAEG,QAAAA,IAAI,EAAE,CAAC;AAAEC,QAAAA,IAAI,EAAE;AAAE,OAAA,GAAI,CAAC;MACnDI,aAAa,EAAER,IAAK,GAAE;AAAES,QAAAA,GAAG,EAAE,CAAC;AAAEC,QAAAA,IAAI,EAAE;AAAE,OAAA,GAAI,CAAC;MAC7CC,mBAAmB,EAAE,IAAI,CAACzC,iBAAiB;MAC3C0C,SAAS,EAAE,IAAI,CAAClC,OAAO;AACvBmC,MAAAA,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;AACfC,MAAAA,YAAY,EAAE;KACjB;GACJ;AACDC,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,OAAO,EAAE,IAAI;AACbT,EAAAA,aAAa,EAAE,IAAI;AACnBU,EAAAA,aAAa,EAAE,IAAI;AACnBC,EAAAA,aAAa,EAAE,IAAI;AACnBC,EAAAA,YAAY,EAAE,CAAC;AACfC,EAAAA,aAAa,EAAE,CAAC;AAChBC,EAAAA,mBAAmB,EAAE,CAAC;AACtBC,EAAAA,oBAAoB,EAAE,CAAC;AACvBC,EAAAA,cAAc,EAAE,KAAK;EACrBC,aAAa,EAAE,EAAE;AACjBC,EAAAA,cAAc,EAAE,IAAI;AACpBC,EAAAA,cAAc,EAAE,IAAI;AACpBC,EAAAA,WAAW,EAAE,KAAK;AAClBC,EAAAA,KAAK,EAAE;AACH3D,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAAC4D,QAAQ,EAAE;MACxB,IAAI,CAACnB,mBAAoB,GAAEmB,QAAQ;KACtC;AACDpD,IAAAA,OAAO,WAAPA,OAAOA,CAACoD,QAAQ,EAAEC,QAAQ,EAAE;AACxB,MAAA,IAAI,IAAI,CAAC1D,IAAG,IAAKyD,QAAO,KAAMC,QAAS,IAAGD,QAAS,KAAI,IAAI,CAAClB,SAAS,EAAE;QACnE,IAAI,CAACA,SAAQ,GAAIkB,QAAQ;AAC7B;KACH;AACDnE,IAAAA,KAAK,EAAE;AACHqE,MAAAA,OAAO,WAAPA,OAAOA,CAACF,QAAQ,EAAEC,QAAQ,EAAE;AACxB,QAAA,IAAI,CAACA,QAAS,IAAGA,QAAQ,CAACE,MAAO,KAAI,CAACH,QAAO,IAAK,EAAE,EAAEG,MAAM,EAAE;UAC1D,IAAI,CAACC,IAAI,EAAE;UACX,IAAI,CAACC,iBAAiB,EAAE;AAC5B;OACH;AACDC,MAAAA,IAAI,EAAE;KACT;IACDvE,QAAQ,EAAA,SAARA,QAAQA,GAAG;MACP,IAAI,CAACqE,IAAI,EAAE;MACX,IAAI,CAACC,iBAAiB,EAAE;KAC3B;IACDlE,WAAW,EAAA,SAAXA,WAAWA,GAAG;MACV,IAAI,CAACuC,gBAAgB,IAAI,CAACP,MAAM,EAAG,GAAE;AAAEQ,QAAAA,GAAG,EAAE,CAAC;AAAEC,QAAAA,IAAI,EAAE;AAAE,OAAE,GAAE,CAAC;KAC/D;IACD3C,YAAY,EAAA,SAAZA,YAAYA,GAAG;MACX,IAAI,CAACmE,IAAI,EAAE;MACX,IAAI,CAACC,iBAAiB,EAAE;KAC3B;IACDnE,WAAW,EAAA,SAAXA,WAAWA,GAAG;MACV,IAAI,CAACkE,IAAI,EAAE;MACX,IAAI,CAACC,iBAAiB,EAAE;AAC5B;GACH;EACDE,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACC,QAAQ,EAAE;IAEf,IAAI,CAAC9B,gBAAgB,IAAI,CAACP,MAAM,EAAG,GAAE;AAAEQ,MAAAA,GAAG,EAAE,CAAC;AAAEC,MAAAA,IAAI,EAAE;AAAE,KAAE,GAAE,CAAC;IAC5D,IAAI,CAACe,gBAAgB,IAAI,CAACA,iBAAiB,EAAE;GAChD;EACDc,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,CAAC,IAAI,CAACX,WAAU,IAAK,IAAI,CAACU,QAAQ,EAAE;GACvC;EACDE,SAAS,EAAA,SAATA,SAASA,GAAG;IACR,IAAI,CAACC,oBAAoB,EAAE;IAE3B,IAAI,CAACb,WAAY,GAAE,KAAK;GAC3B;AACDc,EAAAA,OAAO,EAAE;IACLJ,QAAQ,EAAA,SAARA,QAAQA,GAAG;AACP,MAAA,IAAIK,SAAS,CAAC,IAAI,CAAC3B,OAAO,CAAC,EAAE;AACzB,QAAA,IAAI,CAAC4B,YAAY,CAAC,IAAI,CAAC3B,OAAO,CAAC;QAC/B,IAAI,CAACiB,IAAI,EAAE;QACX,IAAI,CAACC,iBAAiB,EAAE;QAExB,IAAI,CAACf,YAAW,GAAIyB,QAAQ,CAAC,IAAI,CAAC7B,OAAO,CAAC;QAC1C,IAAI,CAACK,aAAY,GAAIyB,SAAS,CAAC,IAAI,CAAC9B,OAAO,CAAC;QAC5C,IAAI,CAACM,sBAAsBuB,QAAQ,CAAC,IAAI,CAAC5B,OAAO,CAAC;QACjD,IAAI,CAACM,uBAAuBuB,SAAS,CAAC,IAAI,CAAC7B,OAAO,CAAC;QACnD,IAAI,CAACW,WAAU,GAAI,IAAI;AAC3B;MAEA,IAAI,IAAI,CAACZ,OAAO,EAAE;QACd,IAAI,CAAC+B,kBAAkB,EAAE;AAC7B;KACH;IACDb,IAAI,EAAA,SAAJA,IAAIA,GAAG;AACH,MAAA,IAAI,CAAC,IAAI,CAAC3D,QAAQ,EAAE;QAChB,IAAI,CAACyE,OAAO,EAAE;QACd,IAAI,CAACC,gBAAgB,EAAE;QACvB,IAAI,CAACC,aAAa,EAAE;AACxB;KACH;IACDC,UAAU,EAAA,SAAVA,UAAUA,GAAG;AACT,MAAA,OAAO,IAAI,CAAClF,WAAY,KAAI,UAAU;KACzC;IACDmF,YAAY,EAAA,SAAZA,YAAYA,GAAG;AACX,MAAA,OAAO,IAAI,CAACnF,WAAY,KAAI,YAAY;KAC3C;IACDgC,MAAM,EAAA,SAANA,MAAMA,GAAG;AACL,MAAA,OAAO,IAAI,CAAChC,WAAY,KAAI,MAAM;KACrC;AACDoF,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAACC,OAAO,EAAE;AACd;MACA,IAAI,CAACtC,OAAQ,IAAG,IAAI,CAACA,OAAO,CAACqC,QAAQ,CAACC,OAAO,CAAC;KACjD;AACDC,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACC,KAAK,EAAqB;AAAA,MAAA,IAAAC,KAAA,GAAA,IAAA;AAAA,MAAA,IAAnBC,QAAS,GAAAC,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAE,MAAM;AAClC,MAAA,IAAM3D,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,MAAA,IAAM4D,aAAa,IAAI,CAACT,YAAY,EAAE;MACtC,IAAMU,KAAM,GAAE9D,OAAOwD,KAAK,CAACO,KAAK,CAAC,UAACC,CAAC,EAAA;QAAA,OAAKA,CAAE,GAAE,EAAE;AAAA,OAAA,CAAA,GAAIR,KAAI,GAAI,EAAE;AAE5D,MAAA,IAAIM,KAAK,EAAE;AACP,QAAA,IAAM5D,KAAI,GAAI,IAAI,CAACA,KAAK;AACxB,QAAA,IAAA+D,aAAA,GAA0C,IAAI,CAACjD,OAAO;UAAAkD,qBAAA,GAAAD,aAAA,CAA9CE,SAAU;AAAVA,UAAAA,SAAU,GAAAD,qBAAA,KAAE,MAAA,GAAA,CAAC,GAAAA,qBAAA;UAAAE,qBAAA,GAAAH,aAAA,CAAEI;AAAAA,UAAAA,uCAAa,MAAA,GAAA,CAAA,GAAAD,qBAAA;AACpC,QAAA,IAAAE,qBAAA,GAA8B,IAAI,CAACC,iBAAiB,EAAE;UAA9CrG,iBAAkB,GAAAoG,qBAAA,CAAlBpG,iBAAkB;AAC1B,QAAA,IAAMsG,aAAa,IAAI,CAACC,kBAAkB,EAAE;AAC5C,QAAA,IAAM5G,QAAO,GAAI,IAAI,CAACA,QAAQ;AAC9B,QAAA,IAAM6G,cAAa,GAAI,SAAjBA,cAAaA,GAAA;AAAA,UAAA,IAAKC,MAAK,GAAAhB,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAI,CAAC;UAAA,IAAEiB,KAAK,GAAAjB,SAAA,CAAA1B,MAAA,GAAA0B,CAAAA,GAAAA,SAAA,MAAAC,SAAA;AAAA,UAAA,OAAMe,MAAK,IAAKC,KAAM,GAAE,CAAE,GAAED,MAAM;SAAC;QAC5E,IAAME,cAAe,GAAE,SAAjBA,cAAeA,CAAGC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAA;AAAA,UAAA,OAAKF,MAAO,GAAEC,KAAM,GAAEC,KAAK;AAAA,SAAA;AACvE,QAAA,IAAM3B,QAAO,GAAI,SAAXA,QAAOA,GAAA;AAAA,UAAA,IAAK3C,IAAG,GAAAiD,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAI,CAAC;AAAA,UAAA,IAAElD,GAAE,GAAAkD,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAI,CAAC;UAAA,OAAKF,KAAI,CAACJ,QAAQ,CAAC;AAAE3C,YAAAA,IAAI,EAAJA,IAAI;AAAED,YAAAA,GAAG,EAAHA,GAAG;AAAEiD,YAAAA,QAAO,EAAPA;AAAS,WAAC,CAAC;AAAA,SAAA;QAC9E,IAAIuB,QAAS,GAAEjF,IAAK,GAAE;AAAEG,UAAAA,IAAI,EAAE,CAAC;AAAEC,UAAAA,IAAI,EAAE;AAAE,SAAA,GAAI,CAAC;QAC9C,IAAIoB,cAAa,GAAI,KAAK;AACtB0D,UAAAA,eAAgB,GAAE,KAAK;AAE3B,QAAA,IAAIlF,IAAI,EAAE;AACNiF,UAAAA,QAAS,GAAE;AAAE9E,YAAAA,IAAI,EAAEuE,cAAc,CAAClB,KAAK,CAAC,CAAC,CAAC,EAAEtF,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAAEkC,IAAI,EAAEsE,cAAc,CAAClB,KAAK,CAAC,CAAC,CAAC,EAAEtF,iBAAiB,CAAC,CAAC,CAAC;WAAG;AACzHmF,UAAAA,QAAQ,CAACwB,cAAc,CAACI,QAAQ,CAAC7E,IAAI,EAAEvC,QAAQ,CAAC,CAAC,CAAC,EAAE2G,UAAU,CAAC9D,IAAI,CAAC,EAAEmE,cAAc,CAACI,QAAQ,CAAC9E,IAAI,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAE2G,UAAU,CAAC/D,GAAG,CAAC,CAAC;AACjIyE,UAAAA,eAAc,GAAI,IAAI,CAAC1E,aAAa,CAACC,GAAE,KAAM0D,aAAa,IAAI,CAAC3D,aAAa,CAACE,SAAS2D,UAAU;AAChG7C,UAAAA,iBAAiByD,QAAQ,CAAC9E,IAAK,KAAID,KAAK,CAACC,QAAQ8E,QAAQ,CAAC7E,IAAG,KAAMF,KAAK,CAACE,IAAI;AACjF,SAAE,MAAK;AACH6E,UAAAA,QAAO,GAAIP,cAAc,CAAClB,KAAK,EAAEtF,iBAAiB,CAAC;AACnD2F,UAAAA,UAAS,GAAIR,QAAQ,CAACwB,cAAc,CAACI,QAAQ,EAAEpH,QAAQ,EAAE2G,UAAU,CAAC9D,IAAI,CAAC,EAAEyD,SAAS,CAAE,GAAEd,QAAQ,CAACgB,UAAU,EAAEQ,cAAc,CAACI,QAAQ,EAAEpH,QAAQ,EAAE2G,UAAU,CAAC/D,GAAG,CAAC,CAAC;UAChKyE,kBAAkB,IAAI,CAAC1E,aAAc,MAAKqD,UAAS,GAAIQ,UAAS,GAAIF,SAAS,CAAC;UAC9E3C,cAAe,GAAEyD,QAAO,KAAM/E,KAAK;AACvC;QAEA,IAAI,CAACsB,cAAa,GAAIA,cAAc;AACpC0D,QAAAA,eAAgB,KAAI,IAAI,CAAChF,KAAM,GAAE+E,QAAQ,CAAC;AAC9C;KACH;AACDE,IAAAA,YAAY,WAAZA,YAAYA,CAAC3B,KAAK,EAAE4B,EAAE,EAAqB;AAAA,MAAA,IAAAC,MAAA,GAAA,IAAA;AAAA,MAAA,IAAnB3B,QAAS,GAAAC,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAE,MAAM;AACrC,MAAA,IAAIyB,EAAE,EAAE;AACJ,QAAA,IAAMpF,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,QAAA,IAAM4D,aAAa,IAAI,CAACT,YAAY,EAAE;QACtC,IAAMU,KAAM,GAAE9D,OAAOwD,KAAK,CAACO,KAAK,CAAC,UAACC,CAAC,EAAA;UAAA,OAAKA,CAAE,GAAE,EAAE;AAAA,SAAA,CAAA,GAAIR,KAAI,GAAI,EAAE;AAE5D,QAAA,IAAIM,KAAK,EAAE;AACP,UAAA,IAAAwB,qBAAA,GAA4B,IAAI,CAACC,gBAAgB,EAAE;YAA3CrF,KAAK,GAAAoF,qBAAA,CAALpF,KAAK;YAAEsF,iCAAAA;AACf,UAAA,IAAMnC,QAAO,GAAI,SAAXA,QAAOA,GAAA;AAAA,YAAA,IAAK3C,IAAG,GAAAiD,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAI,CAAC;AAAA,YAAA,IAAElD,GAAE,GAAAkD,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAI,CAAC;YAAA,OAAK0B,MAAI,CAAChC,QAAQ,CAAC;AAAE3C,cAAAA,IAAI,EAAJA,IAAI;AAAED,cAAAA,GAAG,EAAHA,GAAG;AAAEiD,cAAAA,QAAO,EAAPA;AAAS,aAAC,CAAC;AAAA,WAAA;AAC9E,UAAA,IAAM+B,SAAU,GAAEL,EAAC,KAAM,UAAU;AACnC,UAAA,IAAMM,OAAQ,GAAEN,EAAG,KAAI,QAAQ;AAE/B,UAAA,IAAIK,SAAS,EAAE;AACX,YAAA,IAAIzF,IAAI,EAAE;AACN,cAAA,IAAIwF,QAAQ,CAACtF,KAAK,CAACC,IAAK,GAAED,KAAK,CAACC,OAAOqD,KAAK,CAAC,CAAC,CAAC,EAAE;AAC7CH,gBAAAA,QAAQ,CAACmC,QAAQ,CAACtF,KAAK,CAACE,IAAK,GAAE,IAAI,CAACvC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC2H,QAAQ,CAACtF,KAAK,CAACC,IAAK,GAAE,CAAC,IAAI,IAAI,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClG,eAAE,MAAK,IAAI2H,QAAQ,CAACtF,KAAK,CAACE,IAAG,GAAIF,KAAK,CAACE,IAAK,GAAEoD,KAAK,CAAC,CAAC,CAAC,EAAE;AACpDH,gBAAAA,QAAQ,CAAC,CAACmC,QAAQ,CAACtF,KAAK,CAACE,IAAG,GAAI,CAAC,IAAI,IAAI,CAACvC,QAAQ,CAAC,CAAC,CAAC,EAAE2H,QAAQ,CAACtF,KAAK,CAACC,IAAK,GAAE,IAAI,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClG;AACJ,aAAE,MAAK;AACH,cAAA,IAAI2H,QAAQ,CAACtF,KAAI,GAAIA,KAAI,GAAIsD,KAAK,EAAE;gBAChC,IAAMmC,GAAI,GAAE,CAACH,QAAQ,CAACtF,KAAM,GAAE,CAAC,IAAI,IAAI,CAACrC,QAAQ;AAEhDgG,gBAAAA,UAAS,GAAIR,QAAQ,CAACsC,GAAG,EAAE,CAAC,CAAA,GAAItC,QAAQ,CAAC,CAAC,EAAEsC,GAAG,CAAC;AACpD;AACJ;WACJ,MAAO,IAAID,OAAO,EAAE;AAChB,YAAA,IAAI1F,IAAI,EAAE;AACN,cAAA,IAAIwF,QAAQ,CAACnF,IAAI,CAACF,IAAK,GAAED,KAAK,CAACC,QAAQqD,KAAK,CAAC,CAAC,CAAE,GAAE,CAAC,EAAE;AACjDH,gBAAAA,QAAQ,CAACmC,QAAQ,CAACtF,KAAK,CAACE,IAAK,GAAE,IAAI,CAACvC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC2H,QAAQ,CAACtF,KAAK,CAACC,IAAK,GAAE,CAAC,IAAI,IAAI,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClG,eAAE,MAAK,IAAI2H,QAAQ,CAACnF,IAAI,CAACD,OAAOF,KAAK,CAACE,IAAK,IAAGoD,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,EAAE;AACxDH,gBAAAA,QAAQ,CAAC,CAACmC,QAAQ,CAACtF,KAAK,CAACE,IAAG,GAAI,CAAC,IAAI,IAAI,CAACvC,QAAQ,CAAC,CAAC,CAAC,EAAE2H,QAAQ,CAACtF,KAAK,CAACC,IAAK,GAAE,IAAI,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClG;AACJ,aAAE,MAAK;cACH,IAAI2H,QAAQ,CAACnF,IAAG,GAAIH,KAAI,IAAKsD,KAAM,GAAE,CAAC,EAAE;gBACpC,IAAMmC,KAAI,GAAE,CAACH,QAAQ,CAACtF,KAAM,GAAE,CAAC,IAAI,IAAI,CAACrC,QAAQ;AAEhDgG,gBAAAA,UAAS,GAAIR,QAAQ,CAACsC,KAAG,EAAE,CAAC,CAAA,GAAItC,QAAQ,CAAC,CAAC,EAAEsC,KAAG,CAAC;AACpD;AACJ;AACJ;AACJ;AACJ,OAAE,MAAK;AACH,QAAA,IAAI,CAACpC,aAAa,CAACC,KAAK,EAAEE,QAAQ,CAAC;AACvC;KACH;IACD6B,gBAAgB,EAAA,SAAhBA,gBAAgBA,GAAG;AACf,MAAA,IAAMK,wBAAuB,GAAI,SAA3BA,wBAAuBA,CAAKC,IAAI,EAAEd,KAAK,EAAA;QAAA,OAAKe,IAAI,CAACC,KAAK,CAACF,IAAK,IAAGd,KAAM,IAAGc,IAAI,CAAC,CAAC;AAAA,OAAA;AAEpF,MAAA,IAAIG,eAAc,GAAI,IAAI,CAAC9F,KAAK;MAChC,IAAI+F,cAAa,GAAI,CAAC;MAEtB,IAAI,IAAI,CAACjF,OAAO,EAAE;AACd,QAAA,IAAMhB,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,QAAA,IAAM4D,aAAa,IAAI,CAACT,YAAY,EAAE;AACtC,QAAA,IAAA8C,cAAA,GAAkC,IAAI,CAAClF,OAAO;UAAtCmD,SAAS,GAAA+B,cAAA,CAAT/B,SAAS;UAAEE,UAAW,GAAA6B,cAAA,CAAX7B,UAAW;AAE9B,QAAA,IAAIrE,IAAI,EAAE;AACNgG,UAAAA,eAAgB,GAAE;YAAE7F,IAAI,EAAEyF,wBAAwB,CAACzB,SAAS,EAAE,IAAI,CAACtG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAAEuC,IAAI,EAAEwF,wBAAwB,CAACvB,UAAU,EAAE,IAAI,CAACxG,QAAQ,CAAC,CAAC,CAAC;WAAG;AAC/IoI,UAAAA,cAAe,GAAE;YAAE9F,IAAI,EAAE6F,eAAe,CAAC7F,IAAG,GAAI,IAAI,CAACI,kBAAkB,CAACJ,IAAI;YAAEC,IAAI,EAAE4F,eAAe,CAAC5F,IAAK,GAAE,IAAI,CAACG,kBAAkB,CAACH;WAAM;AAC7I,SAAE,MAAK;AACH,UAAA,IAAM+F,SAAQ,GAAItC,UAAW,GAAEQ,aAAaF,SAAS;UAErD6B,eAAc,GAAIJ,wBAAwB,CAACO,SAAS,EAAE,IAAI,CAACtI,QAAQ,CAAC;AACpEoI,UAAAA,iBAAiBD,eAAc,GAAI,IAAI,CAACzF,kBAAkB;AAC9D;AACJ;MAEA,OAAO;QACHL,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBG,IAAI,EAAE,IAAI,CAACA,IAAI;AACfmF,QAAAA,QAAQ,EAAE;AACNtF,UAAAA,KAAK,EAAE8F,eAAe;AACtB3F,UAAAA,IAAI,EAAE4F;AACV;OACH;KACJ;IACD1B,iBAAiB,EAAA,SAAjBA,iBAAiBA,GAAG;AAChB,MAAA,IAAMvE,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,MAAA,IAAM4D,aAAa,IAAI,CAACT,YAAY,EAAE;AACtC,MAAA,IAAMvF,QAAO,GAAI,IAAI,CAACA,QAAQ;AAC9B,MAAA,IAAM2G,aAAa,IAAI,CAACC,kBAAkB,EAAE;AAC5C,MAAA,IAAM2B,YAAW,GAAI,IAAI,CAACpF,OAAQ,GAAE,IAAI,CAACA,OAAO,CAACqF,WAAU,GAAI7B,UAAU,CAAC9D,IAAG,GAAI,CAAC;AAClF,MAAA,IAAM4F,aAAY,GAAI,IAAI,CAACtF,OAAQ,GAAE,IAAI,CAACA,OAAO,CAACuF,YAAa,GAAE/B,UAAU,CAAC/D,GAAE,GAAI,CAAC;AACnF,MAAA,IAAM+F,2BAA4B,GAAE,SAA9BA,2BAA4BA,CAAGC,YAAY,EAAEC,SAAS,EAAA;QAAA,OAAKZ,IAAI,CAACa,IAAI,CAACF,YAAW,IAAKC,SAAU,IAAGD,YAAY,CAAC,CAAC;AAAA,OAAA;AACtH,MAAA,IAAMG,6BAA6B,SAA7BA,2BAA8BC,SAAS,EAAA;AAAA,QAAA,OAAKf,IAAI,CAACa,IAAI,CAACE,SAAU,GAAE,CAAC,CAAC;AAAA,OAAA;MAC1E,IAAMtG,kBAAmB,GAAEP,IAAG,GACxB;QAAEG,IAAI,EAAEqG,2BAA2B,CAACF,aAAa,EAAEzI,QAAQ,CAAC,CAAC,CAAC,CAAC;QAAEuC,IAAI,EAAEoG,2BAA2B,CAACJ,YAAY,EAAEvI,QAAQ,CAAC,CAAC,CAAC;OAAE,GAC9H2I,2BAA2B,CAAC3C,UAAW,GAAEuC,YAAW,GAAIE,aAAa,EAAEzI,QAAQ,CAAC;AAEtF,MAAA,IAAMK,iBAAkB,GAAE,IAAI,CAACyC,mBAAkB,KAAMX,IAAG,GAAI,CAAC4G,0BAA0B,CAACrG,kBAAkB,CAACJ,IAAI,CAAC,EAAEyG,0BAA0B,CAACrG,kBAAkB,CAACH,IAAI,CAAC,IAAIwG,0BAA0B,CAACrG,kBAAkB,CAAC,CAAC;MAE1N,OAAO;AAAEA,QAAAA,kBAAkB,EAAlBA,kBAAkB;AAAErC,QAAAA,mBAAAA;OAAmB;KACnD;IACD+E,gBAAgB,EAAA,SAAhBA,gBAAgBA,GAAG;AAAA,MAAA,IAAA6D,MAAA,GAAA,IAAA;AACf,MAAA,IAAM9G,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,MAAA,IAAMC,KAAI,GAAI,IAAI,CAACA,KAAK;AACxB,MAAA,IAAA6G,sBAAA,GAAkD,IAAI,CAACxC,iBAAiB,EAAE;QAAlEhE,kBAAkB,GAAAwG,sBAAA,CAAlBxG,kBAAkB;QAAErC,iBAAgB,GAAA6I,sBAAA,CAAhB7I,iBAAgB;MAC5C,IAAM8I,aAAY,GAAI,SAAhBA,aAAYA,CAAKlC,MAAM,EAAEmC,IAAI,EAAErC,KAAK,EAAA;AAAA,QAAA,IAAEsC,OAAQ,GAAAvD,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAE,KAAK;QAAA,OAAKmD,MAAI,CAACK,OAAO,CAACrC,MAAK,GAAImC,OAAO,CAACnC,MAAO,GAAEF,QAAQ,CAAA,GAAI,CAAC,IAAIA,KAAK,EAAEsC,OAAO,CAAC;AAAA,OAAA;MACvI,IAAM7G,IAAK,GAAEL,IAAG,GACV;AAAEG,QAAAA,IAAI,EAAE6G,aAAa,CAAC9G,KAAK,CAACC,IAAI,EAAEI,kBAAkB,CAACJ,IAAI,EAAEjC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAAEkC,QAAAA,IAAI,EAAE4G,aAAa,CAAC9G,KAAK,CAACE,IAAI,EAAEG,kBAAkB,CAACH,IAAI,EAAElC,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI;OAAE,GACvK8I,aAAa,CAAC9G,KAAK,EAAEK,kBAAkB,EAAErC,iBAAiB,CAAC;MAEjE,IAAI,CAACmC,IAAG,GAAIA,IAAI;MAChB,IAAI,CAACE,kBAAiB,GAAIA,kBAAkB;MAC5C,IAAI,CAACI,sBAAsBzC,iBAAiB;MAC5C,IAAI,CAACkJ,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAACzG,mBAAmB,CAAC;MAEhE,IAAI,IAAI,CAAC/B,UAAU,EAAE;QACjB,IAAI,CAACiC,SAAQ,GAAIb,IAAG,GAAIpC,KAAK,CAACyJ,IAAI,CAAC;UAAEpF,MAAM,EAAE1B,kBAAkB,CAACJ;SAAM,CAAC,CAACmH,GAAG,CAAC,YAAA;UAAA,OAAM1J,KAAK,CAACyJ,IAAI,CAAC;YAAEpF,MAAM,EAAE1B,kBAAkB,CAACH;AAAK,WAAC,CAAC;AAAA,SAAA,CAAE,GAAExC,KAAK,CAACyJ,IAAI,CAAC;AAAEpF,UAAAA,MAAM,EAAE1B;AAAmB,SAAC,CAAC;AACnL;MAEA,IAAI,IAAI,CAAClC,IAAI,EAAE;AACXkJ,QAAAA,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,YAAM;AAAA,UAAA,IAAAC,YAAA;UACzBZ,MAAI,CAACrF,gBAAgB;AACjBvB,YAAAA,KAAK,EAAE4G,MAAI,CAAC/H,IAAK,GAAGiB,OAAO;AAAEG,cAAAA,IAAI,EAAE,CAAC;cAAEC,IAAI,EAAEF,KAAK,CAACE;aAAK,GAAI,CAAC,GAAIF,KAAK;AACrEG,YAAAA,IAAI,EAAEyF,IAAI,CAAC6B,GAAG,CAACb,MAAI,CAAC/H,IAAK,GAAE+H,MAAI,CAAC/H,IAAG,GAAIsB,IAAI,EAAE,CAAA,CAAAqH,YAAA,GAAAZ,MAAI,CAACnJ,KAAK,MAAA,IAAA,IAAA+J,YAAA,KAAA,MAAA,GAAA,MAAA,GAAVA,YAAA,CAAYzF,MAAK,KAAK,CAAC;WACvE;UAED6E,MAAI,CAACM,KAAK,CAAC,WAAW,EAAEN,MAAI,CAACrF,aAAa,CAAC;AAC/C,SAAC,CAAC;AACN;KACH;IACDU,iBAAiB,EAAA,SAAjBA,iBAAiBA,GAAG;AAAA,MAAA,IAAAyF,MAAA,GAAA,IAAA;MAChB,IAAI,IAAI,CAAC3I,QAAS,IAAG,CAAC,IAAI,CAAC2B,SAAS,EAAE;AAClC2G,QAAAA,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,YAAM;UACzB,IAAIG,MAAI,CAAC3G,OAAO,EAAE;AACd,YAAA,IAAMjB,OAAO4H,MAAI,CAAC3H,MAAM,EAAE;AAC1B,YAAA,IAAM4D,aAAa+D,MAAI,CAACxE,YAAY,EAAE;AACtC,YAAA,IAAMyE,QAAO,GAAID,MAAI,CAACzE,UAAU,EAAE;AAElCyE,YAAAA,MAAI,CAAC3G,OAAO,CAACvD,KAAK,CAACoK,SAAU,GAAEF,MAAI,CAAC3G,OAAO,CAACvD,KAAK,CAACqK,QAAS,GAAE,MAAM;AACnEH,YAAAA,MAAI,CAAC3G,OAAO,CAACvD,KAAK,CAACsK,WAAW,UAAU;AACxCJ,YAAAA,MAAI,CAAC5G,OAAO,CAACtD,KAAK,CAACuK,OAAQ,GAAE,MAAM;;AAEnC;;AAEA;;AAGA,YAAA,IAAAC,IAAA,GAAwB,CAACrF,QAAQ,CAAC+E,MAAI,CAAC5G,OAAO,CAAC,EAAE8B,SAAS,CAAC8E,MAAI,CAAC5G,OAAO,CAAC,CAAC;AAAlEmH,cAAAA,KAAK,GAAAD,IAAA,CAAA,CAAA,CAAA;AAAEE,cAAAA,MAAM,GAAAF,IAAA,CAAA,CAAA,CAAA;AAEpB,YAAA,CAAClI,IAAK,IAAG6D,UAAU,MAAM+D,MAAI,CAAC5G,OAAO,CAACtD,KAAK,CAACyK,KAAI,GAAIA,KAAI,GAAIP,MAAI,CAACxG,YAAa,GAAE+G,KAAM,GAAE,IAAG,GAAIP,MAAI,CAAC5J,WAAU,IAAK4J,MAAI,CAACxG,YAAa,GAAE,IAAI,CAAC;AAC5I,YAAA,CAACpB,IAAG,IAAK6H,QAAQ,MAAMD,MAAI,CAAC5G,OAAO,CAACtD,KAAK,CAAC0K,MAAO,GAAEA,MAAK,GAAIR,MAAI,CAACvG,aAAc,GAAE+G,MAAK,GAAI,IAAK,GAAER,MAAI,CAAC7J,YAAa,IAAG6J,MAAI,CAACvG,aAAc,GAAE,IAAI,CAAC;AAEhJuG,YAAAA,MAAI,CAAC3G,OAAO,CAACvD,KAAK,CAACoK,SAAU,GAAEF,MAAI,CAAC3G,OAAO,CAACvD,KAAK,CAACqK,QAAO,GAAI,EAAE;AAC/DH,YAAAA,MAAI,CAAC3G,OAAO,CAACvD,KAAK,CAACsK,QAAO,GAAI,EAAE;AAChCJ,YAAAA,MAAI,CAAC5G,OAAO,CAACtD,KAAK,CAACuK,OAAM,GAAI,EAAE;AACnC;AACJ,SAAC,CAAC;AACN;KACH;IACDd,OAAO,EAAA,SAAPA,OAAOA,GAAmB;MAAA,IAAAkB,KAAA,EAAAC,WAAA;AAAA,MAAA,IAAlBjI,2EAAO,CAAC;MAAA,IAAEkI,MAAM,GAAA5E,SAAA,CAAA1B,MAAA,GAAA0B,CAAAA,GAAAA,SAAA,MAAAC,SAAA;MACpB,OAAO,IAAI,CAACjG,QAAQmI,IAAI,CAAC6B,GAAG,CAACY,SAAS,CAAA,CAAAF,KAAA,GAAC,IAAI,CAAC5J,OAAQ,IAAG,IAAI,CAACd,KAAK,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA0K,KAAA,KAA9BA,MAAAA,GAAAA,MAAAA,GAAAA,KAAA,CAAiCpG,MAAK,KAAK,CAAA,GAAI,CAAA,CAAAqG,WAAA,GAAA,IAAI,CAAC3K,KAAK,cAAA2K,WAAA,KAAA,MAAA,GAAA,MAAA,GAAVA,WAAA,CAAYrG,MAAO,KAAG,CAAC,EAAE5B,IAAI,CAAE,GAAE,CAAC;KAC1H;IACDoE,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;MACjB,IAAI,IAAI,CAACxD,OAAO,EAAE;AACd,QAAA,IAAMvD,KAAM,GAAE8K,gBAAgB,CAAC,IAAI,CAACvH,OAAO,CAAC;QAC5C,IAAMP,IAAK,GAAE+H,UAAU,CAAC/K,KAAK,CAACgL,WAAW,CAAE,GAAE5C,IAAI,CAAC6C,GAAG,CAACF,UAAU,CAAC/K,KAAK,CAACgD,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;QACrF,IAAMkI,KAAM,GAAEH,UAAU,CAAC/K,KAAK,CAACmL,YAAY,CAAE,GAAE/C,IAAI,CAAC6C,GAAG,CAACF,UAAU,CAAC/K,KAAK,CAACkL,KAAK,CAAE,IAAG,CAAC,EAAE,CAAC,CAAC;QACxF,IAAMnI,GAAE,GAAIgI,UAAU,CAAC/K,KAAK,CAACoL,UAAU,CAAA,GAAIhD,IAAI,CAAC6C,GAAG,CAACF,UAAU,CAAC/K,KAAK,CAAC+C,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;QAClF,IAAMsI,MAAK,GAAIN,UAAU,CAAC/K,KAAK,CAACsL,aAAa,CAAA,GAAIlD,IAAI,CAAC6C,GAAG,CAACF,UAAU,CAAC/K,KAAK,CAACqL,MAAM,CAAE,IAAG,CAAC,EAAE,CAAC,CAAC;QAE3F,OAAO;AAAErI,UAAAA,IAAI,EAAJA,IAAI;AAAEkI,UAAAA,KAAK,EAALA,KAAK;AAAEnI,UAAAA,GAAG,EAAHA,GAAG;AAAEsI,UAAAA,MAAM,EAANA,MAAM;UAAEE,CAAC,EAAEvI,OAAOkI,KAAK;UAAEM,CAAC,EAAEzI,GAAI,GAAEsI;SAAQ;AACzE;MAEA,OAAO;AAAErI,QAAAA,IAAI,EAAE,CAAC;AAAEkI,QAAAA,KAAK,EAAE,CAAC;AAAEnI,QAAAA,GAAG,EAAE,CAAC;AAAEsI,QAAAA,MAAM,EAAE,CAAC;AAAEE,QAAAA,CAAC,EAAE,CAAC;AAAEC,QAAAA,CAAC,EAAE;OAAG;KAC9D;IACDlG,OAAO,EAAA,SAAPA,OAAOA,GAAG;AAAA,MAAA,IAAAmG,MAAA,GAAA,IAAA;MACN,IAAI,IAAI,CAACnI,OAAO,EAAE;AACd,QAAA,IAAMhB,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,QAAA,IAAM4D,aAAa,IAAI,CAACT,YAAY,EAAE;AACtC,QAAA,IAAMgG,aAAc,GAAE,IAAI,CAACpI,OAAO,CAACoI,aAAa;AAChD,QAAA,IAAMjB,KAAI,GAAI,IAAI,CAACnK,yBAAkB,IAAI,CAACgD,OAAO,CAACqF,WAAU,IAAK+C,aAAa,CAAC/C,WAAW,EAAI,IAAA,CAAA;AAC9F,QAAA,IAAM+B,SAAS,IAAI,CAACrK,YAAa,OAAAsL,MAAA,CAAM,IAAI,CAACrI,OAAO,CAACuF,YAAW,IAAK6C,aAAa,CAAC7C,YAAY,EAAI,IAAA,CAAA;AAClG,QAAA,IAAM+C,OAAQ,GAAE,SAAVA,OAAQA,CAAGC,KAAK,EAAEC,MAAM,EAAA;UAAA,OAAML,MAAI,CAACnI,OAAO,CAACtD,KAAK,CAAC6L,KAAK,CAAA,GAAIC,MAAM;SAAC;QAEvE,IAAIxJ,IAAG,IAAK6D,UAAU,EAAE;AACpByF,UAAAA,OAAO,CAAC,QAAQ,EAAElB,MAAM,CAAC;AACzBkB,UAAAA,OAAO,CAAC,OAAO,EAAEnB,KAAK,CAAC;AAC3B,SAAE,MAAK;AACHmB,UAAAA,OAAO,CAAC,QAAQ,EAAElB,MAAM,CAAC;AAC7B;AACJ;KACH;IACDlF,aAAa,EAAA,SAAbA,aAAaA,GAAG;AAAA,MAAA,IAAAuG,MAAA,GAAA,IAAA;AACZ,MAAA,IAAM9L,KAAI,GAAI,IAAI,CAACA,KAAK;AAExB,MAAA,IAAIA,KAAK,EAAE;AACP,QAAA,IAAMqC,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,QAAA,IAAM4D,aAAa,IAAI,CAACT,YAAY,EAAE;AACtC,QAAA,IAAMoB,aAAa,IAAI,CAACC,kBAAkB,EAAE;QAC5C,IAAM6E,OAAQ,GAAE,SAAVA,OAAQA,CAAGC,KAAK,EAAEC,MAAM,EAAEzE,KAAK,EAAA;AAAA,UAAA,IAAEC,KAAI,GAAArB,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAI,CAAC;AAAA,UAAA,OAAM8F,MAAI,CAAC3I,WAAU,GAAA4I,aAAA,CAAAA,aAAA,CAAA,EAAA,EAASD,MAAI,CAAC3I,WAAW,CAAA,EAAA6I,eAAA,CAAAN,EAAAA,EAAAA,EAAAA,CAAAA,MAAA,CAAWE,KAAK,CAAK,EAAA,CAACC,MAAK,IAAK,EAAE,EAAEvH,MAAK,GAAI8C,KAAM,GAAEC,KAAI,GAAI,IAAK,CAAG,CAAA;SAAC;AAEpK,QAAA,IAAIhF,IAAI,EAAE;AACNsJ,UAAAA,OAAO,CAAC,QAAQ,EAAE3L,KAAK,EAAE,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAE2G,UAAU,CAAC0E,CAAC,CAAC;UACxDI,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC7K,OAAQ,IAAGd,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,EAAE2G,UAAU,CAACyE,CAAC,CAAC;AAC9E,SAAE,MAAK;AACHpF,UAAAA,UAAS,GAAIyF,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC7K,OAAQ,IAAGd,KAAK,EAAE,IAAI,CAACE,QAAQ,EAAE2G,UAAU,CAACyE,CAAC,CAAA,GAAIK,OAAO,CAAC,QAAQ,EAAE3L,KAAK,EAAE,IAAI,CAACE,QAAQ,EAAE2G,UAAU,CAAC0E,CAAC,CAAC;AAC7I;AACJ;KACH;AACDU,IAAAA,kBAAkB,EAAlBA,SAAAA,kBAAkBA,CAACjE,GAAG,EAAE;AAAA,MAAA,IAAAkE,MAAA,GAAA,IAAA;MACpB,IAAI,IAAI,CAAC5I,WAAW,CAAC,IAAI,CAACjC,UAAU,EAAE;AAClC,QAAA,IAAMgB,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,QAAA,IAAM4D,aAAa,IAAI,CAACT,YAAY,EAAE;QACtC,IAAMlD,KAAI,GAAIyF,GAAE,GAAIA,GAAG,CAACzF,QAAQ,IAAI,CAACA,KAAK;AAC1C,QAAA,IAAM4J,qBAAsB,GAAE,SAAxBA,qBAAsBA,CAAGhF,MAAM,EAAEC,KAAK,EAAA;UAAA,OAAKD,MAAO,GAAEC,KAAK;AAAA,SAAA;AAC/D,QAAA,IAAMgF,YAAW,GAAI,SAAfA,YAAWA,GAAA;AAAA,UAAA,IAAKC,EAAC,GAAArG,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAI,CAAC;AAAA,UAAA,IAAEsG,EAAG,GAAAtG,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAE,CAAC;UAAA,OAAMkG,MAAI,CAAC9I,YAAa,GAAA2I,aAAA,CAAAA,aAAA,CAAOG,EAAAA,EAAAA,MAAI,CAAC9I,YAAY,CAAK,EAAA;AAAEmJ,YAAAA,SAAS,iBAAAb,MAAA,CAAiBW,EAAE,EAAAX,MAAAA,CAAAA,CAAAA,MAAA,CAAOY,EAAE,EAAA,QAAA;AAAS,WAAE,CAAC;SAAC;AAE7I,QAAA,IAAIjK,IAAI,EAAE;AACN+J,UAAAA,YAAY,CAACD,qBAAqB,CAAC5J,KAAK,CAACE,IAAI,EAAE,IAAI,CAACvC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEiM,qBAAqB,CAAC5J,KAAK,CAACC,IAAI,EAAE,IAAI,CAACtC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1H,SAAE,MAAK;UACH,IAAMsM,YAAa,GAAEL,qBAAqB,CAAC5J,KAAK,EAAE,IAAI,CAACrC,QAAQ,CAAC;AAEhEgG,UAAAA,UAAW,GAAEkG,YAAY,CAACI,YAAY,EAAE,CAAC,CAAE,GAAEJ,YAAY,CAAC,CAAC,EAAEI,YAAY,CAAC;AAC9E;AACJ;KACH;AACDC,IAAAA,sBAAsB,EAAtBA,SAAAA,sBAAsBA,CAACC,KAAK,EAAE;AAAA,MAAA,IAAAC,MAAA,GAAA,IAAA;AAC1B,MAAA,IAAMC,MAAO,GAAEF,KAAK,CAACE,MAAM;AAC3B,MAAA,IAAMvK,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,MAAA,IAAM4D,aAAa,IAAI,CAACT,YAAY,EAAE;AACtC,MAAA,IAAMoB,aAAa,IAAI,CAACC,kBAAkB,EAAE;AAC5C,MAAA,IAAM+F,kBAAmB,GAAE,SAArBA,kBAAmBA,CAAG3E,IAAI,EAAEb,KAAK,EAAA;AAAA,QAAA,OAAMa,IAAG,GAAKA,OAAOb,KAAM,GAAEa,IAAK,GAAEb,KAAI,GAAIa,IAAI,GAAI,CAAC;OAAC;AAC7F,MAAA,IAAM4E,qBAAoB,GAAI,SAAxBA,qBAAoBA,CAAK5E,IAAI,EAAEd,KAAK,EAAA;QAAA,OAAKe,IAAI,CAACC,KAAK,CAACF,IAAK,IAAGd,KAAM,IAAGc,IAAI,CAAC,CAAC;AAAA,OAAA;AAEjF,MAAA,IAAM6E,qBAAoB,GAAI,SAAxBA,qBAAoBA,CAAKC,aAAa,EAAE7F,MAAM,EAAE8F,KAAK,EAAE3D,IAAI,EAAErC,KAAK,EAAEiG,oBAAoB,EAAK;AAC/F,QAAA,OAAOF,aAAc,IAAG/F,QAAQA,KAAM,GAAEiG,oBAAmB,GAAID,QAAQ3D,IAAG,GAAIrC,QAAQE,MAAK,GAAIF,KAAI,GAAI,CAAC;OAC3G;MAED,IAAMF,iBAAiB,SAAjBA,eAAkBiG,aAAa,EAAEG,aAAa,EAAEhG,MAAM,EAAE8F,KAAK,EAAE3D,IAAI,EAAErC,KAAK,EAAEiG,oBAAoB,EAAE3D,OAAO,EAAK;AAChH,QAAA,IAAIyD,aAAc,IAAG/F,KAAK,EAAE,OAAO,CAAC;AACpC,QAAA,IAAMmG,UAAW,GAAEjF,IAAI,CAAC6C,GAAG,CAAC,CAAC,EAAEkC,oBAAmB,GAAKF,aAAY,GAAIG,gBAAgBhG,MAAK,GAAI6F,aAAc,GAAE/F,KAAK,GAAI+F,aAAc,GAAEG,aAAY,GAAIhG,MAAK,GAAI6F,aAAc,GAAE,IAAI/F,KAAK,CAAC;QAC5L,IAAMoG,QAAS,GAAEV,MAAI,CAACnD,OAAO,CAAC4D,UAAU,EAAE7D,OAAO,CAAC;QAClD,IAAI6D,aAAaC,QAAQ,EAAE,OAAOA,QAAO,GAAI/D,IAAI,CAAA,KAC5C,OAAO8D,UAAU;OACzB;AAED,MAAA,IAAM/D,aAAY,GAAI,SAAhBA,aAAYA,CAAK2D,aAAa,EAAE7F,MAAM,EAAE8F,KAAK,EAAE3D,IAAI,EAAErC,KAAK,EAAEsC,OAAO,EAAK;QAC1E,IAAI+D,SAAU,GAAEnG,MAAO,GAAEmC,IAAG,GAAI,CAAA,GAAIrC,KAAK;QAEzC,IAAI+F,iBAAiB/F,KAAK,EAAE;UACxBqG,SAAQ,IAAKrG,KAAI,GAAI,CAAC;AAC1B;AAEA,QAAA,OAAO0F,MAAI,CAACnD,OAAO,CAAC8D,SAAS,EAAE/D,OAAO,CAAC;OAC1C;MAED,IAAM/C,YAAYqG,kBAAkB,CAACD,MAAM,CAACpG,SAAS,EAAEK,UAAU,CAAC/D,GAAG,CAAC;MACtE,IAAM4D,UAAW,GAAEmG,kBAAkB,CAACD,MAAM,CAAClG,UAAU,EAAEG,UAAU,CAAC9D,IAAI,CAAC;MAEzE,IAAIuE,QAAS,GAAEjF,IAAK,GAAE;AAAEG,QAAAA,IAAI,EAAE,CAAC;AAAEC,QAAAA,IAAI,EAAE;AAAE,OAAA,GAAI,CAAC;AAC9C,MAAA,IAAI8K,OAAM,GAAI,IAAI,CAAC7K,IAAI;MACvB,IAAImB,cAAa,GAAI,KAAK;AAC1B,MAAA,IAAI2J,eAAe,IAAI,CAAC3K,aAAa;AAErC,MAAA,IAAIR,IAAI,EAAE;QACN,IAAMoL,YAAa,GAAE,IAAI,CAAC5K,aAAa,CAACC,GAAI,IAAG0D,SAAS;QACxD,IAAMkH,aAAY,GAAI,IAAI,CAAC7K,aAAa,CAACE,QAAQ2D,UAAU;AAE3D,QAAA,IAAI,CAAC,IAAI,CAACrF,UAAS,IAAM,IAAI,CAACA,UAAS,KAAMoM,YAAa,IAAGC,aAAa,CAAE,EAAE;AAC1E,UAAA,IAAMC,YAAW,GAAI;YAAEnL,IAAI,EAAEsK,qBAAqB,CAACtG,SAAS,EAAE,IAAI,CAACtG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAAEuC,IAAI,EAAEqK,qBAAqB,CAACpG,UAAU,EAAE,IAAI,CAACxG,QAAQ,CAAC,CAAC,CAAC;WAAG;AAC5I,UAAA,IAAM0N,eAAe;AACjBpL,YAAAA,IAAI,EAAEuK,qBAAqB,CAACY,YAAY,CAACnL,IAAI,EAAE,IAAI,CAACD,KAAK,CAACC,IAAI,EAAE,IAAI,CAACE,IAAI,CAACF,IAAI,EAAE,IAAI,CAACI,kBAAkB,CAACJ,IAAI,EAAE,IAAI,CAACQ,mBAAmB,CAAC,CAAC,CAAC,EAAEyK,YAAY,CAAC;AACxJhL,YAAAA,IAAI,EAAEsK,qBAAqB,CAACY,YAAY,CAAClL,IAAI,EAAE,IAAI,CAACF,KAAK,CAACE,IAAI,EAAE,IAAI,CAACC,IAAI,CAACD,IAAI,EAAE,IAAI,CAACG,kBAAkB,CAACH,IAAI,EAAE,IAAI,CAACO,mBAAmB,CAAC,CAAC,CAAC,EAAE0K,aAAa;WAC3J;AAEDpG,UAAAA,WAAW;AACP9E,YAAAA,IAAI,EAAEuE,cAAc,CAAC4G,YAAY,CAACnL,IAAI,EAAEoL,YAAY,CAACpL,IAAI,EAAE,IAAI,CAACD,KAAK,CAACC,IAAI,EAAE,IAAI,CAACE,IAAI,CAACF,IAAI,EAAE,IAAI,CAACI,kBAAkB,CAACJ,IAAI,EAAE,IAAI,CAACQ,mBAAmB,CAAC,CAAC,CAAC,EAAEyK,YAAY,CAAC;AACpKhL,YAAAA,IAAI,EAAEsE,cAAc,CAAC4G,YAAY,CAAClL,IAAI,EAAEmL,YAAY,CAACnL,IAAI,EAAE,IAAI,CAACF,KAAK,CAACE,IAAI,EAAE,IAAI,CAACC,IAAI,CAACD,IAAI,EAAE,IAAI,CAACG,kBAAkB,CAACH,IAAI,EAAE,IAAI,CAACO,mBAAmB,CAAC,CAAC,CAAC,EAAE0K,aAAa,EAAE,IAAI;WAC7K;AACDH,UAAAA,UAAU;AACN/K,YAAAA,IAAI,EAAE6G,aAAa,CAACsE,YAAY,CAACnL,IAAI,EAAE8E,QAAQ,CAAC9E,IAAI,EAAE,IAAI,CAACE,IAAI,CAACF,IAAI,EAAE,IAAI,CAACI,kBAAkB,CAACJ,IAAI,EAAE,IAAI,CAACQ,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAChIP,YAAAA,IAAI,EAAE4G,aAAa,CAACsE,YAAY,CAAClL,IAAI,EAAE6E,QAAQ,CAAC7E,IAAI,EAAE,IAAI,CAACC,IAAI,CAACD,IAAI,EAAE,IAAI,CAACG,kBAAkB,CAACH,IAAI,EAAE,IAAI,CAACO,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI;WACxI;AAEDa,UAAAA,cAAa,GAAIyD,QAAQ,CAAC9E,IAAK,KAAI,IAAI,CAACD,KAAK,CAACC,IAAK,IAAG+K,OAAO,CAAC/K,IAAG,KAAM,IAAI,CAACE,IAAI,CAACF,IAAK,IAAG8E,QAAQ,CAAC7E,IAAK,KAAI,IAAI,CAACF,KAAK,CAACE,IAAK,IAAG8K,OAAO,CAAC9K,IAAG,KAAM,IAAI,CAACC,IAAI,CAACD,IAAK,IAAG,IAAI,CAACoB,cAAc;AACpL2J,UAAAA,YAAa,GAAE;AAAE1K,YAAAA,GAAG,EAAE0D,SAAS;AAAEzD,YAAAA,IAAI,EAAE2D;WAAY;AACvD;AACJ,OAAE,MAAK;AACH,QAAA,IAAM8B,SAAQ,GAAItC,UAAW,GAAEQ,aAAaF,SAAS;AACrD,QAAA,IAAMqH,mBAAoB,GAAE,IAAI,CAAChL,aAAc,IAAG2F,SAAS;QAE3D,IAAI,CAAC,IAAI,CAACnH,UAAW,IAAI,IAAI,CAACA,UAAS,IAAKwM,mBAAoB,EAAE;UAC9D,IAAMF,cAAW,GAAIb,qBAAqB,CAACtE,SAAS,EAAE,IAAI,CAACtI,QAAQ,CAAC;UACpE,IAAM0N,cAAW,GAAIb,qBAAqB,CAACY,cAAY,EAAE,IAAI,CAACpL,KAAK,EAAE,IAAI,CAACG,IAAI,EAAE,IAAI,CAACE,kBAAkB,EAAE,IAAI,CAACI,mBAAmB,EAAE6K,mBAAmB,CAAC;UAEvJvG,QAAS,GAAEP,cAAc,CAAC4G,cAAY,EAAEC,cAAY,EAAE,IAAI,CAACrL,KAAK,EAAE,IAAI,CAACG,IAAI,EAAE,IAAI,CAACE,kBAAkB,EAAE,IAAI,CAACI,mBAAmB,EAAE6K,mBAAmB,CAAC;AACpJN,UAAAA,OAAQ,GAAElE,aAAa,CAACsE,cAAY,EAAErG,QAAQ,EAAE,IAAI,CAAC5E,IAAI,EAAE,IAAI,CAACE,kBAAkB,EAAE,IAAI,CAACI,mBAAmB,CAAC;AAC7Ga,UAAAA,cAAa,GAAIyD,QAAS,KAAI,IAAI,CAAC/E,KAAI,IAAKgL,OAAQ,KAAI,IAAI,CAAC7K,QAAQ,IAAI,CAACmB,cAAc;AACxF2J,UAAAA,eAAehF,SAAS;AAC5B;AACJ;MAEA,OAAO;AACHjG,QAAAA,KAAK,EAAE+E,QAAQ;AACf5E,QAAAA,IAAI,EAAE6K,OAAO;AACb1J,QAAAA,cAAc,EAAdA,cAAc;AACd2E,QAAAA,SAAS,EAAEgF;OACd;KACJ;AACDM,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACpB,KAAK,EAAE;AAClB,MAAA,IAAAqB,qBAAA,GAAmD,IAAI,CAACtB,sBAAsB,CAACC,KAAK,CAAC;QAA7EnK,KAAK,GAAAwL,qBAAA,CAALxL,KAAK;QAAEG,IAAI,GAAAqL,qBAAA,CAAJrL,IAAI;QAAEmB,cAAc,GAAAkK,qBAAA,CAAdlK,cAAc;QAAE2E,kCAAAA;AAErC,MAAA,IAAI3E,cAAc,EAAE;AAChB,QAAA,IAAMmK,QAAO,GAAI;AAAEzL,UAAAA,KAAK,EAALA,KAAK;AAAEG,UAAAA,IAAG,EAAHA;SAAM;AAEhC,QAAA,IAAI,CAACuJ,kBAAkB,CAAC+B,QAAQ,CAAC;QAEjC,IAAI,CAACzL,KAAI,GAAIA,KAAK;QAClB,IAAI,CAACG,IAAG,GAAIA,IAAI;QAChB,IAAI,CAACG,aAAc,GAAE2F,SAAS;AAE9B,QAAA,IAAI,CAACiB,KAAK,CAAC,qBAAqB,EAAEuE,QAAQ,CAAC;QAE3C,IAAI,IAAI,CAACtN,QAAQ,IAAI,CAACuN,aAAa,CAAC1L,KAAK,CAAC,EAAE;UAAA,IAAA2L,YAAA,EAAAC,YAAA;AACxC,UAAA,IAAMrK,gBAAgB;AAClBvB,YAAAA,KAAK,EAAE,IAAI,CAACnB,IAAG,GAAI+G,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACoE,cAAc,CAAC7L,KAAK,CAAA,GAAI,IAAI,CAACnB,IAAI,EAAE,CAAC,CAAA,CAAA8M,YAAA,GAAI,IAAA,CAAClO,KAAK,MAAAkO,IAAAA,IAAAA,YAAA,uBAAVA,YAAA,CAAY5J,WAAU,CAAC,IAAI,IAAI,CAAClD,IAAI,CAAA,GAAImB,KAAK;AAClHG,YAAAA,IAAI,EAAEyF,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAAC5I,IAAK,GAAE,CAAC,IAAI,CAACgN,cAAc,CAAC7L,KAAK,IAAI,CAAC,IAAI,IAAI,CAACnB,IAAK,GAAEsB,IAAI,EAAE,EAAAyL,YAAA,GAAA,IAAI,CAACnO,KAAK,cAAAmO,YAAA,KAAA,MAAA,GAAA,MAAA,GAAVA,YAAA,CAAY7J,MAAO,KAAG,CAAC;WAC1G;UACD,IAAM+J,kBAAmB,GAAE,IAAI,CAACvK,aAAa,CAACvB,KAAI,KAAMuB,aAAa,CAACvB,KAAI,IAAK,IAAI,CAACuB,aAAa,CAACpB,IAAK,KAAIoB,aAAa,CAACpB,IAAI;UAE7H2L,kBAAiB,IAAK,IAAI,CAAC5E,KAAK,CAAC,WAAW,EAAE3F,aAAa,CAAC;UAC5D,IAAI,CAACA,aAAY,GAAIA,aAAa;AACtC;AACJ;KACH;AACDwK,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAAC5B,KAAK,EAAE;AAAA,MAAA,IAAA6B,MAAA,GAAA,IAAA;AACZ,MAAA,IAAI,CAAC9E,KAAK,CAAC,QAAQ,EAAEiD,KAAK,CAAC;MAE3B,IAAI,IAAI,CAAClM,KAAK,EAAE;QACZ,IAAI,IAAI,CAAC+C,aAAa,EAAE;AACpBiL,UAAAA,YAAY,CAAC,IAAI,CAACjL,aAAa,CAAC;AACpC;AAEA,QAAA,IAAI,IAAI,CAAC0K,aAAa,EAAE,EAAE;UACtB,IAAI,CAAC,IAAI,CAAChL,SAAQ,IAAK,IAAI,CAAChC,UAAU,EAAE;AACpC,YAAA,IAAAwN,sBAAA,GAA2B,IAAI,CAAChC,sBAAsB,CAACC,KAAK,CAAC;cAArD7I,cAAa,GAAA4K,sBAAA,CAAb5K,cAAa;AACrB,YAAA,IAAM6K,OAAQ,GAAE7K,cAAe,KAAI,IAAI,CAACzC,IAAG,GAAI,IAAI,CAAC6M,aAAa,EAAG,GAAE,KAAK,CAAC;AAE5ES,YAAAA,OAAM,KAAM,IAAI,CAACzL,SAAU,GAAE,IAAI,CAAC;AACtC;AAEA,UAAA,IAAI,CAACM,aAAc,GAAEoL,UAAU,CAAC,YAAM;AAClCJ,YAAAA,MAAI,CAACT,cAAc,CAACpB,KAAK,CAAC;AAE1B,YAAA,IAAI6B,MAAI,CAACtL,SAAU,IAAGsL,MAAI,CAACtN,UAAS,KAAM,CAACsN,MAAI,CAAC7N,IAAK,IAAG6N,MAAI,CAACxN,OAAM,KAAMkF,SAAS,CAAC,EAAE;cACjFsI,MAAI,CAACtL,SAAU,GAAE,KAAK;AACtBsL,cAAAA,MAAI,CAAC5L,IAAG,GAAI4L,MAAI,CAACH,cAAc,EAAE;AACrC;AACJ,WAAC,EAAE,IAAI,CAAC5N,KAAK,CAAC;AAClB;AACJ,OAAE,MAAK;AACH,QAAA,IAAI,CAACsN,cAAc,CAACpB,KAAK,CAAC;AAC9B;KACH;IACDkC,QAAQ,EAAA,SAARA,QAAQA,GAAG;AAAA,MAAA,IAAAC,MAAA,GAAA,IAAA;MACP,IAAI,IAAI,CAACrL,aAAa,EAAE;AACpBgL,QAAAA,YAAY,CAAC,IAAI,CAAChL,aAAa,CAAC;AACpC;AAEA,MAAA,IAAI,CAACA,aAAc,GAAEmL,UAAU,CAAC,YAAM;AAClC,QAAA,IAAI3J,SAAS,CAAC6J,MAAI,CAACxL,OAAO,CAAC,EAAE;AACzB,UAAA,IAAMhB,OAAOwM,MAAI,CAACvM,MAAM,EAAE;AAC1B,UAAA,IAAM4H,QAAO,GAAI2E,MAAI,CAACrJ,UAAU,EAAE;AAClC,UAAA,IAAMU,aAAa2I,MAAI,CAACpJ,YAAY,EAAE;AACtC,UAAA,IAAAqJ,KAAA,GAAwB,CAAC5J,QAAQ,CAAC2J,MAAI,CAACxL,OAAO,CAAC,EAAE8B,SAAS,CAAC0J,MAAI,CAACxL,OAAO,CAAC,CAAC;AAAlEmH,YAAAA,KAAK,GAAAsE,KAAA,CAAA,CAAA,CAAA;AAAErE,YAAAA,MAAM,GAAAqE,KAAA,CAAA,CAAA,CAAA;AACpB,UAAA,IAAOC,WAAW,GAAmBvE,UAAUqE,MAAI,CAACpL,YAAY;AAA5CuL,YAAAA,YAAY,GAAkCvE,MAAK,KAAMoE,MAAI,CAACnL,aAAa;AAC/F,UAAA,IAAMuL,SAAS5M,IAAG,GAAI0M,WAAU,IAAKC,YAAa,GAAE9I,aAAa6I,cAAc7E,WAAW8E,YAAa,GAAE,KAAK;AAE9G,UAAA,IAAIC,MAAM,EAAE;AACRJ,YAAAA,MAAI,CAAC7L,sBAAsB6L,MAAI,CAACtO,iBAAiB;YACjDsO,MAAI,CAACpL,YAAW,GAAI+G,KAAK;YACzBqE,MAAI,CAACnL,aAAc,GAAE+G,MAAM;YAC3BoE,MAAI,CAAClL,sBAAsBuB,QAAQ,CAAC2J,MAAI,CAACvL,OAAO,CAAC;YACjDuL,MAAI,CAACjL,uBAAuBuB,SAAS,CAAC0J,MAAI,CAACvL,OAAO,CAAC;YAEnDuL,MAAI,CAACtK,IAAI,EAAE;AACf;AACJ;AACJ,OAAC,EAAE,IAAI,CAAC9D,WAAW,CAAC;KACvB;IACD2E,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AAAA,MAAA,IAAA8J,MAAA,GAAA,IAAA;AACjB,MAAA,IAAI,CAAC,IAAI,CAACnL,cAAc,EAAE;QACtB,IAAI,CAACA,cAAa,GAAI,IAAI,CAAC6K,QAAQ,CAACO,IAAI,CAAC,IAAI,CAAC;QAE9CC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACtL,cAAc,CAAC;QACtDqL,MAAM,CAACC,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAACtL,cAAc,CAAC;AAEjE,QAAA,IAAI,CAACC,iBAAiB,IAAIsL,cAAc,CAAC,YAAM;UAC3CJ,MAAI,CAACN,QAAQ,EAAE;AACnB,SAAC,CAAC;QACF,IAAI,CAAC5K,cAAc,CAACuL,OAAO,CAAC,IAAI,CAAClM,OAAO,CAAC;AAC7C;KACH;IACDyB,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;MACnB,IAAI,IAAI,CAACf,cAAc,EAAE;QACrBqL,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACzL,cAAc,CAAC;QACzDqL,MAAM,CAACI,mBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAACzL,cAAc,CAAC;QACpE,IAAI,CAACA,iBAAiB,IAAI;AAC9B;MAEA,IAAI,IAAI,CAACC,cAAc,EAAE;AACrB,QAAA,IAAI,CAACA,cAAc,CAACyL,UAAU,EAAE;QAChC,IAAI,CAACzL,iBAAiB,IAAI;AAC9B;KACH;AACD0L,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACC,aAAa,EAAE;MACtB,IAAMC,KAAI,GAAI,CAAC,IAAI,CAAC5P,KAAM,IAAG,EAAE,EAAEsE,MAAM;MACvC,IAAMuB,KAAM,GAAE,IAAI,CAACvD,MAAM,EAAC,GAAI,IAAI,CAACC,KAAK,CAACC,IAAK,GAAEmN,aAAY,GAAI,IAAI,CAACpN,KAAM,GAAEoN,aAAa;MAE1F,OAAO;AACH9J,QAAAA,KAAK,EAALA,KAAK;AACL+J,QAAAA,KAAK,EAALA,KAAK;QACLrN,KAAK,EAAEsD,KAAI,KAAM,CAAC;AAClBnD,QAAAA,IAAI,EAAEmD,KAAI,KAAM+J,KAAI,GAAI,CAAC;AACzBC,QAAAA,IAAI,EAAEhK,KAAI,GAAI,CAAA,KAAM,CAAC;AACrBiK,QAAAA,GAAG,EAAEjK,KAAI,GAAI,CAAA,KAAM;OACtB;KACJ;AACDkK,IAAAA,gBAAgB,WAAhBA,gBAAgBA,CAAClK,KAAK,EAAEmK,UAAU,EAAE;AAChC,MAAA,IAAIJ,KAAM,GAAE,IAAI,CAAC1M,SAAS,CAACoB,MAAM;AAEjC,MAAA,OAAAyH,aAAA,CAAA;AACIlG,QAAAA,KAAK,EAALA,KAAK;AACL+J,QAAAA,KAAK,EAALA,KAAK;QACLrN,KAAK,EAAEsD,KAAI,KAAM,CAAC;AAClBnD,QAAAA,IAAI,EAAEmD,KAAI,KAAM+J,KAAI,GAAI,CAAC;AACzBC,QAAAA,IAAI,EAAEhK,KAAI,GAAI,CAAA,KAAM,CAAC;AACrBiK,QAAAA,GAAG,EAAEjK,KAAI,GAAI,CAAA,KAAM;AAAC,OAAA,EACjBmK,UAAS,CAAA;KAEnB;AACD5B,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC7L,KAAK,EAAE;AAClB,MAAA,OAAO4F,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC7F,KAAI,KAAA,IAAA,IAAJA,KAAI,KAAA,MAAA,GAAJA,KAAI,GAAK,IAAI,CAACA,KAAK,IAAI,IAAI,CAACS,mBAAoB,GAAE,CAAC,KAAK,IAAI,CAAC5B,IAAG,IAAK,CAAC,CAAC,CAAC;KAC/F;AACD6M,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAAC1L,KAAK,EAAE;AACjB,MAAA,OAAO,IAAI,CAACnB,QAAQ,CAAC,IAAI,CAACV,IAAG,GAAI,IAAI,CAACiC,IAAK,KAAI,IAAI,CAACyL,cAAc,CAAC7L,UAAAA,IAAAA,IAAAA,UAAAA,MAAAA,GAAAA,QAAS,IAAI,CAACA,KAAK,CAAA,GAAI,IAAI;KACjG;AACD0C,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACgL,EAAE,EAAE;AACb,MAAA,IAAI,CAAC3M,UAAU2M,MAAM,IAAI,CAAC3M,WAAW4M,UAAU,CAAC,IAAI,CAAC7M,OAAO,EAAE,6BAA6B,CAAC;KAC/F;AACD8M,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACF,EAAE,EAAE;MACX,IAAI,CAAC5M,OAAQ,GAAE4M,EAAE;KACpB;AACDG,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACH,EAAE,EAAE;MACX,IAAI,CAAC3M,OAAQ,GAAE2M,EAAE;AACrB;GACH;AACDI,EAAAA,QAAQ,EAAE;IACNC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,OAAO,CACH,mBAAmB,EACnB,IAAI,SAAM,EACV;QACI,0BAA0B,EAAE,IAAI,CAACnP,MAAM;AACvC,QAAA,sCAAsC,EAAE,IAAI,CAACmB,MAAM,EAAE;AACrD,QAAA,kDAAkD,EAAE,IAAI,CAACmD,YAAY;AACzE,OAAA,CACH;KACJ;IACD8K,YAAY,EAAA,SAAZA,YAAYA,GAAG;MACX,OAAO,CACH,2BAA2B,EAC3B;QACI,2BAA2B,EAAE,IAAI,CAACtN;AACtC,OAAA,CACH;KACJ;IACDuN,WAAW,EAAA,SAAXA,WAAWA,GAAG;MACV,OAAO,CACH,0BAA0B,EAC1B;AACI,QAAA,+BAA+B,EAAE,CAAC,IAAI,CAACC,MAAM,CAACC;AAClD,OAAA,CACH;KACJ;IACDC,WAAW,EAAA,SAAXA,WAAWA,GAAG;AAAA,MAAA,IAAAC,OAAA,GAAA,IAAA;MACV,IAAI,IAAI,CAAC5Q,KAAI,IAAK,CAAC,IAAI,CAACiD,SAAS,EAAE;AAC/B,QAAA,IAAI,IAAI,CAACX,MAAM,EAAE,EAAE,OAAO,IAAI,CAACtC,KAAK,CAAC6Q,KAAK,CAAC,IAAI,CAACxP,UAAW,GAAE,IAAI,IAAI,CAACkB,KAAK,CAACC,IAAI,EAAE,IAAI,CAACE,IAAI,CAACF,IAAI,CAAC,CAACmH,GAAG,CAAC,UAACmH,IAAI,EAAA;UAAA,OAAMF,OAAI,CAAC9P,OAAQ,GAAEgQ,OAAOA,IAAI,CAACD,KAAK,CAACD,OAAI,CAACvP,UAAW,GAAE,IAAIuP,OAAI,CAACrO,KAAK,CAACE,IAAI,EAAEmO,OAAI,CAAClO,IAAI,CAACD,IAAI,CAAC;AAAA,SAAC,CAAC,CAAA,KACrM,IAAI,IAAI,CAACgD,YAAY,EAAC,IAAK,IAAI,CAAC3E,OAAO,EAAE,OAAO,IAAI,CAACd,KAAK,CAAA,KAC1D,OAAO,IAAI,CAACA,KAAK,CAAC6Q,KAAK,CAAC,IAAI,CAACxP,UAAW,GAAE,CAAE,GAAE,IAAI,CAACkB,KAAK,EAAE,IAAI,CAACG,IAAI,CAAC;AAC7E;AAEA,MAAA,OAAO,EAAE;KACZ;IACDqO,UAAU,EAAA,SAAVA,UAAUA,GAAG;AACT,MAAA,OAAO,IAAI,CAAC9N,SAAQ,GAAK,IAAI,CAACpC,iBAAiB,IAAI,CAACqC,YAAY,EAAE,GAAI,IAAI,CAACyN,WAAW;KACzF;IACDK,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,IAAI,IAAI,CAAClQ,OAAO,EAAE;AACd,QAAA,IAAMuB,OAAO,IAAI,CAACC,MAAM,EAAE;AAC1B,QAAA,IAAM4D,aAAa,IAAI,CAACT,YAAY,EAAE;QAEtC,IAAIpD,IAAG,IAAK6D,UAAU,EAAE;UACpB,OAAO,IAAI,CAACjD,SAAU,IAAG,IAAI,CAACpC,cAAe,GAAGwB,IAAG,GAAI,IAAI,CAACa,SAAS,CAAC,CAAC,CAAA,GAAI,IAAI,CAACA,SAAS,GAAI,IAAI,CAACpC,OAAO,CAAC+P,KAAK,CAACxO,IAAK,GAAE,IAAI,CAACE,KAAK,CAACE,IAAK,GAAE,IAAI,CAACF,KAAK,EAAEF,IAAG,GAAI,IAAI,CAACK,IAAI,CAACD,IAAK,GAAE,IAAI,CAACC,IAAI,CAAC;AAC3L;AACJ;MAEA,OAAO,IAAI,CAAC5B,OAAO;AACvB;GACH;AACDmQ,EAAAA,UAAU,EAAE;AACRC,IAAAA,WAAW,EAAEA;AACjB;AACJ,CAAC;;;;;UCtsBoBC,IAAQ,CAAAvQ,QAAA,IACrBwQ,SAAA,EAAA,EAAAC,kBAAA,CAmCK,OAnCLC,UAmCK,CAAA;;IAnCCC,GAAG,EAAEC,QAAU,CAAArB,UAAA;IAAG,OAAOqB,EAAAA,QAAc,CAAAlB,cAAA;IAAGpP,QAAQ,EAAEiQ,IAAQ,CAAAjQ,QAAA;IAAGnB,KAAK,EAAEoR,IAAK,CAAApR,KAAA;IAAGuO,QAAM;aAAEkD,QAAQ,CAAAlD,QAAA,IAAAkD,QAAA,CAAAlD,QAAA,CAAAmD,KAAA,CAAAD,QAAA,EAAAxL,SAAA,CAAA;KAAA;KAAUmL,IAAI,CAAAO,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAC9GC,UAsBM,CAAAR,IAAA,CAAAV,MAAA,EAAA,SAAA,EAAA;IApBDmB,UAAU,EAAEJ,QAAY,CAAAjB,YAAA;IACxBvQ,KAAK,EAAEwR,QAAW,CAAAb,WAAA;IAClBkB,cAAc,EAAEL,QAAU,CAAA9B,UAAA;IAC1B3O,OAAO,EAAE+Q,KAAS,CAAA7O,SAAA;IAClB8M,gBAAgB,EAAEyB,QAAgB,CAAAzB,gBAAA;IAClC7P,QAAQ,EAAEiR,IAAQ,CAAAjR,QAAA;IAClBsC,IAAI,EAAEgP,QAAU,CAAAT,UAAA;IAChBjQ,OAAO,EAAE0Q,QAAa,CAAAR,aAAA;IACtBZ,UAAU,EAAEoB,QAAU,CAAApB,UAAA;IACtBjN,WAAW,EAAE2O,KAAW,CAAA3O,WAAA;IACxBC,YAAY,EAAE0O,KAAY,CAAA1O,YAAA;AAC1B8G,IAAAA,QAAQ,EAAEsH,QAAU,CAAAhM,UAAA,EAAA;AACpBU,IAAAA,UAAU,EAAEsL,QAAY,CAAA/L,YAAA,EAAA;AACxBpD,IAAAA,IAAI,EAAEmP,QAAM,CAAAlP,MAAA;KAfjB,YAAA;AAAA,IAAA,OAsBM,CALFyP,kBAAA,CAIK,OAJLT,UAIK,CAAA;MAJCC,GAAG,EAAEC,QAAU,CAAApB,UAAA;MAAG,OAAOoB,EAAAA,QAAY,CAAAjB,YAAA;MAAGxQ,KAAK,EAAE+R,KAAY,CAAA1O;OAAU+N,IAAG,CAAAa,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAC1EZ,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAEUY,QAFwB,EAAA,IAAA,EAAAC,UAAA,CAAAV,QAAA,CAAAb,WAAW,EAA3B,UAAAG,IAAI,EAAEjL,KAAK,EAAA;aACzB8L,UAAkE,CAAAR,IAAA,CAAAV,MAAA,EAAA,MAAA,EAAA;aADjB5K,KAAK;AACnCiL,QAAAA,IAAI,EAAEA,IAAI;AAAGnL,QAAAA,OAAO,EAAE6L,QAAU,CAAA9B,UAAA,CAAC7J,KAAK;;;MAI1DsL,IAAU,CAAAnQ,UAAA,IAArBoQ,SAAA,EAAA,EAAAC,kBAAA,CAAyG,OAAzGC,UAAyG,CAAA;;AAAlF,IAAA,OAAA,EAAM,0BAAyB;IAAGvR,KAAK,EAAE+R,KAAW,CAAA3O;KAAUgO,IAAG,CAAAa,GAAA,CAAA,QAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,mCAC5Eb,uBAAkBA,mBAAcW,KAAS,CAAA7O,SAAA,IAArDmO,SAAA,EAAA,EAAAC,kBAAA,CASK,OATLC,UASK,CAAA;;AATmD,IAAA,OAAA,EAAOE,QAAW,CAAAhB;KAAUW,IAAG,CAAAa,GAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CACnEb,eAAUA,IAAM,CAAAV,MAAA,CAACC,MAAM,IACnCU,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAEUY,QAFqB,EAAA;AAAAE,IAAAA,GAAA,EAAA;GAAA,EAAAD,UAAA,CAAAJ,KAAA,CAAA5O,SAAS,EAAtB,UAAAkP,CAAC,EAAEvM,KAAK,EAAA;WACtB8L,UAAiH,CAAAR,IAAA,CAAAV,MAAA,EAAA,QAAA,EAAA;WADrE5K,KAAK;AAC5BF,MAAAA,OAAO,EAAE6L,yBAAgB,CAAC3L,KAAK,EAAE2L,QAAM,CAAAlP,MAAA,EAAA,IAAA;AAAA+P,QAAAA,OAAA,EAAiBlB,IAAoB,CAAAmB,oBAAA,CAAC7P;OAAG;;4CAG7GkP,UAAA,CAEMR,gCAFN,YAAA;AAAA,IAAA,OAEM,CADFoB,WAAA,CAAsFC,wBAAtFlB,UAAsF,CAAA;AAAzEmB,MAAAA,IAAK,EAAL,EAAK;MAAA,OAAM,EAAA;OAAyCtB,IAAG,CAAAa,GAAA,CAAA,aAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA;8EAKpFX,kBAGU,CAAAY,QAAA,EAAA;AAAAE,IAAAA,GAAA,EAAA;AAAA,GAAA,EAAA,CAFNR,UAAY,CAAAR,IAAA,CAAAV,MAAA,EAAA,SAAA,CAAA,EACZkB,UAAiF,CAAAR,IAAA,CAAAV,MAAA,EAAA,SAAA,EAAA;IAA3DzQ,KAAK,EAAEmR,IAAK,CAAAnR,KAAA;IAAGwC,IAAI,EAAE2O,IAAK,CAAAnR,KAAA;IAAGc,OAAO,EAAE0Q,QAAa,CAAAR;;;;;;;;"}