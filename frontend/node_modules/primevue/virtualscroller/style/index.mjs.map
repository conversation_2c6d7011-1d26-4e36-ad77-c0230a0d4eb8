{"version": 3, "file": "index.mjs", "sources": ["../../../src/virtualscroller/style/VirtualScrollerStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/virtualscroller';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst css = `\n.p-virtualscroller {\n    position: relative;\n    overflow: auto;\n    contain: strict;\n    transform: translateZ(0);\n    will-change: scroll-position;\n    outline: 0 none;\n}\n\n.p-virtualscroller-content {\n    position: absolute;\n    top: 0;\n    left: 0;\n    min-height: 100%;\n    min-width: 100%;\n    will-change: transform;\n}\n\n.p-virtualscroller-spacer {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 1px;\n    width: 1px;\n    transform-origin: 0 0;\n    pointer-events: none;\n}\n\n.p-virtualscroller-loader {\n    position: sticky;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n}\n\n.p-virtualscroller-loader-mask {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.p-virtualscroller-horizontal > .p-virtualscroller-content {\n    display: flex;\n}\n\n.p-virtualscroller-inline .p-virtualscroller-content {\n    position: static;\n}\n\n.p-virtualscroller .p-virtualscroller-loading {\n    transform: none !important;\n    min-height: 0;\n    position: sticky;\n    inset-block-start: 0;\n    inset-inline-start: 0;\n}\n`;\n\nexport default BaseStyle.extend({\n    name: 'virtualscroller',\n    css,\n    style\n});\n"], "names": ["css", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,GAAG,GA0DR,0lCAAA;AAED,2BAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,iBAAiB;AACvBH,EAAAA,GAAG,EAAHA,GAAG;AACHI,EAAAA,KAAK,EAALA;AACJ,CAAC,CAAC;;;;"}