{"version": 3, "file": "index.mjs", "sources": ["../../../src/slider/style/SliderStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/slider';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst inlineStyles = {\n    handle: { position: 'absolute' },\n    range: { position: 'absolute' }\n};\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-slider p-component',\n        {\n            'p-disabled': props.disabled,\n            'p-invalid': instance.$invalid,\n            'p-slider-horizontal': props.orientation === 'horizontal',\n            'p-slider-vertical': props.orientation === 'vertical'\n        }\n    ],\n    range: 'p-slider-range',\n    handle: 'p-slider-handle'\n};\n\nexport default BaseStyle.extend({\n    name: 'slider',\n    style,\n    classes,\n    inlineStyles\n});\n"], "names": ["inlineStyles", "handle", "position", "range", "classes", "root", "_ref", "instance", "props", "disabled", "$invalid", "orientation", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,YAAY,GAAG;AACjBC,EAAAA,MAAM,EAAE;AAAEC,IAAAA,QAAQ,EAAE;GAAY;AAChCC,EAAAA,KAAK,EAAE;AAAED,IAAAA,QAAQ,EAAE;AAAW;AAClC,CAAC;AAED,IAAME,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAA,OAAO,CAC3B,sBAAsB,EACtB;MACI,YAAY,EAAEA,KAAK,CAACC,QAAQ;MAC5B,WAAW,EAAEF,QAAQ,CAACG,QAAQ;AAC9B,MAAA,qBAAqB,EAAEF,KAAK,CAACG,WAAW,KAAK,YAAY;AACzD,MAAA,mBAAmB,EAAEH,KAAK,CAACG,WAAW,KAAK;AAC/C,KAAC,CACJ;AAAA,GAAA;AACDR,EAAAA,KAAK,EAAE,gBAAgB;AACvBF,EAAAA,MAAM,EAAE;AACZ,CAAC;AAED,kBAAeW,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,QAAQ;AACdC,EAAAA,KAAK,EAALA,KAAK;AACLX,EAAAA,OAAO,EAAPA,OAAO;AACPJ,EAAAA,YAAY,EAAZA;AACJ,CAAC,CAAC;;;;"}