{"version": 3, "file": "index.mjs", "sources": ["../../../src/splitterpanel/style/SplitterPanelStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance }) => ['p-splitterpanel', { 'p-splitterpanel-nested': instance.isNested }]\n};\n\nexport default BaseStyle.extend({\n    name: 'splitterpanel',\n    classes\n});\n"], "names": ["classes", "root", "_ref", "instance", "isNested", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAA,OAAO,CAAC,iBAAiB,EAAE;MAAE,wBAAwB,EAAEA,QAAQ,CAACC;AAAS,KAAC,CAAC;AAAA;AAChG,CAAC;AAED,yBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,eAAe;AACrBP,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}