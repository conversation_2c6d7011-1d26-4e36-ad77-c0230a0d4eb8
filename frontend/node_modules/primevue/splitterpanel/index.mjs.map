{"version": 3, "file": "index.mjs", "sources": ["../../src/splitterpanel/BaseSplitterPanel.vue", "../../src/splitterpanel/SplitterPanel.vue", "../../src/splitterpanel/SplitterPanel.vue?vue&type=template&id=8490dd80&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport SplitterPanelStyle from 'primevue/splitterpanel/style';\n\nexport default {\n    name: 'BaseSplitterPanel',\n    extends: BaseComponent,\n    props: {\n        size: {\n            type: Number,\n            default: null\n        },\n        minSize: {\n            type: Number,\n            default: null\n        }\n    },\n    style: SplitterPanelStyle,\n    provide() {\n        return {\n            $pcSplitterPanel: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div ref=\"container\" :class=\"cx('root')\" v-bind=\"ptmi('root', getPTOptions)\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport BaseSplitterPanel from './BaseSplitterPanel.vue';\n\nexport default {\n    name: 'SplitterPanel',\n    extends: BaseSplitterPanel,\n    inheritAttrs: false,\n    data() {\n        return {\n            nestedState: null\n        };\n    },\n    computed: {\n        isNested() {\n            return this.$slots.default().some((child) => {\n                this.nestedState = child.type.name === 'Splitter' ? true : null;\n\n                return this.nestedState;\n            });\n        },\n        getPTOptions() {\n            return {\n                context: {\n                    nested: this.isNested\n                }\n            };\n        }\n    }\n};\n</script>\n", "<template>\n    <div ref=\"container\" :class=\"cx('root')\" v-bind=\"ptmi('root', getPTOptions)\">\n        <slot></slot>\n    </div>\n</template>\n\n<script>\nimport BaseSplitterPanel from './BaseSplitterPanel.vue';\n\nexport default {\n    name: 'SplitterPanel',\n    extends: BaseSplitterPanel,\n    inheritAttrs: false,\n    data() {\n        return {\n            nestedState: null\n        };\n    },\n    computed: {\n        isNested() {\n            return this.$slots.default().some((child) => {\n                this.nestedState = child.type.name === 'Splitter' ? true : null;\n\n                return this.nestedState;\n            });\n        },\n        getPTOptions() {\n            return {\n                context: {\n                    nested: this.isNested\n                }\n            };\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "size", "type", "Number", "minSize", "style", "SplitterPanelStyle", "provide", "$pcSplitterPanel", "$parentInstance", "BaseSplitterPanel", "inheritAttrs", "data", "nestedState", "computed", "isNested", "_this", "$slots", "some", "child", "getPTOptions", "context", "nested", "_openBlock", "_createElementBlock", "_mergeProps", "ref", "_ctx", "cx", "ptmi", "$options", "_renderSlot"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,mBAAmB;AACzB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,IAAI,EAAE;AACFC,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,OAAO,EAAE;AACLF,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDE,EAAAA,KAAK,EAAEC,kBAAkB;EACzBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,gBAAgB,EAAE,IAAI;AACtBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACfD,aAAe;AACXX,EAAAA,IAAI,EAAE,eAAe;AACrB,EAAA,SAAA,EAASY,QAAiB;AAC1BC,EAAAA,YAAY,EAAE,KAAK;EACnBC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHC,MAAAA,WAAW,EAAE;KAChB;GACJ;AACDC,EAAAA,QAAQ,EAAE;IACNC,QAAQ,EAAA,SAARA,QAAQA,GAAG;AAAA,MAAA,IAAAC,KAAA,GAAA,IAAA;MACP,OAAO,IAAI,CAACC,MAAM,CAAQ,SAAA,CAAA,EAAE,CAACC,IAAI,CAAC,UAACC,KAAK,EAAK;AACzCH,QAAAA,KAAI,CAACH,WAAU,GAAIM,KAAK,CAACjB,IAAI,CAACJ,IAAG,KAAM,UAAW,GAAE,IAAG,GAAI,IAAI;QAE/D,OAAOkB,KAAI,CAACH,WAAW;AAC3B,OAAC,CAAC;KACL;IACDO,YAAY,EAAA,SAAZA,YAAYA,GAAG;MACX,OAAO;AACHC,QAAAA,OAAO,EAAE;UACLC,MAAM,EAAE,IAAI,CAACP;AACjB;OACH;AACL;AACJ;AACJ,CAAC;;;ECjCG,OAAAQ,SAAA,EAAA,EAAAC,kBAAA,CAEK,OAFLC,UAEK,CAAA;AAFAC,IAAAA,GAAG,EAAC,WAAU;AAAG,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA;GAAkB,EAAAD,IAAA,CAAAE,IAAI,SAASC,QAAY,CAAAV,YAAA,CAAA,CAAA,EAAA,CACtEW,UAAY,CAAAJ,IAAA,CAAAV,MAAA,EAAA,SAAA,CAAA;;;;;;;"}