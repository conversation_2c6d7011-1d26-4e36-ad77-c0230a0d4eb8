{"version": 3, "file": "index.mjs", "sources": ["../../src/togglebutton/BaseToggleButton.vue", "../../src/togglebutton/ToggleButton.vue", "../../src/togglebutton/ToggleButton.vue?vue&type=template&id=7a5c1ac9&lang.js"], "sourcesContent": ["<script>\nimport BaseEditableHolder from '@primevue/core/baseeditableholder';\nimport ToggleButtonStyle from 'primevue/togglebutton/style';\n\nexport default {\n    name: 'BaseToggleButton',\n    extends: BaseEditableHolder,\n    props: {\n        onIcon: String,\n        offIcon: String,\n        onLabel: {\n            type: String,\n            default: 'Yes'\n        },\n        offLabel: {\n            type: String,\n            default: 'No'\n        },\n        iconPos: {\n            type: String,\n            default: 'left'\n        },\n        readonly: {\n            type: Boolean,\n            default: false\n        },\n        tabindex: {\n            type: Number,\n            default: null\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        },\n        size: {\n            type: String,\n            default: null\n        }\n    },\n    style: ToggleButtonStyle,\n    provide() {\n        return {\n            $pcToggleButton: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <button\n        v-ripple\n        type=\"button\"\n        :class=\"cx('root')\"\n        :tabindex=\"tabindex\"\n        :disabled=\"disabled\"\n        :aria-pressed=\"d_value\"\n        @click=\"onChange\"\n        @blur=\"onBlur\"\n        v-bind=\"getPTOptions('root')\"\n        :aria-label=\"ariaLabel\"\n        :aria-labelledby=\"ariaLabelledby\"\n        :data-p-checked=\"active\"\n        :data-p-disabled=\"disabled\"\n        :data-p=\"dataP\"\n    >\n        <span :class=\"cx('content')\" v-bind=\"getPTOptions('content')\" :data-p=\"dataP\">\n            <slot>\n                <slot name=\"icon\" :value=\"d_value\" :class=\"cx('icon')\">\n                    <span v-if=\"onIcon || offIcon\" :class=\"[cx('icon'), d_value ? onIcon : offIcon]\" v-bind=\"getPTOptions('icon')\" />\n                </slot>\n                <span :class=\"cx('label')\" v-bind=\"getPTOptions('label')\">{{ label }}</span>\n            </slot>\n        </span>\n    </button>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { isNotEmpty } from '@primeuix/utils/object';\nimport Ripple from 'primevue/ripple';\nimport BaseToggleButton from './BaseToggleButton.vue';\n\nexport default {\n    name: 'ToggleButton',\n    extends: BaseToggleButton,\n    inheritAttrs: false,\n    emits: ['change'],\n    methods: {\n        getPTOptions(key) {\n            const _ptm = key === 'root' ? this.ptmi : this.ptm;\n\n            return _ptm(key, {\n                context: {\n                    active: this.active,\n                    disabled: this.disabled\n                }\n            });\n        },\n        onChange(event) {\n            if (!this.disabled && !this.readonly) {\n                this.writeValue(!this.d_value, event);\n                this.$emit('change', event);\n            }\n        },\n        onBlur(event) {\n            this.formField.onBlur?.(event);\n        }\n    },\n    computed: {\n        active() {\n            return this.d_value === true;\n        },\n        hasLabel() {\n            return isNotEmpty(this.onLabel) && isNotEmpty(this.offLabel);\n        },\n        label() {\n            return this.hasLabel ? (this.d_value ? this.onLabel : this.offLabel) : '\\u00A0';\n        },\n        dataP() {\n            return cn({\n                checked: this.active,\n                invalid: this.$invalid,\n                [this.size]: this.size\n            });\n        }\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <button\n        v-ripple\n        type=\"button\"\n        :class=\"cx('root')\"\n        :tabindex=\"tabindex\"\n        :disabled=\"disabled\"\n        :aria-pressed=\"d_value\"\n        @click=\"onChange\"\n        @blur=\"onBlur\"\n        v-bind=\"getPTOptions('root')\"\n        :aria-label=\"ariaLabel\"\n        :aria-labelledby=\"ariaLabelledby\"\n        :data-p-checked=\"active\"\n        :data-p-disabled=\"disabled\"\n        :data-p=\"dataP\"\n    >\n        <span :class=\"cx('content')\" v-bind=\"getPTOptions('content')\" :data-p=\"dataP\">\n            <slot>\n                <slot name=\"icon\" :value=\"d_value\" :class=\"cx('icon')\">\n                    <span v-if=\"onIcon || offIcon\" :class=\"[cx('icon'), d_value ? onIcon : offIcon]\" v-bind=\"getPTOptions('icon')\" />\n                </slot>\n                <span :class=\"cx('label')\" v-bind=\"getPTOptions('label')\">{{ label }}</span>\n            </slot>\n        </span>\n    </button>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { isNotEmpty } from '@primeuix/utils/object';\nimport Ripple from 'primevue/ripple';\nimport BaseToggleButton from './BaseToggleButton.vue';\n\nexport default {\n    name: 'ToggleButton',\n    extends: BaseToggleButton,\n    inheritAttrs: false,\n    emits: ['change'],\n    methods: {\n        getPTOptions(key) {\n            const _ptm = key === 'root' ? this.ptmi : this.ptm;\n\n            return _ptm(key, {\n                context: {\n                    active: this.active,\n                    disabled: this.disabled\n                }\n            });\n        },\n        onChange(event) {\n            if (!this.disabled && !this.readonly) {\n                this.writeValue(!this.d_value, event);\n                this.$emit('change', event);\n            }\n        },\n        onBlur(event) {\n            this.formField.onBlur?.(event);\n        }\n    },\n    computed: {\n        active() {\n            return this.d_value === true;\n        },\n        hasLabel() {\n            return isNotEmpty(this.onLabel) && isNotEmpty(this.offLabel);\n        },\n        label() {\n            return this.hasLabel ? (this.d_value ? this.onLabel : this.offLabel) : '\\u00A0';\n        },\n        dataP() {\n            return cn({\n                checked: this.active,\n                invalid: this.$invalid,\n                [this.size]: this.size\n            });\n        }\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n"], "names": ["name", "BaseEditableHolder", "props", "onIcon", "String", "offIcon", "onLabel", "type", "offLabel", "iconPos", "readonly", "Boolean", "tabindex", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "size", "style", "ToggleButtonStyle", "provide", "$pcToggleButton", "$parentInstance", "BaseToggleButton", "inheritAttrs", "emits", "methods", "getPTOptions", "key", "_ptm", "ptmi", "ptm", "context", "active", "disabled", "onChange", "event", "writeValue", "d_value", "$emit", "onBlur", "_this$formField$onBlu", "_this$formField", "formField", "call", "computed", "<PERSON><PERSON><PERSON><PERSON>", "isNotEmpty", "label", "dataP", "cn", "_defineProperty", "checked", "invalid", "$invalid", "directives", "ripple", "<PERSON><PERSON><PERSON>", "_withDirectives", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "onClick", "$options", "apply", "arguments", "_createElementVNode", "_renderSlot", "$slots", "value"], "mappings": ";;;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,kBAAkB;AACxB,EAAA,SAAA,EAASC,kBAAkB;AAC3BC,EAAAA,KAAK,EAAE;AACHC,IAAAA,MAAM,EAAEC,MAAM;AACdC,IAAAA,OAAO,EAAED,MAAM;AACfE,IAAAA,OAAO,EAAE;AACLC,MAAAA,IAAI,EAAEH,MAAM;MACZ,SAAS,EAAA;KACZ;AACDI,IAAAA,QAAQ,EAAE;AACND,MAAAA,IAAI,EAAEH,MAAM;MACZ,SAAS,EAAA;KACZ;AACDK,IAAAA,OAAO,EAAE;AACLF,MAAAA,IAAI,EAAEH,MAAM;MACZ,SAAS,EAAA;KACZ;AACDM,IAAAA,QAAQ,EAAE;AACNH,MAAAA,IAAI,EAAEI,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNL,MAAAA,IAAI,EAAEM,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,cAAc,EAAE;AACZP,MAAAA,IAAI,EAAEH,MAAM;MACZ,SAAS,EAAA;KACZ;AACDW,IAAAA,SAAS,EAAE;AACPR,MAAAA,IAAI,EAAEH,MAAM;MACZ,SAAS,EAAA;KACZ;AACDY,IAAAA,IAAI,EAAE;AACFT,MAAAA,IAAI,EAAEH,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDa,EAAAA,KAAK,EAAEC,iBAAiB;EACxBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,eAAe,EAAE,IAAI;AACrBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;AChBD,aAAe;AACXrB,EAAAA,IAAI,EAAE,cAAc;AACpB,EAAA,SAAA,EAASsB,QAAgB;AACzBC,EAAAA,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAE,CAAC,QAAQ,CAAC;AACjBC,EAAAA,OAAO,EAAE;AACLC,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACC,GAAG,EAAE;AACd,MAAA,IAAMC,IAAG,GAAID,GAAI,KAAI,MAAK,GAAI,IAAI,CAACE,IAAK,GAAE,IAAI,CAACC,GAAG;MAElD,OAAOF,IAAI,CAACD,GAAG,EAAE;AACbI,QAAAA,OAAO,EAAE;UACLC,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBC,QAAQ,EAAE,IAAI,CAACA;AACnB;AACJ,OAAC,CAAC;KACL;AACDC,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAACC,KAAK,EAAE;MACZ,IAAI,CAAC,IAAI,CAACF,QAAO,IAAK,CAAC,IAAI,CAACvB,QAAQ,EAAE;QAClC,IAAI,CAAC0B,UAAU,CAAC,CAAC,IAAI,CAACC,OAAO,EAAEF,KAAK,CAAC;AACrC,QAAA,IAAI,CAACG,KAAK,CAAC,QAAQ,EAAEH,KAAK,CAAC;AAC/B;KACH;AACDI,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACJ,KAAK,EAAE;MAAA,IAAAK,qBAAA,EAAAC,eAAA;AACV,MAAA,CAAAD,qBAAA,GAAAC,CAAAA,eAAA,OAAI,CAACC,SAAS,EAACH,MAAM,MAAA,IAAA,IAAAC,qBAAA,KAAA,MAAA,IAArBA,qBAAA,CAAAG,IAAA,CAAAF,eAAA,EAAwBN,KAAK,CAAC;AAClC;GACH;AACDS,EAAAA,QAAQ,EAAE;IACNZ,MAAM,EAAA,SAANA,MAAMA,GAAG;AACL,MAAA,OAAO,IAAI,CAACK,OAAQ,KAAI,IAAI;KAC/B;IACDQ,QAAQ,EAAA,SAARA,QAAQA,GAAG;AACP,MAAA,OAAOC,UAAU,CAAC,IAAI,CAACxC,OAAO,CAAE,IAAGwC,UAAU,CAAC,IAAI,CAACtC,QAAQ,CAAC;KAC/D;IACDuC,KAAK,EAAA,SAALA,KAAKA,GAAG;AACJ,MAAA,OAAO,IAAI,CAACF,QAAO,GAAK,IAAI,CAACR,UAAU,IAAI,CAAC/B,OAAM,GAAI,IAAI,CAACE,QAAQ,GAAI,MAAQ;KAClF;IACDwC,KAAK,EAAA,SAALA,KAAKA,GAAG;MACJ,OAAOC,EAAE,CAAAC,eAAA,CAAA;QACLC,OAAO,EAAE,IAAI,CAACnB,MAAM;QACpBoB,OAAO,EAAE,IAAI,CAACC;OACb,EAAA,IAAI,CAACrC,IAAI,EAAG,IAAI,CAACA,IAAG,CACxB,CAAC;AACN;GACH;AACDsC,EAAAA,UAAU,EAAE;AACRC,IAAAA,MAAM,EAAEC;AACZ;AACJ,CAAC;;;;;;EChFG,OAAAC,cAAA,EAAAC,SAAA,EAAA,EAAAC,kBAAA,CAwBQ,UAxBRC,UAwBQ,CAAA;AAtBJrD,IAAAA,IAAI,EAAC,QAAO;AACX,IAAA,OAAA,EAAOsD,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;IACTlD,QAAQ,EAAEiD,IAAQ,CAAAjD,QAAA;IAClBqB,QAAQ,EAAE4B,IAAQ,CAAA5B,QAAA;IAClB,cAAY,EAAE4B,IAAO,CAAAxB,OAAA;IACrB0B,OAAK;aAAEC,QAAQ,CAAA9B,QAAA,IAAA8B,QAAA,CAAA9B,QAAA,CAAA+B,KAAA,CAAAD,QAAA,EAAAE,SAAA,CAAA;AAAA,KAAA,CAAA;IACf3B,MAAI;aAAEyB,QAAM,CAAAzB,MAAA,IAAAyB,QAAA,CAAAzB,MAAA,CAAA0B,KAAA,CAAAD,QAAA,EAAAE,SAAA,CAAA;KAAA;KACLF,QAAY,CAAAtC,YAAA,CAAA,MAAA,CAAA,EAAA;IACnB,YAAU,EAAEmC,IAAS,CAAA9C,SAAA;IACrB,iBAAe,EAAE8C,IAAc,CAAA/C,cAAA;IAC/B,gBAAc,EAAEkD,QAAM,CAAAhC,MAAA;IACtB,iBAAe,EAAE6B,IAAQ,CAAA5B,QAAA;IACzB,QAAM,EAAE+B,QAAK,CAAAhB;OAEdmB,kBAAA,CAOM,QAPNP,UAOM,CAAA;AAPC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,SAAA;KAAqBE,QAAY,CAAAtC,YAAA,CAAA,SAAA,CAAA,EAAA;IAAc,QAAM,EAAEsC,QAAK,CAAAhB;AAAA,GAAA,CAAA,EAAA,CACxEoB,UAAA,CAKMP,4BALN,YAAA;IAAA,OAKM,CAJFO,UAEM,CAAAP,IAAA,CAAAQ,MAAA,EAAA,MAAA,EAAA;MAFaC,KAAK,EAAET,IAAO,CAAAxB,OAAA;AAAG,MAAA,OAAA,iBAAOwB,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;OAA7C,YAAA;AAAA,MAAA,OAEM,CADUD,IAAA,CAAA1D,MAAK,IAAK0D,IAAO,CAAAxD,OAAA,IAA7BqD,SAAA,EAAA,EAAAC,kBAAA,CAAgH,QAAhHC,UAAgH,CAAA;;AAAhF,QAAA,OAAA,GAAQC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA,EAAUD,eAAUA,IAAA,CAAA1D,MAAK,GAAI0D,IAAO,CAAAxD,OAAA;SAAW2D,QAAY,CAAAtC,YAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA;QAEzGyC,kBAAA,CAA2E,QAA3EP,UAA2E,CAAA;AAApE,MAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,OAAA;AAAmB,KAAA,EAAAE,QAAA,CAAAtC,YAAY,4BAAcsC,QAAM,CAAAjB,KAAA,CAAA,EAAA,EAAA,CAAA;;;;;;;;"}