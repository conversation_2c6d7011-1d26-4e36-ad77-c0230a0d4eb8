{"version": 3, "file": "index.mjs", "sources": ["../../../src/togglebutton/style/ToggleButtonStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/togglebutton';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-togglebutton p-component',\n        {\n            'p-togglebutton-checked': instance.active,\n            'p-invalid': instance.$invalid,\n            'p-togglebutton-sm p-inputfield-sm': props.size === 'small',\n            'p-togglebutton-lg p-inputfield-lg': props.size === 'large'\n        }\n    ],\n    content: 'p-togglebutton-content',\n    icon: 'p-togglebutton-icon',\n    label: 'p-togglebutton-label'\n};\n\nexport default BaseStyle.extend({\n    name: 'togglebutton',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "instance", "props", "active", "$invalid", "size", "content", "icon", "label", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAA,OAAO,CAC3B,4BAA4B,EAC5B;MACI,wBAAwB,EAAED,QAAQ,CAACE,MAAM;MACzC,WAAW,EAAEF,QAAQ,CAACG,QAAQ;AAC9B,MAAA,mCAAmC,EAAEF,KAAK,CAACG,IAAI,KAAK,OAAO;AAC3D,MAAA,mCAAmC,EAAEH,KAAK,CAACG,IAAI,KAAK;AACxD,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,OAAO,EAAE,wBAAwB;AACjCC,EAAAA,IAAI,EAAE,qBAAqB;AAC3BC,EAAAA,KAAK,EAAE;AACX,CAAC;AAED,wBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,cAAc;AACpBC,EAAAA,KAAK,EAALA,KAAK;AACLd,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}