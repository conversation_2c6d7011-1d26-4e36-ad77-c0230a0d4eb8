{"version": 3, "file": "index.mjs", "sources": ["../../../src/tag/style/TagStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/tag';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => [\n        'p-tag p-component',\n        {\n            'p-tag-info': props.severity === 'info',\n            'p-tag-success': props.severity === 'success',\n            'p-tag-warn': props.severity === 'warn',\n            'p-tag-danger': props.severity === 'danger',\n            'p-tag-secondary': props.severity === 'secondary',\n            'p-tag-contrast': props.severity === 'contrast',\n            'p-tag-rounded': props.rounded\n        }\n    ],\n    icon: 'p-tag-icon',\n    label: 'p-tag-label'\n};\n\nexport default BaseStyle.extend({\n    name: 'tag',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "props", "severity", "rounded", "icon", "label", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAA,OAAO,CACjB,mBAAmB,EACnB;AACI,MAAA,YAAY,EAAEA,KAAK,CAACC,QAAQ,KAAK,MAAM;AACvC,MAAA,eAAe,EAAED,KAAK,CAACC,QAAQ,KAAK,SAAS;AAC7C,MAAA,YAAY,EAAED,KAAK,CAACC,QAAQ,KAAK,MAAM;AACvC,MAAA,cAAc,EAAED,KAAK,CAACC,QAAQ,KAAK,QAAQ;AAC3C,MAAA,iBAAiB,EAAED,KAAK,CAACC,QAAQ,KAAK,WAAW;AACjD,MAAA,gBAAgB,EAAED,KAAK,CAACC,QAAQ,KAAK,UAAU;MAC/C,eAAe,EAAED,KAAK,CAACE;AAC3B,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,IAAI,EAAE,YAAY;AAClBC,EAAAA,KAAK,EAAE;AACX,CAAC;AAED,eAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,KAAK;AACXC,EAAAA,KAAK,EAALA,KAAK;AACLX,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}