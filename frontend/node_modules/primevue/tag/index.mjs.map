{"version": 3, "file": "index.mjs", "sources": ["../../src/tag/BaseTag.vue", "../../src/tag/Tag.vue", "../../src/tag/Tag.vue?vue&type=template&id=e9e4f6e2&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TagStyle from 'primevue/tag/style';\n\nexport default {\n    name: 'BaseTag',\n    extends: BaseComponent,\n    props: {\n        value: null,\n        severity: null,\n        rounded: Boolean,\n        icon: String\n    },\n    style: TagStyle,\n    provide() {\n        return {\n            $pcTag: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <span :class=\"cx('root')\" :data-p=\"dataP\" v-bind=\"ptmi('root')\">\n        <component v-if=\"$slots.icon\" :is=\"$slots.icon\" :class=\"cx('icon')\" v-bind=\"ptm('icon')\" />\n        <span v-else-if=\"icon\" :class=\"[cx('icon'), icon]\" v-bind=\"ptm('icon')\"></span>\n        <slot v-if=\"value != null || $slots.default\">\n            <span :class=\"cx('label')\" v-bind=\"ptm('label')\">{{ value }}</span>\n        </slot>\n    </span>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseTag from './BaseTag.vue';\n\nexport default {\n    name: 'Tag',\n    extends: BaseTag,\n    inheritAttrs: false,\n    computed: {\n        dataP() {\n            return cn({\n                rounded: this.rounded,\n                [this.severity]: this.severity\n            });\n        }\n    }\n};\n</script>\n", "<template>\n    <span :class=\"cx('root')\" :data-p=\"dataP\" v-bind=\"ptmi('root')\">\n        <component v-if=\"$slots.icon\" :is=\"$slots.icon\" :class=\"cx('icon')\" v-bind=\"ptm('icon')\" />\n        <span v-else-if=\"icon\" :class=\"[cx('icon'), icon]\" v-bind=\"ptm('icon')\"></span>\n        <slot v-if=\"value != null || $slots.default\">\n            <span :class=\"cx('label')\" v-bind=\"ptm('label')\">{{ value }}</span>\n        </slot>\n    </span>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseTag from './BaseTag.vue';\n\nexport default {\n    name: 'Tag',\n    extends: BaseTag,\n    inheritAttrs: false,\n    computed: {\n        dataP() {\n            return cn({\n                rounded: this.rounded,\n                [this.severity]: this.severity\n            });\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "value", "severity", "rounded", "Boolean", "icon", "String", "style", "TagStyle", "provide", "$pcTag", "$parentInstance", "BaseTag", "inheritAttrs", "computed", "dataP", "cn", "_defineProperty", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "$options", "ptmi", "$slots", "_createBlock", "_resolveDynamicComponent", "ptm", "_renderSlot", "_createElementVNode"], "mappings": ";;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,SAAS;AACf,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE,IAAI;AACXC,IAAAA,QAAQ,EAAE,IAAI;AACdC,IAAAA,OAAO,EAAEC,OAAO;AAChBC,IAAAA,IAAI,EAAEC;GACT;AACDC,EAAAA,KAAK,EAAEC,QAAQ;EACfC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,MAAM,EAAE,IAAI;AACZC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;ACND,aAAe;AACXb,EAAAA,IAAI,EAAE,KAAK;AACX,EAAA,SAAA,EAASc,QAAO;AAChBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,QAAQ,EAAE;IACNC,KAAK,EAAA,SAALA,KAAKA,GAAG;MACJ,OAAOC,EAAE,CAAAC,eAAA,CAAA;QACLd,OAAO,EAAE,IAAI,CAACA;OACb,EAAA,IAAI,CAACD,QAAQ,EAAG,IAAI,CAACA,QAAO,CAChC,CAAC;AACN;AACJ;AACJ,CAAC;;;;ECzBG,OAAAgB,SAAA,EAAA,EAAAC,kBAAA,CAMM,QANNC,UAMM,CAAA;AANC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;IAAW,QAAM,EAAEC,QAAK,CAAAR;KAAUM,IAAI,CAAAG,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACjCH,IAAA,CAAAI,MAAM,CAACpB,IAAI,IAA5Ba,SAAA,EAAA,EAAAQ,WAAA,CAA0FC,uBAAvD,CAAAN,IAAA,CAAAI,MAAM,CAACpB,IAAI,GAA9Ce,UAA0F,CAAA;;AAAzC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA;KAAkBD,IAAG,CAAAO,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,IAC9DP,IAAI,CAAAhB,IAAA,IAArBa,SAAA,EAAA,EAAAC,kBAAA,CAA8E,QAA9EC,UAA8E,CAAA;;IAAtD,OAAK,EAAA,CAAGC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA,EAAUD,IAAI,CAAAhB,IAAA;KAAWgB,IAAG,CAAAO,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,kCAClDP,IAAI,CAAApB,KAAA,IAAA,IAAA,IAAaoB,IAAM,CAAAI,MAAA,CAAQ,SAAA,CAAA,GAA3CI,UAAA,CAEMR;;KAFN,YAAA;AAAA,IAAA,OAEM,CADFS,kBAAA,CAAkE,QAAlEV,UAAkE,CAAA;AAA3D,MAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,OAAA;AAAmB,KAAA,EAAAD,IAAA,CAAAO,GAAG,4BAAcP,IAAM,CAAApB,KAAA,CAAA,EAAA,EAAA,CAAA;;;;;;;;"}