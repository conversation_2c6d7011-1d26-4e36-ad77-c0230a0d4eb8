{"version": 3, "file": "index.mjs", "sources": ["../../../src/toolbar/style/ToolbarStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/toolbar';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-toolbar p-component',\n    start: 'p-toolbar-start',\n    center: 'p-toolbar-center',\n    end: 'p-toolbar-end'\n};\n\nexport default BaseStyle.extend({\n    name: 'toolbar',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "start", "center", "end", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,uBAAuB;AAC7BC,EAAAA,KAAK,EAAE,iBAAiB;AACxBC,EAAAA,MAAM,EAAE,kBAAkB;AAC1BC,EAAAA,GAAG,EAAE;AACT,CAAC;AAED,mBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,SAAS;AACfC,EAAAA,KAAK,EAALA,KAAK;AACLR,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}