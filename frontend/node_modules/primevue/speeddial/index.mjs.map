{"version": 3, "file": "index.mjs", "sources": ["../../src/speeddial/BaseSpeedDial.vue", "../../src/speeddial/SpeedDial.vue", "../../src/speeddial/SpeedDial.vue?vue&type=template&id=3a333aa5&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport SpeedDialStyle from 'primevue/speeddial/style';\n\nexport default {\n    name: 'BaseSpeedDial',\n    extends: BaseComponent,\n    props: {\n        model: null,\n        visible: {\n            type: Boolean,\n            default: false\n        },\n        direction: {\n            type: String,\n            default: 'up'\n        },\n        transitionDelay: {\n            type: Number,\n            default: 30\n        },\n        type: {\n            type: String,\n            default: 'linear'\n        },\n        radius: {\n            type: Number,\n            default: 0\n        },\n        mask: {\n            type: Boolean,\n            default: false\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        },\n        hideOnClickOutside: {\n            type: Boolean,\n            default: true\n        },\n        buttonClass: null,\n        maskStyle: null,\n        maskClass: null,\n        showIcon: {\n            type: String,\n            default: undefined\n        },\n        hideIcon: {\n            type: String,\n            default: undefined\n        },\n        rotateAnimation: {\n            type: Boolean,\n            default: true\n        },\n        tooltipOptions: null,\n        style: null,\n        class: null,\n        buttonProps: {\n            type: Object,\n            default() {\n                return { rounded: true };\n            }\n        },\n        actionButtonProps: {\n            type: Object,\n            default() {\n                return { severity: 'secondary', rounded: true, size: 'small' };\n            }\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        }\n    },\n    style: SpeedDialStyle,\n    provide() {\n        return {\n            $pcSpeedDial: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :ref=\"containerRef\" :class=\"containerClass\" :style=\"[style, sx('root')]\" v-bind=\"ptmi('root')\">\n        <slot name=\"button\" :visible=\"d_visible\" :toggleCallback=\"onClick\">\n            <Button\n                :class=\"[cx('pcButton'), buttonClass]\"\n                :disabled=\"disabled\"\n                :aria-expanded=\"d_visible\"\n                :aria-haspopup=\"true\"\n                :aria-controls=\"$id + '_list'\"\n                :aria-label=\"ariaLabel\"\n                :aria-labelledby=\"ariaLabelledby\"\n                :unstyled=\"unstyled\"\n                @click=\"onClick($event)\"\n                @keydown=\"onTogglerKeydown\"\n                v-bind=\"buttonProps\"\n                :pt=\"ptm('pcButton')\"\n            >\n                <template #icon=\"slotProps\">\n                    <slot name=\"icon\" :visible=\"d_visible\">\n                        <component v-if=\"d_visible && !!hideIcon\" :is=\"hideIcon ? 'span' : 'PlusIcon'\" :class=\"[hideIcon, slotProps.class]\" v-bind=\"ptm('pcButton')['icon']\" data-pc-section=\"icon\" />\n                        <component v-else :is=\"showIcon ? 'span' : 'PlusIcon'\" :class=\"[d_visible && !!hideIcon ? hideIcon : showIcon, slotProps.class]\" v-bind=\"ptm('pcButton')['icon']\" data-pc-section=\"icon\" />\n                    </slot>\n                </template>\n            </Button>\n        </slot>\n        <ul :ref=\"listRef\" :id=\"$id + '_list'\" :class=\"cx('list')\" :style=\"sx('list')\" role=\"menu\" tabindex=\"-1\" @focus=\"onFocus\" @blur=\"onBlur\" @keydown=\"onKeyDown\" v-bind=\"ptm('list')\">\n            <template v-for=\"(item, index) of model\" :key=\"index\">\n                <li\n                    v-if=\"isItemVisible(item)\"\n                    :id=\"`${$id}_${index}`\"\n                    :class=\"cx('item', { id: `${$id}_${index}` })\"\n                    :style=\"getItemStyle(index)\"\n                    role=\"none\"\n                    :data-p-active=\"isItemActive(`${$id}_${index}`)\"\n                    v-bind=\"getPTOptions(`${$id}_${index}`, 'item')\"\n                >\n                    <template v-if=\"!$slots.item\">\n                        <Button\n                            v-tooltip:[tooltipOptions]=\"{ value: item.label, disabled: !tooltipOptions }\"\n                            :tabindex=\"-1\"\n                            role=\"menuitem\"\n                            :class=\"cx('pcAction', { item })\"\n                            :aria-label=\"item.label\"\n                            :disabled=\"disabled\"\n                            :unstyled=\"unstyled\"\n                            @click=\"onItemClick($event, item)\"\n                            v-bind=\"actionButtonProps\"\n                            :pt=\"getPTOptions(`${$id}_${index}`, 'pcAction')\"\n                        >\n                            <template v-if=\"item.icon\" #icon=\"slotProps\">\n                                <slot name=\"itemicon\" :item=\"item\" :class=\"slotProps.class\">\n                                    <span :class=\"[item.icon, slotProps.class]\" v-bind=\"getPTOptions(`${$id}_${index}`, 'actionIcon')\"></span>\n                                </slot>\n                            </template>\n                        </Button>\n                    </template>\n                    <component v-else :is=\"$slots.item\" :item=\"item\" :onClick=\"(event) => onItemClick(event, item)\" :toggleCallback=\"(event) => onItemClick(event, item)\"></component>\n                </li>\n            </template>\n        </ul>\n    </div>\n    <template v-if=\"mask\">\n        <div :class=\"[cx('mask'), maskClass]\" :style=\"maskStyle\" v-bind=\"ptm('mask')\"></div>\n    </template>\n</template>\n\n<script>\nimport { $dt } from '@primeuix/styled';\nimport { find, findSingle, focus, hasClass } from '@primeuix/utils/dom';\nimport PlusIcon from '@primevue/icons/plus';\nimport Button from 'primevue/button';\nimport Ripple from 'primevue/ripple';\nimport Tooltip from 'primevue/tooltip';\nimport BaseSpeedDial from './BaseSpeedDial.vue';\n\n// Set fix value for SSR.\nconst Math_PI = 3.14159265358979;\n\nexport default {\n    name: 'SpeedDial',\n    extends: BaseSpeedDial,\n    inheritAttrs: false,\n    emits: ['click', 'show', 'hide', 'focus', 'blur'],\n    documentClickListener: null,\n    container: null,\n    list: null,\n    data() {\n        return {\n            d_visible: this.visible,\n            isItemClicked: false,\n            focused: false,\n            focusedOptionIndex: -1\n        };\n    },\n    watch: {\n        visible(newValue) {\n            this.d_visible = newValue;\n        }\n    },\n    mounted() {\n        if (this.type !== 'linear') {\n            const button = findSingle(this.container, '[data-pc-name=\"pcbutton\"]');\n            const firstItem = findSingle(this.list, '[data-pc-section=\"item\"]');\n\n            if (button && firstItem) {\n                const wDiff = Math.abs(button.offsetWidth - firstItem.offsetWidth);\n                const hDiff = Math.abs(button.offsetHeight - firstItem.offsetHeight);\n\n                this.list.style.setProperty($dt('item.diff.x').name, `${wDiff / 2}px`);\n                this.list.style.setProperty($dt('item.diff.y').name, `${hDiff / 2}px`);\n            }\n        }\n\n        if (this.hideOnClickOutside) {\n            this.bindDocumentClickListener();\n        }\n    },\n    beforeUnmount() {\n        this.unbindDocumentClickListener();\n    },\n    methods: {\n        getPTOptions(id, key) {\n            return this.ptm(key, {\n                context: {\n                    active: this.isItemActive(id),\n                    hidden: !this.d_visible\n                }\n            });\n        },\n        onFocus(event) {\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.focusedOptionIndex = -1;\n            this.$emit('blur', event);\n        },\n        onItemClick(e, item) {\n            if (item.command) {\n                item.command({ originalEvent: e, item });\n            }\n\n            this.hide();\n\n            this.isItemClicked = true;\n            e.preventDefault();\n        },\n        onClick(event) {\n            this.d_visible ? this.hide() : this.show();\n            this.isItemClicked = true;\n            this.$emit('click', event);\n        },\n        show() {\n            this.d_visible = true;\n            this.$emit('show');\n        },\n        hide() {\n            this.d_visible = false;\n            this.$emit('hide');\n        },\n        calculateTransitionDelay(index) {\n            const length = this.model.length;\n            const visible = this.d_visible;\n\n            return (visible ? index : length - index - 1) * this.transitionDelay;\n        },\n        onTogglerKeydown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                case 'ArrowLeft':\n                    this.onTogglerArrowDown(event);\n\n                    break;\n\n                case 'ArrowUp':\n                case 'ArrowRight':\n                    this.onTogglerArrowUp(event);\n\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey();\n\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onKeyDown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDown(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUp(event);\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeft(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRight(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                case 'Space':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onTogglerArrowUp(event) {\n            this.show();\n            this.navigatePrevItem(event);\n\n            event.preventDefault();\n        },\n        onTogglerArrowDown(event) {\n            this.show();\n            this.navigateNextItem(event);\n\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            const items = find(this.container, '[data-pc-section=\"item\"]');\n            const itemIndex = [...items].findIndex((item) => item.id === this.focusedOptionIndex);\n            const buttonEl = findSingle(this.container, 'button');\n\n            this.onItemClick(event, this.model[itemIndex]);\n            this.onBlur(event);\n\n            buttonEl && focus(buttonEl);\n        },\n        onEscapeKey() {\n            this.hide();\n\n            const buttonEl = findSingle(this.container, 'button');\n\n            buttonEl && focus(buttonEl);\n        },\n        onArrowUp(event) {\n            if (this.direction === 'down') {\n                this.navigatePrevItem(event);\n            } else {\n                this.navigateNextItem(event);\n            }\n        },\n        onArrowDown(event) {\n            if (this.direction === 'down') {\n                this.navigateNextItem(event);\n            } else {\n                this.navigatePrevItem(event);\n            }\n        },\n\n        onArrowLeft(event) {\n            const leftValidDirections = ['left', 'up-right', 'down-left'];\n            const rightValidDirections = ['right', 'up-left', 'down-right'];\n\n            if (leftValidDirections.includes(this.direction)) {\n                this.navigateNextItem(event);\n            } else if (rightValidDirections.includes(this.direction)) {\n                this.navigatePrevItem(event);\n            } else {\n                this.navigatePrevItem(event);\n            }\n        },\n\n        onArrowRight(event) {\n            const leftValidDirections = ['left', 'up-right', 'down-left'];\n            const rightValidDirections = ['right', 'up-left', 'down-right'];\n\n            if (leftValidDirections.includes(this.direction)) {\n                this.navigatePrevItem(event);\n            } else if (rightValidDirections.includes(this.direction)) {\n                this.navigateNextItem(event);\n            } else {\n                this.navigateNextItem(event);\n            }\n        },\n        onEndKey(event) {\n            event.preventDefault();\n\n            this.focusedOptionIndex = -1;\n            this.navigatePrevItem(event);\n        },\n        onHomeKey(event) {\n            event.preventDefault();\n\n            this.focusedOptionIndex = -1;\n            this.navigateNextItem(event);\n        },\n        navigateNextItem(event) {\n            const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex);\n\n            this.changeFocusedOptionIndex(optionIndex);\n\n            event.preventDefault();\n        },\n        navigatePrevItem(event) {\n            const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex);\n\n            this.changeFocusedOptionIndex(optionIndex);\n\n            event.preventDefault();\n        },\n        changeFocusedOptionIndex(index) {\n            const items = find(this.container, '[data-pc-section=\"item\"]');\n            const filteredItems = [...items].filter((item) => !hasClass(findSingle(item, 'a'), 'p-disabled'));\n\n            if (filteredItems[index]) {\n                this.focusedOptionIndex = filteredItems[index].getAttribute('id');\n                const buttonEl = findSingle(filteredItems[index], '[type=\"button\"]');\n\n                buttonEl && focus(buttonEl);\n            }\n        },\n        findPrevOptionIndex(index) {\n            const items = find(this.container, '[data-pc-section=\"item\"]');\n            const filteredItems = [...items].filter((item) => !hasClass(findSingle(item, 'a'), 'p-disabled'));\n            const newIndex = index === -1 ? filteredItems[filteredItems.length - 1].id : index;\n            let matchedOptionIndex = filteredItems.findIndex((link) => link.getAttribute('id') === newIndex);\n\n            matchedOptionIndex = index === -1 ? filteredItems.length - 1 : matchedOptionIndex - 1;\n\n            return matchedOptionIndex;\n        },\n        findNextOptionIndex(index) {\n            const items = find(this.container, '[data-pc-section=\"item\"]');\n            const filteredItems = [...items].filter((item) => !hasClass(findSingle(item, 'a'), 'p-disabled'));\n            const newIndex = index === -1 ? filteredItems[0].id : index;\n            let matchedOptionIndex = filteredItems.findIndex((link) => link.getAttribute('id') === newIndex);\n\n            matchedOptionIndex = index === -1 ? 0 : matchedOptionIndex + 1;\n\n            return matchedOptionIndex;\n        },\n        calculatePointStyle(index) {\n            const type = this.type;\n\n            if (type !== 'linear') {\n                const length = this.model.length;\n                const radius = this.radius || length * 20;\n\n                if (type === 'circle') {\n                    const step = (2 * Math_PI) / length;\n\n                    return {\n                        left: `calc(${radius * Math.cos(step * index)}px + ${$dt('item.diff.x', '0px').variable})`,\n                        top: `calc(${radius * Math.sin(step * index)}px + ${$dt('item.diff.y', '0px').variable})`\n                    };\n                } else if (type === 'semi-circle') {\n                    const direction = this.direction;\n                    const step = Math_PI / (length - 1);\n                    const x = `calc(${radius * Math.cos(step * index)}px + ${$dt('item.diff.x', '0px').variable})`;\n                    const y = `calc(${radius * Math.sin(step * index)}px + ${$dt('item.diff.y', '0px').variable})`;\n\n                    if (direction === 'up') {\n                        return { left: x, bottom: y };\n                    } else if (direction === 'down') {\n                        return { left: x, top: y };\n                    } else if (direction === 'left') {\n                        return { right: y, top: x };\n                    } else if (direction === 'right') {\n                        return { left: y, top: x };\n                    }\n                } else if (type === 'quarter-circle') {\n                    const direction = this.direction;\n                    const step = Math_PI / (2 * (length - 1));\n                    const x = `calc(${radius * Math.cos(step * index)}px + ${$dt('item.diff.x', '0px').variable})`;\n                    const y = `calc(${radius * Math.sin(step * index)}px + ${$dt('item.diff.y', '0px').variable})`;\n\n                    if (direction === 'up-left') {\n                        return { right: x, bottom: y };\n                    } else if (direction === 'up-right') {\n                        return { left: x, bottom: y };\n                    } else if (direction === 'down-left') {\n                        return { right: y, top: x };\n                    } else if (direction === 'down-right') {\n                        return { left: y, top: x };\n                    }\n                }\n            }\n\n            return {};\n        },\n        getItemStyle(index) {\n            const transitionDelay = this.calculateTransitionDelay(index);\n            const pointStyle = this.calculatePointStyle(index);\n\n            return {\n                transitionDelay: `${transitionDelay}ms`,\n                ...pointStyle\n            };\n        },\n        bindDocumentClickListener() {\n            if (!this.documentClickListener) {\n                this.documentClickListener = (event) => {\n                    if (this.d_visible && this.isOutsideClicked(event)) {\n                        this.hide();\n                    }\n\n                    this.isItemClicked = false;\n                };\n\n                document.addEventListener('click', this.documentClickListener);\n            }\n        },\n        unbindDocumentClickListener() {\n            if (this.documentClickListener) {\n                document.removeEventListener('click', this.documentClickListener);\n                this.documentClickListener = null;\n            }\n        },\n        isOutsideClicked(event) {\n            return this.container && !(this.container.isSameNode(event.target) || this.container.contains(event.target) || this.isItemClicked);\n        },\n        isItemVisible(item) {\n            return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n        },\n        isItemActive(id) {\n            return id === this.focusedOptionId;\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        listRef(el) {\n            this.list = el;\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.cx('root'), this.class];\n        },\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : null;\n        }\n    },\n    components: {\n        Button,\n        PlusIcon\n    },\n    directives: {\n        ripple: Ripple,\n        tooltip: Tooltip\n    }\n};\n</script>\n", "<template>\n    <div :ref=\"containerRef\" :class=\"containerClass\" :style=\"[style, sx('root')]\" v-bind=\"ptmi('root')\">\n        <slot name=\"button\" :visible=\"d_visible\" :toggleCallback=\"onClick\">\n            <Button\n                :class=\"[cx('pcButton'), buttonClass]\"\n                :disabled=\"disabled\"\n                :aria-expanded=\"d_visible\"\n                :aria-haspopup=\"true\"\n                :aria-controls=\"$id + '_list'\"\n                :aria-label=\"ariaLabel\"\n                :aria-labelledby=\"ariaLabelledby\"\n                :unstyled=\"unstyled\"\n                @click=\"onClick($event)\"\n                @keydown=\"onTogglerKeydown\"\n                v-bind=\"buttonProps\"\n                :pt=\"ptm('pcButton')\"\n            >\n                <template #icon=\"slotProps\">\n                    <slot name=\"icon\" :visible=\"d_visible\">\n                        <component v-if=\"d_visible && !!hideIcon\" :is=\"hideIcon ? 'span' : 'PlusIcon'\" :class=\"[hideIcon, slotProps.class]\" v-bind=\"ptm('pcButton')['icon']\" data-pc-section=\"icon\" />\n                        <component v-else :is=\"showIcon ? 'span' : 'PlusIcon'\" :class=\"[d_visible && !!hideIcon ? hideIcon : showIcon, slotProps.class]\" v-bind=\"ptm('pcButton')['icon']\" data-pc-section=\"icon\" />\n                    </slot>\n                </template>\n            </Button>\n        </slot>\n        <ul :ref=\"listRef\" :id=\"$id + '_list'\" :class=\"cx('list')\" :style=\"sx('list')\" role=\"menu\" tabindex=\"-1\" @focus=\"onFocus\" @blur=\"onBlur\" @keydown=\"onKeyDown\" v-bind=\"ptm('list')\">\n            <template v-for=\"(item, index) of model\" :key=\"index\">\n                <li\n                    v-if=\"isItemVisible(item)\"\n                    :id=\"`${$id}_${index}`\"\n                    :class=\"cx('item', { id: `${$id}_${index}` })\"\n                    :style=\"getItemStyle(index)\"\n                    role=\"none\"\n                    :data-p-active=\"isItemActive(`${$id}_${index}`)\"\n                    v-bind=\"getPTOptions(`${$id}_${index}`, 'item')\"\n                >\n                    <template v-if=\"!$slots.item\">\n                        <Button\n                            v-tooltip:[tooltipOptions]=\"{ value: item.label, disabled: !tooltipOptions }\"\n                            :tabindex=\"-1\"\n                            role=\"menuitem\"\n                            :class=\"cx('pcAction', { item })\"\n                            :aria-label=\"item.label\"\n                            :disabled=\"disabled\"\n                            :unstyled=\"unstyled\"\n                            @click=\"onItemClick($event, item)\"\n                            v-bind=\"actionButtonProps\"\n                            :pt=\"getPTOptions(`${$id}_${index}`, 'pcAction')\"\n                        >\n                            <template v-if=\"item.icon\" #icon=\"slotProps\">\n                                <slot name=\"itemicon\" :item=\"item\" :class=\"slotProps.class\">\n                                    <span :class=\"[item.icon, slotProps.class]\" v-bind=\"getPTOptions(`${$id}_${index}`, 'actionIcon')\"></span>\n                                </slot>\n                            </template>\n                        </Button>\n                    </template>\n                    <component v-else :is=\"$slots.item\" :item=\"item\" :onClick=\"(event) => onItemClick(event, item)\" :toggleCallback=\"(event) => onItemClick(event, item)\"></component>\n                </li>\n            </template>\n        </ul>\n    </div>\n    <template v-if=\"mask\">\n        <div :class=\"[cx('mask'), maskClass]\" :style=\"maskStyle\" v-bind=\"ptm('mask')\"></div>\n    </template>\n</template>\n\n<script>\nimport { $dt } from '@primeuix/styled';\nimport { find, findSingle, focus, hasClass } from '@primeuix/utils/dom';\nimport PlusIcon from '@primevue/icons/plus';\nimport Button from 'primevue/button';\nimport Ripple from 'primevue/ripple';\nimport Tooltip from 'primevue/tooltip';\nimport BaseSpeedDial from './BaseSpeedDial.vue';\n\n// Set fix value for SSR.\nconst Math_PI = 3.14159265358979;\n\nexport default {\n    name: 'SpeedDial',\n    extends: BaseSpeedDial,\n    inheritAttrs: false,\n    emits: ['click', 'show', 'hide', 'focus', 'blur'],\n    documentClickListener: null,\n    container: null,\n    list: null,\n    data() {\n        return {\n            d_visible: this.visible,\n            isItemClicked: false,\n            focused: false,\n            focusedOptionIndex: -1\n        };\n    },\n    watch: {\n        visible(newValue) {\n            this.d_visible = newValue;\n        }\n    },\n    mounted() {\n        if (this.type !== 'linear') {\n            const button = findSingle(this.container, '[data-pc-name=\"pcbutton\"]');\n            const firstItem = findSingle(this.list, '[data-pc-section=\"item\"]');\n\n            if (button && firstItem) {\n                const wDiff = Math.abs(button.offsetWidth - firstItem.offsetWidth);\n                const hDiff = Math.abs(button.offsetHeight - firstItem.offsetHeight);\n\n                this.list.style.setProperty($dt('item.diff.x').name, `${wDiff / 2}px`);\n                this.list.style.setProperty($dt('item.diff.y').name, `${hDiff / 2}px`);\n            }\n        }\n\n        if (this.hideOnClickOutside) {\n            this.bindDocumentClickListener();\n        }\n    },\n    beforeUnmount() {\n        this.unbindDocumentClickListener();\n    },\n    methods: {\n        getPTOptions(id, key) {\n            return this.ptm(key, {\n                context: {\n                    active: this.isItemActive(id),\n                    hidden: !this.d_visible\n                }\n            });\n        },\n        onFocus(event) {\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.focusedOptionIndex = -1;\n            this.$emit('blur', event);\n        },\n        onItemClick(e, item) {\n            if (item.command) {\n                item.command({ originalEvent: e, item });\n            }\n\n            this.hide();\n\n            this.isItemClicked = true;\n            e.preventDefault();\n        },\n        onClick(event) {\n            this.d_visible ? this.hide() : this.show();\n            this.isItemClicked = true;\n            this.$emit('click', event);\n        },\n        show() {\n            this.d_visible = true;\n            this.$emit('show');\n        },\n        hide() {\n            this.d_visible = false;\n            this.$emit('hide');\n        },\n        calculateTransitionDelay(index) {\n            const length = this.model.length;\n            const visible = this.d_visible;\n\n            return (visible ? index : length - index - 1) * this.transitionDelay;\n        },\n        onTogglerKeydown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                case 'ArrowLeft':\n                    this.onTogglerArrowDown(event);\n\n                    break;\n\n                case 'ArrowUp':\n                case 'ArrowRight':\n                    this.onTogglerArrowUp(event);\n\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey();\n\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onKeyDown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDown(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUp(event);\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeft(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRight(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                case 'Space':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onTogglerArrowUp(event) {\n            this.show();\n            this.navigatePrevItem(event);\n\n            event.preventDefault();\n        },\n        onTogglerArrowDown(event) {\n            this.show();\n            this.navigateNextItem(event);\n\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            const items = find(this.container, '[data-pc-section=\"item\"]');\n            const itemIndex = [...items].findIndex((item) => item.id === this.focusedOptionIndex);\n            const buttonEl = findSingle(this.container, 'button');\n\n            this.onItemClick(event, this.model[itemIndex]);\n            this.onBlur(event);\n\n            buttonEl && focus(buttonEl);\n        },\n        onEscapeKey() {\n            this.hide();\n\n            const buttonEl = findSingle(this.container, 'button');\n\n            buttonEl && focus(buttonEl);\n        },\n        onArrowUp(event) {\n            if (this.direction === 'down') {\n                this.navigatePrevItem(event);\n            } else {\n                this.navigateNextItem(event);\n            }\n        },\n        onArrowDown(event) {\n            if (this.direction === 'down') {\n                this.navigateNextItem(event);\n            } else {\n                this.navigatePrevItem(event);\n            }\n        },\n\n        onArrowLeft(event) {\n            const leftValidDirections = ['left', 'up-right', 'down-left'];\n            const rightValidDirections = ['right', 'up-left', 'down-right'];\n\n            if (leftValidDirections.includes(this.direction)) {\n                this.navigateNextItem(event);\n            } else if (rightValidDirections.includes(this.direction)) {\n                this.navigatePrevItem(event);\n            } else {\n                this.navigatePrevItem(event);\n            }\n        },\n\n        onArrowRight(event) {\n            const leftValidDirections = ['left', 'up-right', 'down-left'];\n            const rightValidDirections = ['right', 'up-left', 'down-right'];\n\n            if (leftValidDirections.includes(this.direction)) {\n                this.navigatePrevItem(event);\n            } else if (rightValidDirections.includes(this.direction)) {\n                this.navigateNextItem(event);\n            } else {\n                this.navigateNextItem(event);\n            }\n        },\n        onEndKey(event) {\n            event.preventDefault();\n\n            this.focusedOptionIndex = -1;\n            this.navigatePrevItem(event);\n        },\n        onHomeKey(event) {\n            event.preventDefault();\n\n            this.focusedOptionIndex = -1;\n            this.navigateNextItem(event);\n        },\n        navigateNextItem(event) {\n            const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex);\n\n            this.changeFocusedOptionIndex(optionIndex);\n\n            event.preventDefault();\n        },\n        navigatePrevItem(event) {\n            const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex);\n\n            this.changeFocusedOptionIndex(optionIndex);\n\n            event.preventDefault();\n        },\n        changeFocusedOptionIndex(index) {\n            const items = find(this.container, '[data-pc-section=\"item\"]');\n            const filteredItems = [...items].filter((item) => !hasClass(findSingle(item, 'a'), 'p-disabled'));\n\n            if (filteredItems[index]) {\n                this.focusedOptionIndex = filteredItems[index].getAttribute('id');\n                const buttonEl = findSingle(filteredItems[index], '[type=\"button\"]');\n\n                buttonEl && focus(buttonEl);\n            }\n        },\n        findPrevOptionIndex(index) {\n            const items = find(this.container, '[data-pc-section=\"item\"]');\n            const filteredItems = [...items].filter((item) => !hasClass(findSingle(item, 'a'), 'p-disabled'));\n            const newIndex = index === -1 ? filteredItems[filteredItems.length - 1].id : index;\n            let matchedOptionIndex = filteredItems.findIndex((link) => link.getAttribute('id') === newIndex);\n\n            matchedOptionIndex = index === -1 ? filteredItems.length - 1 : matchedOptionIndex - 1;\n\n            return matchedOptionIndex;\n        },\n        findNextOptionIndex(index) {\n            const items = find(this.container, '[data-pc-section=\"item\"]');\n            const filteredItems = [...items].filter((item) => !hasClass(findSingle(item, 'a'), 'p-disabled'));\n            const newIndex = index === -1 ? filteredItems[0].id : index;\n            let matchedOptionIndex = filteredItems.findIndex((link) => link.getAttribute('id') === newIndex);\n\n            matchedOptionIndex = index === -1 ? 0 : matchedOptionIndex + 1;\n\n            return matchedOptionIndex;\n        },\n        calculatePointStyle(index) {\n            const type = this.type;\n\n            if (type !== 'linear') {\n                const length = this.model.length;\n                const radius = this.radius || length * 20;\n\n                if (type === 'circle') {\n                    const step = (2 * Math_PI) / length;\n\n                    return {\n                        left: `calc(${radius * Math.cos(step * index)}px + ${$dt('item.diff.x', '0px').variable})`,\n                        top: `calc(${radius * Math.sin(step * index)}px + ${$dt('item.diff.y', '0px').variable})`\n                    };\n                } else if (type === 'semi-circle') {\n                    const direction = this.direction;\n                    const step = Math_PI / (length - 1);\n                    const x = `calc(${radius * Math.cos(step * index)}px + ${$dt('item.diff.x', '0px').variable})`;\n                    const y = `calc(${radius * Math.sin(step * index)}px + ${$dt('item.diff.y', '0px').variable})`;\n\n                    if (direction === 'up') {\n                        return { left: x, bottom: y };\n                    } else if (direction === 'down') {\n                        return { left: x, top: y };\n                    } else if (direction === 'left') {\n                        return { right: y, top: x };\n                    } else if (direction === 'right') {\n                        return { left: y, top: x };\n                    }\n                } else if (type === 'quarter-circle') {\n                    const direction = this.direction;\n                    const step = Math_PI / (2 * (length - 1));\n                    const x = `calc(${radius * Math.cos(step * index)}px + ${$dt('item.diff.x', '0px').variable})`;\n                    const y = `calc(${radius * Math.sin(step * index)}px + ${$dt('item.diff.y', '0px').variable})`;\n\n                    if (direction === 'up-left') {\n                        return { right: x, bottom: y };\n                    } else if (direction === 'up-right') {\n                        return { left: x, bottom: y };\n                    } else if (direction === 'down-left') {\n                        return { right: y, top: x };\n                    } else if (direction === 'down-right') {\n                        return { left: y, top: x };\n                    }\n                }\n            }\n\n            return {};\n        },\n        getItemStyle(index) {\n            const transitionDelay = this.calculateTransitionDelay(index);\n            const pointStyle = this.calculatePointStyle(index);\n\n            return {\n                transitionDelay: `${transitionDelay}ms`,\n                ...pointStyle\n            };\n        },\n        bindDocumentClickListener() {\n            if (!this.documentClickListener) {\n                this.documentClickListener = (event) => {\n                    if (this.d_visible && this.isOutsideClicked(event)) {\n                        this.hide();\n                    }\n\n                    this.isItemClicked = false;\n                };\n\n                document.addEventListener('click', this.documentClickListener);\n            }\n        },\n        unbindDocumentClickListener() {\n            if (this.documentClickListener) {\n                document.removeEventListener('click', this.documentClickListener);\n                this.documentClickListener = null;\n            }\n        },\n        isOutsideClicked(event) {\n            return this.container && !(this.container.isSameNode(event.target) || this.container.contains(event.target) || this.isItemClicked);\n        },\n        isItemVisible(item) {\n            return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n        },\n        isItemActive(id) {\n            return id === this.focusedOptionId;\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        listRef(el) {\n            this.list = el;\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.cx('root'), this.class];\n        },\n        focusedOptionId() {\n            return this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : null;\n        }\n    },\n    components: {\n        Button,\n        PlusIcon\n    },\n    directives: {\n        ripple: Ripple,\n        tooltip: Tooltip\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "model", "visible", "type", "Boolean", "direction", "String", "transitionDelay", "Number", "radius", "mask", "disabled", "hideOnClickOutside", "buttonClass", "maskStyle", "maskClass", "showIcon", "undefined", "hideIcon", "rotateAnimation", "tooltipOptions", "style", "buttonProps", "Object", "default", "rounded", "actionButtonProps", "severity", "size", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "SpeedDialStyle", "provide", "$pcSpeedDial", "$parentInstance", "Math_PI", "BaseSpeedDial", "inheritAttrs", "emits", "documentClickListener", "container", "list", "data", "d_visible", "isItemClicked", "focused", "focusedOptionIndex", "watch", "newValue", "mounted", "button", "findSingle", "firstItem", "wDiff", "Math", "abs", "offsetWidth", "hDiff", "offsetHeight", "setProperty", "$dt", "concat", "bindDocumentClickListener", "beforeUnmount", "unbindDocumentClickListener", "methods", "getPTOptions", "id", "key", "ptm", "context", "active", "isItemActive", "hidden", "onFocus", "event", "$emit", "onBlur", "onItemClick", "e", "item", "command", "originalEvent", "hide", "preventDefault", "onClick", "show", "calculateTransitionDelay", "index", "length", "onTogglerKeydown", "code", "onTogglerArrowDown", "onTogglerArrowUp", "onEscapeKey", "onKeyDown", "onArrowDown", "onArrowUp", "onArrowLeft", "onArrowRight", "onEnterKey", "onHomeKey", "onEndKey", "navigatePrevItem", "navigateNextItem", "_this", "items", "find", "itemIndex", "_toConsumableArray", "findIndex", "buttonEl", "focus", "leftValidDirections", "rightValidDirections", "includes", "optionIndex", "findNextOptionIndex", "changeFocusedOptionIndex", "findPrevOptionIndex", "filteredItems", "filter", "hasClass", "getAttribute", "newIndex", "matchedOptionIndex", "link", "calculatePointStyle", "step", "left", "cos", "variable", "top", "sin", "x", "y", "bottom", "right", "getItemStyle", "pointStyle", "_objectSpread", "_this2", "isOutsideClicked", "document", "addEventListener", "removeEventListener", "isSameNode", "target", "contains", "isItemVisible", "focusedOptionId", "containerRef", "el", "listRef", "computed", "containerClass", "cx", "components", "<PERSON><PERSON>", "PlusIcon", "directives", "ripple", "<PERSON><PERSON><PERSON>", "tooltip", "<PERSON><PERSON><PERSON>", "_createElementVNode", "_mergeProps", "ref", "$options", "_ctx", "sx", "ptmi", "_renderSlot", "$slots", "$data", "toggleCallback", "_createVNode", "_component_<PERSON><PERSON>", "$id", "unstyled", "_cache", "$event", "onKeydown", "pt", "icon", "_withCtx", "slotProps", "_createBlock", "_resolveDynamicComponent", "role", "tabindex", "apply", "arguments", "_openBlock", "_createElementBlock", "_Fragment", "_renderList", "_withDirectives", "label", "fn", "_normalizeClass", "_directive_tooltip", "value"], "mappings": ";;;;;;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,eAAe;AACrB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE,IAAI;AACXC,IAAAA,OAAO,EAAE;AACLC,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,SAAS,EAAE;AACPF,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,eAAe,EAAE;AACbJ,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;KACZ;AACDL,IAAAA,IAAI,EAAE;AACFA,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAAS,EAAA;KACZ;AACDG,IAAAA,MAAM,EAAE;AACJN,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;KACZ;AACDE,IAAAA,IAAI,EAAE;AACFP,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDO,IAAAA,QAAQ,EAAE;AACNR,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDQ,IAAAA,kBAAkB,EAAE;AAChBT,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDS,IAAAA,WAAW,EAAE,IAAI;AACjBC,IAAAA,SAAS,EAAE,IAAI;AACfC,IAAAA,SAAS,EAAE,IAAI;AACfC,IAAAA,QAAQ,EAAE;AACNb,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAASW,EAAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNf,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAASW,EAAAA;KACZ;AACDE,IAAAA,eAAe,EAAE;AACbhB,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDgB,IAAAA,cAAc,EAAE,IAAI;AACpBC,IAAAA,KAAK,EAAE,IAAI;AACX,IAAA,OAAA,EAAO,IAAI;AACXC,IAAAA,WAAW,EAAE;AACTnB,MAAAA,IAAI,EAAEoB,MAAM;MAAA,SACZC,EAAAA,SAAAA,QAAOA,GAAG;QACN,OAAO;AAAEC,UAAAA,OAAO,EAAE;SAAM;AAC5B;KACH;AACDC,IAAAA,iBAAiB,EAAE;AACfvB,MAAAA,IAAI,EAAEoB,MAAM;MAAA,SACZC,EAAAA,SAAAA,QAAOA,GAAG;QACN,OAAO;AAAEG,UAAAA,QAAQ,EAAE,WAAW;AAAEF,UAAAA,OAAO,EAAE,IAAI;AAAEG,UAAAA,IAAI,EAAE;SAAS;AAClE;KACH;AACDC,IAAAA,cAAc,EAAE;AACZ1B,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAAS,EAAA;KACZ;AACDwB,IAAAA,SAAS,EAAE;AACP3B,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDe,EAAAA,KAAK,EAAEU,cAAc;EACrBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,YAAY,EAAE,IAAI;AAClBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;;;;;;;;;;ACZD;AACA,IAAMC,OAAM,GAAI,gBAAgB;AAEhC,aAAe;AACXrC,EAAAA,IAAI,EAAE,WAAW;AACjB,EAAA,SAAA,EAASsC,QAAa;AACtBC,EAAAA,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;AACjDC,EAAAA,qBAAqB,EAAE,IAAI;AAC3BC,EAAAA,SAAS,EAAE,IAAI;AACfC,EAAAA,IAAI,EAAE,IAAI;EACVC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;MACHC,SAAS,EAAE,IAAI,CAACzC,OAAO;AACvB0C,MAAAA,aAAa,EAAE,KAAK;AACpBC,MAAAA,OAAO,EAAE,KAAK;AACdC,MAAAA,kBAAkB,EAAE;KACvB;GACJ;AACDC,EAAAA,KAAK,EAAE;AACH7C,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAAC8C,QAAQ,EAAE;MACd,IAAI,CAACL,SAAQ,GAAIK,QAAQ;AAC7B;GACH;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,IAAA,IAAI,IAAI,CAAC9C,IAAG,KAAM,QAAQ,EAAE;MACxB,IAAM+C,MAAK,GAAIC,UAAU,CAAC,IAAI,CAACX,SAAS,EAAE,2BAA2B,CAAC;MACtE,IAAMY,SAAU,GAAED,UAAU,CAAC,IAAI,CAACV,IAAI,EAAE,0BAA0B,CAAC;MAEnE,IAAIS,MAAO,IAAGE,SAAS,EAAE;AACrB,QAAA,IAAMC,KAAI,GAAIC,IAAI,CAACC,GAAG,CAACL,MAAM,CAACM,WAAU,GAAIJ,SAAS,CAACI,WAAW,CAAC;AAClE,QAAA,IAAMC,KAAM,GAAEH,IAAI,CAACC,GAAG,CAACL,MAAM,CAACQ,YAAW,GAAIN,SAAS,CAACM,YAAY,CAAC;QAEpE,IAAI,CAACjB,IAAI,CAACpB,KAAK,CAACsC,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC9D,IAAI,KAAA+D,MAAA,CAAKR,KAAM,GAAE,CAAC,OAAI,CAAC;QACtE,IAAI,CAACZ,IAAI,CAACpB,KAAK,CAACsC,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC9D,IAAI,KAAA+D,MAAA,CAAKJ,KAAM,GAAE,CAAC,OAAI,CAAC;AAC1E;AACJ;IAEA,IAAI,IAAI,CAAC7C,kBAAkB,EAAE;MACzB,IAAI,CAACkD,yBAAyB,EAAE;AACpC;GACH;EACDC,aAAa,EAAA,SAAbA,aAAaA,GAAG;IACZ,IAAI,CAACC,2BAA2B,EAAE;GACrC;AACDC,EAAAA,OAAO,EAAE;AACLC,IAAAA,YAAY,WAAZA,YAAYA,CAACC,EAAE,EAAEC,GAAG,EAAE;AAClB,MAAA,OAAO,IAAI,CAACC,GAAG,CAACD,GAAG,EAAE;AACjBE,QAAAA,OAAO,EAAE;AACLC,UAAAA,MAAM,EAAE,IAAI,CAACC,YAAY,CAACL,EAAE,CAAC;UAC7BM,MAAM,EAAE,CAAC,IAAI,CAAC9B;AAClB;AACJ,OAAC,CAAC;KACL;AACD+B,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,KAAK,EAAE;AACX,MAAA,IAAI,CAACC,KAAK,CAAC,OAAO,EAAED,KAAK,CAAC;KAC7B;AACDE,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACF,KAAK,EAAE;AACV,MAAA,IAAI,CAAC7B,kBAAiB,GAAI,EAAE;AAC5B,MAAA,IAAI,CAAC8B,KAAK,CAAC,MAAM,EAAED,KAAK,CAAC;KAC5B;AACDG,IAAAA,WAAW,WAAXA,WAAWA,CAACC,CAAC,EAAEC,IAAI,EAAE;MACjB,IAAIA,IAAI,CAACC,OAAO,EAAE;QACdD,IAAI,CAACC,OAAO,CAAC;AAAEC,UAAAA,aAAa,EAAEH,CAAC;AAAEC,UAAAA,IAAG,EAAHA;AAAK,SAAC,CAAC;AAC5C;MAEA,IAAI,CAACG,IAAI,EAAE;MAEX,IAAI,CAACvC,gBAAgB,IAAI;MACzBmC,CAAC,CAACK,cAAc,EAAE;KACrB;AACDC,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACV,KAAK,EAAE;AACX,MAAA,IAAI,CAAChC,SAAQ,GAAI,IAAI,CAACwC,IAAI,EAAC,GAAI,IAAI,CAACG,IAAI,EAAE;MAC1C,IAAI,CAAC1C,gBAAgB,IAAI;AACzB,MAAA,IAAI,CAACgC,KAAK,CAAC,OAAO,EAAED,KAAK,CAAC;KAC7B;IACDW,IAAI,EAAA,SAAJA,IAAIA,GAAG;MACH,IAAI,CAAC3C,SAAQ,GAAI,IAAI;AACrB,MAAA,IAAI,CAACiC,KAAK,CAAC,MAAM,CAAC;KACrB;IACDO,IAAI,EAAA,SAAJA,IAAIA,GAAG;MACH,IAAI,CAACxC,SAAU,GAAE,KAAK;AACtB,MAAA,IAAI,CAACiC,KAAK,CAAC,MAAM,CAAC;KACrB;AACDW,IAAAA,wBAAwB,EAAxBA,SAAAA,wBAAwBA,CAACC,KAAK,EAAE;AAC5B,MAAA,IAAMC,MAAO,GAAE,IAAI,CAACxF,KAAK,CAACwF,MAAM;AAChC,MAAA,IAAMvF,OAAQ,GAAE,IAAI,CAACyC,SAAS;AAE9B,MAAA,OAAO,CAACzC,UAAUsF,KAAI,GAAIC,MAAO,GAAED,KAAI,GAAI,CAAC,IAAI,IAAI,CAACjF,eAAe;KACvE;AACDmF,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAACf,KAAK,EAAE;MACpB,QAAQA,KAAK,CAACgB,IAAI;AACd,QAAA,KAAK,WAAW;AAChB,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACC,kBAAkB,CAACjB,KAAK,CAAC;AAE9B,UAAA;AAEJ,QAAA,KAAK,SAAS;AACd,QAAA,KAAK,YAAY;AACb,UAAA,IAAI,CAACkB,gBAAgB,CAAClB,KAAK,CAAC;AAE5B,UAAA;AAEJ,QAAA,KAAK,QAAQ;UACT,IAAI,CAACmB,WAAW,EAAE;AAElB,UAAA;AAIR;KACH;AACDC,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACpB,KAAK,EAAE;MACb,QAAQA,KAAK,CAACgB,IAAI;AACd,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACK,WAAW,CAACrB,KAAK,CAAC;AACvB,UAAA;AAEJ,QAAA,KAAK,SAAS;AACV,UAAA,IAAI,CAACsB,SAAS,CAACtB,KAAK,CAAC;AACrB,UAAA;AAEJ,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACuB,WAAW,CAACvB,KAAK,CAAC;AACvB,UAAA;AAEJ,QAAA,KAAK,YAAY;AACb,UAAA,IAAI,CAACwB,YAAY,CAACxB,KAAK,CAAC;AACxB,UAAA;AAEJ,QAAA,KAAK,OAAO;AACZ,QAAA,KAAK,aAAa;AAClB,QAAA,KAAK,OAAO;AACR,UAAA,IAAI,CAACyB,UAAU,CAACzB,KAAK,CAAC;AACtB,UAAA;AAEJ,QAAA,KAAK,QAAQ;AACT,UAAA,IAAI,CAACmB,WAAW,CAACnB,KAAK,CAAC;AACvB,UAAA;AAEJ,QAAA,KAAK,MAAM;AACP,UAAA,IAAI,CAAC0B,SAAS,CAAC1B,KAAK,CAAC;AACrB,UAAA;AAEJ,QAAA,KAAK,KAAK;AACN,UAAA,IAAI,CAAC2B,QAAQ,CAAC3B,KAAK,CAAC;AACpB,UAAA;AAIR;KACH;AACDkB,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAClB,KAAK,EAAE;MACpB,IAAI,CAACW,IAAI,EAAE;AACX,MAAA,IAAI,CAACiB,gBAAgB,CAAC5B,KAAK,CAAC;MAE5BA,KAAK,CAACS,cAAc,EAAE;KACzB;AACDQ,IAAAA,kBAAkB,EAAlBA,SAAAA,kBAAkBA,CAACjB,KAAK,EAAE;MACtB,IAAI,CAACW,IAAI,EAAE;AACX,MAAA,IAAI,CAACkB,gBAAgB,CAAC7B,KAAK,CAAC;MAE5BA,KAAK,CAACS,cAAc,EAAE;KACzB;AACDgB,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACzB,KAAK,EAAE;AAAA,MAAA,IAAA8B,KAAA,GAAA,IAAA;MACd,IAAMC,KAAI,GAAIC,IAAI,CAAC,IAAI,CAACnE,SAAS,EAAE,0BAA0B,CAAC;MAC9D,IAAMoE,SAAQ,GAAIC,kBAAA,CAAIH,KAAK,CAAEI,CAAAA,SAAS,CAAC,UAAC9B,IAAI,EAAA;AAAA,QAAA,OAAKA,IAAI,CAACb,EAAC,KAAMsC,KAAI,CAAC3D,kBAAkB;OAAC,CAAA;MACrF,IAAMiE,QAAS,GAAE5D,UAAU,CAAC,IAAI,CAACX,SAAS,EAAE,QAAQ,CAAC;MAErD,IAAI,CAACsC,WAAW,CAACH,KAAK,EAAE,IAAI,CAAC1E,KAAK,CAAC2G,SAAS,CAAC,CAAC;AAC9C,MAAA,IAAI,CAAC/B,MAAM,CAACF,KAAK,CAAC;AAElBoC,MAAAA,QAAO,IAAKC,KAAK,CAACD,QAAQ,CAAC;KAC9B;IACDjB,WAAW,EAAA,SAAXA,WAAWA,GAAG;MACV,IAAI,CAACX,IAAI,EAAE;MAEX,IAAM4B,QAAS,GAAE5D,UAAU,CAAC,IAAI,CAACX,SAAS,EAAE,QAAQ,CAAC;AAErDuE,MAAAA,QAAO,IAAKC,KAAK,CAACD,QAAQ,CAAC;KAC9B;AACDd,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACtB,KAAK,EAAE;AACb,MAAA,IAAI,IAAI,CAACtE,SAAU,KAAI,MAAM,EAAE;AAC3B,QAAA,IAAI,CAACkG,gBAAgB,CAAC5B,KAAK,CAAC;AAChC,OAAE,MAAK;AACH,QAAA,IAAI,CAAC6B,gBAAgB,CAAC7B,KAAK,CAAC;AAChC;KACH;AACDqB,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACrB,KAAK,EAAE;AACf,MAAA,IAAI,IAAI,CAACtE,SAAU,KAAI,MAAM,EAAE;AAC3B,QAAA,IAAI,CAACmG,gBAAgB,CAAC7B,KAAK,CAAC;AAChC,OAAE,MAAK;AACH,QAAA,IAAI,CAAC4B,gBAAgB,CAAC5B,KAAK,CAAC;AAChC;KACH;AAEDuB,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACvB,KAAK,EAAE;MACf,IAAMsC,mBAAkB,GAAI,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC;MAC7D,IAAMC,oBAAmB,GAAI,CAAC,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC;MAE/D,IAAID,mBAAmB,CAACE,QAAQ,CAAC,IAAI,CAAC9G,SAAS,CAAC,EAAE;AAC9C,QAAA,IAAI,CAACmG,gBAAgB,CAAC7B,KAAK,CAAC;OAC9B,MAAK,IAAIuC,oBAAoB,CAACC,QAAQ,CAAC,IAAI,CAAC9G,SAAS,CAAC,EAAE;AACtD,QAAA,IAAI,CAACkG,gBAAgB,CAAC5B,KAAK,CAAC;AAChC,OAAE,MAAK;AACH,QAAA,IAAI,CAAC4B,gBAAgB,CAAC5B,KAAK,CAAC;AAChC;KACH;AAEDwB,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACxB,KAAK,EAAE;MAChB,IAAMsC,mBAAkB,GAAI,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC;MAC7D,IAAMC,oBAAmB,GAAI,CAAC,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC;MAE/D,IAAID,mBAAmB,CAACE,QAAQ,CAAC,IAAI,CAAC9G,SAAS,CAAC,EAAE;AAC9C,QAAA,IAAI,CAACkG,gBAAgB,CAAC5B,KAAK,CAAC;OAC9B,MAAK,IAAIuC,oBAAoB,CAACC,QAAQ,CAAC,IAAI,CAAC9G,SAAS,CAAC,EAAE;AACtD,QAAA,IAAI,CAACmG,gBAAgB,CAAC7B,KAAK,CAAC;AAChC,OAAE,MAAK;AACH,QAAA,IAAI,CAAC6B,gBAAgB,CAAC7B,KAAK,CAAC;AAChC;KACH;AACD2B,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAAC3B,KAAK,EAAE;MACZA,KAAK,CAACS,cAAc,EAAE;AAEtB,MAAA,IAAI,CAACtC,kBAAiB,GAAI,EAAE;AAC5B,MAAA,IAAI,CAACyD,gBAAgB,CAAC5B,KAAK,CAAC;KAC/B;AACD0B,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAAC1B,KAAK,EAAE;MACbA,KAAK,CAACS,cAAc,EAAE;AAEtB,MAAA,IAAI,CAACtC,kBAAiB,GAAI,EAAE;AAC5B,MAAA,IAAI,CAAC0D,gBAAgB,CAAC7B,KAAK,CAAC;KAC/B;AACD6B,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAC7B,KAAK,EAAE;MACpB,IAAMyC,WAAU,GAAI,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACvE,kBAAkB,CAAC;AAErE,MAAA,IAAI,CAACwE,wBAAwB,CAACF,WAAW,CAAC;MAE1CzC,KAAK,CAACS,cAAc,EAAE;KACzB;AACDmB,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAC5B,KAAK,EAAE;MACpB,IAAMyC,WAAU,GAAI,IAAI,CAACG,mBAAmB,CAAC,IAAI,CAACzE,kBAAkB,CAAC;AAErE,MAAA,IAAI,CAACwE,wBAAwB,CAACF,WAAW,CAAC;MAE1CzC,KAAK,CAACS,cAAc,EAAE;KACzB;AACDkC,IAAAA,wBAAwB,EAAxBA,SAAAA,wBAAwBA,CAAC9B,KAAK,EAAE;MAC5B,IAAMkB,KAAI,GAAIC,IAAI,CAAC,IAAI,CAACnE,SAAS,EAAE,0BAA0B,CAAC;MAC9D,IAAMgF,aAAc,GAAEX,kBAAA,CAAIH,KAAK,CAAEe,CAAAA,MAAM,CAAC,UAACzC,IAAI,EAAA;QAAA,OAAK,CAAC0C,QAAQ,CAACvE,UAAU,CAAC6B,IAAI,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC;OAAC,CAAA;AAEjG,MAAA,IAAIwC,aAAa,CAAChC,KAAK,CAAC,EAAE;QACtB,IAAI,CAAC1C,kBAAiB,GAAI0E,aAAa,CAAChC,KAAK,CAAC,CAACmC,YAAY,CAAC,IAAI,CAAC;QACjE,IAAMZ,QAAO,GAAI5D,UAAU,CAACqE,aAAa,CAAChC,KAAK,CAAC,EAAE,iBAAiB,CAAC;AAEpEuB,QAAAA,QAAO,IAAKC,KAAK,CAACD,QAAQ,CAAC;AAC/B;KACH;AACDQ,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAAC/B,KAAK,EAAE;MACvB,IAAMkB,KAAI,GAAIC,IAAI,CAAC,IAAI,CAACnE,SAAS,EAAE,0BAA0B,CAAC;MAC9D,IAAMgF,aAAc,GAAEX,kBAAA,CAAIH,KAAK,CAAEe,CAAAA,MAAM,CAAC,UAACzC,IAAI,EAAA;QAAA,OAAK,CAAC0C,QAAQ,CAACvE,UAAU,CAAC6B,IAAI,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC;OAAC,CAAA;AACjG,MAAA,IAAM4C,QAAS,GAAEpC,KAAI,KAAM,KAAKgC,aAAa,CAACA,aAAa,CAAC/B,MAAK,GAAI,CAAC,CAAC,CAACtB,KAAKqB,KAAK;AAClF,MAAA,IAAIqC,kBAAiB,GAAIL,aAAa,CAACV,SAAS,CAAC,UAACgB,IAAI,EAAA;AAAA,QAAA,OAAKA,IAAI,CAACH,YAAY,CAAC,IAAI,CAAE,KAAIC,QAAQ;OAAC,CAAA;AAEhGC,MAAAA,kBAAmB,GAAErC,UAAU,KAAKgC,aAAa,CAAC/B,MAAK,GAAI,CAAA,GAAIoC,kBAAiB,GAAI,CAAC;AAErF,MAAA,OAAOA,kBAAkB;KAC5B;AACDR,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAAC7B,KAAK,EAAE;MACvB,IAAMkB,KAAI,GAAIC,IAAI,CAAC,IAAI,CAACnE,SAAS,EAAE,0BAA0B,CAAC;MAC9D,IAAMgF,aAAc,GAAEX,kBAAA,CAAIH,KAAK,CAAEe,CAAAA,MAAM,CAAC,UAACzC,IAAI,EAAA;QAAA,OAAK,CAAC0C,QAAQ,CAACvE,UAAU,CAAC6B,IAAI,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC;OAAC,CAAA;AACjG,MAAA,IAAM4C,WAAWpC,UAAU,EAAG,GAAEgC,aAAa,CAAC,CAAC,CAAC,CAACrD,EAAC,GAAIqB,KAAK;AAC3D,MAAA,IAAIqC,kBAAiB,GAAIL,aAAa,CAACV,SAAS,CAAC,UAACgB,IAAI,EAAA;AAAA,QAAA,OAAKA,IAAI,CAACH,YAAY,CAAC,IAAI,CAAE,KAAIC,QAAQ;OAAC,CAAA;MAEhGC,kBAAmB,GAAErC,KAAI,KAAM,EAAC,GAAI,CAAE,GAAEqC,kBAAmB,GAAE,CAAC;AAE9D,MAAA,OAAOA,kBAAkB;KAC5B;AACDE,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAACvC,KAAK,EAAE;AACvB,MAAA,IAAMrF,IAAG,GAAI,IAAI,CAACA,IAAI;MAEtB,IAAIA,SAAS,QAAQ,EAAE;AACnB,QAAA,IAAMsF,MAAO,GAAE,IAAI,CAACxF,KAAK,CAACwF,MAAM;QAChC,IAAMhF,MAAO,GAAE,IAAI,CAACA,MAAK,IAAKgF,MAAO,GAAE,EAAE;QAEzC,IAAItF,SAAS,QAAQ,EAAE;AACnB,UAAA,IAAM6H,IAAK,GAAG,CAAA,GAAI7F,OAAO,GAAIsD,MAAM;UAEnC,OAAO;YACHwC,IAAI,EAAA,OAAA,CAAApE,MAAA,CAAUpD,MAAO,GAAE6C,IAAI,CAAC4E,GAAG,CAACF,OAAOxC,KAAK,CAAC,EAAA3B,OAAAA,CAAAA,CAAAA,MAAA,CAAQD,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAACuE,QAAQ,EAAG,GAAA,CAAA;YAC1FC,GAAG,EAAA,OAAA,CAAAvE,MAAA,CAAUpD,MAAO,GAAE6C,IAAI,CAAC+E,GAAG,CAACL,OAAOxC,KAAK,CAAC,EAAA,OAAA,CAAA,CAAA3B,MAAA,CAAQD,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAACuE,QAAQ,EAAA,GAAA;WACzF;AACL,SAAE,MAAK,IAAIhI,IAAG,KAAM,aAAa,EAAE;AAC/B,UAAA,IAAME,SAAQ,GAAI,IAAI,CAACA,SAAS;AAChC,UAAA,IAAM2H,KAAK,GAAE7F,OAAM,IAAKsD,MAAK,GAAI,CAAC,CAAC;UACnC,IAAM6C,CAAA,GAAAzE,OAAAA,CAAAA,MAAA,CAAYpD,MAAO,GAAE6C,IAAI,CAAC4E,GAAG,CAACF,KAAK,GAAExC,KAAK,CAAC,EAAA,OAAA,CAAA,CAAA3B,MAAA,CAAQD,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAACuE,QAAQ,EAAG,GAAA,CAAA;UAC9F,IAAMI,CAAA,GAAA1E,OAAAA,CAAAA,MAAA,CAAYpD,MAAO,GAAE6C,IAAI,CAAC+E,GAAG,CAACL,KAAK,GAAExC,KAAK,CAAC,EAAA,OAAA,CAAA,CAAA3B,MAAA,CAAQD,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAACuE,QAAQ,EAAG,GAAA,CAAA;UAE9F,IAAI9H,SAAQ,KAAM,IAAI,EAAE;YACpB,OAAO;AAAE4H,cAAAA,IAAI,EAAEK,CAAC;AAAEE,cAAAA,MAAM,EAAED;aAAG;AACjC,WAAE,MAAK,IAAIlI,SAAU,KAAI,MAAM,EAAE;YAC7B,OAAO;AAAE4H,cAAAA,IAAI,EAAEK,CAAC;AAAEF,cAAAA,GAAG,EAAEG;aAAG;AAC9B,WAAE,MAAK,IAAIlI,SAAU,KAAI,MAAM,EAAE;YAC7B,OAAO;AAAEoI,cAAAA,KAAK,EAAEF,CAAC;AAAEH,cAAAA,GAAG,EAAEE;aAAG;AAC/B,WAAE,MAAK,IAAIjI,SAAQ,KAAM,OAAO,EAAE;YAC9B,OAAO;AAAE4H,cAAAA,IAAI,EAAEM,CAAC;AAAEH,cAAAA,GAAG,EAAEE;aAAG;AAC9B;AACJ,SAAA,MAAO,IAAInI,IAAK,KAAI,gBAAgB,EAAE;AAClC,UAAA,IAAME,UAAQ,GAAI,IAAI,CAACA,SAAS;UAChC,IAAM2H,MAAG,GAAI7F,OAAQ,IAAG,CAAA,IAAKsD,MAAK,GAAI,CAAC,CAAC,CAAC;UACzC,IAAM6C,EAAA,GAAAzE,OAAAA,CAAAA,MAAA,CAAYpD,MAAO,GAAE6C,IAAI,CAAC4E,GAAG,CAACF,MAAK,GAAExC,KAAK,CAAC,EAAA,OAAA,CAAA,CAAA3B,MAAA,CAAQD,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAACuE,QAAQ,EAAG,GAAA,CAAA;UAC9F,IAAMI,EAAA,GAAA1E,OAAAA,CAAAA,MAAA,CAAYpD,MAAO,GAAE6C,IAAI,CAAC+E,GAAG,CAACL,MAAK,GAAExC,KAAK,CAAC,EAAA,OAAA,CAAA,CAAA3B,MAAA,CAAQD,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAACuE,QAAQ,EAAG,GAAA,CAAA;UAE9F,IAAI9H,UAAQ,KAAM,SAAS,EAAE;YACzB,OAAO;AAAEoI,cAAAA,KAAK,EAAEH,EAAC;AAAEE,cAAAA,MAAM,EAAED;aAAG;AAClC,WAAA,MAAO,IAAIlI,UAAQ,KAAM,UAAU,EAAE;YACjC,OAAO;AAAE4H,cAAAA,IAAI,EAAEK,EAAC;AAAEE,cAAAA,MAAM,EAAED;aAAG;AACjC,WAAA,MAAO,IAAIlI,UAAU,KAAI,WAAW,EAAE;YAClC,OAAO;AAAEoI,cAAAA,KAAK,EAAEF,EAAC;AAAEH,cAAAA,GAAG,EAAEE;aAAG;AAC/B,WAAA,MAAO,IAAIjI,UAAU,KAAI,YAAY,EAAE;YACnC,OAAO;AAAE4H,cAAAA,IAAI,EAAEM,EAAC;AAAEH,cAAAA,GAAG,EAAEE;aAAG;AAC9B;AACJ;AACJ;AAEA,MAAA,OAAO,EAAE;KACZ;AACDI,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAAClD,KAAK,EAAE;AAChB,MAAA,IAAMjF,eAAc,GAAI,IAAI,CAACgF,wBAAwB,CAACC,KAAK,CAAC;AAC5D,MAAA,IAAMmD,aAAa,IAAI,CAACZ,mBAAmB,CAACvC,KAAK,CAAC;AAElD,MAAA,OAAAoD,aAAA,CAAA;QACIrI,eAAe,EAAA,EAAA,CAAAsD,MAAA,CAAKtD,eAAe,EAAA,IAAA;AAAI,OAAA,EACpCoI,UAAS,CAAA;KAEnB;IACD7E,yBAAyB,EAAA,SAAzBA,yBAAyBA,GAAG;AAAA,MAAA,IAAA+E,MAAA,GAAA,IAAA;AACxB,MAAA,IAAI,CAAC,IAAI,CAACtG,qBAAqB,EAAE;AAC7B,QAAA,IAAI,CAACA,qBAAsB,GAAE,UAACoC,KAAK,EAAK;UACpC,IAAIkE,MAAI,CAAClG,SAAQ,IAAKkG,MAAI,CAACC,gBAAgB,CAACnE,KAAK,CAAC,EAAE;YAChDkE,MAAI,CAAC1D,IAAI,EAAE;AACf;UAEA0D,MAAI,CAACjG,aAAY,GAAI,KAAK;SAC7B;QAEDmG,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACzG,qBAAqB,CAAC;AAClE;KACH;IACDyB,2BAA2B,EAAA,SAA3BA,2BAA2BA,GAAG;MAC1B,IAAI,IAAI,CAACzB,qBAAqB,EAAE;QAC5BwG,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC1G,qBAAqB,CAAC;QACjE,IAAI,CAACA,qBAAsB,GAAE,IAAI;AACrC;KACH;AACDuG,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAACnE,KAAK,EAAE;AACpB,MAAA,OAAO,IAAI,CAACnC,SAAQ,IAAK,EAAE,IAAI,CAACA,SAAS,CAAC0G,UAAU,CAACvE,KAAK,CAACwE,MAAM,CAAE,IAAG,IAAI,CAAC3G,SAAS,CAAC4G,QAAQ,CAACzE,KAAK,CAACwE,MAAM,CAAA,IAAK,IAAI,CAACvG,aAAa,CAAC;KACrI;AACDyG,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACrE,IAAI,EAAE;AAChB,MAAA,OAAO,OAAOA,IAAI,CAAC9E,YAAY,UAAW,GAAE8E,IAAI,CAAC9E,OAAO,EAAC,GAAI8E,IAAI,CAAC9E,OAAQ,KAAI,KAAK;KACtF;AACDsE,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACL,EAAE,EAAE;AACb,MAAA,OAAOA,EAAG,KAAI,IAAI,CAACmF,eAAe;KACrC;AACDC,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACC,EAAE,EAAE;MACb,IAAI,CAAChH,SAAU,GAAEgH,EAAE;KACtB;AACDC,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACD,EAAE,EAAE;MACR,IAAI,CAAC/G,IAAG,GAAI+G,EAAE;AAClB;GACH;AACDE,EAAAA,QAAQ,EAAE;IACNC,cAAc,EAAA,SAAdA,cAAcA,GAAG;MACb,OAAO,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAA,OAAA,CAAM,CAAC;KACvC;IACDN,eAAe,EAAA,SAAfA,eAAeA,GAAG;MACd,OAAO,IAAI,CAACxG,kBAAiB,KAAM,EAAG,GAAE,IAAI,CAACA,kBAAmB,GAAE,IAAI;AAC1E;GACH;AACD+G,EAAAA,UAAU,EAAE;AACRC,IAAAA,MAAM,EAANA,MAAM;AACNC,IAAAA,QAAO,EAAPA;GACH;AACDC,EAAAA,UAAU,EAAE;AACRC,IAAAA,MAAM,EAAEC,MAAM;AACdC,IAAAA,OAAO,EAAEC;AACb;AACJ,CAAC;;;;;;;0DC9cGC,kBAAA,CA2DK,OA3DLC,UA2DK,CAAA;IA3DCC,GAAG,EAAEC,QAAY,CAAAjB,YAAA;IAAG,OAAOiB,EAAAA,QAAc,CAAAb,cAAA;IAAGtI,KAAK,EAAA,CAAGoJ,IAAK,CAAApJ,KAAA,EAAEoJ,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;KAAmBD,IAAI,CAAAE,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACtFC,UAsBM,CAAAH,IAAA,CAAAI,MAAA,EAAA,QAAA,EAAA;IAtBe3K,OAAO,EAAE4K,KAAS,CAAAnI,SAAA;IAAGoI,cAAc,EAAEP,QAAO,CAAAnF;KAAjE,YAAA;AAAA,IAAA,OAsBM,CArBF2F,WAAA,CAoBQC,mBApBRX,UAoBQ,CAAA;MAnBH,OAAK,EAAA,CAAGG,IAAE,CAAAb,EAAA,CAAA,UAAA,CAAA,EAAca,IAAW,CAAA5J,WAAA,CAAA;MACnCF,QAAQ,EAAE8J,IAAQ,CAAA9J,QAAA;MAClB,eAAa,EAAEmK,KAAS,CAAAnI,SAAA;AACxB,MAAA,eAAa,EAAE,IAAI;AACnB,MAAA,eAAa,EAAE8H,IAAI,CAAAS,GAAA,GAAA,OAAA;MACnB,YAAU,EAAET,IAAS,CAAA3I,SAAA;MACrB,iBAAe,EAAE2I,IAAc,CAAA5I,cAAA;MAC/BsJ,QAAQ,EAAEV,IAAQ,CAAAU,QAAA;AAClB9F,MAAAA,OAAK,EAAA+F,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,QAAA,OAAEb,QAAO,CAAAnF,OAAA,CAACgG,MAAM,CAAA;AAAA,OAAA,CAAA;MACrBC,SAAO,EAAEd,QAAgB,CAAA9E;OAClB+E,IAAW,CAAAnJ,WAAA,EAAA;AAClBiK,MAAAA,EAAE,EAAEd,IAAG,CAAApG,GAAA,CAAA,UAAA;;AAEGmH,MAAAA,IAAI,EAAAC,OAAA,CACX,UAGMC,SAJgB,EAAA;QAAA,OAAA,CACtBd,UAAA,CAGMH,IAHa,CAAAI,MAAA,EAAA,MAAA,EAAA;UAAA3K,OAAO,EAAE4K,KAAA,CAAAnI;WAA5B,YAAA;AAAA,UAAA,OAGM,CAFemI,KAAA,CAAAnI,SAAU,MAAK8H,IAAQ,CAAAvJ,QAAA,iBAAxCyK,WAA6K,CAAAC,uBAAA,CAA9HnB,IAAO,CAAAvJ,QAAA,GAAA,MAAA,GAAA,UAAA,CAAA,EAAtDoJ,UAA6K,CAAA;;AAA7F,YAAA,OAAA,EAAQ,CAAAG,IAAA,CAAAvJ,QAAQ,EAAEwK,SAAS,CAAM,OAAA,CAAA;aAAWjB,IAAG,CAAApG,GAAA,CAAA,UAAA,CAAA,CAAA,MAAA,CAAA,EAAA;AAAsB,YAAA,iBAAe,EAAC;oDACrKsH,WAA0L,CAAAC,uBAAA,CAAnKnB,IAAS,CAAAzJ,QAAA,GAAA,MAAA,GAAA,UAAA,CAAA,EAAhCsJ,UAA0L,CAAA;;YAAlI,OAAK,EAAA,CAAGQ,KAAU,CAAAnI,SAAA,IAAA,CAAA,CAAK8H,IAAO,CAAAvJ,QAAA,GAAIuJ,gBAAWA,IAAQ,CAAAzJ,QAAA,EAAE0K,SAAS,CAAM,OAAA,CAAA;aAAWjB,IAAG,CAAApG,GAAA,CAAA,UAAA,CAAA,CAAA,MAAA,CAAA,EAAA;AAAsB,YAAA,iBAAe,EAAC;;;;;;MAKlMgG,kBAAA,CAkCI,MAlCJC,UAkCI,CAAA;IAlCCC,GAAG,EAAEC,QAAO,CAAAf,OAAA;AAAGtF,IAAAA,EAAE,EAAEsG,IAAE,CAAAS,GAAA,GAAA,OAAA;AAAc,IAAA,OAAA,EAAOT,IAAE,CAAAb,EAAA,CAAA,MAAA,CAAA;AAAWvI,IAAAA,KAAK,EAAEoJ,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;AAAUmB,IAAAA,IAAI,EAAC,MAAK;AAAEC,IAAAA,QAAQ,EAAC;IAAMpH,OAAK;aAAE8F,QAAO,CAAA9F,OAAA,IAAA8F,QAAA,CAAA9F,OAAA,CAAAqH,KAAA,CAAAvB,QAAA,EAAAwB,SAAA,CAAA;AAAA,KAAA,CAAA;IAAGnH,MAAI;aAAE2F,QAAM,CAAA3F,MAAA,IAAA2F,QAAA,CAAA3F,MAAA,CAAAkH,KAAA,CAAAvB,QAAA,EAAAwB,SAAA,CAAA;AAAA,KAAA,CAAA;IAAGV,SAAO;aAAEd,QAAS,CAAAzE,SAAA,IAAAyE,QAAA,CAAAzE,SAAA,CAAAgG,KAAA,CAAAvB,QAAA,EAAAwB,SAAA,CAAA;KAAA;KAAUvB,IAAG,CAAApG,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EACrK4H,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAgCUC,QAhCwB,EAAA,IAAA,EAAAC,UAAA,CAAA3B,IAAA,CAAAxK,KAAK,EAArB,UAAA+E,IAAI,EAAEQ,KAAK,EAAA;;WAAkBA;AAAK,KAAA,EAAA,CAEtCgF,QAAA,CAAAnB,aAAa,CAACrE,IAAI,CAAA,IAD5BiH,SAAA,EAAA,EAAAC,kBAAA,CA8BI,MA9BJ5B,UA8BI,CAAA;;MA5BCnG,EAAE,EAAA,EAAA,CAAAN,MAAA,CAAK4G,IAAG,CAAAS,GAAA,EAAArH,GAAAA,CAAAA,CAAAA,MAAA,CAAI2B,KAAK,CAAA;AACnB,MAAA,OAAA,EAAOiF,IAAA,CAAAb,EAAE,CAAkB,MAAA,EAAA;QAAAzF,EAAA,EAAA,EAAA,CAAAN,MAAA,CAAA4G,IAAA,CAAAS,GAAG,EAAA,GAAA,CAAA,CAAArH,MAAA,CAAI2B,KAAK;AAAA,OAAA,CAAA;AACvCnE,MAAAA,KAAK,EAAEmJ,QAAY,CAAA9B,YAAA,CAAClD,KAAK,CAAA;AAC1BqG,MAAAA,IAAI,EAAC,MAAK;AACT,MAAA,eAAa,EAAErB,QAAA,CAAAhG,YAAY,IAAAX,MAAA,CAAI4G,IAAA,CAAAS,GAAG,EAAA,GAAA,CAAA,CAAArH,MAAA,CAAI2B,KAAK,CAAA;;;OACpCgF,QAAY,CAAAtG,YAAA,CAAA,EAAA,CAAAL,MAAA,CAAI4G,IAAG,CAAAS,GAAA,OAAArH,MAAA,CAAI2B,KAAK,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,CAEnB,CAAAiF,IAAA,CAAAI,MAAM,CAAC7F,IAAI,GACxBqH,cAAA,EAAAJ,SAAA,EAAA,EAAAN,WAAA,CAiBQV,mBAjBRX,UAiBQ,CAAA;;MAfHwB,QAAQ,EAAE,EAAE;AACbD,MAAAA,IAAI,EAAC,UAAS;AACb,MAAA,OAAA,EAAOpB,IAAE,CAAAb,EAAA,CAAA,UAAA,EAAA;AAAe5E,QAAAA,IAAK,EAALA;AAAK,OAAA,CAAA;MAC7B,YAAU,EAAEA,IAAI,CAACsH,KAAK;MACtB3L,QAAQ,EAAE8J,IAAQ,CAAA9J,QAAA;MAClBwK,QAAQ,EAAEV,IAAQ,CAAAU,QAAA;AAClB9F,MAAAA,OAAK,EAAE,SAAPA,OAAKA,CAAEgG,MAAA,EAAA;AAAA,QAAA,OAAAb,QAAA,CAAA1F,WAAW,CAACuG,MAAM,EAAErG,IAAI,CAAA;AAAA;;;OACxByF,IAAiB,CAAA/I,iBAAA,EAAA;AACxB6J,MAAAA,EAAE,EAAEf,QAAA,CAAAtG,YAAY,IAAAL,MAAA,CAAI4G,IAAA,CAAAS,GAAG,EAAArH,GAAAA,CAAAA,CAAAA,MAAA,CAAI2B,KAAK,GAAA,UAAA;;;QAEjBR,IAAI,CAACwG,IAAI;YAAG,MAAI;AAC5Be,MAAAA,EAAA,EAAAd,OAAA,CAAA,UAD8BC,SAAS,EAAA;QAAA,OAAA,CACvCd,UAEM,CAAAH,IAAA,CAAAI,MAAA,EAAA,UAAA,EAAA;AAFiB7F,UAAAA,IAAI,EAAEA,IAAI;UAAG,OAAKwH,EAAAA,cAAA,CAAEd,SAAS,CAAM,OAAA,CAAA;WAA1D,YAAA;AAAA,UAAA,OAEM,CADFrB,kBAAA,CAAyG,QAAzGC,UAAyG,CAAA;AAAlG,YAAA,OAAA,GAAQtF,IAAI,CAACwG,IAAI,EAAEE,SAAS,CAAM,OAAA,CAAA;;;aAAWlB,QAAY,CAAAtG,YAAA,IAAAL,MAAA,CAAI4G,IAAG,CAAAS,GAAA,EAAA,GAAA,CAAA,CAAArH,MAAA,CAAI2B,KAAK,CAAA,EAAA,YAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA;;;;gGAbnD,CAAAiH,kBAAA,EAAA;MAAAC,KAAA,EAAA1H,IAAI,CAACsH,KAAK;MAAa3L,QAAA,EAAA,CAAA8J,IAAA,CAAArJ;OAAlDqJ,IAAgB,CAAArJ,cAAA,CAAA,mBAkBlCuK,WAAiK,CAAAC,uBAAA,CAA1InB,IAAM,CAAAI,MAAA,CAAC7F,IAAI,CAAA,EAAA;;AAAGA,MAAAA,IAAI,EAAEA,IAAI;AAAGK,MAAAA,OAAO,WAAPA,OAAOA,CAAGV,KAAK,EAAA;AAAA,QAAA,OAAK6F,oBAAW,CAAC7F,KAAK,EAAEK,IAAI,CAAA;AAAA,OAAA;AAAI+F,MAAAA,cAAc,WAAdA,cAAcA,CAAGpG,KAAK,EAAA;AAAA,QAAA,OAAK6F,oBAAW,CAAC7F,KAAK,EAAEK,IAAI,CAAA;AAAA;;qCAKnJyF,IAAI,CAAA/J,IAAA,IAChBuL,SAAA,EAAA,EAAAC,kBAAA,CAAmF,OAAnF5B,UAAmF,CAAA;;IAA7E,OAAK,EAAA,CAAGG,IAAE,CAAAb,EAAA,CAAA,MAAA,CAAA,EAAUa,IAAS,CAAA1J,SAAA,CAAA;IAAIM,KAAK,EAAEoJ,IAAS,CAAA3J;KAAU2J,IAAG,CAAApG,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA;;;;;;;"}