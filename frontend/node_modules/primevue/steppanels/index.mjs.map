{"version": 3, "file": "index.mjs", "sources": ["../../src/steppanels/BaseStepPanels.vue", "../../src/steppanels/StepPanels.vue", "../../src/steppanels/StepPanels.vue?vue&type=template&id=43d040c1&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport StepPanelsStyle from 'primevue/steppanels/style';\n\nexport default {\n    name: 'BaseStepPanels',\n    extends: BaseComponent,\n    style: StepPanelsStyle,\n    provide() {\n        return {\n            $pcStepPanels: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot />\n    </div>\n</template>\n\n<script>\nimport BaseStepPanels from './BaseStepPanels.vue';\n\nexport default {\n    name: 'StepPanels',\n    extends: BaseStepPanels,\n    inheritAttrs: false\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot />\n    </div>\n</template>\n\n<script>\nimport BaseStepPanels from './BaseStepPanels.vue';\n\nexport default {\n    name: 'StepPanels',\n    extends: BaseStepPanels,\n    inheritAttrs: false\n};\n</script>\n"], "names": ["name", "BaseComponent", "style", "StepPanelsStyle", "provide", "$pcStepPanels", "$parentInstance", "BaseStepPanels", "inheritAttrs", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "ptmi", "_renderSlot", "$slots"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,gBAAgB;AACtB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAEC,eAAe;EACtBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,aAAa,EAAE,IAAI;AACnBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACLD,aAAe;AACXN,EAAAA,IAAI,EAAE,YAAY;AAClB,EAAA,SAAA,EAASO,QAAc;AACvBC,EAAAA,YAAY,EAAE;AAClB,CAAC;;;ECZG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAEK,OAFLC,UAEK,CAAA;AAFC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA;KAAkBD,IAAI,CAAAE,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACjCC,UAAO,CAAAH,IAAA,CAAAI,MAAA,EAAA,SAAA,CAAA;;;;;;;"}