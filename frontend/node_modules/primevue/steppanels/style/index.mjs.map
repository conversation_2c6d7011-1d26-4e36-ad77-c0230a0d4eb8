{"version": 3, "file": "index.mjs", "sources": ["../../../src/steppanels/style/StepPanelsStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-steppanels'\n};\n\nexport default BaseStyle.extend({\n    name: 'steppanels',\n    classes\n});\n"], "names": ["classes", "root", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE;AACV,CAAC;AAED,sBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,YAAY;AAClBJ,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}