{"Accordion": {"description": "Accordion groups a collection of contents in tabs.", "attributes": ["multiple", "activeIndex", "lazy", "expandIcon", "collapseIcon", "tabindex", "selectOnFocus", "pt", "unstyled"]}, "AccordionTab": {"description": "Accordion element consists of one or more AccordionTab elements.", "attributes": ["header", "headerStyle", "headerClass", "headerProps", "headerActionProps", "contentStyle", "contentClass", "contentProps", "disabled", "pt"]}, "AutoComplete": {"description": "AutoComplete is an input component that provides real-time suggestions when being typed.", "attributes": ["modelValue", "suggestions", "field", "optionLabel", "optionDisabled", "optionGroupLabel", "optionGroupChildren", "scrollHeight", "dropdown", "dropdownMode", "autoHighlight", "multiple", "placeholder", "loading", "disabled", "invalid", "variant", "dataKey", "<PERSON><PERSON><PERSON><PERSON>", "delay", "appendTo", "forceSelection", "completeOnFocus", "inputId", "inputStyle", "inputClass", "inputProps", "panelStyle", "panelClass", "panelProps", "dropdownIcon", "dropdownClass", "loadingIcon", "removeTokenIcon", "virtualScrollerOptions", "autoOptionFocus", "selectOnFocus", "searchLocale", "searchMessage", "selectionMessage", "emptySelectionMessage", "emptySearchMessage", "tabindex", "aria-label", "aria-<PERSON>by", "pt", "unstyled"]}, "Avatar": {"description": "Avatar represents people using icons, labels and images.", "attributes": ["label", "icon", "image", "size", "shape", "pt", "unstyled"]}, "AvatarGroup": {"description": "A set of Avatars can be displayed together using the AvatarGroup component.", "attributes": ["label", "icon", "image", "size", "shape", "pt", "unstyled"]}, "Badge": {"description": "Badge is a small status indicator for another element.", "attributes": ["value", "severity", "size", "pt", "unstyled"]}, "BlockUI": {"description": "BlockUI can either block other components or the whole page.", "attributes": ["blocked", "fullscreen", "baseZIndex", "autoZIndex", "pt", "unstyled"]}, "Breadcrumb": {"description": "Breadcrumb provides contextual information about page hierarchy.", "attributes": ["model", "home", "pt", "unstyled"]}, "Button": {"description": "Button is an extension to standard button element with icons and theming.", "attributes": ["label", "icon", "iconPos", "iconClass", "badge", "badgeClass", "loading", "loadingIcon", "link", "severity", "raised", "rounded", "text", "outlined", "size", "plain", "pt", "unstyled"]}, "Calendar": {"description": "Calendar is an input component to select a date.", "attributes": ["modelValue", "selectionMode", "dateFormat", "inline", "showOtherMonths", "selectOtherMonths", "showIcon", "icon", "prevIcon", "nextIcon", "incrementIcon", "decrementIcon", "numberOfMonths", "view", "monthNavigator", "yearNavigator", "year<PERSON><PERSON><PERSON>", "panelClass", "minDate", "maxDate", "disabledDates", "disabledDays", "maxDateCount", "showOnFocus", "autoZIndex", "baseZIndex", "showButtonBar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showTime", "timeOnly", "hourFormat", "step<PERSON><PERSON>", "step<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showSeconds", "hideOnDateTimeSelect", "hideOnRangeSelection", "timeSeparator", "showWeek", "manualInput", "appendTo", "disabled", "invalid", "variant", "readonly", "placeholder", "id", "inputId", "inputClass", "inputStyle", "inputProps", "panelClass", "panelStyle", "panelProps", "pt", "unstyled"]}, "Card": {"description": "Card is a flexible container component.", "attributes": ["pt", "unstyled"]}, "Carousel": {"description": "Carousel is a content slider featuring various customization options.", "attributes": ["value", "page", "circular", "autoplayInterval", "numVisible", "numScroll", "responsiveOptions", "orientation", "verticalViewPortHeight", "contentClass", "containerClass", "indicatorsContentClass", "showNavigators", "showIndicators", "pt", "unstyled"]}, "CascadeSelect": {"description": "CascadeSelect displays a nested structure of options.", "attributes": ["modelValue", "options", "optionLabel", "optionValue", "optionDisabled", "optionGroupLabel", "optionGroupChildren", "placeholder", "disabled", "invalid", "variant", "dataKey", "inputStyle", "inputClass", "inputProps", "panelStyle", "panelClass", "panelProps", "appendTo", "loading", "dropdownIcon", "loadingIcon", "optionGroupIcon", "autoOptionFocus", "selectOnFocus", "searchLocale", "searchMessage", "selectionMessage", "emptySelectionMessage", "emptySearchMessage", "tabindex", "aria-label", "aria-<PERSON>by", "pt", "unstyled"]}, "Chart": {"description": "Chart components are based on Charts.js, an open source HTML5 based charting library.", "attributes": ["type", "data", "options", "plugins", "width", "height", "pt", "unstyled"]}, "Checkbox": {"description": "Checkbox is an extension to standard checkbox element with theming.", "attributes": ["value", "modelValue", "binary", "disabled", "invalid", "variant", "readonly", "required", "tabindex", "trueValue", "falseValue", "inputId", "inputClass", "inputStyle", "inputProps", "aria-<PERSON>by", "aria-label", "pt", "unstyled"]}, "Chip": {"description": "Chip represents entities using icons, labels and images", "attributes": ["label", "icon", "image", "removable", "removeIconClass", "pt", "unstyled"]}, "chips": {"description": "Chips is used to enter multiple values on an input field.", "attributes": ["modelValue", "max", "separator", "addOnBlur", "allowDuplicate", "disabled", "invalid", "variant", "placeholder", "inputId", "inputClass", "inputStyle", "inputProps", "pt", "unstyled"]}, "ColorPicker": {"description": "ColorPicker is an input component to select a color.", "attributes": ["modelValue", "defaultColor", "inline", "format", "disabled", "tabindex", "baseZIndex", "autoZIndex", "panelClass", "appendTo", "aria-<PERSON>by", "aria-label", "pt", "unstyled"]}, "Column": {"description": "DataTable requires a value as an array of objects and columns defined with Column component.", "attributes": ["column<PERSON>ey", "field", "sortField", "filterField", "sortable", "header", "footer", "style", "class", "headerStyle", "headerClass", "bodyStyle", "bodyClass", "footerStyle", "footerClass", "showFilterMenu", "showFilterOperator", "showClearButton", "showApplyButton", "showFilterMatchModes", "showAddButton", "filterMatchModeOptions", "maxConstraints", "excludeGlobalFilter", "filterHeaderStyle", "filterHeaderClass", "filterMenuStyle", "filterMenuClass", "selectionMode", "expander", "colspan", "rowspan", "<PERSON><PERSON><PERSON><PERSON>", "rowReorderIcon", "reorderableColumn", "rowEditor", "frozen", "align<PERSON><PERSON>zen", "exportable", "exportHeader", "exportFooter", "hidden", "pt"]}, "ColumnGroup": {"description": "Columns can be grouped at header and footer sections by defining a ColumnGroup with nested rows and columns", "attributes": ["type", "pt"]}, "ConfirmDialog": {"description": "ConfirmDialog uses a Dialog UI that is integrated with the Confirmation API.", "attributes": ["group", "breakpoints", "draggable", "pt", "unstyled"]}, "ConfirmPopup": {"description": "ConfirmPopup displays a confirmation overlay displayed relatively to its target.", "attributes": ["group", "pt", "unstyled"]}, "ContextMenu": {"description": "ContextMenu displays an overlay menu on right click of its target.", "attributes": ["model", "appendTo", "baseZIndex", "autoZIndex", "global", "pt", "unstyled"]}, "DataTable": {"description": "DataTable displays data in tabular format.", "attributes": ["value", "dataKey", "rows", "first", "totalRecords", "paginator", "paginatorPosition", "alwaysShowPaginator", "paginatorTemplate", "pageLinkSize", "rowsPerPageOptions", "currentPageReportTemplate", "lazy", "loading", "loadingIcon", "sortField", "sortOrder", "defaultSortOrder", "multiSortMeta", "sortMode", "removableSort", "filters", "filterDisplay", "filterLocale", "selection", "selectionMode", "compareSelectionBy", "metaKeySelection", "contextMenu", "contextMenuSelection", "rowHover", "selectAll", "csvSeparator", "exportFilename", "exportFunction", "autoLayout", "resizableColumns", "columnResizeMode", "reorderableColumns", "expandedRows", "expandedRowIcon", "collapsedRowIcon", "rowGroupMode", "groupRowsBy", "expandableRowGroups", "expandedRowGroups", "stateStorage", "stateKey", "editMode", "editingRows", "rowClass", "rowStyle", "scrollable", "scrollDirection", "scrollHeight", "virtualScrollerOptions", "frozenValue", "breakpoint", "showGridlines", "stripedRows", "highlightOnSelect", "size", "tableStyle", "tableClass", "pt", "unstyled"]}, "DataView": {"description": "DataView displays data in grid or list layout with pagination and sorting features.", "attributes": ["value", "layout", "rows", "first", "totalRecords", "paginator", "paginatorPosition", "alwaysShowPaginator", "paginatorTemplate", "pageLinkSize", "rowsPerPageOptions", "currentPageReportTemplate", "sortField", "sortOrder", "lazy", "dataKey", "pt", "unstyled"]}, "DeferredContent": {"description": "DeferredContent postpones the loading the content that is initially not in the viewport until it becomes visible on scroll.", "attributes": ["pt", "unstyled"]}, "Dialog": {"description": "Dialog is a container to display content in an overlay window.", "attributes": ["header", "footer", "visible", "modal", "closeOnEscape", "dismissableMask", "position", "contentStyle", "contentClass", "closable", "showHeader", "blockScroll", "baseZIndex", "autoZIndex", "ariaCloseLabel", "maximizable", "breakpoints", "draggable", "minX", "minY", "keepInViewport", "appendTo", "pt", "unstyled"]}, "Divider": {"description": "Divider is used to separate contents.", "attributes": ["align", "layout", "type", "pt", "unstyled"]}, "Dock": {"description": "Dock is a navigation component consisting of menuitems.", "attributes": ["model", "position", "class", "style", "tooltipOptions", "pt", "unstyled"]}, "Dropdown": {"description": "Dropdown is used to select an item from a list of options.", "attributes": ["modelValue", "options", "optionLabel", "optionValue", "optionDisabled", "optionGroupLabel", "optionGroupChildren", "scrollHeight", "filter", "filterPlaceholder", "filterLocale", "filterMatchMode", "filterFields", "editable", "placeholder", "disabled", "invalid", "variant", "dataKey", "showClear", "inputId", "inputStyle", "inputClass", "inputProps", "panelStyle", "panelClass", "panelProps", "filterInputProps", "clearIconProps", "appendTo", "loading", "loadingIcon", "resetFilterOnHide", "resetFilterOnClear", "virtualScrollerOptions", "autoOptionFocus", "autoFilterFocus", "selectOnFocus", "filterMessage", "selectionMessage", "emptySelectionMessage", "emptyFilterMessage", "emptyMessage", "tabindex", "aria-label", "aria-<PERSON>by", "pt", "unstyled"]}, "Editor": {"description": "Editor is rich text editor component based on Quill.", "attributes": ["modelValue", "placeholder", "readonly", "formats", "editor<PERSON><PERSON><PERSON>", "modules", "pt", "unstyled"]}, "Fieldset": {"description": "Fieldset is a grouping component with the optional content toggle feature.", "attributes": ["legend", "toggleable", "collapsed", "toggleButtonProps", "pt", "unstyled"]}, "FileUpload": {"description": "FileUpload is an advanced uploader with dragdrop support, multi file uploads, auto uploading, progress tracking and validations.", "attributes": ["name", "url", "mode", "multiple", "accept", "disabled", "auto", "maxFileSize", "invalidFileSizeMessage", "invalidFileLimitMessage", "fileLimit", "withCredentials", "previewWidth", "<PERSON><PERSON><PERSON><PERSON>", "uploadLabel", "cancelLabel", "customUpload", "showUploadButton", "showCancelButton", "chooseIcon", "uploadIcon", "cancelIcon", "style", "class", "pt", "unstyled"]}, "Galleria": {"description": "Galleria is an advanced content gallery component.", "attributes": ["id", "value", "activeIndex", "fullscreen", "visible", "numVisible", "responsiveOptions", "showItemNavigators", "showThumbnailNavigators", "showItemNavigatorsOnHover", "changeItemOnIndicatorHover", "circular", "autoPlay", "transitionInterval", "showThumbnails", "thumbnailsPosition", "verticalThumbnailViewPortHeight", "showIndicators", "showIndicatorsOnItem", "indicatorsPosition", "baseZIndex", "maskClass", "containerStyle", "containerClass", "pt", "unstyled"]}, "Image": {"description": "Displays an image with preview and tranformation options.", "attributes": ["preview", "indicatorIcon", "zoomInDisabled", "zoomOutDisabled", "pt", "unstyled"]}, "InlineMessage": {"description": "InlineMessage component is useful in cases where a single message needs to be displayed related to an element such as forms", "attributes": ["severity", "icon", "pt", "unstyled"]}, "Inplace": {"description": "Inplace provides an easy to do editing and display at the same time where clicking the output displays the actual content.", "attributes": ["active", "disabled", "pt", "unstyled"]}, "InputMask": {"description": "InputMask component is used to enter input in a certain format such as numeric, date, currency, email and phone.", "attributes": ["modelValue", "mask", "slotChar", "autoClear", "unmask", "invalid", "variant", "pt", "unstyled"]}, "InputNumber": {"description": "InputNumber is an input component to provide numerical input.", "attributes": ["modelValue", "format", "showButtons", "buttonLayout", "incrementButtonClass", "decrementButtonClass", "incrementButtonIcon", "decrementButtonIcon", "locale", "localeMatcher", "mode", "prefix", "suffix", "currency", "currencyDisplay", "useGrouping", "minFractionDigits", "maxFractionDigits", "min", "max", "step", "allowEmpty", "readonly", "placeholder", "invalid", "variant", "inputId", "inputStyle", "inputClass", "inputProps", "incrementButtonProps", "decrementButtonProps", "pt", "unstyled"]}, "InputOtp": {"description": "InputOtp  is used to enter one time passwords.", "attributes": ["modelValue", "trueValue", "falseValue", "inputId", "inputStyle", "inputClass", "inputProps", "pt", "unstyled"]}, "InputSwitch": {"description": "InputSwitch is used to select a boolean value.", "attributes": ["modelValue", "trueValue", "falseValue", "inputId", "inputStyle", "inputClass", "inputProps", "pt", "unstyled"]}, "InputText": {"description": "InputText renders a text field to enter data.", "attributes": ["modelValue", "size", "invalid", "variant", "pt", "unstyled"]}, "Knob": {"description": "Knob is a form component to define number inputs with a dial.", "attributes": ["modelValue", "size", "disabled", "readonly", "step", "min", "max", "valueColor", "rangeColor", "textColor", "strokeWidth", "showValue", "valueTemplate", "tabindex", "aria-<PERSON>by", "aria-label", "pt", "unstyled"]}, "Listbox": {"description": "Listbox is used to select one or more values from a list of items.", "attributes": ["modelValue", "options", "optionLabel", "optionValue", "optionDisabled", "optionGroupLabel", "optionGroupChildren", "listStyle", "disabled", "invalid", "dataKey", "multiple", "metaKeySelection", "filter", "filterPlaceholder", "filterLocale", "filterMatchMode", "filterFields", "filterInputProps", "virtualScrollerOptions", "autoOptionFocus", "selectOnFocus", "filterMessage", "selectionMessage", "emptySelectionMessage", "emptyFilterMessage", "emptyMessage", "tabindex", "aria-label", "aria-<PERSON>by", "pt", "unstyled"]}, "MegaMenu": {"description": "MegaMenu is navigation component that displays submenus together.", "attributes": ["modelValue", "orientation", "pt", "unstyled"]}, "Menu": {"description": "Menu is a navigation / command component that supports dynamic and static positioning.", "attributes": ["modelValue", "popup", "appendTo", "baseZIndex", "autoZIndex", "pt", "unstyled"]}, "Menubar": {"description": "Menubar is a horizontal menu component.", "attributes": ["modelValue", "pt", "unstyled"]}, "Message": {"description": "Messages is used to display inline messages with various severities.", "attributes": ["severity", "closable", "sticky", "life", "icon", "closeIcon", "pt", "unstyled"]}, "MultiSelect": {"description": "MultiSelect is used to multiple values from a list of options.", "attributes": ["modelValue", "options", "optionLabel", "optionValue", "optionDisabled", "optionGroupLabel", "optionGroupChildren", "scrollHeight", "placeholder", "disabled", "invalid", "variant", "inputId", "inputProps", "panelStyle", "panelClass", "panelProps", "filterInputProps", "closeButtonProps", "dataKey", "filter", "filterPlaceholder", "filterLocale", "filterMatchMode", "filterFields", "appendTo", "display", "selectedItemsLabel", "maxSelectedLabels", "selectionLimit", "showToggleAll", "loading", "loadingIcon", "checkboxIcon", "closeIcon", "dropdownIcon", "filterIcon", "removeTokenIcon", "selectAll", "resetFilterOnHide", "virtualScrollerOptions", "autoOptionFocus", "autoFilterFocus", "highlightOnSelect", "filterMessage", "selectionMessage", "emptySelectionMessage", "emptyFilterMessage", "emptyMessage", "tabindex", "aria-label", "aria-<PERSON>by", "pt", "unstyled"]}, "OrderList": {"description": "OrderList is used to managed the order of a collection.", "attributes": ["modelValue", "selection", "metaKeySelection", "autoOptionFocus", "dataKey", "listStyle", "responsive", "breakpoint", "striped", "pt", "unstyled"]}, "OrganizationChart": {"description": "OrganizationChart visualizes hierarchical organization data.", "attributes": ["value", "<PERSON><PERSON><PERSON><PERSON>", "selectionMode", "collapsible", "collapsed<PERSON>ey<PERSON>", "pt", "unstyled"]}, "OverlayPanel": {"description": "OverlayPanel is a container component positioned as connected to its target.", "attributes": ["dismissable", "showCloseIcon", "appendTo", "baseZIndex", "autoZIndex", "ariaCloseLabel", "breakpoints", "closeIcon", "closeOnEscape", "pt", "unstyled"]}, "Paginator": {"description": "Paginator is a generic component to display content in paged format.", "attributes": ["totalRecords", "rows", "first", "pageLinkSize", "rowsPerPageOptions", "template", "currentPageReportTemplate", "alwaysShow", "pt", "unstyled"]}, "Panel": {"description": "Panel is a container with the optional content toggle feature.", "attributes": ["header", "toggleable", "collapsed", "toggleButtonProps", "pt", "unstyled"]}, "PanelMenu": {"description": "PanelMenu is a hybrid of Accordion and Tree components", "attributes": ["model", "expandedKeys", "pt", "unstyled"]}, "Password": {"description": "Password displays strength indicator for password fields.", "attributes": ["modelValue", "inputId", "prompt<PERSON><PERSON><PERSON>", "mediumRegex", "strongRegex", "<PERSON><PERSON><PERSON><PERSON>", "mediumLabel", "<PERSON><PERSON><PERSON><PERSON>", "feedback", "toogleMask", "appendTo", "hideIcon", "showIcon", "placeholder", "invalid", "variant", "required", "inputId", "inputStyle", "inputClass", "inputProps", "panelId", "panelClass", "panelStyle", "panelProps", "pt", "unstyled"]}, "PickList": {"description": "PickList is used to reorder items between different lists.", "attributes": ["modelValue", "selection", "metaKeySelection", "autoOptionFocus", "dataKey", "listStyle", "responsive", "breakpoint", "striped", "showSourceControls", "showTargetControls", "pt", "unstyled"]}, "Portal": {"description": "Portal moves its container to a specific location based on target elements. Basically it uses <Teleport> in the background.", "attributes": ["appendTo", "disabled"]}, "ProgressBar": {"description": "ProgressBar is a process status indicator.", "attributes": ["value", "mode", "showValue", "pt", "unstyled"]}, "ProgressSpinner": {"description": "ProgressSpinner is a process status indicator", "attributes": ["strokeWidth", "fill", "animationDuration", "pt", "unstyled"]}, "RadioButton": {"description": "RadioButton is an extension to standard radio button element with theming.", "attributes": ["value", "modelValue", "name", "disabled", "invalid", "variant", "inputId", "inputClass", "inputStyle", "inputProps", "aria-<PERSON>by", "aria-label", "pt", "unstyled"]}, "rating": {"description": "Rating component is a star based selection input.", "attributes": ["modelValue", "disabled", "readonly", "stars", "onIcon", "offIcon", "pt", "unstyled"]}, "Row": {"description": "DataTable can be grouped by defining a Row component with nested columns", "attributes": ["type", "pt"]}, "ScrollPanel": {"description": "ScrollPanel is a cross browser, lightweight and themable alternative to native browser scrollbar.", "attributes": ["step", "pt", "unstyled"]}, "ScrollTop": {"description": "ScrollTop gets displayed after a certain scroll position and used to navigates to the top of the page quickly.", "attributes": ["target", "threshold", "icon", "behavior", "pt", "unstyled"]}, "SelectButton": {"description": "SelectButton is a form component to choose a value from a list of options using button elements.", "attributes": ["modelValue", "options", "optionLabel", "optionValue", "optionDisabled", "multiple", "disabled", "invalid", "dataKey", "unselectable", "allowEmpty", "aria-<PERSON>by", "pt", "unstyled"]}, "Sidebar": {"description": "Sidebar is a panel component displayed as an overlay at the edges of the screen.", "attributes": ["visible", "position", "baseZIndex", "autoZIndex", "dismissable", "showCloseIcon", "modal", "ariaCloseLabel", "blockScroll", "closeIcon", "pt", "unstyled"]}, "Skeleton": {"description": "Skeleton is a placeholder to display instead of the actual content.", "attributes": ["shape", "size", "width", "height", "borderRadius", "animation", "pt", "unstyled"]}, "Slider": {"description": "Slider is an input component to provide a numerical input", "attributes": ["modelValue", "min", "max", "orientation", "step", "range", "disabled", "tabindex", "aria-<PERSON>by", "aria-label", "pt", "unstyled"]}, "SpeedDial": {"description": "When pressed, a floating action button can display multiple primary actions that can be performed on a page.", "attributes": ["model", "visible", "direction", "transitionDelay", "type", "radius", "mask", "disabled", "hideOnClickOutside", "buttonClass", "maskClass", "maskStyle", "showIcon", "hideIcon", "rotateAnimation", "class", "style", "tooltipOptions", "pt", "unstyled"]}, "SplitButton": {"description": "SplitButton groups a set of commands in an overlay with a default command.", "attributes": ["label", "icon", "model", "autoZIndex", "baseZIndex", "appendTo", "disabled", "class", "style", "menuButtonIcon", "severity", "raised", "rounded", "text", "outlined", "size", "plain", "pt", "unstyled"]}, "Splitter": {"description": "Splitter is utilized to separate and resize panels", "attributes": ["layout", "gutterSize", "stateKey", "stateStorage", "step", "pt", "unstyled"]}, "SplitterPanel": {"description": "Splitter requires two SplitterPanel components to wrap.", "attributes": ["size", "minSize", "pt"]}, "steps": {"description": "Steps components is an indicator for the steps in a wizard workflow.", "attributes": ["id", "model", "activeStep", "readonly", "pt", "unstyled"]}, "TabMenu": {"description": "TabMenu is a navigation component that displays items as tab headers.", "attributes": ["model", "activeIndex", "pt", "unstyled"]}, "TabPanel": {"description": "TabView element consists of one or more TabPanel elements.", "attributes": ["header", "headerStyle", "headerClass", "headerProps", "headerActionProps", "contentStyle", "contentClass", "contentProps", "disabled", "pt"]}, "TabView": {"description": "TabView is a container component to group content with tabs.", "attributes": ["activeIndex", "lazy", "scrollable", "tabindex", "selectOnFocus", "prevButtonProps", "nextButtonProps", "prevIcon", "nextIcon", "pt", "unstyled"]}, "Tag": {"description": "Tag component is used to categorize content.", "attributes": ["value", "severity", "rounded", "icon", "pt", "unstyled"]}, "Terminal": {"description": "Terminal is a text based user interface.", "attributes": ["welcomeMessage", "prompt", "pt", "unstyled"]}, "Textarea": {"description": "Textarea is a multi-line text input element.", "attributes": ["modelValue", "autoResize", "invalid", "variant", "pt", "unstyled"]}, "TieredMenu": {"description": "TieredMenu displays submenus in nested overlays.", "attributes": ["model", "popup", "appendTo", "baseZIndex", "autoZIndex", "pt", "unstyled"]}, "Timeline": {"description": "Timeline visualizes a series of chained events.", "attributes": ["value", "align", "layout", "dataKey", "pt", "unstyled"]}, "Toast": {"description": "Toast is used to display messages in an overlay.", "attributes": ["group", "position", "autoZIndex", "baseZIndex", "breakpoints", "pt", "unstyled"]}, "ToggleButton": {"description": "ToggleButton is used to select a boolean value using a button.", "attributes": ["modelValue", "onIcon", "offIcon", "onLabel", "offLabel", "iconPos", "tabindex", "disabled", "invalid", "inputId", "inputClass", "inputStyle", "inputProps", "pt", "unstyled"]}, "Toolbar": {"description": "Toolbar is a grouping component for buttons and other content.", "attributes": ["aria-<PERSON>by", "pt", "unstyled"]}, "Tree": {"description": "Tree is used to display hierarchical data.", "attributes": ["value", "expandedKeys", "selectionMode", "<PERSON><PERSON><PERSON><PERSON>", "metaKeySelection", "loading", "loadingIcon", "filter", "filterBy", "filterMode", "filterPlaceholder", "filterLocale", "highlightOnSelect", "scrollHeight", "pt", "unstyled"]}, "TreeSelect": {"description": "TreeSelect is a form component to choose from hierarchical data.", "attributes": ["modelValue", "options", "scrollHeight", "placeholder", "disabled", "invalid", "variant", "tabindex", "inputId", "inputStyle", "inputClass", "selectionMode", "panelClass", "appendTo", "emptyMessage", "display", "metaKeySelection", "aria-<PERSON>by", "aria-label", "pt", "unstyled"]}, "TreeTable": {"description": "TreeTable is used to display hierarchical data in tabular format.", "attributes": ["value", "dataKey", "expandedKeys", "<PERSON><PERSON><PERSON><PERSON>", "selectionMode", "metaKeySelection", "rows", "first", "totalRecords", "paginator", "paginatorPosition", "alwaysShowPaginator", "paginatorTemplate", "pageLinkSize", "rowsPerPageOptions", "currentPageReportTemplate", "lazy", "loading", "loadingIcon", "rowHover", "autoLayout", "sortField", "sortOrder", "defaultSortOrder", "multiSortMeta", "sortMode", "removableSort", "filters", "filterMode", "filterLocale", "resizableColumns", "columnResizeMode", "indentation", "showGridlines", "scrollable", "scrollHeight", "size", "pt", "unstyled"]}, "VirtualScroller": {"description": "VirtualScroller is a performant approach to handle huge data efficiently.", "attributes": ["id", "style", "class", "items", "itemSize", "scrollHeight", "scrollWidth", "orientation", "numToleratedItems", "delay", "lazy", "disabled", "loaderDisabled", "loading", "showSpacer", "<PERSON><PERSON><PERSON><PERSON>", "tabindex", "pt"]}}