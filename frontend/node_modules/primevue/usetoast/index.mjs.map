{"version": 3, "file": "index.mjs", "sources": ["../../src/usetoast/UseToast.js"], "sourcesContent": ["import { inject } from 'vue';\n\nexport const PrimeVueToastSymbol = Symbol();\n\nexport function useToast() {\n    const PrimeVueToast = inject(PrimeVueToastSymbol);\n\n    if (!PrimeVueToast) {\n        throw new Error('No PrimeVue Toast provided!');\n    }\n\n    return PrimeVueToast;\n}\n"], "names": ["PrimeVueToastSymbol", "Symbol", "useToast", "PrimeVueToast", "inject", "Error"], "mappings": ";;AAEaA,IAAAA,mBAAmB,GAAGC,MAAM;AAElC,SAASC,QAAQA,GAAG;AACvB,EAAA,IAAMC,aAAa,GAAGC,MAAM,CAACJ,mBAAmB,CAAC;EAEjD,IAAI,CAACG,aAAa,EAAE;AAChB,IAAA,MAAM,IAAIE,KAAK,CAAC,6BAA6B,CAAC;AAClD;AAEA,EAAA,OAAOF,aAAa;AACxB;;;;"}