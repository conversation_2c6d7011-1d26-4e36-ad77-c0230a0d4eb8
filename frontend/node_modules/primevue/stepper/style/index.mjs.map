{"version": 3, "file": "index.mjs", "sources": ["../../../src/stepper/style/StepperStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/stepper';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => [\n        'p-stepper p-component',\n        {\n            'p-readonly': props.linear\n        }\n    ],\n    separator: 'p-stepper-separator'\n};\n\nexport default BaseStyle.extend({\n    name: 'stepper',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "props", "linear", "separator", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAA,OAAO,CACjB,uBAAuB,EACvB;MACI,YAAY,EAAEA,KAAK,CAACC;AACxB,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,SAAS,EAAE;AACf,CAAC;AAED,mBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,SAAS;AACfC,EAAAA,KAAK,EAALA,KAAK;AACLT,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}