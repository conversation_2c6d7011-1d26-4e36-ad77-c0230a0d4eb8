{"version": 3, "file": "index.mjs", "sources": ["../../src/utils/Utils.js"], "sourcesContent": ["import { $dt } from '@primeuix/styled';\nimport * as utils from '@primeuix/utils';\n\nexport function blockBodyScroll() {\n    utils.blockBodyScroll({ variableName: $dt('scrollbar.width').name });\n}\n\nexport function unblockBodyScroll() {\n    utils.unblockBodyScroll({ variableName: $dt('scrollbar.width').name });\n}\n"], "names": ["blockBodyScroll", "utils", "variableName", "$dt", "name", "unblockBodyScroll"], "mappings": ";;;AAGO,SAASA,eAAeA,GAAG;EAC9BC,KAAK,CAACD,eAAe,CAAC;AAAEE,IAAAA,YAAY,EAAEC,GAAG,CAAC,iBAAiB,CAAC,CAACC;AAAK,GAAC,CAAC;AACxE;AAEO,SAASC,iBAAiBA,GAAG;EAChCJ,KAAK,CAACI,iBAAiB,CAAC;AAAEH,IAAAA,YAAY,EAAEC,GAAG,CAAC,iBAAiB,CAAC,CAACC;AAAK,GAAC,CAAC;AAC1E;;;;"}