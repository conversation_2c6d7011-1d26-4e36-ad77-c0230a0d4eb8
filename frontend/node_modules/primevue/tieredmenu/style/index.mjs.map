{"version": 3, "file": "index.mjs", "sources": ["../../../src/tieredmenu/style/TieredMenuStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/tieredmenu';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst inlineStyles = {\n    submenu: ({ instance, processedItem }) => ({ display: instance.isItemActive(processedItem) ? 'flex' : 'none' })\n};\n\nconst classes = {\n    root: ({ props, instance }) => [\n        'p-tieredmenu p-component',\n        {\n            'p-tieredmenu-overlay': props.popup,\n            'p-tieredmenu-mobile': instance.queryMatches\n        }\n    ],\n    start: 'p-tieredmenu-start',\n    rootList: 'p-tieredmenu-root-list',\n    item: ({ instance, processedItem }) => [\n        'p-tieredmenu-item',\n        {\n            'p-tieredmenu-item-active': instance.isItemActive(processedItem),\n            'p-focus': instance.isItemFocused(processedItem),\n            'p-disabled': instance.isItemDisabled(processedItem)\n        }\n    ],\n    itemContent: 'p-tieredmenu-item-content',\n    itemLink: 'p-tieredmenu-item-link',\n    itemIcon: 'p-tieredmenu-item-icon',\n    itemLabel: 'p-tieredmenu-item-label',\n    submenuIcon: 'p-tieredmenu-submenu-icon',\n    submenu: 'p-tieredmenu-submenu',\n    separator: 'p-tieredmenu-separator',\n    end: 'p-tieredmenu-end'\n};\n\nexport default BaseStyle.extend({\n    name: 'tieredmenu',\n    style,\n    classes,\n    inlineStyles\n});\n"], "names": ["inlineStyles", "submenu", "_ref", "instance", "processedItem", "display", "isItemActive", "classes", "root", "_ref2", "props", "popup", "queryMatches", "start", "rootList", "item", "_ref3", "isItemFocused", "isItemDisabled", "itemContent", "itemLink", "itemIcon", "itemLabel", "submenuIcon", "separator", "end", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,YAAY,GAAG;AACjBC,EAAAA,OAAO,EAAE,SAATA,OAAOA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,aAAa,GAAAF,IAAA,CAAbE,aAAa;IAAA,OAAQ;MAAEC,OAAO,EAAEF,QAAQ,CAACG,YAAY,CAACF,aAAa,CAAC,GAAG,MAAM,GAAG;KAAQ;AAAA;AAClH,CAAC;AAED,IAAMG,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,KAAA,CAALC,KAAK;MAAEP,QAAQ,GAAAM,KAAA,CAARN,QAAQ;IAAA,OAAO,CAC3B,0BAA0B,EAC1B;MACI,sBAAsB,EAAEO,KAAK,CAACC,KAAK;MACnC,qBAAqB,EAAER,QAAQ,CAACS;AACpC,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,KAAK,EAAE,oBAAoB;AAC3BC,EAAAA,QAAQ,EAAE,wBAAwB;AAClCC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKb,QAAQ,GAAAa,KAAA,CAARb,QAAQ;MAAEC,aAAa,GAAAY,KAAA,CAAbZ,aAAa;IAAA,OAAO,CACnC,mBAAmB,EACnB;AACI,MAAA,0BAA0B,EAAED,QAAQ,CAACG,YAAY,CAACF,aAAa,CAAC;AAChE,MAAA,SAAS,EAAED,QAAQ,CAACc,aAAa,CAACb,aAAa,CAAC;AAChD,MAAA,YAAY,EAAED,QAAQ,CAACe,cAAc,CAACd,aAAa;AACvD,KAAC,CACJ;AAAA,GAAA;AACDe,EAAAA,WAAW,EAAE,2BAA2B;AACxCC,EAAAA,QAAQ,EAAE,wBAAwB;AAClCC,EAAAA,QAAQ,EAAE,wBAAwB;AAClCC,EAAAA,SAAS,EAAE,yBAAyB;AACpCC,EAAAA,WAAW,EAAE,2BAA2B;AACxCtB,EAAAA,OAAO,EAAE,sBAAsB;AAC/BuB,EAAAA,SAAS,EAAE,wBAAwB;AACnCC,EAAAA,GAAG,EAAE;AACT,CAAC;AAED,sBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,YAAY;AAClBC,EAAAA,KAAK,EAALA,KAAK;AACLtB,EAAAA,OAAO,EAAPA,OAAO;AACPP,EAAAA,YAAY,EAAZA;AACJ,CAAC,CAAC;;;;"}