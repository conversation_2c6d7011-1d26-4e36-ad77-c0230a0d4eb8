{"version": 3, "file": "index.mjs", "sources": ["../../src/tieredmenu/BaseTieredMenu.vue", "../../src/tieredmenu/TieredMenuSub.vue", "../../src/tieredmenu/TieredMenuSub.vue?vue&type=template&id=34d7057f&lang.js", "../../src/tieredmenu/TieredMenu.vue", "../../src/tieredmenu/TieredMenu.vue?vue&type=template&id=6f9903c3&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TieredMenuStyle from 'primevue/tieredmenu/style';\n\nexport default {\n    name: 'BaseTieredMenu',\n    extends: BaseComponent,\n    props: {\n        popup: {\n            type: Boolean,\n            default: false\n        },\n        model: {\n            type: Array,\n            default: null\n        },\n        appendTo: {\n            type: [String, Object],\n            default: 'body'\n        },\n        breakpoint: {\n            type: String,\n            default: '960px'\n        },\n        autoZIndex: {\n            type: Boolean,\n            default: true\n        },\n        baseZIndex: {\n            type: Number,\n            default: 0\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        }\n    },\n    style: TieredMenuStyle,\n    provide() {\n        return {\n            $pcTieredMenu: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <transition name=\"p-tieredmenu\" @enter=\"onEnter\" v-bind=\"ptm('menu.transition')\">\n        <ul v-if=\"level === 0 ? true : visible\" :ref=\"containerRef\" :tabindex=\"tabindex\">\n            <template v-for=\"(processedItem, index) of items\" :key=\"getItemKey(processedItem)\">\n                <li\n                    v-if=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    :id=\"getItemId(processedItem)\"\n                    :style=\"getItemProp(processedItem, 'style')\"\n                    :class=\"[cx('item', { processedItem }), getItemProp(processedItem, 'class')]\"\n                    role=\"menuitem\"\n                    :aria-label=\"getItemLabel(processedItem)\"\n                    :aria-disabled=\"isItemDisabled(processedItem) || undefined\"\n                    :aria-expanded=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    :aria-haspopup=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    :aria-level=\"level + 1\"\n                    :aria-setsize=\"getAriaSetSize()\"\n                    :aria-posinset=\"getAriaPosInset(index)\"\n                    v-bind=\"getPTOptions(processedItem, index, 'item')\"\n                    :data-p-active=\"isItemActive(processedItem)\"\n                    :data-p-focused=\"isItemFocused(processedItem)\"\n                    :data-p-disabled=\"isItemDisabled(processedItem)\"\n                >\n                    <div\n                        :class=\"cx('itemContent')\"\n                        @click=\"onItemClick($event, processedItem)\"\n                        @mouseenter=\"onItemMouseEnter($event, processedItem)\"\n                        @mousemove=\"onItemMouseMove($event, processedItem)\"\n                        v-bind=\"getPTOptions(processedItem, index, 'itemContent')\"\n                    >\n                        <template v-if=\"!templates.item\">\n                            <a v-ripple :href=\"getItemProp(processedItem, 'url')\" :class=\"cx('itemLink')\" :target=\"getItemProp(processedItem, 'target')\" tabindex=\"-1\" v-bind=\"getPTOptions(processedItem, index, 'itemLink')\">\n                                <component v-if=\"templates.itemicon\" :is=\"templates.itemicon\" :item=\"processedItem.item\" :class=\"cx('itemIcon')\" />\n                                <span v-else-if=\"getItemProp(processedItem, 'icon')\" :class=\"[cx('itemIcon'), getItemProp(processedItem, 'icon')]\" v-bind=\"getPTOptions(processedItem, index, 'itemIcon')\" />\n                                <span :id=\"getItemLabelId(processedItem)\" :class=\"cx('itemLabel')\" v-bind=\"getPTOptions(processedItem, index, 'itemLabel')\">{{ getItemLabel(processedItem) }}</span>\n                                <template v-if=\"getItemProp(processedItem, 'items')\">\n                                    <component v-if=\"templates.submenuicon\" :is=\"templates.submenuicon\" :class=\"cx('submenuIcon')\" :active=\"isItemActive(processedItem)\" v-bind=\"getPTOptions(processedItem, index, 'submenuIcon')\" />\n                                    <AngleRightIcon v-else :class=\"cx('submenuIcon')\" v-bind=\"getPTOptions(processedItem, index, 'submenuIcon')\" />\n                                </template>\n                            </a>\n                        </template>\n                        <component v-else :is=\"templates.item\" :item=\"processedItem.item\" :hasSubmenu=\"getItemProp(processedItem, 'items')\" :label=\"getItemLabel(processedItem)\" :props=\"getMenuItemProps(processedItem, index)\"></component>\n                    </div>\n                    <TieredMenuSub\n                        v-if=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        :id=\"getItemId(processedItem) + '_list'\"\n                        :class=\"cx('submenu')\"\n                        :style=\"sx('submenu', true, { processedItem })\"\n                        :aria-labelledby=\"getItemLabelId(processedItem)\"\n                        role=\"menu\"\n                        :menuId=\"menuId\"\n                        :focusedItemId=\"focusedItemId\"\n                        :items=\"processedItem.items\"\n                        :templates=\"templates\"\n                        :activeItemPath=\"activeItemPath\"\n                        :level=\"level + 1\"\n                        :visible=\"isItemActive(processedItem) && isItemGroup(processedItem)\"\n                        :pt=\"pt\"\n                        :unstyled=\"unstyled\"\n                        @item-click=\"$emit('item-click', $event)\"\n                        @item-mouseenter=\"$emit('item-mouseenter', $event)\"\n                        @item-mousemove=\"$emit('item-mousemove', $event)\"\n                        v-bind=\"ptm('submenu')\"\n                    />\n                </li>\n                <li\n                    v-if=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    :id=\"getItemId(processedItem)\"\n                    :style=\"getItemProp(processedItem, 'style')\"\n                    :class=\"[cx('separator'), getItemProp(processedItem, 'class')]\"\n                    role=\"separator\"\n                    v-bind=\"ptm('separator')\"\n                ></li>\n            </template>\n        </ul>\n    </transition>\n</template>\n\n<script>\nimport { nestedPosition } from '@primeuix/utils/dom';\nimport { isNotEmpty, resolve } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport AngleRightIcon from '@primevue/icons/angleright';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'TieredMenuSub',\n    hostName: 'TieredMenu',\n    extends: BaseComponent,\n    emits: ['item-click', 'item-mouseenter', 'item-mousemove'],\n    container: null,\n    props: {\n        menuId: {\n            type: String,\n            default: null\n        },\n        focusedItemId: {\n            type: String,\n            default: null\n        },\n        items: {\n            type: Array,\n            default: null\n        },\n        visible: {\n            type: Boolean,\n            default: false\n        },\n        level: {\n            type: Number,\n            default: 0\n        },\n        templates: {\n            type: Object,\n            default: null\n        },\n        activeItemPath: {\n            type: Object,\n            default: null\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        }\n    },\n    methods: {\n        getItemId(processedItem) {\n            return `${this.menuId}_${processedItem.key}`;\n        },\n        getItemKey(processedItem) {\n            return this.getItemId(processedItem);\n        },\n        getItemProp(processedItem, name, params) {\n            return processedItem && processedItem.item ? resolve(processedItem.item[name], params) : undefined;\n        },\n        getItemLabel(processedItem) {\n            return this.getItemProp(processedItem, 'label');\n        },\n        getItemLabelId(processedItem) {\n            return `${this.menuId}_${processedItem.key}_label`;\n        },\n        getPTOptions(processedItem, index, key) {\n            return this.ptm(key, {\n                context: {\n                    item: processedItem.item,\n                    index,\n                    active: this.isItemActive(processedItem),\n                    focused: this.isItemFocused(processedItem),\n                    disabled: this.isItemDisabled(processedItem)\n                }\n            });\n        },\n        isItemActive(processedItem) {\n            return this.activeItemPath.some((path) => path.key === processedItem.key);\n        },\n        isItemVisible(processedItem) {\n            return this.getItemProp(processedItem, 'visible') !== false;\n        },\n        isItemDisabled(processedItem) {\n            return this.getItemProp(processedItem, 'disabled');\n        },\n        isItemFocused(processedItem) {\n            return this.focusedItemId === this.getItemId(processedItem);\n        },\n        isItemGroup(processedItem) {\n            return isNotEmpty(processedItem.items);\n        },\n        onEnter() {\n            nestedPosition(this.container, this.level);\n        },\n        onItemClick(event, processedItem) {\n            this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n            this.$emit('item-click', { originalEvent: event, processedItem, isFocus: true });\n        },\n        onItemMouseEnter(event, processedItem) {\n            this.$emit('item-mouseenter', { originalEvent: event, processedItem });\n        },\n        onItemMouseMove(event, processedItem) {\n            this.$emit('item-mousemove', { originalEvent: event, processedItem });\n        },\n        getAriaSetSize() {\n            return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n        },\n        getAriaPosInset(index) {\n            return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n        },\n        getMenuItemProps(processedItem, index) {\n            return {\n                action: mergeProps(\n                    {\n                        class: this.cx('itemLink'),\n                        tabindex: -1\n                    },\n                    this.getPTOptions(processedItem, index, 'itemLink')\n                ),\n                icon: mergeProps(\n                    {\n                        class: [this.cx('itemIcon'), this.getItemProp(processedItem, 'icon')]\n                    },\n                    this.getPTOptions(processedItem, index, 'itemIcon')\n                ),\n                label: mergeProps(\n                    {\n                        class: this.cx('itemLabel')\n                    },\n                    this.getPTOptions(processedItem, index, 'itemLabel')\n                ),\n                submenuicon: mergeProps(\n                    {\n                        class: this.cx('submenuIcon')\n                    },\n                    this.getPTOptions(processedItem, index, 'submenuIcon')\n                )\n            };\n        },\n        containerRef(el) {\n            this.container = el;\n        }\n    },\n    components: {\n        AngleRightIcon: AngleRightIcon\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <transition name=\"p-tieredmenu\" @enter=\"onEnter\" v-bind=\"ptm('menu.transition')\">\n        <ul v-if=\"level === 0 ? true : visible\" :ref=\"containerRef\" :tabindex=\"tabindex\">\n            <template v-for=\"(processedItem, index) of items\" :key=\"getItemKey(processedItem)\">\n                <li\n                    v-if=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    :id=\"getItemId(processedItem)\"\n                    :style=\"getItemProp(processedItem, 'style')\"\n                    :class=\"[cx('item', { processedItem }), getItemProp(processedItem, 'class')]\"\n                    role=\"menuitem\"\n                    :aria-label=\"getItemLabel(processedItem)\"\n                    :aria-disabled=\"isItemDisabled(processedItem) || undefined\"\n                    :aria-expanded=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    :aria-haspopup=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    :aria-level=\"level + 1\"\n                    :aria-setsize=\"getAriaSetSize()\"\n                    :aria-posinset=\"getAriaPosInset(index)\"\n                    v-bind=\"getPTOptions(processedItem, index, 'item')\"\n                    :data-p-active=\"isItemActive(processedItem)\"\n                    :data-p-focused=\"isItemFocused(processedItem)\"\n                    :data-p-disabled=\"isItemDisabled(processedItem)\"\n                >\n                    <div\n                        :class=\"cx('itemContent')\"\n                        @click=\"onItemClick($event, processedItem)\"\n                        @mouseenter=\"onItemMouseEnter($event, processedItem)\"\n                        @mousemove=\"onItemMouseMove($event, processedItem)\"\n                        v-bind=\"getPTOptions(processedItem, index, 'itemContent')\"\n                    >\n                        <template v-if=\"!templates.item\">\n                            <a v-ripple :href=\"getItemProp(processedItem, 'url')\" :class=\"cx('itemLink')\" :target=\"getItemProp(processedItem, 'target')\" tabindex=\"-1\" v-bind=\"getPTOptions(processedItem, index, 'itemLink')\">\n                                <component v-if=\"templates.itemicon\" :is=\"templates.itemicon\" :item=\"processedItem.item\" :class=\"cx('itemIcon')\" />\n                                <span v-else-if=\"getItemProp(processedItem, 'icon')\" :class=\"[cx('itemIcon'), getItemProp(processedItem, 'icon')]\" v-bind=\"getPTOptions(processedItem, index, 'itemIcon')\" />\n                                <span :id=\"getItemLabelId(processedItem)\" :class=\"cx('itemLabel')\" v-bind=\"getPTOptions(processedItem, index, 'itemLabel')\">{{ getItemLabel(processedItem) }}</span>\n                                <template v-if=\"getItemProp(processedItem, 'items')\">\n                                    <component v-if=\"templates.submenuicon\" :is=\"templates.submenuicon\" :class=\"cx('submenuIcon')\" :active=\"isItemActive(processedItem)\" v-bind=\"getPTOptions(processedItem, index, 'submenuIcon')\" />\n                                    <AngleRightIcon v-else :class=\"cx('submenuIcon')\" v-bind=\"getPTOptions(processedItem, index, 'submenuIcon')\" />\n                                </template>\n                            </a>\n                        </template>\n                        <component v-else :is=\"templates.item\" :item=\"processedItem.item\" :hasSubmenu=\"getItemProp(processedItem, 'items')\" :label=\"getItemLabel(processedItem)\" :props=\"getMenuItemProps(processedItem, index)\"></component>\n                    </div>\n                    <TieredMenuSub\n                        v-if=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        :id=\"getItemId(processedItem) + '_list'\"\n                        :class=\"cx('submenu')\"\n                        :style=\"sx('submenu', true, { processedItem })\"\n                        :aria-labelledby=\"getItemLabelId(processedItem)\"\n                        role=\"menu\"\n                        :menuId=\"menuId\"\n                        :focusedItemId=\"focusedItemId\"\n                        :items=\"processedItem.items\"\n                        :templates=\"templates\"\n                        :activeItemPath=\"activeItemPath\"\n                        :level=\"level + 1\"\n                        :visible=\"isItemActive(processedItem) && isItemGroup(processedItem)\"\n                        :pt=\"pt\"\n                        :unstyled=\"unstyled\"\n                        @item-click=\"$emit('item-click', $event)\"\n                        @item-mouseenter=\"$emit('item-mouseenter', $event)\"\n                        @item-mousemove=\"$emit('item-mousemove', $event)\"\n                        v-bind=\"ptm('submenu')\"\n                    />\n                </li>\n                <li\n                    v-if=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    :id=\"getItemId(processedItem)\"\n                    :style=\"getItemProp(processedItem, 'style')\"\n                    :class=\"[cx('separator'), getItemProp(processedItem, 'class')]\"\n                    role=\"separator\"\n                    v-bind=\"ptm('separator')\"\n                ></li>\n            </template>\n        </ul>\n    </transition>\n</template>\n\n<script>\nimport { nestedPosition } from '@primeuix/utils/dom';\nimport { isNotEmpty, resolve } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport AngleRightIcon from '@primevue/icons/angleright';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'TieredMenuSub',\n    hostName: 'TieredMenu',\n    extends: BaseComponent,\n    emits: ['item-click', 'item-mouseenter', 'item-mousemove'],\n    container: null,\n    props: {\n        menuId: {\n            type: String,\n            default: null\n        },\n        focusedItemId: {\n            type: String,\n            default: null\n        },\n        items: {\n            type: Array,\n            default: null\n        },\n        visible: {\n            type: Boolean,\n            default: false\n        },\n        level: {\n            type: Number,\n            default: 0\n        },\n        templates: {\n            type: Object,\n            default: null\n        },\n        activeItemPath: {\n            type: Object,\n            default: null\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        }\n    },\n    methods: {\n        getItemId(processedItem) {\n            return `${this.menuId}_${processedItem.key}`;\n        },\n        getItemKey(processedItem) {\n            return this.getItemId(processedItem);\n        },\n        getItemProp(processedItem, name, params) {\n            return processedItem && processedItem.item ? resolve(processedItem.item[name], params) : undefined;\n        },\n        getItemLabel(processedItem) {\n            return this.getItemProp(processedItem, 'label');\n        },\n        getItemLabelId(processedItem) {\n            return `${this.menuId}_${processedItem.key}_label`;\n        },\n        getPTOptions(processedItem, index, key) {\n            return this.ptm(key, {\n                context: {\n                    item: processedItem.item,\n                    index,\n                    active: this.isItemActive(processedItem),\n                    focused: this.isItemFocused(processedItem),\n                    disabled: this.isItemDisabled(processedItem)\n                }\n            });\n        },\n        isItemActive(processedItem) {\n            return this.activeItemPath.some((path) => path.key === processedItem.key);\n        },\n        isItemVisible(processedItem) {\n            return this.getItemProp(processedItem, 'visible') !== false;\n        },\n        isItemDisabled(processedItem) {\n            return this.getItemProp(processedItem, 'disabled');\n        },\n        isItemFocused(processedItem) {\n            return this.focusedItemId === this.getItemId(processedItem);\n        },\n        isItemGroup(processedItem) {\n            return isNotEmpty(processedItem.items);\n        },\n        onEnter() {\n            nestedPosition(this.container, this.level);\n        },\n        onItemClick(event, processedItem) {\n            this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n            this.$emit('item-click', { originalEvent: event, processedItem, isFocus: true });\n        },\n        onItemMouseEnter(event, processedItem) {\n            this.$emit('item-mouseenter', { originalEvent: event, processedItem });\n        },\n        onItemMouseMove(event, processedItem) {\n            this.$emit('item-mousemove', { originalEvent: event, processedItem });\n        },\n        getAriaSetSize() {\n            return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n        },\n        getAriaPosInset(index) {\n            return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n        },\n        getMenuItemProps(processedItem, index) {\n            return {\n                action: mergeProps(\n                    {\n                        class: this.cx('itemLink'),\n                        tabindex: -1\n                    },\n                    this.getPTOptions(processedItem, index, 'itemLink')\n                ),\n                icon: mergeProps(\n                    {\n                        class: [this.cx('itemIcon'), this.getItemProp(processedItem, 'icon')]\n                    },\n                    this.getPTOptions(processedItem, index, 'itemIcon')\n                ),\n                label: mergeProps(\n                    {\n                        class: this.cx('itemLabel')\n                    },\n                    this.getPTOptions(processedItem, index, 'itemLabel')\n                ),\n                submenuicon: mergeProps(\n                    {\n                        class: this.cx('submenuIcon')\n                    },\n                    this.getPTOptions(processedItem, index, 'submenuIcon')\n                )\n            };\n        },\n        containerRef(el) {\n            this.container = el;\n        }\n    },\n    components: {\n        AngleRightIcon: AngleRightIcon\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <Portal :appendTo=\"appendTo\" :disabled=\"!popup\">\n        <transition name=\"p-connected-overlay\" @enter=\"onEnter\" @after-enter=\"onAfterEnter\" @leave=\"onLeave\" @after-leave=\"onAfterLeave\" v-bind=\"ptm('transition')\">\n            <div v-if=\"visible\" :ref=\"containerRef\" :id=\"$id\" :class=\"cx('root')\" @click=\"onOverlayClick\" v-bind=\"ptmi('root')\">\n                <div v-if=\"$slots.start\" :class=\"cx('start')\" v-bind=\"ptm('start')\">\n                    <slot name=\"start\"></slot>\n                </div>\n                <TieredMenuSub\n                    :ref=\"menubarRef\"\n                    :id=\"$id + '_list'\"\n                    :class=\"cx('rootList')\"\n                    :tabindex=\"!disabled ? tabindex : -1\"\n                    role=\"menubar\"\n                    :aria-label=\"ariaLabel\"\n                    :aria-labelledby=\"ariaLabelledby\"\n                    :aria-disabled=\"disabled || undefined\"\n                    aria-orientation=\"vertical\"\n                    :aria-activedescendant=\"focused ? focusedItemId : undefined\"\n                    :menuId=\"$id\"\n                    :focusedItemId=\"focused ? focusedItemId : undefined\"\n                    :items=\"processedItems\"\n                    :templates=\"$slots\"\n                    :activeItemPath=\"activeItemPath\"\n                    :level=\"0\"\n                    :visible=\"submenuVisible\"\n                    :pt=\"pt\"\n                    :unstyled=\"unstyled\"\n                    @focus=\"onFocus\"\n                    @blur=\"onBlur\"\n                    @keydown=\"onKeyDown\"\n                    @item-click=\"onItemClick\"\n                    @item-mouseenter=\"onItemMouseEnter\"\n                    @item-mousemove=\"onItemMouseMove\"\n                    v-bind=\"ptm('rootList')\"\n                />\n                <div v-if=\"$slots.end\" :class=\"cx('end')\" v-bind=\"ptm('end')\">\n                    <slot name=\"end\"></slot>\n                </div>\n            </div>\n        </transition>\n    </Portal>\n</template>\n\n<script>\nimport { absolutePosition, addStyle, findSingle, focus, getOuterWidth, isTouchDevice } from '@primeuix/utils/dom';\nimport { findLastIndex, isEmpty, isNotEmpty, isPrintableCharacter, resolve } from '@primeuix/utils/object';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { ConnectedOverlayScrollHandler } from '@primevue/core/utils';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport BaseTieredMenu from './BaseTieredMenu.vue';\nimport TieredMenuSub from './TieredMenuSub.vue';\n\nexport default {\n    name: 'TieredMenu',\n    extends: BaseTieredMenu,\n    inheritAttrs: false,\n    emits: ['focus', 'blur', 'before-show', 'before-hide', 'hide', 'show'],\n    outsideClickListener: null,\n    matchMediaListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    target: null,\n    container: null,\n    menubar: null,\n    searchTimeout: null,\n    searchValue: null,\n    data() {\n        return {\n            focused: false,\n            focusedItemInfo: { index: -1, level: 0, parentKey: '' },\n            activeItemPath: [],\n            visible: !this.popup,\n            submenuVisible: false,\n            dirty: false,\n            query: null,\n            queryMatches: false\n        };\n    },\n    watch: {\n        activeItemPath(newPath) {\n            if (!this.popup) {\n                if (isNotEmpty(newPath)) {\n                    this.bindOutsideClickListener();\n                    this.bindResizeListener();\n                } else {\n                    this.unbindOutsideClickListener();\n                    this.unbindResizeListener();\n                }\n            }\n        }\n    },\n    mounted() {\n        this.bindMatchMediaListener();\n    },\n    beforeUnmount() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n        this.unbindMatchMediaListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        if (this.container && this.autoZIndex) {\n            ZIndex.clear(this.container);\n        }\n\n        this.target = null;\n        this.container = null;\n    },\n    methods: {\n        getItemProp(item, name) {\n            return item ? resolve(item[name]) : undefined;\n        },\n        getItemLabel(item) {\n            return this.getItemProp(item, 'label');\n        },\n        isItemDisabled(item) {\n            return this.getItemProp(item, 'disabled');\n        },\n        isItemVisible(item) {\n            return this.getItemProp(item, 'visible') !== false;\n        },\n        isItemGroup(item) {\n            return isNotEmpty(this.getItemProp(item, 'items'));\n        },\n        isItemSeparator(item) {\n            return this.getItemProp(item, 'separator');\n        },\n        getProccessedItemLabel(processedItem) {\n            return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n        },\n        isProccessedItemGroup(processedItem) {\n            return processedItem && isNotEmpty(processedItem.items);\n        },\n        toggle(event) {\n            this.visible ? this.hide(event, true) : this.show(event);\n        },\n        show(event, isFocus) {\n            if (this.popup) {\n                this.$emit('before-show');\n                this.visible = true;\n                this.target = this.target || event.currentTarget;\n                this.relatedTarget = event.relatedTarget || null;\n            }\n\n            isFocus && focus(this.menubar);\n        },\n        hide(event, isFocus) {\n            if (this.popup) {\n                this.$emit('before-hide');\n                this.visible = false;\n            }\n\n            this.activeItemPath = [];\n            this.focusedItemInfo = { index: -1, level: 0, parentKey: '' };\n\n            isFocus && focus(this.relatedTarget || this.target || this.menubar);\n            this.dirty = false;\n        },\n        onFocus(event) {\n            this.focused = true;\n\n            if (!this.popup) {\n                this.focusedItemInfo = this.focusedItemInfo.index !== -1 ? this.focusedItemInfo : { index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '' };\n            }\n\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.focused = false;\n            this.focusedItemInfo = { index: -1, level: 0, parentKey: '' };\n            this.searchValue = '';\n            this.dirty = false;\n            this.$emit('blur', event);\n        },\n        onKeyDown(event) {\n            if (this.disabled) {\n                event.preventDefault();\n\n                return;\n            }\n\n            const metaKey = event.metaKey || event.ctrlKey;\n\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeftKey(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRightKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event);\n                    break;\n\n                case 'PageDown':\n                case 'PageUp':\n                case 'Backspace':\n                case 'ShiftLeft':\n                case 'ShiftRight':\n                    //NOOP\n                    break;\n\n                default:\n                    if (!metaKey && isPrintableCharacter(event.key)) {\n                        this.searchItems(event, event.key);\n                    }\n\n                    break;\n            }\n        },\n        onItemChange(event, type) {\n            const { processedItem, isFocus } = event;\n\n            if (isEmpty(processedItem)) return;\n\n            const { index, key, level, parentKey, items } = processedItem;\n            const grouped = isNotEmpty(items);\n\n            const activeItemPath = this.activeItemPath.filter((p) => p.parentKey !== parentKey && p.parentKey !== key);\n\n            if (grouped) {\n                activeItemPath.push(processedItem);\n                this.submenuVisible = true;\n            }\n\n            this.focusedItemInfo = { index, level, parentKey };\n\n            grouped && (this.dirty = true);\n            isFocus && focus(this.menubar);\n\n            if (type === 'hover' && this.queryMatches) {\n                return;\n            }\n\n            this.activeItemPath = activeItemPath;\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.target\n            });\n        },\n        onItemClick(event) {\n            const { originalEvent, processedItem } = event;\n            const grouped = this.isProccessedItemGroup(processedItem);\n            const root = isEmpty(processedItem.parent);\n            const selected = this.isSelected(processedItem);\n\n            if (selected) {\n                const { index, key, level, parentKey } = processedItem;\n\n                this.activeItemPath = this.activeItemPath.filter((p) => key !== p.key && key.startsWith(p.key));\n                this.focusedItemInfo = { index, level, parentKey };\n\n                this.dirty = !root;\n                focus(this.menubar);\n            } else {\n                if (grouped) {\n                    this.onItemChange(event);\n                } else {\n                    const rootProcessedItem = root ? processedItem : this.activeItemPath.find((p) => p.parentKey === '');\n\n                    this.hide(originalEvent);\n                    this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n\n                    focus(this.menubar);\n                }\n            }\n        },\n        onItemMouseEnter(event) {\n            if (this.dirty) {\n                this.onItemChange(event, 'hover');\n            }\n        },\n        onItemMouseMove(event) {\n            if (this.focused) {\n                this.changeFocusedItemIndex(event, event.processedItem.index);\n            }\n        },\n        onArrowDownKey(event) {\n            const itemIndex = this.focusedItemInfo.index !== -1 ? this.findNextItemIndex(this.focusedItemInfo.index) : this.findFirstFocusedItemIndex();\n\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            if (event.altKey) {\n                if (this.focusedItemInfo.index !== -1) {\n                    const processedItem = this.visibleItems[this.focusedItemInfo.index];\n                    const grouped = this.isProccessedItemGroup(processedItem);\n\n                    !grouped && this.onItemChange({ originalEvent: event, processedItem });\n                }\n\n                this.popup && this.hide(event, true);\n                event.preventDefault();\n            } else {\n                const itemIndex = this.focusedItemInfo.index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo.index) : this.findLastFocusedItemIndex();\n\n                this.changeFocusedItemIndex(event, itemIndex);\n                event.preventDefault();\n            }\n        },\n        onArrowLeftKey(event) {\n            const processedItem = this.visibleItems[this.focusedItemInfo.index];\n            const parentItem = this.activeItemPath.find((p) => p.key === processedItem.parentKey);\n            const root = isEmpty(processedItem.parent);\n\n            if (!root) {\n                this.focusedItemInfo = { index: -1, parentKey: parentItem ? parentItem.parentKey : '' };\n                this.searchValue = '';\n                this.onArrowDownKey(event);\n            }\n\n            this.activeItemPath = this.activeItemPath.filter((p) => p.parentKey !== this.focusedItemInfo.parentKey);\n\n            event.preventDefault();\n        },\n        onArrowRightKey(event) {\n            const processedItem = this.visibleItems[this.focusedItemInfo.index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo = { index: -1, parentKey: processedItem.key };\n                this.searchValue = '';\n                this.onArrowDownKey(event);\n            }\n\n            event.preventDefault();\n        },\n        onHomeKey(event) {\n            this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            this.changeFocusedItemIndex(event, this.findLastItemIndex());\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            if (this.focusedItemInfo.index !== -1) {\n                const element = findSingle(this.menubar, `li[id=\"${`${this.focusedItemId}`}\"]`);\n                const anchorElement = element && findSingle(element, '[data-pc-section=\"itemlink\"]');\n\n                anchorElement ? anchorElement.click() : element && element.click();\n\n                if (!this.popup) {\n                    const processedItem = this.visibleItems[this.focusedItemInfo.index];\n                    const grouped = this.isProccessedItemGroup(processedItem);\n\n                    !grouped && (this.focusedItemInfo.index = this.findFirstFocusedItemIndex());\n                }\n            }\n\n            event.preventDefault();\n        },\n        onSpaceKey(event) {\n            this.onEnterKey(event);\n        },\n        onEscapeKey(event) {\n            if (this.popup || this.focusedItemInfo.level !== 0) {\n                const _focusedItemInfo = this.focusedItemInfo;\n\n                this.hide(event, false);\n                this.focusedItemInfo = { index: Number(_focusedItemInfo.parentKey.split('_')[0]), level: 0, parentKey: '' };\n                this.popup && focus(this.target);\n            }\n\n            event.preventDefault();\n        },\n        onTabKey(event) {\n            if (this.focusedItemInfo.index !== -1) {\n                const processedItem = this.visibleItems[this.focusedItemInfo.index];\n                const grouped = this.isProccessedItemGroup(processedItem);\n\n                !grouped && this.onItemChange({ originalEvent: event, processedItem });\n            }\n\n            this.hide();\n        },\n        onEnter(el) {\n            if (this.autoZIndex) {\n                ZIndex.set('menu', el, this.baseZIndex + this.$primevue.config.zIndex.menu);\n            }\n\n            addStyle(el, { position: 'absolute', top: '0' });\n            this.alignOverlay();\n            focus(this.menubar);\n            this.scrollInView();\n        },\n        onAfterEnter() {\n            this.bindOutsideClickListener();\n            this.bindScrollListener();\n            this.bindResizeListener();\n\n            this.$emit('show');\n        },\n        onLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindScrollListener();\n            this.unbindResizeListener();\n\n            this.$emit('hide');\n            this.container = null;\n            this.dirty = false;\n        },\n        onAfterLeave(el) {\n            if (this.autoZIndex) {\n                ZIndex.clear(el);\n            }\n        },\n        alignOverlay() {\n            absolutePosition(this.container, this.target);\n            const targetWidth = getOuterWidth(this.target);\n\n            if (targetWidth > getOuterWidth(this.container)) {\n                this.container.style.minWidth = getOuterWidth(this.target) + 'px';\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    const isOutsideContainer = this.container && !this.container.contains(event.target);\n                    const isOutsideTarget = this.popup ? !(this.target && (this.target === event.target || this.target.contains(event.target))) : true;\n\n                    if (isOutsideContainer && isOutsideTarget) {\n                        this.hide();\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener, true);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener, true);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, (event) => {\n                    this.hide(event, true);\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = (event) => {\n                    if (!isTouchDevice()) {\n                        this.hide(event, true);\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        bindMatchMediaListener() {\n            if (!this.matchMediaListener) {\n                const query = matchMedia(`(max-width: ${this.breakpoint})`);\n\n                this.query = query;\n                this.queryMatches = query.matches;\n\n                this.matchMediaListener = () => {\n                    this.queryMatches = query.matches;\n                };\n\n                this.query.addEventListener('change', this.matchMediaListener);\n            }\n        },\n        unbindMatchMediaListener() {\n            if (this.matchMediaListener) {\n                this.query.removeEventListener('change', this.matchMediaListener);\n                this.matchMediaListener = null;\n            }\n        },\n        isItemMatched(processedItem) {\n            return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem)?.toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n        },\n        isValidItem(processedItem) {\n            return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item) && this.isItemVisible(processedItem.item);\n        },\n        isValidSelectedItem(processedItem) {\n            return this.isValidItem(processedItem) && this.isSelected(processedItem);\n        },\n        isSelected(processedItem) {\n            return this.activeItemPath.some((p) => p.key === processedItem.key);\n        },\n        findFirstItemIndex() {\n            return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n        },\n        findLastItemIndex() {\n            return findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n        },\n        findNextItemIndex(index) {\n            const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n\n            return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n        },\n        findPrevItemIndex(index) {\n            const matchedItemIndex = index > 0 ? findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n\n            return matchedItemIndex > -1 ? matchedItemIndex : index;\n        },\n        findSelectedItemIndex() {\n            return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n        },\n        findFirstFocusedItemIndex() {\n            const selectedIndex = this.findSelectedItemIndex();\n\n            return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n        },\n        findLastFocusedItemIndex() {\n            const selectedIndex = this.findSelectedItemIndex();\n\n            return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n        },\n        searchItems(event, char) {\n            this.searchValue = (this.searchValue || '') + char;\n\n            let itemIndex = -1;\n            let matched = false;\n\n            if (this.focusedItemInfo.index !== -1) {\n                itemIndex = this.visibleItems.slice(this.focusedItemInfo.index).findIndex((processedItem) => this.isItemMatched(processedItem));\n                itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo.index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo.index;\n            } else {\n                itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n            }\n\n            if (itemIndex !== -1) {\n                matched = true;\n            }\n\n            if (itemIndex === -1 && this.focusedItemInfo.index === -1) {\n                itemIndex = this.findFirstFocusedItemIndex();\n            }\n\n            if (itemIndex !== -1) {\n                this.changeFocusedItemIndex(event, itemIndex);\n            }\n\n            if (this.searchTimeout) {\n                clearTimeout(this.searchTimeout);\n            }\n\n            this.searchTimeout = setTimeout(() => {\n                this.searchValue = '';\n                this.searchTimeout = null;\n            }, 500);\n\n            return matched;\n        },\n        changeFocusedItemIndex(event, index) {\n            if (this.focusedItemInfo.index !== index) {\n                this.focusedItemInfo.index = index;\n                this.scrollInView();\n            }\n        },\n        scrollInView(index = -1) {\n            const id = index !== -1 ? `${this.$id}_${index}` : this.focusedItemId;\n            const element = findSingle(this.menubar, `li[id=\"${id}\"]`);\n\n            if (element) {\n                element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'start' });\n            }\n        },\n        createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n            const processedItems = [];\n\n            items &&\n                items.forEach((item, index) => {\n                    const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                    const newItem = {\n                        item,\n                        index,\n                        level,\n                        key,\n                        parent,\n                        parentKey\n                    };\n\n                    newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                    processedItems.push(newItem);\n                });\n\n            return processedItems;\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        menubarRef(el) {\n            this.menubar = el ? el.$el : undefined;\n        }\n    },\n    computed: {\n        processedItems() {\n            return this.createProcessedItems(this.model || []);\n        },\n        visibleItems() {\n            const processedItem = this.activeItemPath.find((p) => p.key === this.focusedItemInfo.parentKey);\n\n            return processedItem ? processedItem.items : this.processedItems;\n        },\n        focusedItemId() {\n            return this.focusedItemInfo.index !== -1 ? `${this.$id}${isNotEmpty(this.focusedItemInfo.parentKey) ? '_' + this.focusedItemInfo.parentKey : ''}_${this.focusedItemInfo.index}` : null;\n        }\n    },\n    components: {\n        TieredMenuSub: TieredMenuSub,\n        Portal: Portal\n    }\n};\n</script>\n", "<template>\n    <Portal :appendTo=\"appendTo\" :disabled=\"!popup\">\n        <transition name=\"p-connected-overlay\" @enter=\"onEnter\" @after-enter=\"onAfterEnter\" @leave=\"onLeave\" @after-leave=\"onAfterLeave\" v-bind=\"ptm('transition')\">\n            <div v-if=\"visible\" :ref=\"containerRef\" :id=\"$id\" :class=\"cx('root')\" @click=\"onOverlayClick\" v-bind=\"ptmi('root')\">\n                <div v-if=\"$slots.start\" :class=\"cx('start')\" v-bind=\"ptm('start')\">\n                    <slot name=\"start\"></slot>\n                </div>\n                <TieredMenuSub\n                    :ref=\"menubarRef\"\n                    :id=\"$id + '_list'\"\n                    :class=\"cx('rootList')\"\n                    :tabindex=\"!disabled ? tabindex : -1\"\n                    role=\"menubar\"\n                    :aria-label=\"ariaLabel\"\n                    :aria-labelledby=\"ariaLabelledby\"\n                    :aria-disabled=\"disabled || undefined\"\n                    aria-orientation=\"vertical\"\n                    :aria-activedescendant=\"focused ? focusedItemId : undefined\"\n                    :menuId=\"$id\"\n                    :focusedItemId=\"focused ? focusedItemId : undefined\"\n                    :items=\"processedItems\"\n                    :templates=\"$slots\"\n                    :activeItemPath=\"activeItemPath\"\n                    :level=\"0\"\n                    :visible=\"submenuVisible\"\n                    :pt=\"pt\"\n                    :unstyled=\"unstyled\"\n                    @focus=\"onFocus\"\n                    @blur=\"onBlur\"\n                    @keydown=\"onKeyDown\"\n                    @item-click=\"onItemClick\"\n                    @item-mouseenter=\"onItemMouseEnter\"\n                    @item-mousemove=\"onItemMouseMove\"\n                    v-bind=\"ptm('rootList')\"\n                />\n                <div v-if=\"$slots.end\" :class=\"cx('end')\" v-bind=\"ptm('end')\">\n                    <slot name=\"end\"></slot>\n                </div>\n            </div>\n        </transition>\n    </Portal>\n</template>\n\n<script>\nimport { absolutePosition, addStyle, findSingle, focus, getOuterWidth, isTouchDevice } from '@primeuix/utils/dom';\nimport { findLastIndex, isEmpty, isNotEmpty, isPrintableCharacter, resolve } from '@primeuix/utils/object';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { ConnectedOverlayScrollHandler } from '@primevue/core/utils';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport BaseTieredMenu from './BaseTieredMenu.vue';\nimport TieredMenuSub from './TieredMenuSub.vue';\n\nexport default {\n    name: 'TieredMenu',\n    extends: BaseTieredMenu,\n    inheritAttrs: false,\n    emits: ['focus', 'blur', 'before-show', 'before-hide', 'hide', 'show'],\n    outsideClickListener: null,\n    matchMediaListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    target: null,\n    container: null,\n    menubar: null,\n    searchTimeout: null,\n    searchValue: null,\n    data() {\n        return {\n            focused: false,\n            focusedItemInfo: { index: -1, level: 0, parentKey: '' },\n            activeItemPath: [],\n            visible: !this.popup,\n            submenuVisible: false,\n            dirty: false,\n            query: null,\n            queryMatches: false\n        };\n    },\n    watch: {\n        activeItemPath(newPath) {\n            if (!this.popup) {\n                if (isNotEmpty(newPath)) {\n                    this.bindOutsideClickListener();\n                    this.bindResizeListener();\n                } else {\n                    this.unbindOutsideClickListener();\n                    this.unbindResizeListener();\n                }\n            }\n        }\n    },\n    mounted() {\n        this.bindMatchMediaListener();\n    },\n    beforeUnmount() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n        this.unbindMatchMediaListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        if (this.container && this.autoZIndex) {\n            ZIndex.clear(this.container);\n        }\n\n        this.target = null;\n        this.container = null;\n    },\n    methods: {\n        getItemProp(item, name) {\n            return item ? resolve(item[name]) : undefined;\n        },\n        getItemLabel(item) {\n            return this.getItemProp(item, 'label');\n        },\n        isItemDisabled(item) {\n            return this.getItemProp(item, 'disabled');\n        },\n        isItemVisible(item) {\n            return this.getItemProp(item, 'visible') !== false;\n        },\n        isItemGroup(item) {\n            return isNotEmpty(this.getItemProp(item, 'items'));\n        },\n        isItemSeparator(item) {\n            return this.getItemProp(item, 'separator');\n        },\n        getProccessedItemLabel(processedItem) {\n            return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n        },\n        isProccessedItemGroup(processedItem) {\n            return processedItem && isNotEmpty(processedItem.items);\n        },\n        toggle(event) {\n            this.visible ? this.hide(event, true) : this.show(event);\n        },\n        show(event, isFocus) {\n            if (this.popup) {\n                this.$emit('before-show');\n                this.visible = true;\n                this.target = this.target || event.currentTarget;\n                this.relatedTarget = event.relatedTarget || null;\n            }\n\n            isFocus && focus(this.menubar);\n        },\n        hide(event, isFocus) {\n            if (this.popup) {\n                this.$emit('before-hide');\n                this.visible = false;\n            }\n\n            this.activeItemPath = [];\n            this.focusedItemInfo = { index: -1, level: 0, parentKey: '' };\n\n            isFocus && focus(this.relatedTarget || this.target || this.menubar);\n            this.dirty = false;\n        },\n        onFocus(event) {\n            this.focused = true;\n\n            if (!this.popup) {\n                this.focusedItemInfo = this.focusedItemInfo.index !== -1 ? this.focusedItemInfo : { index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '' };\n            }\n\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.focused = false;\n            this.focusedItemInfo = { index: -1, level: 0, parentKey: '' };\n            this.searchValue = '';\n            this.dirty = false;\n            this.$emit('blur', event);\n        },\n        onKeyDown(event) {\n            if (this.disabled) {\n                event.preventDefault();\n\n                return;\n            }\n\n            const metaKey = event.metaKey || event.ctrlKey;\n\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeftKey(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRightKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event);\n                    break;\n\n                case 'PageDown':\n                case 'PageUp':\n                case 'Backspace':\n                case 'ShiftLeft':\n                case 'ShiftRight':\n                    //NOOP\n                    break;\n\n                default:\n                    if (!metaKey && isPrintableCharacter(event.key)) {\n                        this.searchItems(event, event.key);\n                    }\n\n                    break;\n            }\n        },\n        onItemChange(event, type) {\n            const { processedItem, isFocus } = event;\n\n            if (isEmpty(processedItem)) return;\n\n            const { index, key, level, parentKey, items } = processedItem;\n            const grouped = isNotEmpty(items);\n\n            const activeItemPath = this.activeItemPath.filter((p) => p.parentKey !== parentKey && p.parentKey !== key);\n\n            if (grouped) {\n                activeItemPath.push(processedItem);\n                this.submenuVisible = true;\n            }\n\n            this.focusedItemInfo = { index, level, parentKey };\n\n            grouped && (this.dirty = true);\n            isFocus && focus(this.menubar);\n\n            if (type === 'hover' && this.queryMatches) {\n                return;\n            }\n\n            this.activeItemPath = activeItemPath;\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.target\n            });\n        },\n        onItemClick(event) {\n            const { originalEvent, processedItem } = event;\n            const grouped = this.isProccessedItemGroup(processedItem);\n            const root = isEmpty(processedItem.parent);\n            const selected = this.isSelected(processedItem);\n\n            if (selected) {\n                const { index, key, level, parentKey } = processedItem;\n\n                this.activeItemPath = this.activeItemPath.filter((p) => key !== p.key && key.startsWith(p.key));\n                this.focusedItemInfo = { index, level, parentKey };\n\n                this.dirty = !root;\n                focus(this.menubar);\n            } else {\n                if (grouped) {\n                    this.onItemChange(event);\n                } else {\n                    const rootProcessedItem = root ? processedItem : this.activeItemPath.find((p) => p.parentKey === '');\n\n                    this.hide(originalEvent);\n                    this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n\n                    focus(this.menubar);\n                }\n            }\n        },\n        onItemMouseEnter(event) {\n            if (this.dirty) {\n                this.onItemChange(event, 'hover');\n            }\n        },\n        onItemMouseMove(event) {\n            if (this.focused) {\n                this.changeFocusedItemIndex(event, event.processedItem.index);\n            }\n        },\n        onArrowDownKey(event) {\n            const itemIndex = this.focusedItemInfo.index !== -1 ? this.findNextItemIndex(this.focusedItemInfo.index) : this.findFirstFocusedItemIndex();\n\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            if (event.altKey) {\n                if (this.focusedItemInfo.index !== -1) {\n                    const processedItem = this.visibleItems[this.focusedItemInfo.index];\n                    const grouped = this.isProccessedItemGroup(processedItem);\n\n                    !grouped && this.onItemChange({ originalEvent: event, processedItem });\n                }\n\n                this.popup && this.hide(event, true);\n                event.preventDefault();\n            } else {\n                const itemIndex = this.focusedItemInfo.index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo.index) : this.findLastFocusedItemIndex();\n\n                this.changeFocusedItemIndex(event, itemIndex);\n                event.preventDefault();\n            }\n        },\n        onArrowLeftKey(event) {\n            const processedItem = this.visibleItems[this.focusedItemInfo.index];\n            const parentItem = this.activeItemPath.find((p) => p.key === processedItem.parentKey);\n            const root = isEmpty(processedItem.parent);\n\n            if (!root) {\n                this.focusedItemInfo = { index: -1, parentKey: parentItem ? parentItem.parentKey : '' };\n                this.searchValue = '';\n                this.onArrowDownKey(event);\n            }\n\n            this.activeItemPath = this.activeItemPath.filter((p) => p.parentKey !== this.focusedItemInfo.parentKey);\n\n            event.preventDefault();\n        },\n        onArrowRightKey(event) {\n            const processedItem = this.visibleItems[this.focusedItemInfo.index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo = { index: -1, parentKey: processedItem.key };\n                this.searchValue = '';\n                this.onArrowDownKey(event);\n            }\n\n            event.preventDefault();\n        },\n        onHomeKey(event) {\n            this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            this.changeFocusedItemIndex(event, this.findLastItemIndex());\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            if (this.focusedItemInfo.index !== -1) {\n                const element = findSingle(this.menubar, `li[id=\"${`${this.focusedItemId}`}\"]`);\n                const anchorElement = element && findSingle(element, '[data-pc-section=\"itemlink\"]');\n\n                anchorElement ? anchorElement.click() : element && element.click();\n\n                if (!this.popup) {\n                    const processedItem = this.visibleItems[this.focusedItemInfo.index];\n                    const grouped = this.isProccessedItemGroup(processedItem);\n\n                    !grouped && (this.focusedItemInfo.index = this.findFirstFocusedItemIndex());\n                }\n            }\n\n            event.preventDefault();\n        },\n        onSpaceKey(event) {\n            this.onEnterKey(event);\n        },\n        onEscapeKey(event) {\n            if (this.popup || this.focusedItemInfo.level !== 0) {\n                const _focusedItemInfo = this.focusedItemInfo;\n\n                this.hide(event, false);\n                this.focusedItemInfo = { index: Number(_focusedItemInfo.parentKey.split('_')[0]), level: 0, parentKey: '' };\n                this.popup && focus(this.target);\n            }\n\n            event.preventDefault();\n        },\n        onTabKey(event) {\n            if (this.focusedItemInfo.index !== -1) {\n                const processedItem = this.visibleItems[this.focusedItemInfo.index];\n                const grouped = this.isProccessedItemGroup(processedItem);\n\n                !grouped && this.onItemChange({ originalEvent: event, processedItem });\n            }\n\n            this.hide();\n        },\n        onEnter(el) {\n            if (this.autoZIndex) {\n                ZIndex.set('menu', el, this.baseZIndex + this.$primevue.config.zIndex.menu);\n            }\n\n            addStyle(el, { position: 'absolute', top: '0' });\n            this.alignOverlay();\n            focus(this.menubar);\n            this.scrollInView();\n        },\n        onAfterEnter() {\n            this.bindOutsideClickListener();\n            this.bindScrollListener();\n            this.bindResizeListener();\n\n            this.$emit('show');\n        },\n        onLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindScrollListener();\n            this.unbindResizeListener();\n\n            this.$emit('hide');\n            this.container = null;\n            this.dirty = false;\n        },\n        onAfterLeave(el) {\n            if (this.autoZIndex) {\n                ZIndex.clear(el);\n            }\n        },\n        alignOverlay() {\n            absolutePosition(this.container, this.target);\n            const targetWidth = getOuterWidth(this.target);\n\n            if (targetWidth > getOuterWidth(this.container)) {\n                this.container.style.minWidth = getOuterWidth(this.target) + 'px';\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = (event) => {\n                    const isOutsideContainer = this.container && !this.container.contains(event.target);\n                    const isOutsideTarget = this.popup ? !(this.target && (this.target === event.target || this.target.contains(event.target))) : true;\n\n                    if (isOutsideContainer && isOutsideTarget) {\n                        this.hide();\n                    }\n                };\n\n                document.addEventListener('click', this.outsideClickListener, true);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener, true);\n                this.outsideClickListener = null;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, (event) => {\n                    this.hide(event, true);\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = (event) => {\n                    if (!isTouchDevice()) {\n                        this.hide(event, true);\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        bindMatchMediaListener() {\n            if (!this.matchMediaListener) {\n                const query = matchMedia(`(max-width: ${this.breakpoint})`);\n\n                this.query = query;\n                this.queryMatches = query.matches;\n\n                this.matchMediaListener = () => {\n                    this.queryMatches = query.matches;\n                };\n\n                this.query.addEventListener('change', this.matchMediaListener);\n            }\n        },\n        unbindMatchMediaListener() {\n            if (this.matchMediaListener) {\n                this.query.removeEventListener('change', this.matchMediaListener);\n                this.matchMediaListener = null;\n            }\n        },\n        isItemMatched(processedItem) {\n            return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem)?.toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n        },\n        isValidItem(processedItem) {\n            return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item) && this.isItemVisible(processedItem.item);\n        },\n        isValidSelectedItem(processedItem) {\n            return this.isValidItem(processedItem) && this.isSelected(processedItem);\n        },\n        isSelected(processedItem) {\n            return this.activeItemPath.some((p) => p.key === processedItem.key);\n        },\n        findFirstItemIndex() {\n            return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n        },\n        findLastItemIndex() {\n            return findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n        },\n        findNextItemIndex(index) {\n            const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n\n            return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n        },\n        findPrevItemIndex(index) {\n            const matchedItemIndex = index > 0 ? findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n\n            return matchedItemIndex > -1 ? matchedItemIndex : index;\n        },\n        findSelectedItemIndex() {\n            return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n        },\n        findFirstFocusedItemIndex() {\n            const selectedIndex = this.findSelectedItemIndex();\n\n            return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n        },\n        findLastFocusedItemIndex() {\n            const selectedIndex = this.findSelectedItemIndex();\n\n            return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n        },\n        searchItems(event, char) {\n            this.searchValue = (this.searchValue || '') + char;\n\n            let itemIndex = -1;\n            let matched = false;\n\n            if (this.focusedItemInfo.index !== -1) {\n                itemIndex = this.visibleItems.slice(this.focusedItemInfo.index).findIndex((processedItem) => this.isItemMatched(processedItem));\n                itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo.index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo.index;\n            } else {\n                itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n            }\n\n            if (itemIndex !== -1) {\n                matched = true;\n            }\n\n            if (itemIndex === -1 && this.focusedItemInfo.index === -1) {\n                itemIndex = this.findFirstFocusedItemIndex();\n            }\n\n            if (itemIndex !== -1) {\n                this.changeFocusedItemIndex(event, itemIndex);\n            }\n\n            if (this.searchTimeout) {\n                clearTimeout(this.searchTimeout);\n            }\n\n            this.searchTimeout = setTimeout(() => {\n                this.searchValue = '';\n                this.searchTimeout = null;\n            }, 500);\n\n            return matched;\n        },\n        changeFocusedItemIndex(event, index) {\n            if (this.focusedItemInfo.index !== index) {\n                this.focusedItemInfo.index = index;\n                this.scrollInView();\n            }\n        },\n        scrollInView(index = -1) {\n            const id = index !== -1 ? `${this.$id}_${index}` : this.focusedItemId;\n            const element = findSingle(this.menubar, `li[id=\"${id}\"]`);\n\n            if (element) {\n                element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'start' });\n            }\n        },\n        createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n            const processedItems = [];\n\n            items &&\n                items.forEach((item, index) => {\n                    const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                    const newItem = {\n                        item,\n                        index,\n                        level,\n                        key,\n                        parent,\n                        parentKey\n                    };\n\n                    newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                    processedItems.push(newItem);\n                });\n\n            return processedItems;\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        menubarRef(el) {\n            this.menubar = el ? el.$el : undefined;\n        }\n    },\n    computed: {\n        processedItems() {\n            return this.createProcessedItems(this.model || []);\n        },\n        visibleItems() {\n            const processedItem = this.activeItemPath.find((p) => p.key === this.focusedItemInfo.parentKey);\n\n            return processedItem ? processedItem.items : this.processedItems;\n        },\n        focusedItemId() {\n            return this.focusedItemInfo.index !== -1 ? `${this.$id}${isNotEmpty(this.focusedItemInfo.parentKey) ? '_' + this.focusedItemInfo.parentKey : ''}_${this.focusedItemInfo.index}` : null;\n        }\n    },\n    components: {\n        TieredMenuSub: TieredMenuSub,\n        Portal: Portal\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "popup", "type", "Boolean", "model", "Array", "appendTo", "String", "Object", "breakpoint", "autoZIndex", "baseZIndex", "Number", "disabled", "tabindex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "style", "TieredMenuStyle", "provide", "$pcTieredMenu", "$parentInstance", "hostName", "emits", "container", "menuId", "focusedItemId", "items", "visible", "level", "templates", "activeItemPath", "methods", "getItemId", "processedItem", "concat", "key", "getItemKey", "getItemProp", "params", "item", "resolve", "undefined", "getItemLabel", "getItemLabelId", "getPTOptions", "index", "ptm", "context", "active", "isItemActive", "focused", "isItemFocused", "isItemDisabled", "some", "path", "isItemVisible", "isItemGroup", "isNotEmpty", "onEnter", "nestedPosition", "onItemClick", "event", "originalEvent", "$emit", "isFocus", "onItemMouseEnter", "onItemMouseMove", "getAriaSetSize", "_this", "filter", "length", "getAriaPosInset", "_this2", "slice", "getMenuItemProps", "action", "mergeProps", "cx", "icon", "label", "submenuicon", "containerRef", "el", "components", "AngleRightIcon", "directives", "ripple", "<PERSON><PERSON><PERSON>", "_openBlock", "_createBlock", "_Transition", "_mergeProps", "$options", "_ctx", "$props", "_createElementBlock", "ref", "_Fragment", "_renderList", "id", "role", "_createElementVNode", "onClick", "$event", "onMouseenter", "onMousemove", "_withDirectives", "href", "target", "itemicon", "_resolveDynamicComponent", "ref_for", "_toDisplayString", "_hoisted_5", "_component_AngleRightIcon", "hasSubmenu", "_component_TieredMenuSub", "sx", "pt", "unstyled", "_cache", "onItemMouseenter", "onItemMousemove", "_hoisted_6", "BaseTieredMenu", "inheritAttrs", "outsideClickListener", "matchMediaListener", "<PERSON><PERSON><PERSON><PERSON>", "resizeListener", "menubar", "searchTimeout", "searchValue", "data", "focusedItemInfo", "parent<PERSON><PERSON>", "submenuVisible", "dirty", "query", "queryMatches", "watch", "newPath", "bindOutsideClickListener", "bindResizeListener", "unbindOutsideClickListener", "unbindResizeListener", "mounted", "bindMatchMediaListener", "beforeUnmount", "unbindMatchMediaListener", "destroy", "ZIndex", "clear", "isItemSeparator", "getProccessedItemLabel", "isProccessedItemGroup", "toggle", "hide", "show", "currentTarget", "relatedTarget", "focus", "onFocus", "findFirstFocusedItemIndex", "onBlur", "onKeyDown", "preventDefault", "metaKey", "ctrl<PERSON>ey", "code", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onArrowRightKey", "onHomeKey", "onEndKey", "onSpaceKey", "onEnterKey", "onEscapeKey", "onTabKey", "isPrintableCharacter", "searchItems", "onItemChange", "isEmpty", "grouped", "p", "push", "onOverlayClick", "OverlayEventBus", "emit", "root", "parent", "selected", "isSelected", "startsWith", "rootProcessedItem", "find", "changeFocusedItemIndex", "itemIndex", "findNextItemIndex", "altKey", "visibleItems", "findPrevItemIndex", "findLastFocusedItemIndex", "parentItem", "findFirstItemIndex", "findLastItemIndex", "element", "findSingle", "anchorElement", "click", "_focusedItemInfo", "split", "set", "$primevue", "config", "zIndex", "menu", "addStyle", "position", "top", "alignOverlay", "scrollInView", "onAfterEnter", "bindScrollListener", "onLeave", "unbindScrollListener", "onAfterLeave", "absolutePosition", "targetWidth", "getOuterWidth", "min<PERSON><PERSON><PERSON>", "isOutsideContainer", "contains", "isOutsideTarget", "document", "addEventListener", "removeEventListener", "_this3", "ConnectedOverlayScrollHandler", "_this4", "isTouchDevice", "window", "_this5", "matchMedia", "matches", "isItemMatched", "_this$getProccessedIt", "isValidItem", "toLocaleLowerCase", "isValidSelectedItem", "_this6", "findIndex", "_this7", "findLastIndex", "_this8", "matchedItemIndex", "_this9", "findSelectedItemIndex", "_this0", "selectedIndex", "char", "_this1", "matched", "clearTimeout", "setTimeout", "$id", "scrollIntoView", "block", "inline", "createProcessedItems", "_this10", "arguments", "processedItems", "for<PERSON>ach", "newItem", "menubarRef", "$el", "computed", "_this11", "TieredMenuSub", "Portal", "_component_Portal", "_createVNode", "$data", "apply", "ptmi", "$slots", "start", "_renderSlot", "onKeydown", "end"], "mappings": ";;;;;;;;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,gBAAgB;AACtB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,KAAK,EAAE;AACHF,MAAAA,IAAI,EAAEG,KAAK;MACX,SAAS,EAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNJ,MAAAA,IAAI,EAAE,CAACK,MAAM,EAAEC,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRP,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;KACZ;AACDG,IAAAA,UAAU,EAAE;AACRR,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDQ,IAAAA,UAAU,EAAE;AACRT,MAAAA,IAAI,EAAEU,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNX,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACDW,IAAAA,QAAQ,EAAE;AACNZ,MAAAA,IAAI,EAAEU,MAAM;MACZ,SAAS,EAAA;KACZ;AACDG,IAAAA,cAAc,EAAE;AACZb,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;KACZ;AACDS,IAAAA,SAAS,EAAE;AACPd,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDU,EAAAA,KAAK,EAAEC,eAAe;EACtBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,aAAa,EAAE,IAAI;AACnBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;AC6BD,eAAe;AACXvB,EAAAA,IAAI,EAAE,eAAe;AACrBwB,EAAAA,QAAQ,EAAE,YAAY;AACtB,EAAA,SAAA,EAASvB,aAAa;AACtBwB,EAAAA,KAAK,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,gBAAgB,CAAC;AAC1DC,EAAAA,SAAS,EAAE,IAAI;AACfxB,EAAAA,KAAK,EAAE;AACHyB,IAAAA,MAAM,EAAE;AACJvB,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;KACZ;AACDmB,IAAAA,aAAa,EAAE;AACXxB,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;KACZ;AACDoB,IAAAA,KAAK,EAAE;AACHzB,MAAAA,IAAI,EAAEG,KAAK;MACX,SAAS,EAAA;KACZ;AACDuB,IAAAA,OAAO,EAAE;AACL1B,MAAAA,IAAI,EAAEC,OAAO;MACb,SAAS,EAAA;KACZ;AACD0B,IAAAA,KAAK,EAAE;AACH3B,MAAAA,IAAI,EAAEU,MAAM;MACZ,SAAS,EAAA;KACZ;AACDkB,IAAAA,SAAS,EAAE;AACP5B,MAAAA,IAAI,EAAEM,MAAM;MACZ,SAAS,EAAA;KACZ;AACDuB,IAAAA,cAAc,EAAE;AACZ7B,MAAAA,IAAI,EAAEM,MAAM;MACZ,SAAS,EAAA;KACZ;AACDM,IAAAA,QAAQ,EAAE;AACNZ,MAAAA,IAAI,EAAEU,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDoB,EAAAA,OAAO,EAAE;AACLC,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACC,aAAa,EAAE;MACrB,OAAAC,EAAAA,CAAAA,MAAA,CAAU,IAAI,CAACV,MAAM,OAAAU,MAAA,CAAID,aAAa,CAACE,GAAG,CAAA;KAC7C;AACDC,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACH,aAAa,EAAE;AACtB,MAAA,OAAO,IAAI,CAACD,SAAS,CAACC,aAAa,CAAC;KACvC;IACDI,WAAW,EAAA,SAAXA,WAAWA,CAACJ,aAAa,EAAEpC,IAAI,EAAEyC,MAAM,EAAE;AACrC,MAAA,OAAOL,aAAY,IAAKA,aAAa,CAACM,IAAG,GAAIC,OAAO,CAACP,aAAa,CAACM,IAAI,CAAC1C,IAAI,CAAC,EAAEyC,MAAM,IAAIG,SAAS;KACrG;AACDC,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACT,aAAa,EAAE;AACxB,MAAA,OAAO,IAAI,CAACI,WAAW,CAACJ,aAAa,EAAE,OAAO,CAAC;KAClD;AACDU,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACV,aAAa,EAAE;MAC1B,OAAAC,EAAAA,CAAAA,MAAA,CAAU,IAAI,CAACV,MAAM,OAAAU,MAAA,CAAID,aAAa,CAACE,GAAG,EAAA,QAAA,CAAA;KAC7C;IACDS,YAAY,EAAA,SAAZA,YAAYA,CAACX,aAAa,EAAEY,KAAK,EAAEV,GAAG,EAAE;AACpC,MAAA,OAAO,IAAI,CAACW,GAAG,CAACX,GAAG,EAAE;AACjBY,QAAAA,OAAO,EAAE;UACLR,IAAI,EAAEN,aAAa,CAACM,IAAI;AACxBM,UAAAA,KAAK,EAALA,KAAK;AACLG,UAAAA,MAAM,EAAE,IAAI,CAACC,YAAY,CAAChB,aAAa,CAAC;AACxCiB,UAAAA,OAAO,EAAE,IAAI,CAACC,aAAa,CAAClB,aAAa,CAAC;AAC1CrB,UAAAA,QAAQ,EAAE,IAAI,CAACwC,cAAc,CAACnB,aAAa;AAC/C;AACJ,OAAC,CAAC;KACL;AACDgB,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAAChB,aAAa,EAAE;AACxB,MAAA,OAAO,IAAI,CAACH,cAAc,CAACuB,IAAI,CAAC,UAACC,IAAI,EAAA;AAAA,QAAA,OAAKA,IAAI,CAACnB,GAAI,KAAIF,aAAa,CAACE,GAAG;OAAC,CAAA;KAC5E;AACDoB,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACtB,aAAa,EAAE;MACzB,OAAO,IAAI,CAACI,WAAW,CAACJ,aAAa,EAAE,SAAS,CAAE,KAAI,KAAK;KAC9D;AACDmB,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACnB,aAAa,EAAE;AAC1B,MAAA,OAAO,IAAI,CAACI,WAAW,CAACJ,aAAa,EAAE,UAAU,CAAC;KACrD;AACDkB,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAAClB,aAAa,EAAE;MACzB,OAAO,IAAI,CAACR,aAAY,KAAM,IAAI,CAACO,SAAS,CAACC,aAAa,CAAC;KAC9D;AACDuB,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACvB,aAAa,EAAE;AACvB,MAAA,OAAOwB,UAAU,CAACxB,aAAa,CAACP,KAAK,CAAC;KACzC;IACDgC,OAAO,EAAA,SAAPA,OAAOA,GAAG;MACNC,cAAc,CAAC,IAAI,CAACpC,SAAS,EAAE,IAAI,CAACK,KAAK,CAAC;KAC7C;AACDgC,IAAAA,WAAW,WAAXA,WAAWA,CAACC,KAAK,EAAE5B,aAAa,EAAE;AAC9B,MAAA,IAAI,CAACI,WAAW,CAACJ,aAAa,EAAE,SAAS,EAAE;AAAE6B,QAAAA,aAAa,EAAED,KAAK;QAAEtB,IAAI,EAAEN,aAAa,CAACM;AAAK,OAAC,CAAC;AAC9F,MAAA,IAAI,CAACwB,KAAK,CAAC,YAAY,EAAE;AAAED,QAAAA,aAAa,EAAED,KAAK;AAAE5B,QAAAA,aAAa,EAAbA,aAAa;AAAE+B,QAAAA,OAAO,EAAE;AAAK,OAAC,CAAC;KACnF;AACDC,IAAAA,gBAAgB,WAAhBA,gBAAgBA,CAACJ,KAAK,EAAE5B,aAAa,EAAE;AACnC,MAAA,IAAI,CAAC8B,KAAK,CAAC,iBAAiB,EAAE;AAAED,QAAAA,aAAa,EAAED,KAAK;AAAE5B,QAAAA,aAAY,EAAZA;AAAc,OAAC,CAAC;KACzE;AACDiC,IAAAA,eAAe,WAAfA,eAAeA,CAACL,KAAK,EAAE5B,aAAa,EAAE;AAClC,MAAA,IAAI,CAAC8B,KAAK,CAAC,gBAAgB,EAAE;AAAED,QAAAA,aAAa,EAAED,KAAK;AAAE5B,QAAAA,aAAY,EAAZA;AAAc,OAAC,CAAC;KACxE;IACDkC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AAAA,MAAA,IAAAC,KAAA,GAAA,IAAA;AACb,MAAA,OAAO,IAAI,CAAC1C,KAAK,CAAC2C,MAAM,CAAC,UAACpC,aAAa,EAAA;AAAA,QAAA,OAAKmC,KAAI,CAACb,aAAa,CAACtB,aAAa,CAAA,IAAK,CAACmC,KAAI,CAAC/B,WAAW,CAACJ,aAAa,EAAE,WAAW,CAAC;AAAA,OAAA,CAAC,CAACqC,MAAM;KACzI;AACDC,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAAC1B,KAAK,EAAE;AAAA,MAAA,IAAA2B,MAAA,GAAA,IAAA;AACnB,MAAA,OAAO3B,QAAQ,IAAI,CAACnB,KAAK,CAAC+C,KAAK,CAAC,CAAC,EAAE5B,KAAK,CAAC,CAACwB,MAAM,CAAC,UAACpC,aAAa,EAAA;AAAA,QAAA,OAAKuC,MAAI,CAACjB,aAAa,CAACtB,aAAa,CAAE,IAAGuC,MAAI,CAACnC,WAAW,CAACJ,aAAa,EAAE,WAAW,CAAC;OAAC,CAAA,CAACqC,MAAK,GAAI,CAAC;KACpK;AACDI,IAAAA,gBAAgB,WAAhBA,gBAAgBA,CAACzC,aAAa,EAAEY,KAAK,EAAE;MACnC,OAAO;QACH8B,MAAM,EAAEC,UAAU,CACd;AACI,UAAA,OAAA,EAAO,IAAI,CAACC,EAAE,CAAC,UAAU,CAAC;AAC1BhE,UAAAA,QAAQ,EAAE;SACb,EACD,IAAI,CAAC+B,YAAY,CAACX,aAAa,EAAEY,KAAK,EAAE,UAAU,CACtD,CAAC;QACDiC,IAAI,EAAEF,UAAU,CACZ;AACI,UAAA,OAAA,EAAO,CAAC,IAAI,CAACC,EAAE,CAAC,UAAU,CAAC,EAAE,IAAI,CAACxC,WAAW,CAACJ,aAAa,EAAE,MAAM,CAAC;SACvE,EACD,IAAI,CAACW,YAAY,CAACX,aAAa,EAAEY,KAAK,EAAE,UAAU,CACtD,CAAC;QACDkC,KAAK,EAAEH,UAAU,CACb;AACI,UAAA,OAAA,EAAO,IAAI,CAACC,EAAE,CAAC,WAAW;SAC7B,EACD,IAAI,CAACjC,YAAY,CAACX,aAAa,EAAEY,KAAK,EAAE,WAAW,CACvD,CAAC;QACDmC,WAAW,EAAEJ,UAAU,CACnB;AACI,UAAA,OAAA,EAAO,IAAI,CAACC,EAAE,CAAC,aAAa;SAC/B,EACD,IAAI,CAACjC,YAAY,CAACX,aAAa,EAAEY,KAAK,EAAE,aAAa,CACzD;OACH;KACJ;AACDoC,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACC,EAAE,EAAE;MACb,IAAI,CAAC3D,SAAU,GAAE2D,EAAE;AACvB;GACH;AACDC,EAAAA,UAAU,EAAE;AACRC,IAAAA,cAAc,EAAEA;GACnB;AACDC,EAAAA,UAAU,EAAE;AACRC,IAAAA,MAAM,EAAEC;AACZ;AACJ,CAAC;;;;;;;;;;;;EChOG,OAAAC,SAAA,EAAA,EAAAC,WAAA,CAyEYC,YAzEZC,UAyEY,CAAA;AAzEA9F,IAAAA,IAAI,EAAC,cAAa;IAAG6D,OAAK,EAAEkC,QAAO,CAAAlC;KAAUmC,IAAG,CAAA/C,GAAA,CAAA,iBAAA,CAAA,CAAA,EAAA;uBACxD,YAAA;MAAA,OAuEI,CAvEM,CAAAgD,MAAA,CAAAlE,KAAI,gBAAiBkE,MAAO,CAAAnE,OAAA,kBAAtCoE,kBAuEI,CAAA,IAAA,EAAA;;QAvEqCC,GAAG,EAAEJ,QAAY,CAAAX,YAAA;QAAGpE,QAAQ,EAAEiF,MAAQ,CAAAjF;WAC3E2E,SAAA,CAAA,IAAA,CAAA,EAAAO,kBAAA,CAqEUE,QArEiC,EAAA,IAAA,EAAAC,UAAA,CAAAJ,MAAA,CAAApE,KAAK,EAA9B,UAAAO,aAAa,EAAEY,KAAK,EAAA;;AAAkBV,UAAAA,GAAA,EAAAyD,QAAA,CAAAxD,UAAU,CAACH,aAAa;YAElE2D,QAAA,CAAArC,aAAa,CAACtB,aAAa,CAAM,IAAA,CAAA2D,QAAA,CAAAvD,WAAW,CAACJ,aAAa,EAAA,WAAA,CAAA,IADpEuD,SAAA,EAAA,EAAAO,kBAAA,CA2DI,MA3DJJ,UA2DI,CAAA;;AAzDCQ,UAAAA,EAAE,EAAEP,QAAS,CAAA5D,SAAA,CAACC,aAAa,CAAA;UAC3BjB,KAAK,EAAE4E,QAAW,CAAAvD,WAAA,CAACJ,aAAa,EAAA,OAAA,CAAA;AAChC,UAAA,OAAA,GAAQ4D,IAAE,CAAAhB,EAAA,CAAA,MAAA,EAAA;AAAW5C,YAAAA,eAAAA;WAAkB,CAAA,EAAA2D,QAAA,CAAAvD,WAAW,CAACJ,aAAa,EAAA,OAAA,CAAA,CAAA;AACjEmE,UAAAA,IAAI,EAAC,UAAS;AACb,UAAA,YAAU,EAAER,QAAY,CAAAlD,YAAA,CAACT,aAAa,CAAA;UACtC,eAAa,EAAE2D,QAAA,CAAAxC,cAAc,CAACnB,aAAa,KAAKQ,SAAS;AACzD,UAAA,eAAa,EAAEmD,oBAAW,CAAC3D,aAAa,IAAI2D,QAAY,CAAA3C,YAAA,CAAChB,aAAa,CAAA,GAAIQ,SAAS;UACnF,eAAa,EAAEmD,oBAAW,CAAC3D,aAAa,MAAM2D,QAAW,CAAAvD,WAAA,CAACJ,aAAa,EAAA,IAAA,CAAA,GAAA,MAAA,GAAmBQ,SAAS;AACnG,UAAA,YAAU,EAAEqD,MAAI,CAAAlE,KAAA,GAAA,CAAA;AAChB,UAAA,cAAY,EAAEgE,QAAc,CAAAzB,cAAA,EAAA;AAC5B,UAAA,eAAa,EAAEyB,QAAe,CAAArB,eAAA,CAAC1B,KAAK;;;WAC7B+C,QAAY,CAAAhD,YAAA,CAACX,aAAa,EAAEY,KAAK,EAAA,MAAA,CAAA,EAAA;AACxC,UAAA,eAAa,EAAE+C,QAAY,CAAA3C,YAAA,CAAChB,aAAa,CAAA;AACzC,UAAA,gBAAc,EAAE2D,QAAa,CAAAzC,aAAA,CAAClB,aAAa,CAAA;AAC3C,UAAA,iBAAe,EAAE2D,QAAc,CAAAxC,cAAA,CAACnB,aAAa;aAE9CoE,kBAAA,CAmBK,OAnBLV,UAmBK,CAAA;AAlBA,UAAA,OAAA,EAAOE,IAAE,CAAAhB,EAAA,CAAA,aAAA,CAAA;AACTyB,UAAAA,OAAK,EAAE,SAAPA,OAAKA,CAAEC,MAAA,EAAA;AAAA,YAAA,OAAAX,QAAA,CAAAhC,WAAW,CAAC2C,MAAM,EAAEtE,aAAa,CAAA;WAAA;AACxCuE,UAAAA,YAAU,EAAE,SAAZA,YAAUA,CAAED,MAAA,EAAA;AAAA,YAAA,OAAAX,QAAA,CAAA3B,gBAAgB,CAACsC,MAAM,EAAEtE,aAAa,CAAA;WAAA;AAClDwE,UAAAA,WAAS,EAAE,SAAXA,WAASA,CAAEF,MAAA,EAAA;AAAA,YAAA,OAAAX,QAAA,CAAA1B,eAAe,CAACqC,MAAM,EAAEtE,aAAa,CAAA;AAAA;;;WACzC2D,QAAY,CAAAhD,YAAA,CAACX,aAAa,EAAEY,KAAK,EAAA,aAAA,CAAA,CAAA,EAAA,CAExB,CAAAiD,MAAA,CAAAjE,SAAS,CAACU,IAAI,GAC3BmE,cAAA,EAAAlB,SAAA,EAAA,EAAAO,kBAAA,CAQG,KARHJ,UAQG,CAAA;;UARUgB,IAAI,EAAEf,QAAW,CAAAvD,WAAA,CAACJ,aAAa,EAAA,KAAA,CAAA;AAAW,UAAA,OAAA,EAAO4D,IAAE,CAAAhB,EAAA,CAAA,UAAA,CAAA;UAAe+B,MAAM,EAAEhB,QAAW,CAAAvD,WAAA,CAACJ,aAAa,EAAA,QAAA,CAAA;AAAapB,UAAAA,QAAQ,EAAC;;;WAAa+E,QAAY,CAAAhD,YAAA,CAACX,aAAa,EAAEY,KAAK,EAAA,UAAA,CAAA,CAAA,EAAA,CAC/JiD,MAAA,CAAAjE,SAAS,CAACgF,QAAQ,iBAAnCpB,WAAkH,CAAAqB,uBAAA,CAAxEhB,MAAS,CAAAjE,SAAA,CAACgF,QAAQ,CAAA,EAAA;;UAAGtE,IAAI,EAAEN,aAAa,CAACM,IAAI;AAAG,UAAA,OAAA,iBAAOsD,IAAE,CAAAhB,EAAA,CAAA,UAAA,CAAA;0CAClFe,QAAA,CAAAvD,WAAW,CAACJ,aAAa,EAAA,MAAA,CAAA,IAA1CuD,SAAA,EAAA,EAAAO,kBAAA,CAA4K,QAA5KJ,UAA4K,CAAA;;AAAtH,UAAA,OAAA,EAAQ,CAAAE,IAAA,CAAAhB,EAAE,CAAc,UAAA,CAAA,EAAAe,QAAA,CAAAvD,WAAW,CAACJ,aAAa,EAAA,MAAA,CAAA;;;WAAoB2D,QAAY,CAAAhD,YAAA,CAACX,aAAa,EAAEY,KAAK,EAAA,UAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,kCAC5JwD,kBAAA,CAAmK,QAAnKV,UAAmK,CAAA;AAA5JQ,UAAAA,EAAE,EAAEP,QAAc,CAAAjD,cAAA,CAACV,aAAa,CAAA;AAAI,UAAA,OAAA,EAAO4D,IAAE,CAAAhB,EAAA,CAAA,WAAA;AAAuB,SAAA,EAAA;AAAAkC,UAAAA,OAAA,EAAA;SAAA,EAAAnB,QAAA,CAAAhD,YAAY,CAACX,aAAa,EAAEY,KAAK,EAAmB,WAAA,CAAA,CAAA,EAAAmE,eAAA,CAAApB,QAAA,CAAAlD,YAAY,CAACT,aAAa,CAAA,CAAA,EAAA,EAAA,EAAAgF,UAAA,CAAA,EACzIrB,QAAA,CAAAvD,WAAW,CAACJ,aAAa,EAAA,OAAA,CAAA,iBAAzC8D,kBAGU,CAAAE,QAAA,EAAA;AAAA9D,UAAAA,GAAA,EAAA;SAAA,EAAA,CAFW2D,MAAA,CAAAjE,SAAS,CAACmD,WAAW,IAAtCQ,SAAA,EAAA,EAAAC,WAAA,CAAiMqB,uBAApJ,CAAAhB,MAAA,CAAAjE,SAAS,CAACmD,WAAW,GAAlEW,UAAiM,CAAA;;AAA5H,UAAA,OAAA,EAAOE,IAAE,CAAAhB,EAAA,CAAA,aAAA,CAAA;AAAkB7B,UAAAA,MAAM,EAAE4C,QAAY,CAAA3C,YAAA,CAAChB,aAAa;;;WAAW2D,QAAY,CAAAhD,YAAA,CAACX,aAAa,EAAEY,KAAK,EAAA,aAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,CAAA,CAAA,KAC9K2C,SAAA,EAAA,EAAAC,WAAA,CAA8GyB,2BAA9GvB,UAA8G,CAAA;;AAAtF,UAAA,OAAA,EAAOE,IAAE,CAAAhB,EAAA,CAAA,aAAA;;;WAAyBe,QAAY,CAAAhD,YAAA,CAACX,aAAa,EAAEY,KAAK,EAAA,aAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA,kGAIvG4C,WAAoN,CAAAqB,uBAAA,CAA7LhB,MAAS,CAAAjE,SAAA,CAACU,IAAI,CAAA,EAAA;;UAAGA,IAAI,EAAEN,aAAa,CAACM,IAAI;UAAG4E,UAAU,EAAEvB,QAAW,CAAAvD,WAAA,CAACJ,aAAa,EAAA,OAAA,CAAA;AAAa8C,UAAAA,KAAK,EAAEa,QAAY,CAAAlD,YAAA,CAACT,aAAa,CAAA;AAAIlC,UAAAA,KAAK,EAAE6F,QAAA,CAAAlB,gBAAgB,CAACzC,aAAa,EAAEY,KAAK;kFAGhM+C,QAAA,CAAArC,aAAa,CAACtB,aAAa,CAAK,IAAA2D,QAAA,CAAApC,WAAW,CAACvB,aAAa,CAAA,IADnEuD,SAAA,EAAA,EAAAC,WAAA,CAoBC2B,0BApBDzB,UAoBC,CAAA;;UAlBIQ,EAAE,EAAEP,QAAS,CAAA5D,SAAA,CAACC,aAAa,CAAA,GAAA,OAAA;AAC3B,UAAA,OAAA,EAAO4D,IAAE,CAAAhB,EAAA,CAAA,SAAA,CAAA;UACT7D,KAAK,EAAE6E,IAAE,CAAAwB,EAAA,CAAA,SAAA,EAAA,IAAA,EAAA;AAAoBpF,YAAAA,aAAY,EAAZA;AAAY,WAAA,CAAA;AACzC,UAAA,iBAAe,EAAE2D,QAAc,CAAAjD,cAAA,CAACV,aAAa,CAAA;AAC9CmE,UAAAA,IAAI,EAAC,MAAK;UACT5E,MAAM,EAAEsE,MAAM,CAAAtE,MAAA;UACdC,aAAa,EAAEqE,MAAa,CAAArE,aAAA;UAC5BC,KAAK,EAAEO,aAAa,CAACP,KAAK;UAC1BG,SAAS,EAAEiE,MAAS,CAAAjE,SAAA;UACpBC,cAAc,EAAEgE,MAAc,CAAAhE,cAAA;AAC9BF,UAAAA,KAAK,EAAEkE,MAAI,CAAAlE,KAAA,GAAA,CAAA;AACXD,UAAAA,OAAO,EAAEiE,QAAY,CAAA3C,YAAA,CAAChB,aAAa,CAAK,IAAA2D,QAAA,CAAApC,WAAW,CAACvB,aAAa,CAAA;UACjEqF,EAAE,EAAEzB,IAAE,CAAAyB,EAAA;UACNC,QAAQ,EAAE1B,IAAQ,CAAA0B,QAAA;AAClB3D,UAAAA,WAAU,EAAA4D,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAjB,MAAA,EAAA;AAAA,YAAA,OAAEV,IAAK,CAAA9B,KAAA,CAAA,YAAA,EAAewC,MAAM,CAAA;AAAA,WAAA,CAAA;AACtCkB,UAAAA,gBAAe,EAAAD,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAjB,MAAA,EAAA;AAAA,YAAA,OAAEV,IAAK,CAAA9B,KAAA,CAAA,iBAAA,EAAoBwC,MAAM,CAAA;AAAA,WAAA,CAAA;AAChDmB,UAAAA,eAAc,EAAAF,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAjB,MAAA,EAAA;AAAA,YAAA,OAAEV,IAAK,CAAA9B,KAAA,CAAA,gBAAA,EAAmBwC,MAAM,CAAA;WAAA;;;WACvCV,IAAG,CAAA/C,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,eAAA,EAAA,OAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,UAAA,CAAA,CAAA,oFAIT8C,QAAA,CAAArC,aAAa,CAACtB,aAAa,CAAK,IAAA2D,QAAA,CAAAvD,WAAW,CAACJ,aAAa,EAAA,WAAA,CAAA,IADnEuD,SAAA,EAAA,EAAAO,kBAAA,CAOK,MAPLJ,UAOK,CAAA;;AALAQ,UAAAA,EAAE,EAAEP,QAAS,CAAA5D,SAAA,CAACC,aAAa,CAAA;UAC3BjB,KAAK,EAAE4E,QAAW,CAAAvD,WAAA,CAACJ,aAAa,EAAA,OAAA,CAAA;AAChC,UAAA,OAAA,EAAQ,CAAA4D,IAAA,CAAAhB,EAAE,CAAe,WAAA,CAAA,EAAAe,QAAA,CAAAvD,WAAW,CAACJ,aAAa,EAAA,OAAA,CAAA,CAAA;AACnDmE,UAAAA,IAAI,EAAC;;;WACGP,IAAG,CAAA/C,GAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA6E,UAAA,CAAA;;;;;;;;;ACjB/B,aAAe;AACX9H,EAAAA,IAAI,EAAE,YAAY;AAClB,EAAA,SAAA,EAAS+H,QAAc;AACvBC,EAAAA,YAAY,EAAE,KAAK;AACnBvG,EAAAA,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC;AACtEwG,EAAAA,oBAAoB,EAAE,IAAI;AAC1BC,EAAAA,kBAAkB,EAAE,IAAI;AACxBC,EAAAA,aAAa,EAAE,IAAI;AACnBC,EAAAA,cAAc,EAAE,IAAI;AACpBrB,EAAAA,MAAM,EAAE,IAAI;AACZrF,EAAAA,SAAS,EAAE,IAAI;AACf2G,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,aAAa,EAAE,IAAI;AACnBC,EAAAA,WAAW,EAAE,IAAI;EACjBC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHnF,MAAAA,OAAO,EAAE,KAAK;AACdoF,MAAAA,eAAe,EAAE;QAAEzF,KAAK,EAAE,EAAE;AAAEjB,QAAAA,KAAK,EAAE,CAAC;AAAE2G,QAAAA,SAAS,EAAE;OAAI;AACvDzG,MAAAA,cAAc,EAAE,EAAE;AAClBH,MAAAA,OAAO,EAAE,CAAC,IAAI,CAAC3B,KAAK;AACpBwI,MAAAA,cAAc,EAAE,KAAK;AACrBC,MAAAA,KAAK,EAAE,KAAK;AACZC,MAAAA,KAAK,EAAE,IAAI;AACXC,MAAAA,YAAY,EAAE;KACjB;GACJ;AACDC,EAAAA,KAAK,EAAE;AACH9G,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC+G,OAAO,EAAE;AACpB,MAAA,IAAI,CAAC,IAAI,CAAC7I,KAAK,EAAE;AACb,QAAA,IAAIyD,UAAU,CAACoF,OAAO,CAAC,EAAE;UACrB,IAAI,CAACC,wBAAwB,EAAE;UAC/B,IAAI,CAACC,kBAAkB,EAAE;AAC7B,SAAE,MAAK;UACH,IAAI,CAACC,0BAA0B,EAAE;UACjC,IAAI,CAACC,oBAAoB,EAAE;AAC/B;AACJ;AACJ;GACH;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACC,sBAAsB,EAAE;GAChC;EACDC,aAAa,EAAA,SAAbA,aAAaA,GAAG;IACZ,IAAI,CAACJ,0BAA0B,EAAE;IACjC,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACI,wBAAwB,EAAE;IAE/B,IAAI,IAAI,CAACrB,aAAa,EAAE;AACpB,MAAA,IAAI,CAACA,aAAa,CAACsB,OAAO,EAAE;MAC5B,IAAI,CAACtB,gBAAgB,IAAI;AAC7B;AAEA,IAAA,IAAI,IAAI,CAACzG,SAAQ,IAAK,IAAI,CAACd,UAAU,EAAE;AACnC8I,MAAAA,MAAM,CAACC,KAAK,CAAC,IAAI,CAACjI,SAAS,CAAC;AAChC;IAEA,IAAI,CAACqF,MAAO,GAAE,IAAI;IAClB,IAAI,CAACrF,SAAQ,GAAI,IAAI;GACxB;AACDQ,EAAAA,OAAO,EAAE;AACLM,IAAAA,WAAW,WAAXA,WAAWA,CAACE,IAAI,EAAE1C,IAAI,EAAE;MACpB,OAAO0C,IAAK,GAAEC,OAAO,CAACD,IAAI,CAAC1C,IAAI,CAAC,CAAA,GAAI4C,SAAS;KAChD;AACDC,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACH,IAAI,EAAE;AACf,MAAA,OAAO,IAAI,CAACF,WAAW,CAACE,IAAI,EAAE,OAAO,CAAC;KACzC;AACDa,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACb,IAAI,EAAE;AACjB,MAAA,OAAO,IAAI,CAACF,WAAW,CAACE,IAAI,EAAE,UAAU,CAAC;KAC5C;AACDgB,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAAChB,IAAI,EAAE;MAChB,OAAO,IAAI,CAACF,WAAW,CAACE,IAAI,EAAE,SAAS,CAAA,KAAM,KAAK;KACrD;AACDiB,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACjB,IAAI,EAAE;MACd,OAAOkB,UAAU,CAAC,IAAI,CAACpB,WAAW,CAACE,IAAI,EAAE,OAAO,CAAC,CAAC;KACrD;AACDkH,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAAClH,IAAI,EAAE;AAClB,MAAA,OAAO,IAAI,CAACF,WAAW,CAACE,IAAI,EAAE,WAAW,CAAC;KAC7C;AACDmH,IAAAA,sBAAsB,EAAtBA,SAAAA,sBAAsBA,CAACzH,aAAa,EAAE;MAClC,OAAOA,aAAc,GAAE,IAAI,CAACS,YAAY,CAACT,aAAa,CAACM,IAAI,IAAIE,SAAS;KAC3E;AACDkH,IAAAA,qBAAqB,EAArBA,SAAAA,qBAAqBA,CAAC1H,aAAa,EAAE;AACjC,MAAA,OAAOA,aAAY,IAAKwB,UAAU,CAACxB,aAAa,CAACP,KAAK,CAAC;KAC1D;AACDkI,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAAC/F,KAAK,EAAE;AACV,MAAA,IAAI,CAAClC,OAAM,GAAI,IAAI,CAACkI,IAAI,CAAChG,KAAK,EAAE,IAAI,CAAA,GAAI,IAAI,CAACiG,IAAI,CAACjG,KAAK,CAAC;KAC3D;AACDiG,IAAAA,IAAI,WAAJA,IAAIA,CAACjG,KAAK,EAAEG,OAAO,EAAE;MACjB,IAAI,IAAI,CAAChE,KAAK,EAAE;AACZ,QAAA,IAAI,CAAC+D,KAAK,CAAC,aAAa,CAAC;QACzB,IAAI,CAACpC,OAAQ,GAAE,IAAI;QACnB,IAAI,CAACiF,SAAS,IAAI,CAACA,MAAO,IAAG/C,KAAK,CAACkG,aAAa;AAChD,QAAA,IAAI,CAACC,aAAc,GAAEnG,KAAK,CAACmG,aAAc,IAAG,IAAI;AACpD;AAEAhG,MAAAA,OAAQ,IAAGiG,KAAK,CAAC,IAAI,CAAC/B,OAAO,CAAC;KACjC;AACD2B,IAAAA,IAAI,WAAJA,IAAIA,CAAChG,KAAK,EAAEG,OAAO,EAAE;MACjB,IAAI,IAAI,CAAChE,KAAK,EAAE;AACZ,QAAA,IAAI,CAAC+D,KAAK,CAAC,aAAa,CAAC;QACzB,IAAI,CAACpC,UAAU,KAAK;AACxB;MAEA,IAAI,CAACG,cAAe,GAAE,EAAE;MACxB,IAAI,CAACwG,kBAAkB;QAAEzF,KAAK,EAAE,EAAE;AAAEjB,QAAAA,KAAK,EAAE,CAAC;AAAE2G,QAAAA,SAAS,EAAE;OAAI;AAE7DvE,MAAAA,OAAQ,IAAGiG,KAAK,CAAC,IAAI,CAACD,aAAc,IAAG,IAAI,CAACpD,MAAK,IAAK,IAAI,CAACsB,OAAO,CAAC;MACnE,IAAI,CAACO,KAAI,GAAI,KAAK;KACrB;AACDyB,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACrG,KAAK,EAAE;MACX,IAAI,CAACX,OAAQ,GAAE,IAAI;AAEnB,MAAA,IAAI,CAAC,IAAI,CAAClD,KAAK,EAAE;AACb,QAAA,IAAI,CAACsI,eAAc,GAAI,IAAI,CAACA,eAAe,CAACzF,KAAI,KAAM,EAAC,GAAI,IAAI,CAACyF,eAAc,GAAI;AAAEzF,UAAAA,KAAK,EAAE,IAAI,CAACsH,yBAAyB,EAAE;AAAEvI,UAAAA,KAAK,EAAE,CAAC;AAAE2G,UAAAA,SAAS,EAAE;SAAI;AAC1J;AAEA,MAAA,IAAI,CAACxE,KAAK,CAAC,OAAO,EAAEF,KAAK,CAAC;KAC7B;AACDuG,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACvG,KAAK,EAAE;MACV,IAAI,CAACX,UAAU,KAAK;MACpB,IAAI,CAACoF,kBAAkB;QAAEzF,KAAK,EAAE,EAAE;AAAEjB,QAAAA,KAAK,EAAE,CAAC;AAAE2G,QAAAA,SAAS,EAAE;OAAI;MAC7D,IAAI,CAACH,WAAY,GAAE,EAAE;MACrB,IAAI,CAACK,KAAI,GAAI,KAAK;AAClB,MAAA,IAAI,CAAC1E,KAAK,CAAC,MAAM,EAAEF,KAAK,CAAC;KAC5B;AACDwG,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACxG,KAAK,EAAE;MACb,IAAI,IAAI,CAACjD,QAAQ,EAAE;QACfiD,KAAK,CAACyG,cAAc,EAAE;AAEtB,QAAA;AACJ;MAEA,IAAMC,UAAU1G,KAAK,CAAC0G,WAAW1G,KAAK,CAAC2G,OAAO;MAE9C,QAAQ3G,KAAK,CAAC4G,IAAI;AACd,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACC,cAAc,CAAC7G,KAAK,CAAC;AAC1B,UAAA;AAEJ,QAAA,KAAK,SAAS;AACV,UAAA,IAAI,CAAC8G,YAAY,CAAC9G,KAAK,CAAC;AACxB,UAAA;AAEJ,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAAC+G,cAAc,CAAC/G,KAAK,CAAC;AAC1B,UAAA;AAEJ,QAAA,KAAK,YAAY;AACb,UAAA,IAAI,CAACgH,eAAe,CAAChH,KAAK,CAAC;AAC3B,UAAA;AAEJ,QAAA,KAAK,MAAM;AACP,UAAA,IAAI,CAACiH,SAAS,CAACjH,KAAK,CAAC;AACrB,UAAA;AAEJ,QAAA,KAAK,KAAK;AACN,UAAA,IAAI,CAACkH,QAAQ,CAAClH,KAAK,CAAC;AACpB,UAAA;AAEJ,QAAA,KAAK,OAAO;AACR,UAAA,IAAI,CAACmH,UAAU,CAACnH,KAAK,CAAC;AACtB,UAAA;AAEJ,QAAA,KAAK,OAAO;AACZ,QAAA,KAAK,aAAa;AACd,UAAA,IAAI,CAACoH,UAAU,CAACpH,KAAK,CAAC;AACtB,UAAA;AAEJ,QAAA,KAAK,QAAQ;AACT,UAAA,IAAI,CAACqH,WAAW,CAACrH,KAAK,CAAC;AACvB,UAAA;AAEJ,QAAA,KAAK,KAAK;AACN,UAAA,IAAI,CAACsH,QAAQ,CAACtH,KAAK,CAAC;AACpB,UAAA;AAEJ,QAAA,KAAK,UAAU;AACf,QAAA,KAAK,QAAQ;AACb,QAAA,KAAK,WAAW;AAChB,QAAA,KAAK,WAAW;AAChB,QAAA,KAAK,YAAY;AACb;AACA,UAAA;AAEJ,QAAA;UACI,IAAI,CAAC0G,WAAWa,oBAAoB,CAACvH,KAAK,CAAC1B,GAAG,CAAC,EAAE;YAC7C,IAAI,CAACkJ,WAAW,CAACxH,KAAK,EAAEA,KAAK,CAAC1B,GAAG,CAAC;AACtC;AAEA,UAAA;AACR;KACH;AACDmJ,IAAAA,YAAY,WAAZA,YAAYA,CAACzH,KAAK,EAAE5D,IAAI,EAAE;AACtB,MAAA,IAAQgC,aAAa,GAAc4B,KAAK,CAAhC5B,aAAa;QAAE+B,OAAQ,GAAIH,KAAK,CAAjBG,OAAQ;AAE/B,MAAA,IAAIuH,OAAO,CAACtJ,aAAa,CAAC,EAAE;AAE5B,MAAA,IAAQY,KAAK,GAAmCZ,aAAa,CAArDY,KAAK;QAAEV,GAAG,GAA8BF,aAAa,CAA9CE,GAAG;QAAEP,KAAK,GAAuBK,aAAa,CAAzCL,KAAK;QAAE2G,SAAS,GAAYtG,aAAa,CAAlCsG,SAAS;QAAE7G,KAAI,GAAMO,aAAa,CAAvBP,KAAI;AAC1C,MAAA,IAAM8J,OAAQ,GAAE/H,UAAU,CAAC/B,KAAK,CAAC;MAEjC,IAAMI,cAAe,GAAE,IAAI,CAACA,cAAc,CAACuC,MAAM,CAAC,UAACoH,CAAC,EAAA;QAAA,OAAKA,CAAC,CAAClD,cAAcA,aAAakD,CAAC,CAAClD,SAAU,KAAIpG,GAAG;OAAC,CAAA;AAE1G,MAAA,IAAIqJ,OAAO,EAAE;AACT1J,QAAAA,cAAc,CAAC4J,IAAI,CAACzJ,aAAa,CAAC;QAClC,IAAI,CAACuG,iBAAiB,IAAI;AAC9B;MAEA,IAAI,CAACF,kBAAkB;AAAEzF,QAAAA,KAAK,EAALA,KAAK;AAAEjB,QAAAA,KAAK,EAALA,KAAK;AAAE2G,QAAAA,WAAAA;OAAW;AAElDiD,MAAAA,YAAY,IAAI,CAAC/C,KAAI,GAAI,IAAI,CAAC;AAC9BzE,MAAAA,OAAQ,IAAGiG,KAAK,CAAC,IAAI,CAAC/B,OAAO,CAAC;AAE9B,MAAA,IAAIjI,IAAK,KAAI,OAAQ,IAAG,IAAI,CAAC0I,YAAY,EAAE;AACvC,QAAA;AACJ;MAEA,IAAI,CAAC7G,cAAa,GAAIA,cAAc;KACvC;AACD6J,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC9H,KAAK,EAAE;AAClB+H,MAAAA,eAAe,CAACC,IAAI,CAAC,eAAe,EAAE;AAClC/H,QAAAA,aAAa,EAAED,KAAK;QACpB+C,MAAM,EAAE,IAAI,CAACA;AACjB,OAAC,CAAC;KACL;AACDhD,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACC,KAAK,EAAE;AACf,MAAA,IAAQC,aAAa,GAAoBD,KAAK,CAAtCC,aAAa;QAAE7B,gBAAkB4B,KAAK,CAAvB5B;AACvB,MAAA,IAAMuJ,OAAQ,GAAE,IAAI,CAAC7B,qBAAqB,CAAC1H,aAAa,CAAC;AACzD,MAAA,IAAM6J,IAAG,GAAIP,OAAO,CAACtJ,aAAa,CAAC8J,MAAM,CAAC;AAC1C,MAAA,IAAMC,QAAO,GAAI,IAAI,CAACC,UAAU,CAAChK,aAAa,CAAC;AAE/C,MAAA,IAAI+J,QAAQ,EAAE;AACV,QAAA,IAAQnJ,KAAK,GAA4BZ,aAAa,CAA9CY,KAAK;UAAEV,GAAG,GAAuBF,aAAa,CAAvCE,GAAG;UAAEP,KAAK,GAAgBK,aAAa,CAAlCL,KAAK;UAAE2G,SAAU,GAAItG,aAAa,CAA3BsG,SAAU;QAErC,IAAI,CAACzG,iBAAiB,IAAI,CAACA,cAAc,CAACuC,MAAM,CAAC,UAACoH,CAAC,EAAA;AAAA,UAAA,OAAKtJ,GAAI,KAAIsJ,CAAC,CAACtJ,OAAOA,GAAG,CAAC+J,UAAU,CAACT,CAAC,CAACtJ,GAAG,CAAC;SAAC,CAAA;QAC/F,IAAI,CAACmG,kBAAkB;AAAEzF,UAAAA,KAAK,EAALA,KAAK;AAAEjB,UAAAA,KAAK,EAALA,KAAK;AAAE2G,UAAAA,WAAAA;SAAW;AAElD,QAAA,IAAI,CAACE,KAAI,GAAI,CAACqD,IAAI;AAClB7B,QAAAA,KAAK,CAAC,IAAI,CAAC/B,OAAO,CAAC;AACvB,OAAE,MAAK;AACH,QAAA,IAAIsD,OAAO,EAAE;AACT,UAAA,IAAI,CAACF,YAAY,CAACzH,KAAK,CAAC;AAC5B,SAAE,MAAK;AACH,UAAA,IAAMsI,iBAAkB,GAAEL,OAAO7J,aAAY,GAAI,IAAI,CAACH,cAAc,CAACsK,IAAI,CAAC,UAACX,CAAC,EAAA;AAAA,YAAA,OAAKA,CAAC,CAAClD,SAAU,KAAI,EAAE;WAAC,CAAA;AAEpG,UAAA,IAAI,CAACsB,IAAI,CAAC/F,aAAa,CAAC;AACxB,UAAA,IAAI,CAACuI,sBAAsB,CAACvI,aAAa,EAAEqI,iBAAgB,GAAIA,iBAAiB,CAACtJ,QAAQ,EAAE,CAAC;AAE5FoH,UAAAA,KAAK,CAAC,IAAI,CAAC/B,OAAO,CAAC;AACvB;AACJ;KACH;AACDjE,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAACJ,KAAK,EAAE;MACpB,IAAI,IAAI,CAAC4E,KAAK,EAAE;AACZ,QAAA,IAAI,CAAC6C,YAAY,CAACzH,KAAK,EAAE,OAAO,CAAC;AACrC;KACH;AACDK,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAACL,KAAK,EAAE;MACnB,IAAI,IAAI,CAACX,OAAO,EAAE;QACd,IAAI,CAACmJ,sBAAsB,CAACxI,KAAK,EAAEA,KAAK,CAAC5B,aAAa,CAACY,KAAK,CAAC;AACjE;KACH;AACD6H,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC7G,KAAK,EAAE;MAClB,IAAMyI,SAAU,GAAE,IAAI,CAAChE,eAAe,CAACzF,KAAM,KAAI,EAAC,GAAI,IAAI,CAAC0J,iBAAiB,CAAC,IAAI,CAACjE,eAAe,CAACzF,KAAK,CAAE,GAAE,IAAI,CAACsH,yBAAyB,EAAE;AAE3I,MAAA,IAAI,CAACkC,sBAAsB,CAACxI,KAAK,EAAEyI,SAAS,CAAC;MAC7CzI,KAAK,CAACyG,cAAc,EAAE;KACzB;AACDK,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAAC9G,KAAK,EAAE;MAChB,IAAIA,KAAK,CAAC2I,MAAM,EAAE;QACd,IAAI,IAAI,CAAClE,eAAe,CAACzF,UAAU,EAAE,EAAE;UACnC,IAAMZ,aAAc,GAAE,IAAI,CAACwK,YAAY,CAAC,IAAI,CAACnE,eAAe,CAACzF,KAAK,CAAC;AACnE,UAAA,IAAM2I,OAAQ,GAAE,IAAI,CAAC7B,qBAAqB,CAAC1H,aAAa,CAAC;AAEzD,UAAA,CAACuJ,OAAQ,IAAG,IAAI,CAACF,YAAY,CAAC;AAAExH,YAAAA,aAAa,EAAED,KAAK;AAAE5B,YAAAA,aAAY,EAAZA;AAAc,WAAC,CAAC;AAC1E;QAEA,IAAI,CAACjC,KAAM,IAAG,IAAI,CAAC6J,IAAI,CAAChG,KAAK,EAAE,IAAI,CAAC;QACpCA,KAAK,CAACyG,cAAc,EAAE;AAC1B,OAAE,MAAK;QACH,IAAMgC,SAAU,GAAE,IAAI,CAAChE,eAAe,CAACzF,KAAI,KAAM,EAAG,GAAE,IAAI,CAAC6J,iBAAiB,CAAC,IAAI,CAACpE,eAAe,CAACzF,KAAK,CAAA,GAAI,IAAI,CAAC8J,wBAAwB,EAAE;AAE1I,QAAA,IAAI,CAACN,sBAAsB,CAACxI,KAAK,EAAEyI,SAAS,CAAC;QAC7CzI,KAAK,CAACyG,cAAc,EAAE;AAC1B;KACH;AACDM,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC/G,KAAK,EAAE;AAAA,MAAA,IAAAO,KAAA,GAAA,IAAA;MAClB,IAAMnC,aAAc,GAAE,IAAI,CAACwK,YAAY,CAAC,IAAI,CAACnE,eAAe,CAACzF,KAAK,CAAC;MACnE,IAAM+J,UAAW,GAAE,IAAI,CAAC9K,cAAc,CAACsK,IAAI,CAAC,UAACX,CAAC,EAAA;AAAA,QAAA,OAAKA,CAAC,CAACtJ,GAAE,KAAMF,aAAa,CAACsG,SAAS;OAAC,CAAA;AACrF,MAAA,IAAMuD,IAAG,GAAIP,OAAO,CAACtJ,aAAa,CAAC8J,MAAM,CAAC;MAE1C,IAAI,CAACD,IAAI,EAAE;QACP,IAAI,CAACxD,eAAc,GAAI;UAAEzF,KAAK,EAAE,EAAE;AAAE0F,UAAAA,SAAS,EAAEqE,UAAW,GAAEA,UAAU,CAACrE,SAAQ,GAAI;SAAI;QACvF,IAAI,CAACH,WAAY,GAAE,EAAE;AACrB,QAAA,IAAI,CAACsC,cAAc,CAAC7G,KAAK,CAAC;AAC9B;MAEA,IAAI,CAAC/B,cAAe,GAAE,IAAI,CAACA,cAAc,CAACuC,MAAM,CAAC,UAACoH,CAAC,EAAA;QAAA,OAAKA,CAAC,CAAClD,SAAQ,KAAMnE,KAAI,CAACkE,eAAe,CAACC,SAAS;OAAC,CAAA;MAEvG1E,KAAK,CAACyG,cAAc,EAAE;KACzB;AACDO,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAAChH,KAAK,EAAE;MACnB,IAAM5B,aAAc,GAAE,IAAI,CAACwK,YAAY,CAAC,IAAI,CAACnE,eAAe,CAACzF,KAAK,CAAC;AACnE,MAAA,IAAM2I,OAAQ,GAAE,IAAI,CAAC7B,qBAAqB,CAAC1H,aAAa,CAAC;AAEzD,MAAA,IAAIuJ,OAAO,EAAE;QACT,IAAI,CAACF,YAAY,CAAC;AAAExH,UAAAA,aAAa,EAAED,KAAK;AAAE5B,UAAAA,aAAc,EAAdA;AAAc,SAAC,CAAC;QAC1D,IAAI,CAACqG,eAAgB,GAAE;UAAEzF,KAAK,EAAE,EAAE;UAAE0F,SAAS,EAAEtG,aAAa,CAACE;SAAK;QAClE,IAAI,CAACiG,WAAY,GAAE,EAAE;AACrB,QAAA,IAAI,CAACsC,cAAc,CAAC7G,KAAK,CAAC;AAC9B;MAEAA,KAAK,CAACyG,cAAc,EAAE;KACzB;AACDQ,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACjH,KAAK,EAAE;MACb,IAAI,CAACwI,sBAAsB,CAACxI,KAAK,EAAE,IAAI,CAACgJ,kBAAkB,EAAE,CAAC;MAC7DhJ,KAAK,CAACyG,cAAc,EAAE;KACzB;AACDS,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAAClH,KAAK,EAAE;MACZ,IAAI,CAACwI,sBAAsB,CAACxI,KAAK,EAAE,IAAI,CAACiJ,iBAAiB,EAAE,CAAC;MAC5DjJ,KAAK,CAACyG,cAAc,EAAE;KACzB;AACDW,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACpH,KAAK,EAAE;MACd,IAAI,IAAI,CAACyE,eAAe,CAACzF,UAAU,EAAE,EAAE;AACnC,QAAA,IAAMkK,OAAQ,GAAEC,UAAU,CAAC,IAAI,CAAC9E,OAAO,EAAA,UAAA,CAAAhG,MAAA,CAAA,EAAA,CAAAA,MAAA,CAAe,IAAI,CAACT,aAAa,SAAM,CAAC;QAC/E,IAAMwL,aAAc,GAAEF,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,8BAA8B,CAAC;AAEpFE,QAAAA,aAAc,GAAEA,aAAa,CAACC,KAAK,EAAC,GAAIH,OAAM,IAAKA,OAAO,CAACG,KAAK,EAAE;AAElE,QAAA,IAAI,CAAC,IAAI,CAAClN,KAAK,EAAE;UACb,IAAMiC,aAAc,GAAE,IAAI,CAACwK,YAAY,CAAC,IAAI,CAACnE,eAAe,CAACzF,KAAK,CAAC;AACnE,UAAA,IAAM2I,OAAQ,GAAE,IAAI,CAAC7B,qBAAqB,CAAC1H,aAAa,CAAC;AAEzD,UAAA,CAACuJ,OAAM,KAAM,IAAI,CAAClD,eAAe,CAACzF,KAAI,GAAI,IAAI,CAACsH,yBAAyB,EAAE,CAAC;AAC/E;AACJ;MAEAtG,KAAK,CAACyG,cAAc,EAAE;KACzB;AACDU,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACnH,KAAK,EAAE;AACd,MAAA,IAAI,CAACoH,UAAU,CAACpH,KAAK,CAAC;KACzB;AACDqH,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACrH,KAAK,EAAE;MACf,IAAI,IAAI,CAAC7D,KAAM,IAAG,IAAI,CAACsI,eAAe,CAAC1G,KAAI,KAAM,CAAC,EAAE;AAChD,QAAA,IAAMuL,gBAAe,GAAI,IAAI,CAAC7E,eAAe;AAE7C,QAAA,IAAI,CAACuB,IAAI,CAAChG,KAAK,EAAE,KAAK,CAAC;QACvB,IAAI,CAACyE,eAAc,GAAI;AAAEzF,UAAAA,KAAK,EAAElC,MAAM,CAACwM,gBAAgB,CAAC5E,SAAS,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAAExL,UAAAA,KAAK,EAAE,CAAC;AAAE2G,UAAAA,SAAS,EAAE;SAAI;QAC3G,IAAI,CAACvI,KAAM,IAAGiK,KAAK,CAAC,IAAI,CAACrD,MAAM,CAAC;AACpC;MAEA/C,KAAK,CAACyG,cAAc,EAAE;KACzB;AACDa,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAACtH,KAAK,EAAE;MACZ,IAAI,IAAI,CAACyE,eAAe,CAACzF,UAAU,EAAE,EAAE;QACnC,IAAMZ,aAAc,GAAE,IAAI,CAACwK,YAAY,CAAC,IAAI,CAACnE,eAAe,CAACzF,KAAK,CAAC;AACnE,QAAA,IAAM2I,OAAQ,GAAE,IAAI,CAAC7B,qBAAqB,CAAC1H,aAAa,CAAC;AAEzD,QAAA,CAACuJ,OAAQ,IAAG,IAAI,CAACF,YAAY,CAAC;AAAExH,UAAAA,aAAa,EAAED,KAAK;AAAE5B,UAAAA,aAAY,EAAZA;AAAc,SAAC,CAAC;AAC1E;MAEA,IAAI,CAAC4H,IAAI,EAAE;KACd;AACDnG,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACwB,EAAE,EAAE;MACR,IAAI,IAAI,CAACzE,UAAU,EAAE;QACjB8I,MAAM,CAAC8D,GAAG,CAAC,MAAM,EAAEnI,EAAE,EAAE,IAAI,CAACxE,UAAW,GAAE,IAAI,CAAC4M,SAAS,CAACC,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC;AAC/E;MAEAC,QAAQ,CAACxI,EAAE,EAAE;AAAEyI,QAAAA,QAAQ,EAAE,UAAU;AAAEC,QAAAA,GAAG,EAAE;AAAI,OAAC,CAAC;MAChD,IAAI,CAACC,YAAY,EAAE;AACnB5D,MAAAA,KAAK,CAAC,IAAI,CAAC/B,OAAO,CAAC;MACnB,IAAI,CAAC4F,YAAY,EAAE;KACtB;IACDC,YAAY,EAAA,SAAZA,YAAYA,GAAG;MACX,IAAI,CAACjF,wBAAwB,EAAE;MAC/B,IAAI,CAACkF,kBAAkB,EAAE;MACzB,IAAI,CAACjF,kBAAkB,EAAE;AAEzB,MAAA,IAAI,CAAChF,KAAK,CAAC,MAAM,CAAC;KACrB;IACDkK,OAAO,EAAA,SAAPA,OAAOA,GAAG;MACN,IAAI,CAACjF,0BAA0B,EAAE;MACjC,IAAI,CAACkF,oBAAoB,EAAE;MAC3B,IAAI,CAACjF,oBAAoB,EAAE;AAE3B,MAAA,IAAI,CAAClF,KAAK,CAAC,MAAM,CAAC;MAClB,IAAI,CAACxC,SAAQ,GAAI,IAAI;MACrB,IAAI,CAACkH,KAAI,GAAI,KAAK;KACrB;AACD0F,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACjJ,EAAE,EAAE;MACb,IAAI,IAAI,CAACzE,UAAU,EAAE;AACjB8I,QAAAA,MAAM,CAACC,KAAK,CAACtE,EAAE,CAAC;AACpB;KACH;IACD2I,YAAY,EAAA,SAAZA,YAAYA,GAAG;MACXO,gBAAgB,CAAC,IAAI,CAAC7M,SAAS,EAAE,IAAI,CAACqF,MAAM,CAAC;AAC7C,MAAA,IAAMyH,cAAcC,aAAa,CAAC,IAAI,CAAC1H,MAAM,CAAC;MAE9C,IAAIyH,cAAcC,aAAa,CAAC,IAAI,CAAC/M,SAAS,CAAC,EAAE;AAC7C,QAAA,IAAI,CAACA,SAAS,CAACP,KAAK,CAACuN,QAAS,GAAED,aAAa,CAAC,IAAI,CAAC1H,MAAM,CAAA,GAAI,IAAI;AACrE;KACH;IACDkC,wBAAwB,EAAA,SAAxBA,wBAAwBA,GAAG;AAAA,MAAA,IAAAtE,MAAA,GAAA,IAAA;AACvB,MAAA,IAAI,CAAC,IAAI,CAACsD,oBAAoB,EAAE;AAC5B,QAAA,IAAI,CAACA,oBAAqB,GAAE,UAACjE,KAAK,EAAK;AACnC,UAAA,IAAM2K,kBAAmB,GAAEhK,MAAI,CAACjD,SAAU,IAAG,CAACiD,MAAI,CAACjD,SAAS,CAACkN,QAAQ,CAAC5K,KAAK,CAAC+C,MAAM,CAAC;AACnF,UAAA,IAAM8H,eAAc,GAAIlK,MAAI,CAACxE,KAAI,GAAI,EAAEwE,MAAI,CAACoC,MAAO,KAAIpC,MAAI,CAACoC,WAAW/C,KAAK,CAAC+C,UAAUpC,MAAI,CAACoC,MAAM,CAAC6H,QAAQ,CAAC5K,KAAK,CAAC+C,MAAM,CAAC,CAAC,CAAA,GAAI,IAAI;UAElI,IAAI4H,kBAAiB,IAAKE,eAAe,EAAE;YACvClK,MAAI,CAACqF,IAAI,EAAE;AACf;SACH;QAED8E,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC9G,oBAAoB,EAAE,IAAI,CAAC;AACvE;KACH;IACDkB,0BAA0B,EAAA,SAA1BA,0BAA0BA,GAAG;MACzB,IAAI,IAAI,CAAClB,oBAAoB,EAAE;QAC3B6G,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC/G,oBAAoB,EAAE,IAAI,CAAC;QACtE,IAAI,CAACA,oBAAmB,GAAI,IAAI;AACpC;KACH;IACDkG,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AAAA,MAAA,IAAAc,MAAA,GAAA,IAAA;AACjB,MAAA,IAAI,CAAC,IAAI,CAAC9G,aAAa,EAAE;AACrB,QAAA,IAAI,CAACA,aAAc,GAAE,IAAI+G,6BAA6B,CAAC,IAAI,CAACnI,MAAM,EAAE,UAAC/C,KAAK,EAAK;AAC3EiL,UAAAA,MAAI,CAACjF,IAAI,CAAChG,KAAK,EAAE,IAAI,CAAC;AAC1B,SAAC,CAAC;AACN;AAEA,MAAA,IAAI,CAACmE,aAAa,CAACgG,kBAAkB,EAAE;KAC1C;IACDE,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;MACnB,IAAI,IAAI,CAAClG,aAAa,EAAE;AACpB,QAAA,IAAI,CAACA,aAAa,CAACkG,oBAAoB,EAAE;AAC7C;KACH;IACDnF,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AAAA,MAAA,IAAAiG,MAAA,GAAA,IAAA;AACjB,MAAA,IAAI,CAAC,IAAI,CAAC/G,cAAc,EAAE;AACtB,QAAA,IAAI,CAACA,cAAe,GAAE,UAACpE,KAAK,EAAK;AAC7B,UAAA,IAAI,CAACoL,aAAa,EAAE,EAAE;AAClBD,YAAAA,MAAI,CAACnF,IAAI,CAAChG,KAAK,EAAE,IAAI,CAAC;AAC1B;SACH;QAEDqL,MAAM,CAACN,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC3G,cAAc,CAAC;AAC1D;KACH;IACDgB,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;MACnB,IAAI,IAAI,CAAChB,cAAc,EAAE;QACrBiH,MAAM,CAACL,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC5G,cAAc,CAAC;QACzD,IAAI,CAACA,iBAAiB,IAAI;AAC9B;KACH;IACDkB,sBAAsB,EAAA,SAAtBA,sBAAsBA,GAAG;AAAA,MAAA,IAAAgG,MAAA,GAAA,IAAA;AACrB,MAAA,IAAI,CAAC,IAAI,CAACpH,kBAAkB,EAAE;QAC1B,IAAMW,KAAM,GAAE0G,UAAU,CAAAlN,cAAAA,CAAAA,MAAA,CAAgB,IAAI,CAAC1B,UAAU,EAAA,GAAA,CAAG,CAAC;QAE3D,IAAI,CAACkI,KAAI,GAAIA,KAAK;AAClB,QAAA,IAAI,CAACC,YAAW,GAAID,KAAK,CAAC2G,OAAO;QAEjC,IAAI,CAACtH,kBAAiB,GAAI,YAAM;AAC5BoH,UAAAA,MAAI,CAACxG,YAAW,GAAID,KAAK,CAAC2G,OAAO;SACpC;QAED,IAAI,CAAC3G,KAAK,CAACkG,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC7G,kBAAkB,CAAC;AAClE;KACH;IACDsB,wBAAwB,EAAA,SAAxBA,wBAAwBA,GAAG;MACvB,IAAI,IAAI,CAACtB,kBAAkB,EAAE;QACzB,IAAI,CAACW,KAAK,CAACmG,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC9G,kBAAkB,CAAC;QACjE,IAAI,CAACA,kBAAiB,GAAI,IAAI;AAClC;KACH;AACDuH,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACrN,aAAa,EAAE;AAAA,MAAA,IAAAsN,qBAAA;AACzB,MAAA,OAAO,IAAI,CAACC,WAAW,CAACvN,aAAa,CAAA,KAAA,CAAAsN,qBAAA,GAAK,IAAI,CAAC7F,sBAAsB,CAACzH,aAAa,CAAC,MAAA,IAAA,IAAAsN,qBAAA,KAAA,MAAA,GAAA,MAAA,GAA1CA,qBAAA,CAA4CE,iBAAiB,EAAE,CAACvD,UAAU,CAAC,IAAI,CAAC9D,WAAW,CAACqH,iBAAiB,EAAE,CAAC,CAAA;KAC7J;AACDD,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACvN,aAAa,EAAE;AACvB,MAAA,OAAO,CAAC,CAACA,aAAY,IAAK,CAAC,IAAI,CAACmB,cAAc,CAACnB,aAAa,CAACM,IAAI,CAAA,IAAK,CAAC,IAAI,CAACkH,eAAe,CAACxH,aAAa,CAACM,IAAI,CAAE,IAAG,IAAI,CAACgB,aAAa,CAACtB,aAAa,CAACM,IAAI,CAAC;KAC5J;AACDmN,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAACzN,aAAa,EAAE;AAC/B,MAAA,OAAO,IAAI,CAACuN,WAAW,CAACvN,aAAa,CAAE,IAAG,IAAI,CAACgK,UAAU,CAAChK,aAAa,CAAC;KAC3E;AACDgK,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAAChK,aAAa,EAAE;AACtB,MAAA,OAAO,IAAI,CAACH,cAAc,CAACuB,IAAI,CAAC,UAACoI,CAAC,EAAA;AAAA,QAAA,OAAKA,CAAC,CAACtJ,GAAE,KAAMF,aAAa,CAACE,GAAG;OAAC,CAAA;KACtE;IACD0K,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AAAA,MAAA,IAAA8C,MAAA,GAAA,IAAA;AACjB,MAAA,OAAO,IAAI,CAAClD,YAAY,CAACmD,SAAS,CAAC,UAAC3N,aAAa,EAAA;AAAA,QAAA,OAAK0N,MAAI,CAACH,WAAW,CAACvN,aAAa,CAAC;OAAC,CAAA;KACzF;IACD6K,iBAAiB,EAAA,SAAjBA,iBAAiBA,GAAG;AAAA,MAAA,IAAA+C,MAAA,GAAA,IAAA;AAChB,MAAA,OAAOC,aAAa,CAAC,IAAI,CAACrD,YAAY,EAAE,UAACxK,aAAa,EAAA;AAAA,QAAA,OAAK4N,MAAI,CAACL,WAAW,CAACvN,aAAa,CAAC;OAAC,CAAA;KAC9F;AACDsK,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAAC1J,KAAK,EAAE;AAAA,MAAA,IAAAkN,MAAA,GAAA,IAAA;MACrB,IAAMC,gBAAiB,GAAEnN,KAAI,GAAI,IAAI,CAAC4J,YAAY,CAACnI,MAAO,GAAE,IAAI,IAAI,CAACmI,YAAY,CAAChI,KAAK,CAAC5B,KAAI,GAAI,CAAC,CAAC,CAAC+M,SAAS,CAAC,UAAC3N,aAAa,EAAA;AAAA,QAAA,OAAK8N,MAAI,CAACP,WAAW,CAACvN,aAAa,CAAC;OAAE,CAAA,GAAE,EAAE;MAErK,OAAO+N,gBAAe,GAAI,EAAG,GAAEA,gBAAe,GAAInN,KAAM,GAAE,CAAE,GAAEA,KAAK;KACtE;AACD6J,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAAC7J,KAAK,EAAE;AAAA,MAAA,IAAAoN,MAAA,GAAA,IAAA;MACrB,IAAMD,gBAAe,GAAInN,KAAI,GAAI,CAAA,GAAIiN,aAAa,CAAC,IAAI,CAACrD,YAAY,CAAChI,KAAK,CAAC,CAAC,EAAE5B,KAAK,CAAC,EAAE,UAACZ,aAAa,EAAA;AAAA,QAAA,OAAKgO,MAAI,CAACT,WAAW,CAACvN,aAAa,CAAC;OAAE,CAAA,GAAE,EAAE;AAE9I,MAAA,OAAO+N,gBAAe,GAAI,KAAKA,gBAAe,GAAInN,KAAK;KAC1D;IACDqN,qBAAqB,EAAA,SAArBA,qBAAqBA,GAAG;AAAA,MAAA,IAAAC,MAAA,GAAA,IAAA;AACpB,MAAA,OAAO,IAAI,CAAC1D,YAAY,CAACmD,SAAS,CAAC,UAAC3N,aAAa,EAAA;AAAA,QAAA,OAAKkO,MAAI,CAACT,mBAAmB,CAACzN,aAAa,CAAC;OAAC,CAAA;KACjG;IACDkI,yBAAyB,EAAA,SAAzBA,yBAAyBA,GAAG;AACxB,MAAA,IAAMiG,gBAAgB,IAAI,CAACF,qBAAqB,EAAE;MAElD,OAAOE,aAAc,GAAE,CAAE,GAAE,IAAI,CAACvD,kBAAkB,EAAG,GAAEuD,aAAa;KACvE;IACDzD,wBAAwB,EAAA,SAAxBA,wBAAwBA,GAAG;AACvB,MAAA,IAAMyD,gBAAgB,IAAI,CAACF,qBAAqB,EAAE;MAElD,OAAOE,aAAc,GAAE,CAAE,GAAE,IAAI,CAACtD,iBAAiB,EAAG,GAAEsD,aAAa;KACtE;AACD/E,IAAAA,WAAW,WAAXA,WAAWA,CAACxH,KAAK,EAAEwM,KAAI,EAAE;AAAA,MAAA,IAAAC,MAAA,GAAA,IAAA;MACrB,IAAI,CAAClI,WAAU,GAAI,CAAC,IAAI,CAACA,eAAe,EAAE,IAAIiI,KAAI;MAElD,IAAI/D,SAAU,GAAE,EAAE;MAClB,IAAIiE,OAAM,GAAI,KAAK;MAEnB,IAAI,IAAI,CAACjI,eAAe,CAACzF,UAAU,EAAE,EAAE;AACnCyJ,QAAAA,SAAU,GAAE,IAAI,CAACG,YAAY,CAAChI,KAAK,CAAC,IAAI,CAAC6D,eAAe,CAACzF,KAAK,CAAC,CAAC+M,SAAS,CAAC,UAAC3N,aAAa,EAAA;AAAA,UAAA,OAAKqO,MAAI,CAAChB,aAAa,CAACrN,aAAa,CAAC;SAAC,CAAA;QAC/HqK,SAAU,GAAEA,cAAc,KAAK,IAAI,CAACG,YAAY,CAAChI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC6D,eAAe,CAACzF,KAAK,CAAC,CAAC+M,SAAS,CAAC,UAAC3N,aAAa,EAAA;AAAA,UAAA,OAAKqO,MAAI,CAAChB,aAAa,CAACrN,aAAa,CAAC;AAAA,SAAA,CAAE,GAAEqK,YAAY,IAAI,CAAChE,eAAe,CAACzF,KAAK;AAClM,OAAE,MAAK;QACHyJ,SAAU,GAAE,IAAI,CAACG,YAAY,CAACmD,SAAS,CAAC,UAAC3N,aAAa,EAAA;AAAA,UAAA,OAAKqO,MAAI,CAAChB,aAAa,CAACrN,aAAa,CAAC;SAAC,CAAA;AACjG;AAEA,MAAA,IAAIqK,cAAc,EAAE,EAAE;AAClBiE,QAAAA,UAAU,IAAI;AAClB;AAEA,MAAA,IAAIjE,SAAQ,KAAM,EAAG,IAAG,IAAI,CAAChE,eAAe,CAACzF,KAAI,KAAM,EAAE,EAAE;AACvDyJ,QAAAA,SAAU,GAAE,IAAI,CAACnC,yBAAyB,EAAE;AAChD;AAEA,MAAA,IAAImC,cAAc,EAAE,EAAE;AAClB,QAAA,IAAI,CAACD,sBAAsB,CAACxI,KAAK,EAAEyI,SAAS,CAAC;AACjD;MAEA,IAAI,IAAI,CAACnE,aAAa,EAAE;AACpBqI,QAAAA,YAAY,CAAC,IAAI,CAACrI,aAAa,CAAC;AACpC;AAEA,MAAA,IAAI,CAACA,aAAc,GAAEsI,UAAU,CAAC,YAAM;QAClCH,MAAI,CAAClI,WAAY,GAAE,EAAE;QACrBkI,MAAI,CAACnI,gBAAgB,IAAI;OAC5B,EAAE,GAAG,CAAC;AAEP,MAAA,OAAOoI,OAAO;KACjB;AACDlE,IAAAA,sBAAsB,WAAtBA,sBAAsBA,CAACxI,KAAK,EAAEhB,KAAK,EAAE;AACjC,MAAA,IAAI,IAAI,CAACyF,eAAe,CAACzF,KAAI,KAAMA,KAAK,EAAE;AACtC,QAAA,IAAI,CAACyF,eAAe,CAACzF,KAAI,GAAIA,KAAK;QAClC,IAAI,CAACiL,YAAY,EAAE;AACvB;KACH;IACDA,YAAY,EAAA,SAAZA,YAAYA,GAAa;AAAA,MAAA,IAAZjL,4EAAQ,EAAE;AACnB,MAAA,IAAMsD,EAAG,GAAEtD,KAAM,KAAI,EAAC,MAAAX,MAAA,CAAO,IAAI,CAACwO,GAAG,OAAAxO,MAAA,CAAIW,KAAK,CAAK,GAAA,IAAI,CAACpB,aAAa;AACrE,MAAA,IAAMsL,OAAQ,GAAEC,UAAU,CAAC,IAAI,CAAC9E,OAAO,EAAA,UAAA,CAAAhG,MAAA,CAAYiE,EAAE,EAAA,KAAA,CAAI,CAAC;AAE1D,MAAA,IAAI4G,OAAO,EAAE;AACTA,QAAAA,OAAO,CAAC4D,kBAAkB5D,OAAO,CAAC4D,cAAc,CAAC;AAAEC,UAAAA,KAAK,EAAE,SAAS;AAAEC,UAAAA,MAAM,EAAE;AAAQ,SAAC,CAAC;AAC3F;KACH;AACDC,IAAAA,oBAAoB,EAApBA,SAAAA,oBAAoBA,CAACpP,KAAK,EAA0C;AAAA,MAAA,IAAAqP,OAAA,GAAA,IAAA;AAAA,MAAA,IAAxCnP,KAAI,GAAAoP,SAAA,CAAA1M,MAAA,GAAA,CAAA,IAAA0M,SAAA,CAAA,CAAA,CAAA,KAAAvO,SAAA,GAAAuO,SAAA,CAAA,CAAA,CAAA,GAAI,CAAC;AAAA,MAAA,IAAEjF,MAAO,GAAAiF,SAAA,CAAA1M,MAAA,GAAA,CAAA,IAAA0M,SAAA,CAAA,CAAA,CAAA,KAAAvO,SAAA,GAAAuO,SAAA,CAAA,CAAA,CAAA,GAAE,EAAE;AAAA,MAAA,IAAEzI,SAAU,GAAAyI,SAAA,CAAA1M,MAAA,GAAA,CAAA,IAAA0M,SAAA,CAAA,CAAA,CAAA,KAAAvO,SAAA,GAAAuO,SAAA,CAAA,CAAA,CAAA,GAAE,EAAE;MAC9D,IAAMC,cAAe,GAAE,EAAE;MAEzBvP,KAAI,IACAA,KAAK,CAACwP,OAAO,CAAC,UAAC3O,IAAI,EAAEM,KAAK,EAAK;AAC3B,QAAA,IAAMV,GAAE,GAAI,CAACoG,SAAU,KAAI,EAAC,GAAIA,YAAY,GAAI,GAAE,EAAE,IAAI1F,KAAK;AAC7D,QAAA,IAAMsO,OAAM,GAAI;AACZ5O,UAAAA,IAAI,EAAJA,IAAI;AACJM,UAAAA,KAAK,EAALA,KAAK;AACLjB,UAAAA,KAAK,EAALA,KAAK;AACLO,UAAAA,GAAG,EAAHA,GAAG;AACH4J,UAAAA,MAAM,EAANA,MAAM;AACNxD,UAAAA,SAAQ,EAARA;SACH;AAED4I,QAAAA,OAAO,CAAC,OAAO,IAAIJ,OAAI,CAACD,oBAAoB,CAACvO,IAAI,CAACb,KAAK,EAAEE,KAAM,GAAE,CAAC,EAAEuP,OAAO,EAAEhP,GAAG,CAAC;AACjF8O,QAAAA,cAAc,CAACvF,IAAI,CAACyF,OAAO,CAAC;AAChC,OAAC,CAAC;AAEN,MAAA,OAAOF,cAAc;KACxB;AACDhM,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACC,EAAE,EAAE;MACb,IAAI,CAAC3D,SAAU,GAAE2D,EAAE;KACtB;AACDkM,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAAClM,EAAE,EAAE;MACX,IAAI,CAACgD,OAAQ,GAAEhD,EAAG,GAAEA,EAAE,CAACmM,GAAE,GAAI5O,SAAS;AAC1C;GACH;AACD6O,EAAAA,QAAQ,EAAE;IACNL,cAAc,EAAA,SAAdA,cAAcA,GAAG;MACb,OAAO,IAAI,CAACH,oBAAoB,CAAC,IAAI,CAAC3Q,SAAS,EAAE,CAAC;KACrD;IACDsM,YAAY,EAAA,SAAZA,YAAYA,GAAG;AAAA,MAAA,IAAA8E,OAAA,GAAA,IAAA;MACX,IAAMtP,aAAY,GAAI,IAAI,CAACH,cAAc,CAACsK,IAAI,CAAC,UAACX,CAAC,EAAA;QAAA,OAAKA,CAAC,CAACtJ,GAAI,KAAIoP,OAAI,CAACjJ,eAAe,CAACC,SAAS;OAAC,CAAA;MAE/F,OAAOtG,aAAY,GAAIA,aAAa,CAACP,KAAM,GAAE,IAAI,CAACuP,cAAc;KACnE;IACDxP,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,OAAO,IAAI,CAAC6G,eAAe,CAACzF,KAAI,KAAM,EAAG,GAAAX,EAAAA,CAAAA,MAAA,CAAK,IAAI,CAACwO,GAAG,CAAAxO,CAAAA,MAAA,CAAGuB,UAAU,CAAC,IAAI,CAAC6E,eAAe,CAACC,SAAS,CAAA,GAAI,GAAI,GAAE,IAAI,CAACD,eAAe,CAACC,SAAQ,GAAI,EAAE,EAAArG,GAAAA,CAAAA,CAAAA,MAAA,CAAI,IAAI,CAACoG,eAAe,CAACzF,KAAK,IAAK,IAAI;AAC1L;GACH;AACDsC,EAAAA,UAAU,EAAE;AACRqM,IAAAA,aAAa,EAAEA,QAAa;AAC5BC,IAAAA,MAAM,EAAEA;AACZ;AACJ,CAAC;;;;;;sBCppBGhM,WAuCQ,CAAAiM,iBAAA,EAAA;IAvCCrR,QAAQ,EAAEwF,IAAQ,CAAAxF,QAAA;IAAGO,QAAQ,GAAGiF,IAAK,CAAA7F;;uBAC1C,YAAA;AAAA,MAAA,OAqCY,CArCZ2R,WAAA,CAqCYjM,YArCZC,UAqCY,CAAA;AArCA9F,QAAAA,IAAI,EAAC,qBAAsB;QAAC6D,OAAK,EAAEkC,QAAO,CAAAlC,OAAA;QAAGqK,YAAW,EAAEnI,QAAY,CAAAmI,YAAA;QAAGE,OAAK,EAAErI,QAAO,CAAAqI,OAAA;QAAGE,YAAW,EAAEvI,QAAY,CAAAuI;SAAUtI,IAAG,CAAA/C,GAAA,CAAA,YAAA,CAAA,CAAA,EAAA;2BACxI,YAAA;AAAA,UAAA,OAmCK,CAnCM8O,KAAO,CAAAjQ,OAAA,IAAlB6D,SAAA,EAAA,EAAAO,kBAAA,CAmCK,OAnCLJ,UAmCK,CAAA;;YAnCgBK,GAAG,EAAEJ,QAAY,CAAAX,YAAA;YAAGkB,EAAE,EAAEN,IAAG,CAAA6K,GAAA;AAAG,YAAA,OAAA,EAAO7K,IAAE,CAAAhB,EAAA,CAAA,MAAA,CAAA;YAAWyB,OAAK;qBAAEV,QAAc,CAAA+F,cAAA,IAAA/F,QAAA,CAAA+F,cAAA,CAAAkG,KAAA,CAAAjM,QAAA,EAAAoL,SAAA,CAAA;aAAA;aAAUnL,IAAI,CAAAiM,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAC3FjM,IAAA,CAAAkM,MAAM,CAACC,KAAK,IAAvBxM,SAAA,EAAA,EAAAO,kBAAA,CAEK,OAFLJ,UAEK,CAAA;;AAFqB,YAAA,OAAA,EAAOE,IAAE,CAAAhB,EAAA,CAAA,OAAA;aAAmBgB,IAAG,CAAA/C,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CACrDmP,UAAyB,CAAApM,IAAA,CAAAkM,MAAA,EAAA,OAAA,CAAA,wCAE7BJ,WAAA,CA2BCvK,0BA3BDzB,UA2BC,CAAA;YA1BIK,GAAG,EAAEJ,QAAU,CAAAwL,UAAA;AACfjL,YAAAA,EAAE,EAAEN,IAAE,CAAA6K,GAAA,GAAA,OAAA;AACN,YAAA,OAAA,EAAO7K,IAAE,CAAAhB,EAAA,CAAA,UAAA,CAAA;YACThE,QAAQ,EAAA,CAAGgF,IAAO,CAAAjF,QAAA,GAAIiF,IAAS,CAAAhF,QAAA,GAAA,EAAA;AAChCuF,YAAAA,IAAI,EAAC,SAAQ;YACZ,YAAU,EAAEP,IAAS,CAAA9E,SAAA;YACrB,iBAAe,EAAE8E,IAAc,CAAA/E,cAAA;AAC/B,YAAA,eAAa,EAAE+E,iBAAYpD,SAAS;AACrC,YAAA,kBAAgB,EAAC,UAAS;YACzB,uBAAqB,EAAEmP,KAAA,CAAA1O,OAAQ,GAAE0C,QAAA,CAAAnE,aAAc,GAAEgB,SAAS;YAC1DjB,MAAM,EAAEqE,IAAG,CAAA6K,GAAA;YACXjP,aAAa,EAAEmQ,KAAA,CAAA1O,OAAM,GAAI0C,QAAA,CAAAnE,aAAc,GAAEgB,SAAS;YAClDf,KAAK,EAAEkE,QAAc,CAAAqL,cAAA;YACrBpP,SAAS,EAAEgE,IAAM,CAAAkM,MAAA;YACjBjQ,cAAc,EAAE8P,KAAc,CAAA9P,cAAA;AAC9BF,YAAAA,KAAK,EAAE,CAAC;YACRD,OAAO,EAAEiQ,KAAc,CAAApJ,cAAA;YACvBlB,EAAE,EAAEzB,IAAE,CAAAyB,EAAA;YACNC,QAAQ,EAAE1B,IAAQ,CAAA0B,QAAA;YAClB2C,OAAK,EAAEtE,QAAO,CAAAsE,OAAA;YACdE,MAAI,EAAExE,QAAM,CAAAwE,MAAA;YACZ8H,SAAO,EAAEtM,QAAS,CAAAyE,SAAA;YAClBzG,WAAU,EAAEgC,QAAW,CAAAhC,WAAA;YACvB6D,gBAAe,EAAE7B,QAAgB,CAAA3B,gBAAA;YACjCyD,eAAc,EAAE9B,QAAe,CAAA1B;aACxB2B,IAAG,CAAA/C,GAAA,CAAA,UAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,uBAAA,EAAA,QAAA,EAAA,eAAA,EAAA,OAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,IAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,kBAAA,EAAA,iBAAA,CAAA,CAAA,EAEJ+C,IAAA,CAAAkM,MAAM,CAACI,GAAG,IAArB3M,SAAA,EAAA,EAAAO,kBAAA,CAEK,OAFLJ,UAEK,CAAA;;AAFmB,YAAA,OAAA,EAAOE,IAAE,CAAAhB,EAAA,CAAA,KAAA;aAAiBgB,IAAG,CAAA/C,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CACjDmP,UAAuB,CAAApM,IAAA,CAAAkM,MAAA,EAAA,KAAA,CAAA;;;;;;;;;;;;;"}