{"version": 3, "file": "index.mjs", "sources": ["../../../src/splitbutton/style/SplitButtonStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/splitbutton';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-splitbutton p-component',\n        {\n            'p-splitbutton-raised': props.raised,\n            'p-splitbutton-rounded': props.rounded,\n            'p-splitbutton-fluid': instance.hasFluid\n        }\n    ],\n    pcButton: 'p-splitbutton-button',\n    pcDropdown: 'p-splitbutton-dropdown'\n};\n\nexport default BaseStyle.extend({\n    name: 'splitbutton',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "instance", "props", "raised", "rounded", "hasFluid", "pc<PERSON><PERSON><PERSON>", "pcDropdown", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAA,OAAO,CAC3B,2BAA2B,EAC3B;MACI,sBAAsB,EAAEA,KAAK,CAACC,MAAM;MACpC,uBAAuB,EAAED,KAAK,CAACE,OAAO;MACtC,qBAAqB,EAAEH,QAAQ,CAACI;AACpC,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,QAAQ,EAAE,sBAAsB;AAChCC,EAAAA,UAAU,EAAE;AAChB,CAAC;AAED,uBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,aAAa;AACnBC,EAAAA,KAAK,EAALA,KAAK;AACLb,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}