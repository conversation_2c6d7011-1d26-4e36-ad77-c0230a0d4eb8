{"version": 3, "file": "index.mjs", "sources": ["../../src/splitter/BaseSplitter.vue", "../../src/splitter/Splitter.vue", "../../src/splitter/Splitter.vue?vue&type=template&id=92aa5fd8&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport SplitterStyle from 'primevue/splitter/style';\n\nexport default {\n    name: 'BaseSplitter',\n    extends: BaseComponent,\n    props: {\n        layout: {\n            type: String,\n            default: 'horizontal'\n        },\n        gutterSize: {\n            type: Number,\n            default: 4\n        },\n        stateKey: {\n            type: String,\n            default: null\n        },\n        stateStorage: {\n            type: String,\n            default: 'session'\n        },\n        step: {\n            type: Number,\n            default: 5\n        }\n    },\n    style: SplitterStyle,\n    provide() {\n        return {\n            $pcSplitter: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :data-p-resizing=\"false\" :data-p=\"dataP\" v-bind=\"ptmi('root', getPTOptions)\">\n        <template v-for=\"(panel, i) of panels\" :key=\"i\">\n            <component :is=\"panel\" tabindex=\"-1\"></component>\n            <div\n                v-if=\"i !== panels.length - 1\"\n                ref=\"gutter\"\n                :class=\"cx('gutter')\"\n                role=\"separator\"\n                tabindex=\"-1\"\n                @mousedown=\"onGutterMouseDown($event, i)\"\n                @touchstart=\"onGutterTouchStart($event, i)\"\n                @touchmove=\"onGutterTouchMove($event, i)\"\n                @touchend=\"onGutterTouchEnd($event, i)\"\n                :data-p-gutter-resizing=\"false\"\n                :data-p=\"dataP\"\n                v-bind=\"ptm('gutter')\"\n            >\n                <div :class=\"cx('gutterHandle')\" tabindex=\"0\" :style=\"[gutterStyle]\" :aria-orientation=\"layout\" :aria-valuenow=\"prevSize\" @keyup=\"onGutterKeyUp\" @keydown=\"onGutterKeyDown($event, i)\" :data-p=\"dataP\" v-bind=\"ptm('gutterHandle')\"></div>\n            </div>\n        </template>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { getHeight, getOuterHeight, getOuterWidth, getWidth, isRTL } from '@primeuix/utils/dom';\nimport { isArray, isNotEmpty } from '@primeuix/utils/object';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport BaseSplitter from './BaseSplitter.vue';\n\nexport default {\n    name: 'Splitter',\n    extends: BaseSplitter,\n    inheritAttrs: false,\n    emits: ['resizestart', 'resizeend', 'resize'],\n    dragging: false,\n    mouseMoveListener: null,\n    mouseUpListener: null,\n    touchMoveListener: null,\n    touchEndListener: null,\n    size: null,\n    gutterElement: null,\n    startPos: null,\n    prevPanelElement: null,\n    nextPanelElement: null,\n    nextPanelSize: null,\n    prevPanelSize: null,\n    panelSizes: null,\n    prevPanelIndex: null,\n    timer: null,\n    data() {\n        return {\n            prevSize: null\n        };\n    },\n    mounted() {\n        this.initializePanels();\n    },\n    beforeUnmount() {\n        this.clear();\n        this.unbindMouseListeners();\n    },\n    methods: {\n        isSplitterPanel(child) {\n            return child.type.name === 'SplitterPanel';\n        },\n        initializePanels() {\n            if (this.panels && this.panels.length) {\n                let initialized = false;\n\n                if (this.isStateful()) {\n                    initialized = this.restoreState();\n                }\n\n                if (!initialized) {\n                    let children = [...this.$el.children].filter((child) => child.getAttribute('data-pc-name') === 'splitterpanel');\n                    let _panelSizes = [];\n\n                    this.panels.map((panel, i) => {\n                        let panelInitialSize = panel.props && isNotEmpty(panel.props.size) ? panel.props.size : null;\n                        let panelSize = panelInitialSize ?? 100 / this.panels.length;\n\n                        _panelSizes[i] = panelSize;\n                        children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n                    });\n\n                    this.panelSizes = _panelSizes;\n                    this.prevSize = parseFloat(_panelSizes[0]).toFixed(4);\n                }\n            }\n        },\n        onResizeStart(event, index, isKeyDown) {\n            this.gutterElement = event.currentTarget || event.target.parentElement;\n            this.size = this.horizontal ? getWidth(this.$el) : getHeight(this.$el);\n\n            if (!isKeyDown) {\n                this.dragging = true;\n                this.startPos = this.layout === 'horizontal' ? event.pageX || event.changedTouches[0].pageX : event.pageY || event.changedTouches[0].pageY;\n            }\n\n            this.prevPanelElement = this.gutterElement.previousElementSibling;\n            this.nextPanelElement = this.gutterElement.nextElementSibling;\n\n            if (isKeyDown) {\n                this.prevPanelSize = this.horizontal ? getOuterWidth(this.prevPanelElement, true) : getOuterHeight(this.prevPanelElement, true);\n                this.nextPanelSize = this.horizontal ? getOuterWidth(this.nextPanelElement, true) : getOuterHeight(this.nextPanelElement, true);\n            } else {\n                this.prevPanelSize = (100 * (this.horizontal ? getOuterWidth(this.prevPanelElement, true) : getOuterHeight(this.prevPanelElement, true))) / this.size;\n                this.nextPanelSize = (100 * (this.horizontal ? getOuterWidth(this.nextPanelElement, true) : getOuterHeight(this.nextPanelElement, true))) / this.size;\n            }\n\n            this.prevPanelIndex = index;\n            this.$emit('resizestart', { originalEvent: event, sizes: this.panelSizes });\n            this.$refs.gutter[index].setAttribute('data-p-gutter-resizing', true);\n            this.$el.setAttribute('data-p-resizing', true);\n        },\n        onResize(event, step, isKeyDown) {\n            let newPos, newPrevPanelSize, newNextPanelSize;\n\n            if (isKeyDown) {\n                if (this.horizontal) {\n                    newPrevPanelSize = (100 * (this.prevPanelSize + step)) / this.size;\n                    newNextPanelSize = (100 * (this.nextPanelSize - step)) / this.size;\n                } else {\n                    newPrevPanelSize = (100 * (this.prevPanelSize - step)) / this.size;\n                    newNextPanelSize = (100 * (this.nextPanelSize + step)) / this.size;\n                }\n            } else {\n                if (this.horizontal) {\n                    if (isRTL(this.$el)) {\n                        newPos = ((this.startPos - event.pageX) * 100) / this.size;\n                    } else {\n                        newPos = ((event.pageX - this.startPos) * 100) / this.size;\n                    }\n                } else {\n                    newPos = ((event.pageY - this.startPos) * 100) / this.size;\n                }\n\n                newPrevPanelSize = this.prevPanelSize + newPos;\n                newNextPanelSize = this.nextPanelSize - newPos;\n            }\n\n            if (!this.validateResize(newPrevPanelSize, newNextPanelSize)) {\n                newPrevPanelSize = Math.min(Math.max(this.prevPanelMinSize, newPrevPanelSize), 100 - this.nextPanelMinSize);\n                newNextPanelSize = Math.min(Math.max(this.nextPanelMinSize, newNextPanelSize), 100 - this.prevPanelMinSize);\n            }\n\n            this.prevPanelElement.style.flexBasis = 'calc(' + newPrevPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            this.nextPanelElement.style.flexBasis = 'calc(' + newNextPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            this.panelSizes[this.prevPanelIndex] = newPrevPanelSize;\n            this.panelSizes[this.prevPanelIndex + 1] = newNextPanelSize;\n            this.prevSize = parseFloat(newPrevPanelSize).toFixed(4);\n\n            this.$emit('resize', { originalEvent: event, sizes: this.panelSizes });\n        },\n        onResizeEnd(event) {\n            if (this.isStateful()) {\n                this.saveState();\n            }\n\n            this.$emit('resizeend', { originalEvent: event, sizes: this.panelSizes });\n            this.$refs.gutter.forEach((gutter) => gutter.setAttribute('data-p-gutter-resizing', false));\n            this.$el.setAttribute('data-p-resizing', false);\n            this.clear();\n        },\n        repeat(event, index, step) {\n            this.onResizeStart(event, index, true);\n            this.onResize(event, step, true);\n        },\n        setTimer(event, index, step) {\n            if (!this.timer) {\n                this.timer = setInterval(() => {\n                    this.repeat(event, index, step);\n                }, 40);\n            }\n        },\n        clearTimer() {\n            if (this.timer) {\n                clearInterval(this.timer);\n                this.timer = null;\n            }\n        },\n        onGutterKeyUp() {\n            this.clearTimer();\n            this.onResizeEnd();\n        },\n        onGutterKeyDown(event, index) {\n            switch (event.code) {\n                case 'ArrowLeft': {\n                    if (this.layout === 'horizontal') {\n                        this.setTimer(event, index, this.step * -1);\n                    }\n\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowRight': {\n                    if (this.layout === 'horizontal') {\n                        this.setTimer(event, index, this.step);\n                    }\n\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowDown': {\n                    if (this.layout === 'vertical') {\n                        this.setTimer(event, index, this.step * -1);\n                    }\n\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowUp': {\n                    if (this.layout === 'vertical') {\n                        this.setTimer(event, index, this.step);\n                    }\n\n                    event.preventDefault();\n                    break;\n                }\n\n                default:\n                    //no op\n                    break;\n            }\n        },\n        onGutterMouseDown(event, index) {\n            this.onResizeStart(event, index);\n            this.bindMouseListeners();\n        },\n        onGutterTouchStart(event, index) {\n            this.onResizeStart(event, index);\n            this.bindTouchListeners();\n            event.preventDefault();\n        },\n        onGutterTouchMove(event) {\n            this.onResize(event);\n            event.preventDefault();\n        },\n        onGutterTouchEnd(event) {\n            this.onResizeEnd(event);\n            this.unbindTouchListeners();\n            event.preventDefault();\n        },\n        bindMouseListeners() {\n            if (!this.mouseMoveListener) {\n                this.mouseMoveListener = (event) => this.onResize(event);\n                document.addEventListener('mousemove', this.mouseMoveListener);\n            }\n\n            if (!this.mouseUpListener) {\n                this.mouseUpListener = (event) => {\n                    this.onResizeEnd(event);\n                    this.unbindMouseListeners();\n                };\n\n                document.addEventListener('mouseup', this.mouseUpListener);\n            }\n        },\n        bindTouchListeners() {\n            if (!this.touchMoveListener) {\n                this.touchMoveListener = (event) => this.onResize(event.changedTouches[0]);\n                document.addEventListener('touchmove', this.touchMoveListener);\n            }\n\n            if (!this.touchEndListener) {\n                this.touchEndListener = (event) => {\n                    this.resizeEnd(event);\n                    this.unbindTouchListeners();\n                };\n\n                document.addEventListener('touchend', this.touchEndListener);\n            }\n        },\n        validateResize(newPrevPanelSize, newNextPanelSize) {\n            if (newPrevPanelSize > 100 || newPrevPanelSize < 0) return false;\n            if (newNextPanelSize > 100 || newNextPanelSize < 0) return false;\n\n            if (this.prevPanelMinSize > newPrevPanelSize) {\n                return false;\n            }\n\n            if (this.nextPanelMinSize > newNextPanelSize) {\n                return false;\n            }\n\n            return true;\n        },\n        unbindMouseListeners() {\n            if (this.mouseMoveListener) {\n                document.removeEventListener('mousemove', this.mouseMoveListener);\n                this.mouseMoveListener = null;\n            }\n\n            if (this.mouseUpListener) {\n                document.removeEventListener('mouseup', this.mouseUpListener);\n                this.mouseUpListener = null;\n            }\n        },\n        unbindTouchListeners() {\n            if (this.touchMoveListener) {\n                document.removeEventListener('touchmove', this.touchMoveListener);\n                this.touchMoveListener = null;\n            }\n\n            if (this.touchEndListener) {\n                document.removeEventListener('touchend', this.touchEndListener);\n                this.touchEndListener = null;\n            }\n        },\n        clear() {\n            this.dragging = false;\n            this.size = null;\n            this.startPos = null;\n            this.prevPanelElement = null;\n            this.nextPanelElement = null;\n            this.prevPanelSize = null;\n            this.nextPanelSize = null;\n            this.gutterElement = null;\n            this.prevPanelIndex = null;\n        },\n        isStateful() {\n            return this.stateKey != null;\n        },\n        getStorage() {\n            switch (this.stateStorage) {\n                case 'local':\n                    return window.localStorage;\n\n                case 'session':\n                    return window.sessionStorage;\n\n                default:\n                    throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are \"local\" and \"session\".');\n            }\n        },\n        saveState() {\n            if (isArray(this.panelSizes)) {\n                this.getStorage().setItem(this.stateKey, JSON.stringify(this.panelSizes));\n            }\n        },\n        restoreState() {\n            const storage = this.getStorage();\n            const stateString = storage.getItem(this.stateKey);\n\n            if (stateString) {\n                this.panelSizes = JSON.parse(stateString);\n                let children = [...this.$el.children].filter((child) => child.getAttribute('data-pc-name') === 'splitterpanel');\n\n                children.forEach((child, i) => {\n                    child.style.flexBasis = 'calc(' + this.panelSizes[i] + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n                });\n\n                return true;\n            }\n\n            return false;\n        },\n        resetState() {\n            this.initializePanels();\n        }\n    },\n    computed: {\n        panels() {\n            const panels = [];\n\n            this.$slots.default().forEach((child) => {\n                if (this.isSplitterPanel(child)) {\n                    panels.push(child);\n                } else if (child.children instanceof Array) {\n                    child.children.forEach((nestedChild) => {\n                        if (this.isSplitterPanel(nestedChild)) {\n                            panels.push(nestedChild);\n                        }\n                    });\n                }\n            });\n\n            return panels;\n        },\n        gutterStyle() {\n            if (this.horizontal) return { width: this.gutterSize + 'px' };\n            else return { height: this.gutterSize + 'px' };\n        },\n        horizontal() {\n            return this.layout === 'horizontal';\n        },\n        getPTOptions() {\n            return {\n                context: {\n                    nested: this.$parentInstance?.nestedState\n                }\n            };\n        },\n        prevPanelMinSize() {\n            const prevPanelMinSize = getVNodeProp(this.panels[this.prevPanelIndex], 'minSize');\n\n            if (this.panels[this.prevPanelIndex].props && prevPanelMinSize) {\n                return prevPanelMinSize;\n            }\n\n            return 0;\n        },\n        nextPanelMinSize() {\n            const nextPanelMinSize = getVNodeProp(this.panels[this.prevPanelIndex + 1], 'minSize');\n\n            if (this.panels[this.prevPanelIndex + 1].props && nextPanelMinSize) {\n                return nextPanelMinSize;\n            }\n\n            return 0;\n        },\n        dataP() {\n            return cn({\n                [this.layout]: this.layout,\n                nested: this.$parentInstance?.nestedState != null\n            });\n        }\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :data-p-resizing=\"false\" :data-p=\"dataP\" v-bind=\"ptmi('root', getPTOptions)\">\n        <template v-for=\"(panel, i) of panels\" :key=\"i\">\n            <component :is=\"panel\" tabindex=\"-1\"></component>\n            <div\n                v-if=\"i !== panels.length - 1\"\n                ref=\"gutter\"\n                :class=\"cx('gutter')\"\n                role=\"separator\"\n                tabindex=\"-1\"\n                @mousedown=\"onGutterMouseDown($event, i)\"\n                @touchstart=\"onGutterTouchStart($event, i)\"\n                @touchmove=\"onGutterTouchMove($event, i)\"\n                @touchend=\"onGutterTouchEnd($event, i)\"\n                :data-p-gutter-resizing=\"false\"\n                :data-p=\"dataP\"\n                v-bind=\"ptm('gutter')\"\n            >\n                <div :class=\"cx('gutterHandle')\" tabindex=\"0\" :style=\"[gutterStyle]\" :aria-orientation=\"layout\" :aria-valuenow=\"prevSize\" @keyup=\"onGutterKeyUp\" @keydown=\"onGutterKeyDown($event, i)\" :data-p=\"dataP\" v-bind=\"ptm('gutterHandle')\"></div>\n            </div>\n        </template>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport { getHeight, getOuterHeight, getOuterWidth, getWidth, isRTL } from '@primeuix/utils/dom';\nimport { isArray, isNotEmpty } from '@primeuix/utils/object';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport BaseSplitter from './BaseSplitter.vue';\n\nexport default {\n    name: 'Splitter',\n    extends: BaseSplitter,\n    inheritAttrs: false,\n    emits: ['resizestart', 'resizeend', 'resize'],\n    dragging: false,\n    mouseMoveListener: null,\n    mouseUpListener: null,\n    touchMoveListener: null,\n    touchEndListener: null,\n    size: null,\n    gutterElement: null,\n    startPos: null,\n    prevPanelElement: null,\n    nextPanelElement: null,\n    nextPanelSize: null,\n    prevPanelSize: null,\n    panelSizes: null,\n    prevPanelIndex: null,\n    timer: null,\n    data() {\n        return {\n            prevSize: null\n        };\n    },\n    mounted() {\n        this.initializePanels();\n    },\n    beforeUnmount() {\n        this.clear();\n        this.unbindMouseListeners();\n    },\n    methods: {\n        isSplitterPanel(child) {\n            return child.type.name === 'SplitterPanel';\n        },\n        initializePanels() {\n            if (this.panels && this.panels.length) {\n                let initialized = false;\n\n                if (this.isStateful()) {\n                    initialized = this.restoreState();\n                }\n\n                if (!initialized) {\n                    let children = [...this.$el.children].filter((child) => child.getAttribute('data-pc-name') === 'splitterpanel');\n                    let _panelSizes = [];\n\n                    this.panels.map((panel, i) => {\n                        let panelInitialSize = panel.props && isNotEmpty(panel.props.size) ? panel.props.size : null;\n                        let panelSize = panelInitialSize ?? 100 / this.panels.length;\n\n                        _panelSizes[i] = panelSize;\n                        children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n                    });\n\n                    this.panelSizes = _panelSizes;\n                    this.prevSize = parseFloat(_panelSizes[0]).toFixed(4);\n                }\n            }\n        },\n        onResizeStart(event, index, isKeyDown) {\n            this.gutterElement = event.currentTarget || event.target.parentElement;\n            this.size = this.horizontal ? getWidth(this.$el) : getHeight(this.$el);\n\n            if (!isKeyDown) {\n                this.dragging = true;\n                this.startPos = this.layout === 'horizontal' ? event.pageX || event.changedTouches[0].pageX : event.pageY || event.changedTouches[0].pageY;\n            }\n\n            this.prevPanelElement = this.gutterElement.previousElementSibling;\n            this.nextPanelElement = this.gutterElement.nextElementSibling;\n\n            if (isKeyDown) {\n                this.prevPanelSize = this.horizontal ? getOuterWidth(this.prevPanelElement, true) : getOuterHeight(this.prevPanelElement, true);\n                this.nextPanelSize = this.horizontal ? getOuterWidth(this.nextPanelElement, true) : getOuterHeight(this.nextPanelElement, true);\n            } else {\n                this.prevPanelSize = (100 * (this.horizontal ? getOuterWidth(this.prevPanelElement, true) : getOuterHeight(this.prevPanelElement, true))) / this.size;\n                this.nextPanelSize = (100 * (this.horizontal ? getOuterWidth(this.nextPanelElement, true) : getOuterHeight(this.nextPanelElement, true))) / this.size;\n            }\n\n            this.prevPanelIndex = index;\n            this.$emit('resizestart', { originalEvent: event, sizes: this.panelSizes });\n            this.$refs.gutter[index].setAttribute('data-p-gutter-resizing', true);\n            this.$el.setAttribute('data-p-resizing', true);\n        },\n        onResize(event, step, isKeyDown) {\n            let newPos, newPrevPanelSize, newNextPanelSize;\n\n            if (isKeyDown) {\n                if (this.horizontal) {\n                    newPrevPanelSize = (100 * (this.prevPanelSize + step)) / this.size;\n                    newNextPanelSize = (100 * (this.nextPanelSize - step)) / this.size;\n                } else {\n                    newPrevPanelSize = (100 * (this.prevPanelSize - step)) / this.size;\n                    newNextPanelSize = (100 * (this.nextPanelSize + step)) / this.size;\n                }\n            } else {\n                if (this.horizontal) {\n                    if (isRTL(this.$el)) {\n                        newPos = ((this.startPos - event.pageX) * 100) / this.size;\n                    } else {\n                        newPos = ((event.pageX - this.startPos) * 100) / this.size;\n                    }\n                } else {\n                    newPos = ((event.pageY - this.startPos) * 100) / this.size;\n                }\n\n                newPrevPanelSize = this.prevPanelSize + newPos;\n                newNextPanelSize = this.nextPanelSize - newPos;\n            }\n\n            if (!this.validateResize(newPrevPanelSize, newNextPanelSize)) {\n                newPrevPanelSize = Math.min(Math.max(this.prevPanelMinSize, newPrevPanelSize), 100 - this.nextPanelMinSize);\n                newNextPanelSize = Math.min(Math.max(this.nextPanelMinSize, newNextPanelSize), 100 - this.prevPanelMinSize);\n            }\n\n            this.prevPanelElement.style.flexBasis = 'calc(' + newPrevPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            this.nextPanelElement.style.flexBasis = 'calc(' + newNextPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            this.panelSizes[this.prevPanelIndex] = newPrevPanelSize;\n            this.panelSizes[this.prevPanelIndex + 1] = newNextPanelSize;\n            this.prevSize = parseFloat(newPrevPanelSize).toFixed(4);\n\n            this.$emit('resize', { originalEvent: event, sizes: this.panelSizes });\n        },\n        onResizeEnd(event) {\n            if (this.isStateful()) {\n                this.saveState();\n            }\n\n            this.$emit('resizeend', { originalEvent: event, sizes: this.panelSizes });\n            this.$refs.gutter.forEach((gutter) => gutter.setAttribute('data-p-gutter-resizing', false));\n            this.$el.setAttribute('data-p-resizing', false);\n            this.clear();\n        },\n        repeat(event, index, step) {\n            this.onResizeStart(event, index, true);\n            this.onResize(event, step, true);\n        },\n        setTimer(event, index, step) {\n            if (!this.timer) {\n                this.timer = setInterval(() => {\n                    this.repeat(event, index, step);\n                }, 40);\n            }\n        },\n        clearTimer() {\n            if (this.timer) {\n                clearInterval(this.timer);\n                this.timer = null;\n            }\n        },\n        onGutterKeyUp() {\n            this.clearTimer();\n            this.onResizeEnd();\n        },\n        onGutterKeyDown(event, index) {\n            switch (event.code) {\n                case 'ArrowLeft': {\n                    if (this.layout === 'horizontal') {\n                        this.setTimer(event, index, this.step * -1);\n                    }\n\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowRight': {\n                    if (this.layout === 'horizontal') {\n                        this.setTimer(event, index, this.step);\n                    }\n\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowDown': {\n                    if (this.layout === 'vertical') {\n                        this.setTimer(event, index, this.step * -1);\n                    }\n\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowUp': {\n                    if (this.layout === 'vertical') {\n                        this.setTimer(event, index, this.step);\n                    }\n\n                    event.preventDefault();\n                    break;\n                }\n\n                default:\n                    //no op\n                    break;\n            }\n        },\n        onGutterMouseDown(event, index) {\n            this.onResizeStart(event, index);\n            this.bindMouseListeners();\n        },\n        onGutterTouchStart(event, index) {\n            this.onResizeStart(event, index);\n            this.bindTouchListeners();\n            event.preventDefault();\n        },\n        onGutterTouchMove(event) {\n            this.onResize(event);\n            event.preventDefault();\n        },\n        onGutterTouchEnd(event) {\n            this.onResizeEnd(event);\n            this.unbindTouchListeners();\n            event.preventDefault();\n        },\n        bindMouseListeners() {\n            if (!this.mouseMoveListener) {\n                this.mouseMoveListener = (event) => this.onResize(event);\n                document.addEventListener('mousemove', this.mouseMoveListener);\n            }\n\n            if (!this.mouseUpListener) {\n                this.mouseUpListener = (event) => {\n                    this.onResizeEnd(event);\n                    this.unbindMouseListeners();\n                };\n\n                document.addEventListener('mouseup', this.mouseUpListener);\n            }\n        },\n        bindTouchListeners() {\n            if (!this.touchMoveListener) {\n                this.touchMoveListener = (event) => this.onResize(event.changedTouches[0]);\n                document.addEventListener('touchmove', this.touchMoveListener);\n            }\n\n            if (!this.touchEndListener) {\n                this.touchEndListener = (event) => {\n                    this.resizeEnd(event);\n                    this.unbindTouchListeners();\n                };\n\n                document.addEventListener('touchend', this.touchEndListener);\n            }\n        },\n        validateResize(newPrevPanelSize, newNextPanelSize) {\n            if (newPrevPanelSize > 100 || newPrevPanelSize < 0) return false;\n            if (newNextPanelSize > 100 || newNextPanelSize < 0) return false;\n\n            if (this.prevPanelMinSize > newPrevPanelSize) {\n                return false;\n            }\n\n            if (this.nextPanelMinSize > newNextPanelSize) {\n                return false;\n            }\n\n            return true;\n        },\n        unbindMouseListeners() {\n            if (this.mouseMoveListener) {\n                document.removeEventListener('mousemove', this.mouseMoveListener);\n                this.mouseMoveListener = null;\n            }\n\n            if (this.mouseUpListener) {\n                document.removeEventListener('mouseup', this.mouseUpListener);\n                this.mouseUpListener = null;\n            }\n        },\n        unbindTouchListeners() {\n            if (this.touchMoveListener) {\n                document.removeEventListener('touchmove', this.touchMoveListener);\n                this.touchMoveListener = null;\n            }\n\n            if (this.touchEndListener) {\n                document.removeEventListener('touchend', this.touchEndListener);\n                this.touchEndListener = null;\n            }\n        },\n        clear() {\n            this.dragging = false;\n            this.size = null;\n            this.startPos = null;\n            this.prevPanelElement = null;\n            this.nextPanelElement = null;\n            this.prevPanelSize = null;\n            this.nextPanelSize = null;\n            this.gutterElement = null;\n            this.prevPanelIndex = null;\n        },\n        isStateful() {\n            return this.stateKey != null;\n        },\n        getStorage() {\n            switch (this.stateStorage) {\n                case 'local':\n                    return window.localStorage;\n\n                case 'session':\n                    return window.sessionStorage;\n\n                default:\n                    throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are \"local\" and \"session\".');\n            }\n        },\n        saveState() {\n            if (isArray(this.panelSizes)) {\n                this.getStorage().setItem(this.stateKey, JSON.stringify(this.panelSizes));\n            }\n        },\n        restoreState() {\n            const storage = this.getStorage();\n            const stateString = storage.getItem(this.stateKey);\n\n            if (stateString) {\n                this.panelSizes = JSON.parse(stateString);\n                let children = [...this.$el.children].filter((child) => child.getAttribute('data-pc-name') === 'splitterpanel');\n\n                children.forEach((child, i) => {\n                    child.style.flexBasis = 'calc(' + this.panelSizes[i] + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n                });\n\n                return true;\n            }\n\n            return false;\n        },\n        resetState() {\n            this.initializePanels();\n        }\n    },\n    computed: {\n        panels() {\n            const panels = [];\n\n            this.$slots.default().forEach((child) => {\n                if (this.isSplitterPanel(child)) {\n                    panels.push(child);\n                } else if (child.children instanceof Array) {\n                    child.children.forEach((nestedChild) => {\n                        if (this.isSplitterPanel(nestedChild)) {\n                            panels.push(nestedChild);\n                        }\n                    });\n                }\n            });\n\n            return panels;\n        },\n        gutterStyle() {\n            if (this.horizontal) return { width: this.gutterSize + 'px' };\n            else return { height: this.gutterSize + 'px' };\n        },\n        horizontal() {\n            return this.layout === 'horizontal';\n        },\n        getPTOptions() {\n            return {\n                context: {\n                    nested: this.$parentInstance?.nestedState\n                }\n            };\n        },\n        prevPanelMinSize() {\n            const prevPanelMinSize = getVNodeProp(this.panels[this.prevPanelIndex], 'minSize');\n\n            if (this.panels[this.prevPanelIndex].props && prevPanelMinSize) {\n                return prevPanelMinSize;\n            }\n\n            return 0;\n        },\n        nextPanelMinSize() {\n            const nextPanelMinSize = getVNodeProp(this.panels[this.prevPanelIndex + 1], 'minSize');\n\n            if (this.panels[this.prevPanelIndex + 1].props && nextPanelMinSize) {\n                return nextPanelMinSize;\n            }\n\n            return 0;\n        },\n        dataP() {\n            return cn({\n                [this.layout]: this.layout,\n                nested: this.$parentInstance?.nestedState != null\n            });\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "layout", "type", "String", "gutterSize", "Number", "stateKey", "stateStorage", "step", "style", "SplitterStyle", "provide", "$pcSplitter", "$parentInstance", "BaseSplitter", "inheritAttrs", "emits", "dragging", "mouseMoveListener", "mouseUpListener", "touchMoveListener", "touchEndListener", "size", "gutterElement", "startPos", "prevPanelElement", "nextPanelElement", "nextPanelSize", "prevPanelSize", "panelSizes", "prevPanelIndex", "timer", "data", "prevSize", "mounted", "initializePanels", "beforeUnmount", "clear", "unbindMouseListeners", "methods", "isSplitterPanel", "child", "_this", "panels", "length", "initialized", "isStateful", "restoreState", "children", "_toConsumableArray", "$el", "filter", "getAttribute", "_panelSizes", "map", "panel", "i", "panelInitialSize", "isNotEmpty", "panelSize", "flexBasis", "parseFloat", "toFixed", "onResizeStart", "event", "index", "isKeyDown", "currentTarget", "target", "parentElement", "horizontal", "getWidth", "getHeight", "pageX", "changedTouches", "pageY", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "getOuterWidth", "getOuterHeight", "$emit", "originalEvent", "sizes", "$refs", "gutter", "setAttribute", "onResize", "newPos", "newPrevPanelSize", "newNextPanelSize", "isRTL", "validateResize", "Math", "min", "max", "prevPanelMinSize", "nextPanelMinSize", "onResizeEnd", "saveState", "for<PERSON>ach", "repeat", "setTimer", "_this2", "setInterval", "clearTimer", "clearInterval", "onGutterKeyUp", "onGutterKeyDown", "code", "preventDefault", "onGutterMouseDown", "bindMouseListeners", "onGutterTouchStart", "bindTouchListeners", "onGutterTouchMove", "onGutterTouchEnd", "unbindTouchListeners", "_this3", "document", "addEventListener", "_this4", "resizeEnd", "removeEventListener", "getStorage", "window", "localStorage", "sessionStorage", "Error", "isArray", "setItem", "JSON", "stringify", "_this5", "storage", "stateString", "getItem", "parse", "resetState", "computed", "_this6", "$slots", "push", "Array", "nested<PERSON><PERSON><PERSON>", "gutterStyle", "width", "height", "getPTOptions", "_this$$parentInstance", "context", "nested", "nestedState", "getVNodeProp", "dataP", "_this$$parentInstance2", "cn", "_defineProperty", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "$options", "ptmi", "_Fragment", "_renderList", "_createBlock", "_resolveDynamicComponent", "tabindex", "ref", "role", "onMousedown", "$event", "onTouchstart", "onTouchmove", "onTouchend", "ptm", "_createElementVNode", "$data", "onKeyup", "apply", "arguments", "onKeydown", "_hoisted_3"], "mappings": ";;;;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,cAAc;AACpB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,MAAM,EAAE;AACJC,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRF,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNJ,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDI,IAAAA,YAAY,EAAE;AACVL,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDK,IAAAA,IAAI,EAAE;AACFN,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDI,EAAAA,KAAK,EAAEC,aAAa;EACpBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,WAAW,EAAE,IAAI;AACjBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;;;;;;;ACLD,aAAe;AACXf,EAAAA,IAAI,EAAE,UAAU;AAChB,EAAA,SAAA,EAASgB,QAAY;AACrBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,KAAK,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,QAAQ,CAAC;AAC7CC,EAAAA,QAAQ,EAAE,KAAK;AACfC,EAAAA,iBAAiB,EAAE,IAAI;AACvBC,EAAAA,eAAe,EAAE,IAAI;AACrBC,EAAAA,iBAAiB,EAAE,IAAI;AACvBC,EAAAA,gBAAgB,EAAE,IAAI;AACtBC,EAAAA,IAAI,EAAE,IAAI;AACVC,EAAAA,aAAa,EAAE,IAAI;AACnBC,EAAAA,QAAQ,EAAE,IAAI;AACdC,EAAAA,gBAAgB,EAAE,IAAI;AACtBC,EAAAA,gBAAgB,EAAE,IAAI;AACtBC,EAAAA,aAAa,EAAE,IAAI;AACnBC,EAAAA,aAAa,EAAE,IAAI;AACnBC,EAAAA,UAAU,EAAE,IAAI;AAChBC,EAAAA,cAAc,EAAE,IAAI;AACpBC,EAAAA,KAAK,EAAE,IAAI;EACXC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHC,MAAAA,QAAQ,EAAE;KACb;GACJ;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACC,gBAAgB,EAAE;GAC1B;EACDC,aAAa,EAAA,SAAbA,aAAaA,GAAG;IACZ,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACC,oBAAoB,EAAE;GAC9B;AACDC,EAAAA,OAAO,EAAE;AACLC,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAACC,KAAK,EAAE;AACnB,MAAA,OAAOA,KAAK,CAACvC,IAAI,CAACJ,IAAG,KAAM,eAAe;KAC7C;IACDqC,gBAAgB,EAAA,SAAhBA,gBAAgBA,GAAG;AAAA,MAAA,IAAAO,KAAA,GAAA,IAAA;MACf,IAAI,IAAI,CAACC,MAAK,IAAK,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;QACnC,IAAIC,WAAY,GAAE,KAAK;AAEvB,QAAA,IAAI,IAAI,CAACC,UAAU,EAAE,EAAE;AACnBD,UAAAA,WAAU,GAAI,IAAI,CAACE,YAAY,EAAE;AACrC;QAEA,IAAI,CAACF,WAAW,EAAE;AACd,UAAA,IAAIG,QAAS,GAAEC,kBAAA,CAAI,IAAI,CAACC,GAAG,CAACF,QAAQ,CAAA,CAAEG,MAAM,CAAC,UAACV,KAAK,EAAA;AAAA,YAAA,OAAKA,KAAK,CAACW,YAAY,CAAC,cAAc,MAAM,eAAe;WAAC,CAAA;UAC/G,IAAIC,WAAY,GAAE,EAAE;UAEpB,IAAI,CAACV,MAAM,CAACW,GAAG,CAAC,UAACC,KAAK,EAAEC,CAAC,EAAK;YAC1B,IAAIC,gBAAiB,GAAEF,KAAK,CAACvD,KAAM,IAAG0D,UAAU,CAACH,KAAK,CAACvD,KAAK,CAACsB,IAAI,CAAE,GAAEiC,KAAK,CAACvD,KAAK,CAACsB,IAAG,GAAI,IAAI;AAC5F,YAAA,IAAIqC,SAAQ,GAAIF,gBAAe,KAAA,IAAA,IAAfA,gBAAe,KAAfA,MAAAA,GAAAA,gBAAe,GAAK,MAAMf,KAAI,CAACC,MAAM,CAACC,MAAM;AAE5DS,YAAAA,WAAW,CAACG,CAAC,CAAE,GAAEG,SAAS;YAC1BX,QAAQ,CAACQ,CAAC,CAAC,CAAC/C,KAAK,CAACmD,YAAY,OAAM,GAAID,YAAY,MAAK,GAAI,CAACjB,KAAI,CAACC,MAAM,CAACC,MAAO,GAAE,CAAC,IAAIF,KAAI,CAACtC,UAAS,GAAI,KAAK;AACnH,WAAC,CAAC;UAEF,IAAI,CAACyB,UAAW,GAAEwB,WAAW;AAC7B,UAAA,IAAI,CAACpB,WAAW4B,UAAU,CAACR,WAAW,CAAC,CAAC,CAAC,CAAC,CAACS,OAAO,CAAC,CAAC,CAAC;AACzD;AACJ;KACH;IACDC,aAAa,EAAA,SAAbA,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;MACnC,IAAI,CAAC3C,aAAY,GAAIyC,KAAK,CAACG,aAAc,IAAGH,KAAK,CAACI,MAAM,CAACC,aAAa;AACtE,MAAA,IAAI,CAAC/C,IAAG,GAAI,IAAI,CAACgD,UAAS,GAAIC,QAAQ,CAAC,IAAI,CAACrB,GAAG,CAAA,GAAIsB,SAAS,CAAC,IAAI,CAACtB,GAAG,CAAC;MAEtE,IAAI,CAACgB,SAAS,EAAE;QACZ,IAAI,CAACjD,QAAS,GAAE,IAAI;AACpB,QAAA,IAAI,CAACO,QAAO,GAAI,IAAI,CAACvB,MAAK,KAAM,YAAa,GAAE+D,KAAK,CAACS,KAAM,IAAGT,KAAK,CAACU,cAAc,CAAC,CAAC,CAAC,CAACD,KAAI,GAAIT,KAAK,CAACW,KAAI,IAAKX,KAAK,CAACU,cAAc,CAAC,CAAC,CAAC,CAACC,KAAK;AAC9I;AAEA,MAAA,IAAI,CAAClD,gBAAe,GAAI,IAAI,CAACF,aAAa,CAACqD,sBAAsB;AACjE,MAAA,IAAI,CAAClD,gBAAe,GAAI,IAAI,CAACH,aAAa,CAACsD,kBAAkB;AAE7D,MAAA,IAAIX,SAAS,EAAE;QACX,IAAI,CAACtC,aAAc,GAAE,IAAI,CAAC0C,UAAS,GAAIQ,aAAa,CAAC,IAAI,CAACrD,gBAAgB,EAAE,IAAI,CAAE,GAAEsD,cAAc,CAAC,IAAI,CAACtD,gBAAgB,EAAE,IAAI,CAAC;QAC/H,IAAI,CAACE,aAAc,GAAE,IAAI,CAAC2C,UAAS,GAAIQ,aAAa,CAAC,IAAI,CAACpD,gBAAgB,EAAE,IAAI,CAAE,GAAEqD,cAAc,CAAC,IAAI,CAACrD,gBAAgB,EAAE,IAAI,CAAC;AACnI,OAAE,MAAK;AACH,QAAA,IAAI,CAACE,aAAY,GAAK,GAAI,IAAG,IAAI,CAAC0C,UAAW,GAAEQ,aAAa,CAAC,IAAI,CAACrD,gBAAgB,EAAE,IAAI,CAAA,GAAIsD,cAAc,CAAC,IAAI,CAACtD,gBAAgB,EAAE,IAAI,CAAC,CAAC,GAAI,IAAI,CAACH,IAAI;AACrJ,QAAA,IAAI,CAACK,aAAY,GAAK,GAAI,IAAG,IAAI,CAAC2C,UAAW,GAAEQ,aAAa,CAAC,IAAI,CAACpD,gBAAgB,EAAE,IAAI,CAAA,GAAIqD,cAAc,CAAC,IAAI,CAACrD,gBAAgB,EAAE,IAAI,CAAC,CAAC,GAAI,IAAI,CAACJ,IAAI;AACzJ;MAEA,IAAI,CAACQ,cAAa,GAAImC,KAAK;AAC3B,MAAA,IAAI,CAACe,KAAK,CAAC,aAAa,EAAE;AAAEC,QAAAA,aAAa,EAAEjB,KAAK;QAAEkB,KAAK,EAAE,IAAI,CAACrD;AAAW,OAAC,CAAC;AAC3E,MAAA,IAAI,CAACsD,KAAK,CAACC,MAAM,CAACnB,KAAK,CAAC,CAACoB,YAAY,CAAC,wBAAwB,EAAE,IAAI,CAAC;MACrE,IAAI,CAACnC,GAAG,CAACmC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC;KACjD;IACDC,QAAQ,EAAA,SAARA,QAAQA,CAACtB,KAAK,EAAExD,IAAI,EAAE0D,SAAS,EAAE;AAC7B,MAAA,IAAIqB,MAAM,EAAEC,gBAAgB,EAAEC,gBAAgB;AAE9C,MAAA,IAAIvB,SAAS,EAAE;QACX,IAAI,IAAI,CAACI,UAAU,EAAE;AACjBkB,UAAAA,gBAAiB,GAAG,GAAE,IAAK,IAAI,CAAC5D,aAAc,GAAEpB,IAAI,CAAC,GAAI,IAAI,CAACc,IAAI;AAClEmE,UAAAA,gBAAiB,GAAG,GAAE,IAAK,IAAI,CAAC9D,aAAc,GAAEnB,IAAI,CAAC,GAAI,IAAI,CAACc,IAAI;AACtE,SAAE,MAAK;AACHkE,UAAAA,gBAAiB,GAAG,GAAE,IAAK,IAAI,CAAC5D,aAAc,GAAEpB,IAAI,CAAC,GAAI,IAAI,CAACc,IAAI;AAClEmE,UAAAA,gBAAiB,GAAG,GAAE,IAAK,IAAI,CAAC9D,aAAc,GAAEnB,IAAI,CAAC,GAAI,IAAI,CAACc,IAAI;AACtE;AACJ,OAAE,MAAK;QACH,IAAI,IAAI,CAACgD,UAAU,EAAE;AACjB,UAAA,IAAIoB,KAAK,CAAC,IAAI,CAACxC,GAAG,CAAC,EAAE;AACjBqC,YAAAA,SAAU,CAAC,IAAI,CAAC/D,QAAS,GAAEwC,KAAK,CAACS,KAAK,IAAI,GAAG,GAAI,IAAI,CAACnD,IAAI;AAC9D,WAAE,MAAK;AACHiE,YAAAA,SAAU,CAACvB,KAAK,CAACS,KAAM,GAAE,IAAI,CAACjD,QAAQ,IAAI,GAAG,GAAI,IAAI,CAACF,IAAI;AAC9D;AACJ,SAAE,MAAK;AACHiE,UAAAA,SAAU,CAACvB,KAAK,CAACW,KAAM,GAAE,IAAI,CAACnD,QAAQ,IAAI,GAAG,GAAI,IAAI,CAACF,IAAI;AAC9D;AAEAkE,QAAAA,mBAAmB,IAAI,CAAC5D,gBAAgB2D,MAAM;AAC9CE,QAAAA,mBAAmB,IAAI,CAAC9D,gBAAgB4D,MAAM;AAClD;MAEA,IAAI,CAAC,IAAI,CAACI,cAAc,CAACH,gBAAgB,EAAEC,gBAAgB,CAAC,EAAE;QAC1DD,gBAAiB,GAAEI,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,IAAI,CAACC,gBAAgB,EAAEP,gBAAgB,CAAC,EAAE,GAAI,GAAE,IAAI,CAACQ,gBAAgB,CAAC;QAC3GP,gBAAiB,GAAEG,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,IAAI,CAACE,gBAAgB,EAAEP,gBAAgB,CAAC,EAAE,GAAI,GAAE,IAAI,CAACM,gBAAgB,CAAC;AAC/G;MAEA,IAAI,CAACtE,gBAAgB,CAAChB,KAAK,CAACmD,SAAQ,GAAI,OAAQ,GAAE4B,gBAAiB,GAAE,MAAO,GAAE,CAAC,IAAI,CAAC7C,MAAM,CAACC,MAAK,GAAI,CAAC,IAAI,IAAI,CAACxC,UAAW,GAAE,KAAK;MAChI,IAAI,CAACsB,gBAAgB,CAACjB,KAAK,CAACmD,SAAQ,GAAI,OAAQ,GAAE6B,gBAAiB,GAAE,MAAO,GAAE,CAAC,IAAI,CAAC9C,MAAM,CAACC,MAAK,GAAI,CAAC,IAAI,IAAI,CAACxC,UAAW,GAAE,KAAK;MAChI,IAAI,CAACyB,UAAU,CAAC,IAAI,CAACC,cAAc,CAAA,GAAI0D,gBAAgB;MACvD,IAAI,CAAC3D,UAAU,CAAC,IAAI,CAACC,iBAAiB,CAAC,CAAE,GAAE2D,gBAAgB;MAC3D,IAAI,CAACxD,WAAW4B,UAAU,CAAC2B,gBAAgB,CAAC,CAAC1B,OAAO,CAAC,CAAC,CAAC;AAEvD,MAAA,IAAI,CAACkB,KAAK,CAAC,QAAQ,EAAE;AAAEC,QAAAA,aAAa,EAAEjB,KAAK;QAAEkB,KAAK,EAAE,IAAI,CAACrD;AAAW,OAAC,CAAC;KACzE;AACDoE,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACjC,KAAK,EAAE;AACf,MAAA,IAAI,IAAI,CAAClB,UAAU,EAAE,EAAE;QACnB,IAAI,CAACoD,SAAS,EAAE;AACpB;AAEA,MAAA,IAAI,CAAClB,KAAK,CAAC,WAAW,EAAE;AAAEC,QAAAA,aAAa,EAAEjB,KAAK;QAAEkB,KAAK,EAAE,IAAI,CAACrD;AAAW,OAAC,CAAC;MACzE,IAAI,CAACsD,KAAK,CAACC,MAAM,CAACe,OAAO,CAAC,UAACf,MAAM,EAAA;AAAA,QAAA,OAAKA,MAAM,CAACC,YAAY,CAAC,wBAAwB,EAAE,KAAK,CAAC;OAAC,CAAA;MAC3F,IAAI,CAACnC,GAAG,CAACmC,YAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC;MAC/C,IAAI,CAAChD,KAAK,EAAE;KACf;IACD+D,MAAM,EAAA,SAANA,MAAMA,CAACpC,KAAK,EAAEC,KAAK,EAAEzD,IAAI,EAAE;MACvB,IAAI,CAACuD,aAAa,CAACC,KAAK,EAAEC,KAAK,EAAE,IAAI,CAAC;MACtC,IAAI,CAACqB,QAAQ,CAACtB,KAAK,EAAExD,IAAI,EAAE,IAAI,CAAC;KACnC;IACD6F,QAAQ,EAAA,SAARA,QAAQA,CAACrC,KAAK,EAAEC,KAAK,EAAEzD,IAAI,EAAE;AAAA,MAAA,IAAA8F,MAAA,GAAA,IAAA;AACzB,MAAA,IAAI,CAAC,IAAI,CAACvE,KAAK,EAAE;AACb,QAAA,IAAI,CAACA,KAAM,GAAEwE,WAAW,CAAC,YAAM;UAC3BD,MAAI,CAACF,MAAM,CAACpC,KAAK,EAAEC,KAAK,EAAEzD,IAAI,CAAC;SAClC,EAAE,EAAE,CAAC;AACV;KACH;IACDgG,UAAU,EAAA,SAAVA,UAAUA,GAAG;MACT,IAAI,IAAI,CAACzE,KAAK,EAAE;AACZ0E,QAAAA,aAAa,CAAC,IAAI,CAAC1E,KAAK,CAAC;QACzB,IAAI,CAACA,KAAM,GAAE,IAAI;AACrB;KACH;IACD2E,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,IAAI,CAACF,UAAU,EAAE;MACjB,IAAI,CAACP,WAAW,EAAE;KACrB;AACDU,IAAAA,eAAe,WAAfA,eAAeA,CAAC3C,KAAK,EAAEC,KAAK,EAAE;MAC1B,QAAQD,KAAK,CAAC4C,IAAI;AACd,QAAA,KAAK,WAAW;AAAE,UAAA;AACd,YAAA,IAAI,IAAI,CAAC3G,MAAO,KAAI,YAAY,EAAE;AAC9B,cAAA,IAAI,CAACoG,QAAQ,CAACrC,KAAK,EAAEC,KAAK,EAAE,IAAI,CAACzD,OAAO,EAAE,CAAC;AAC/C;YAEAwD,KAAK,CAAC6C,cAAc,EAAE;AACtB,YAAA;AACJ;AAEA,QAAA,KAAK,YAAY;AAAE,UAAA;AACf,YAAA,IAAI,IAAI,CAAC5G,MAAO,KAAI,YAAY,EAAE;cAC9B,IAAI,CAACoG,QAAQ,CAACrC,KAAK,EAAEC,KAAK,EAAE,IAAI,CAACzD,IAAI,CAAC;AAC1C;YAEAwD,KAAK,CAAC6C,cAAc,EAAE;AACtB,YAAA;AACJ;AAEA,QAAA,KAAK,WAAW;AAAE,UAAA;AACd,YAAA,IAAI,IAAI,CAAC5G,MAAK,KAAM,UAAU,EAAE;AAC5B,cAAA,IAAI,CAACoG,QAAQ,CAACrC,KAAK,EAAEC,KAAK,EAAE,IAAI,CAACzD,OAAO,EAAE,CAAC;AAC/C;YAEAwD,KAAK,CAAC6C,cAAc,EAAE;AACtB,YAAA;AACJ;AAEA,QAAA,KAAK,SAAS;AAAE,UAAA;AACZ,YAAA,IAAI,IAAI,CAAC5G,MAAK,KAAM,UAAU,EAAE;cAC5B,IAAI,CAACoG,QAAQ,CAACrC,KAAK,EAAEC,KAAK,EAAE,IAAI,CAACzD,IAAI,CAAC;AAC1C;YAEAwD,KAAK,CAAC6C,cAAc,EAAE;AACtB,YAAA;AACJ;AAKJ;KACH;AACDC,IAAAA,iBAAiB,WAAjBA,iBAAiBA,CAAC9C,KAAK,EAAEC,KAAK,EAAE;AAC5B,MAAA,IAAI,CAACF,aAAa,CAACC,KAAK,EAAEC,KAAK,CAAC;MAChC,IAAI,CAAC8C,kBAAkB,EAAE;KAC5B;AACDC,IAAAA,kBAAkB,WAAlBA,kBAAkBA,CAAChD,KAAK,EAAEC,KAAK,EAAE;AAC7B,MAAA,IAAI,CAACF,aAAa,CAACC,KAAK,EAAEC,KAAK,CAAC;MAChC,IAAI,CAACgD,kBAAkB,EAAE;MACzBjD,KAAK,CAAC6C,cAAc,EAAE;KACzB;AACDK,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAAClD,KAAK,EAAE;AACrB,MAAA,IAAI,CAACsB,QAAQ,CAACtB,KAAK,CAAC;MACpBA,KAAK,CAAC6C,cAAc,EAAE;KACzB;AACDM,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAACnD,KAAK,EAAE;AACpB,MAAA,IAAI,CAACiC,WAAW,CAACjC,KAAK,CAAC;MACvB,IAAI,CAACoD,oBAAoB,EAAE;MAC3BpD,KAAK,CAAC6C,cAAc,EAAE;KACzB;IACDE,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AAAA,MAAA,IAAAM,MAAA,GAAA,IAAA;AACjB,MAAA,IAAI,CAAC,IAAI,CAACnG,iBAAiB,EAAE;AACzB,QAAA,IAAI,CAACA,iBAAgB,GAAI,UAAC8C,KAAK,EAAA;AAAA,UAAA,OAAKqD,MAAI,CAAC/B,QAAQ,CAACtB,KAAK,CAAC;AAAA,SAAA;QACxDsD,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACrG,iBAAiB,CAAC;AAClE;AAEA,MAAA,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;AACvB,QAAA,IAAI,CAACA,eAAgB,GAAE,UAAC6C,KAAK,EAAK;AAC9BqD,UAAAA,MAAI,CAACpB,WAAW,CAACjC,KAAK,CAAC;UACvBqD,MAAI,CAAC/E,oBAAoB,EAAE;SAC9B;QAEDgF,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACpG,eAAe,CAAC;AAC9D;KACH;IACD8F,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AAAA,MAAA,IAAAO,MAAA,GAAA,IAAA;AACjB,MAAA,IAAI,CAAC,IAAI,CAACpG,iBAAiB,EAAE;AACzB,QAAA,IAAI,CAACA,iBAAkB,GAAE,UAAC4C,KAAK,EAAA;UAAA,OAAKwD,MAAI,CAAClC,QAAQ,CAACtB,KAAK,CAACU,cAAc,CAAC,CAAC,CAAC,CAAC;AAAA,SAAA;QAC1E4C,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACnG,iBAAiB,CAAC;AAClE;AAEA,MAAA,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;AACxB,QAAA,IAAI,CAACA,gBAAe,GAAI,UAAC2C,KAAK,EAAK;AAC/BwD,UAAAA,MAAI,CAACC,SAAS,CAACzD,KAAK,CAAC;UACrBwD,MAAI,CAACJ,oBAAoB,EAAE;SAC9B;QAEDE,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAClG,gBAAgB,CAAC;AAChE;KACH;AACDsE,IAAAA,cAAc,WAAdA,cAAcA,CAACH,gBAAgB,EAAEC,gBAAgB,EAAE;MAC/C,IAAID,gBAAiB,GAAE,GAAE,IAAKA,gBAAe,GAAI,CAAC,EAAE,OAAO,KAAK;MAChE,IAAIC,gBAAiB,GAAE,GAAE,IAAKA,gBAAe,GAAI,CAAC,EAAE,OAAO,KAAK;AAEhE,MAAA,IAAI,IAAI,CAACM,mBAAmBP,gBAAgB,EAAE;AAC1C,QAAA,OAAO,KAAK;AAChB;AAEA,MAAA,IAAI,IAAI,CAACQ,mBAAmBP,gBAAgB,EAAE;AAC1C,QAAA,OAAO,KAAK;AAChB;AAEA,MAAA,OAAO,IAAI;KACd;IACDnD,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;MACnB,IAAI,IAAI,CAACpB,iBAAiB,EAAE;QACxBoG,QAAQ,CAACI,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACxG,iBAAiB,CAAC;QACjE,IAAI,CAACA,iBAAgB,GAAI,IAAI;AACjC;MAEA,IAAI,IAAI,CAACC,eAAe,EAAE;QACtBmG,QAAQ,CAACI,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACvG,eAAe,CAAC;QAC7D,IAAI,CAACA,kBAAkB,IAAI;AAC/B;KACH;IACDiG,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;MACnB,IAAI,IAAI,CAAChG,iBAAiB,EAAE;QACxBkG,QAAQ,CAACI,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACtG,iBAAiB,CAAC;QACjE,IAAI,CAACA,iBAAgB,GAAI,IAAI;AACjC;MAEA,IAAI,IAAI,CAACC,gBAAgB,EAAE;QACvBiG,QAAQ,CAACI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACrG,gBAAgB,CAAC;QAC/D,IAAI,CAACA,mBAAmB,IAAI;AAChC;KACH;IACDgB,KAAK,EAAA,SAALA,KAAKA,GAAG;MACJ,IAAI,CAACpB,QAAS,GAAE,KAAK;MACrB,IAAI,CAACK,IAAG,GAAI,IAAI;MAChB,IAAI,CAACE,QAAS,GAAE,IAAI;MACpB,IAAI,CAACC,mBAAmB,IAAI;MAC5B,IAAI,CAACC,mBAAmB,IAAI;MAC5B,IAAI,CAACE,gBAAgB,IAAI;MACzB,IAAI,CAACD,gBAAgB,IAAI;MACzB,IAAI,CAACJ,gBAAgB,IAAI;MACzB,IAAI,CAACO,iBAAiB,IAAI;KAC7B;IACDgB,UAAU,EAAA,SAAVA,UAAUA,GAAG;AACT,MAAA,OAAO,IAAI,CAACxC,QAAO,IAAK,IAAI;KAC/B;IACDqH,UAAU,EAAA,SAAVA,UAAUA,GAAG;MACT,QAAQ,IAAI,CAACpH,YAAY;AACrB,QAAA,KAAK,OAAO;UACR,OAAOqH,MAAM,CAACC,YAAY;AAE9B,QAAA,KAAK,SAAS;UACV,OAAOD,MAAM,CAACE,cAAc;AAEhC,QAAA;UACI,MAAM,IAAIC,KAAK,CAAC,IAAI,CAACxH,YAAW,GAAI,0FAA0F,CAAC;AACvI;KACH;IACD2F,SAAS,EAAA,SAATA,SAASA,GAAG;AACR,MAAA,IAAI8B,OAAO,CAAC,IAAI,CAACnG,UAAU,CAAC,EAAE;AAC1B,QAAA,IAAI,CAAC8F,UAAU,EAAE,CAACM,OAAO,CAAC,IAAI,CAAC3H,QAAQ,EAAE4H,IAAI,CAACC,SAAS,CAAC,IAAI,CAACtG,UAAU,CAAC,CAAC;AAC7E;KACH;IACDkB,YAAY,EAAA,SAAZA,YAAYA,GAAG;AAAA,MAAA,IAAAqF,MAAA,GAAA,IAAA;AACX,MAAA,IAAMC,OAAQ,GAAE,IAAI,CAACV,UAAU,EAAE;MACjC,IAAMW,WAAY,GAAED,OAAO,CAACE,OAAO,CAAC,IAAI,CAACjI,QAAQ,CAAC;AAElD,MAAA,IAAIgI,WAAW,EAAE;QACb,IAAI,CAACzG,UAAW,GAAEqG,IAAI,CAACM,KAAK,CAACF,WAAW,CAAC;AACzC,QAAA,IAAItF,QAAS,GAAEC,kBAAA,CAAI,IAAI,CAACC,GAAG,CAACF,QAAQ,CAAA,CAAEG,MAAM,CAAC,UAACV,KAAK,EAAA;AAAA,UAAA,OAAKA,KAAK,CAACW,YAAY,CAAC,cAAc,MAAM,eAAe;SAAC,CAAA;AAE/GJ,QAAAA,QAAQ,CAACmD,OAAO,CAAC,UAAC1D,KAAK,EAAEe,CAAC,EAAK;AAC3Bf,UAAAA,KAAK,CAAChC,KAAK,CAACmD,SAAU,GAAE,OAAQ,GAAEwE,MAAI,CAACvG,UAAU,CAAC2B,CAAC,CAAE,GAAE,SAAS,CAAC4E,MAAI,CAACzF,MAAM,CAACC,MAAO,GAAE,CAAC,IAAIwF,MAAI,CAAChI,UAAS,GAAI,KAAK;AACtH,SAAC,CAAC;AAEF,QAAA,OAAO,IAAI;AACf;AAEA,MAAA,OAAO,KAAK;KACf;IACDqI,UAAU,EAAA,SAAVA,UAAUA,GAAG;MACT,IAAI,CAACtG,gBAAgB,EAAE;AAC3B;GACH;AACDuG,EAAAA,QAAQ,EAAE;IACN/F,MAAM,EAAA,SAANA,MAAMA,GAAG;AAAA,MAAA,IAAAgG,MAAA,GAAA,IAAA;MACL,IAAMhG,MAAK,GAAI,EAAE;MAEjB,IAAI,CAACiG,MAAM,CAAA,SAAA,CAAQ,EAAE,CAACzC,OAAO,CAAC,UAAC1D,KAAK,EAAK;AACrC,QAAA,IAAIkG,MAAI,CAACnG,eAAe,CAACC,KAAK,CAAC,EAAE;AAC7BE,UAAAA,MAAM,CAACkG,IAAI,CAACpG,KAAK,CAAC;AACtB,SAAA,MAAO,IAAIA,KAAK,CAACO,oBAAoB8F,KAAK,EAAE;AACxCrG,UAAAA,KAAK,CAACO,QAAQ,CAACmD,OAAO,CAAC,UAAC4C,WAAW,EAAK;AACpC,YAAA,IAAIJ,MAAI,CAACnG,eAAe,CAACuG,WAAW,CAAC,EAAE;AACnCpG,cAAAA,MAAM,CAACkG,IAAI,CAACE,WAAW,CAAC;AAC5B;AACJ,WAAC,CAAC;AACN;AACJ,OAAC,CAAC;AAEF,MAAA,OAAOpG,MAAM;KAChB;IACDqG,WAAW,EAAA,SAAXA,WAAWA,GAAG;AACV,MAAA,IAAI,IAAI,CAAC1E,UAAU,EAAE,OAAO;AAAE2E,QAAAA,KAAK,EAAE,IAAI,CAAC7I,UAAS,GAAI;OAAM,CAAA,KACxD,OAAO;AAAE8I,QAAAA,MAAM,EAAE,IAAI,CAAC9I,UAAW,GAAE;OAAM;KACjD;IACDkE,UAAU,EAAA,SAAVA,UAAUA,GAAG;AACT,MAAA,OAAO,IAAI,CAACrE,MAAO,KAAI,YAAY;KACtC;IACDkJ,YAAY,EAAA,SAAZA,YAAYA,GAAG;AAAA,MAAA,IAAAC,qBAAA;MACX,OAAO;AACHC,QAAAA,OAAO,EAAE;UACLC,MAAM,EAAA,CAAAF,qBAAA,GAAE,IAAI,CAACvI,eAAe,MAAAuI,IAAAA,IAAAA,qBAAA,KAApBA,MAAAA,GAAAA,MAAAA,GAAAA,qBAAA,CAAsBG;AAClC;OACH;KACJ;IACDxD,gBAAgB,EAAA,SAAhBA,gBAAgBA,GAAG;AACf,MAAA,IAAMA,gBAAe,GAAIyD,YAAY,CAAC,IAAI,CAAC7G,MAAM,CAAC,IAAI,CAACb,cAAc,CAAC,EAAE,SAAS,CAAC;AAElF,MAAA,IAAI,IAAI,CAACa,MAAM,CAAC,IAAI,CAACb,cAAc,CAAC,CAAC9B,KAAI,IAAK+F,gBAAgB,EAAE;AAC5D,QAAA,OAAOA,gBAAgB;AAC3B;AAEA,MAAA,OAAO,CAAC;KACX;IACDC,gBAAgB,EAAA,SAAhBA,gBAAgBA,GAAG;AACf,MAAA,IAAMA,gBAAe,GAAIwD,YAAY,CAAC,IAAI,CAAC7G,MAAM,CAAC,IAAI,CAACb,cAAe,GAAE,CAAC,CAAC,EAAE,SAAS,CAAC;AAEtF,MAAA,IAAI,IAAI,CAACa,MAAM,CAAC,IAAI,CAACb,cAAe,GAAE,CAAC,CAAC,CAAC9B,KAAI,IAAKgG,gBAAgB,EAAE;AAChE,QAAA,OAAOA,gBAAgB;AAC3B;AAEA,MAAA,OAAO,CAAC;KACX;IACDyD,KAAK,EAAA,SAALA,KAAKA,GAAG;AAAA,MAAA,IAAAC,sBAAA;AACJ,MAAA,OAAOC,EAAE,CAAAC,eAAA,CAAAA,eAAA,CAAA,EAAA,EACJ,IAAI,CAAC3J,MAAM,EAAG,IAAI,CAACA,MAAM,CAAA,EAAA,QAAA,EAClB,CAAAyJ,CAAAA,sBAAA,GAAI,IAAA,CAAC7I,eAAe,MAAA,IAAA,IAAA6I,sBAAA,KAAA,MAAA,GAAA,MAAA,GAApBA,sBAAA,CAAsBH,WAAU,KAAK,IAAG,CACnD,CAAC;AACN;AACJ;AACJ,CAAC;;;;;;ECtaG,OAAAM,SAAA,EAAA,EAAAC,kBAAA,CAoBK,OApBLC,UAoBK,CAAA;AApBC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;AAAW,IAAA,iBAAe,EAAE,KAAK;IAAG,QAAM,EAAEC,QAAK,CAAAT;AAAU,GAAA,EAAAO,IAAA,CAAAG,IAAI,SAASD,QAAY,CAAAf,YAAA,CAAA,CAAA,EAAA,EAC/FU,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAkBUM,QAlBqB,EAAA,IAAA,EAAAC,UAAA,CAAAH,QAAA,CAAAvH,MAAM,EAAnB,UAAAY,KAAK,EAAEC,CAAC,EAAA;;WAAmBA;AAAC,KAAA,EAAA,EAC1CqG,SAAA,EAAA,EAAAS,WAAA,CAAgDC,uBAAhC,CAAAhH,KAAK,CAAE,EAAA;AAAAiH,MAAAA,QAAQ,EAAC;KAAI,CAAA,GAE1BhH,CAAE,KAAI0G,QAAM,CAAAvH,MAAA,CAACC,MAAO,GAAA,CAAA,IAD9BiH,SAAA,EAAA,EAAAC,kBAAA,CAeK,OAfLC,UAeK,CAAA;;;AAbDU,MAAAA,GAAG,EAAC,QAAO;AACV,MAAA,OAAA,EAAOT,IAAE,CAAAC,EAAA,CAAA,QAAA,CAAA;AACVS,MAAAA,IAAI,EAAC,WAAU;AACfF,MAAAA,QAAQ,EAAC,IAAG;AACXG,MAAAA,WAAS,EAAE,SAAXA,WAASA,CAAEC,MAAA,EAAA;AAAA,QAAA,OAAAV,QAAA,CAAApD,iBAAiB,CAAC8D,MAAM,EAAEpH,CAAC,CAAA;OAAA;AACtCqH,MAAAA,YAAU,EAAE,SAAZA,YAAUA,CAAED,MAAA,EAAA;AAAA,QAAA,OAAAV,QAAA,CAAAlD,kBAAkB,CAAC4D,MAAM,EAAEpH,CAAC,CAAA;OAAA;AACxCsH,MAAAA,WAAS,EAAE,SAAXA,WAASA,CAAEF,MAAA,EAAA;AAAA,QAAA,OAAAV,QAAA,CAAAhD,iBAAiB,CAAC0D,MAAM,EAAEpH,CAAC,CAAA;OAAA;AACtCuH,MAAAA,UAAQ,EAAE,SAAVA,UAAQA,CAAEH,MAAA,EAAA;AAAA,QAAA,OAAAV,QAAA,CAAA/C,gBAAgB,CAACyD,MAAM,EAAEpH,CAAC,CAAA;OAAA;AACpC,MAAA,wBAAsB,EAAE,KAAK;MAC7B,QAAM,EAAE0G,QAAK,CAAAT;;;OACNO,IAAG,CAAAgB,GAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAEXC,kBAAA,CAAyO,OAAzOlB,UAAyO,CAAA;AAAnO,MAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,cAAA,CAAA;AAAkBO,MAAAA,QAAQ,EAAC,GAAI;AAAC/J,MAAAA,KAAK,GAAGyJ,QAAW,CAAAlB,WAAA,CAAA;MAAI,kBAAgB,EAAEgB,IAAM,CAAA/J,MAAA;MAAG,eAAa,EAAEiL,KAAQ,CAAAjJ,QAAA;MAAGkJ,OAAK;eAAEjB,QAAa,CAAAxD,aAAA,IAAAwD,QAAA,CAAAxD,aAAA,CAAA0E,KAAA,CAAAlB,QAAA,EAAAmB,SAAA,CAAA;AAAA,OAAA,CAAA;AAAGC,MAAAA,SAAO,EAAE,SAATA,SAAOA,CAAEV,MAAA,EAAA;AAAA,QAAA,OAAAV,QAAA,CAAAvD,eAAe,CAACiE,MAAM,EAAEpH,CAAC,CAAA;OAAA;MAAI,QAAM,EAAE0G,QAAK,CAAAT;;;OAAUO,IAAG,CAAAgB,GAAA,CAAA,cAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAAO,UAAA,CAAA;;;;;;;;"}