{"version": 3, "file": "index.mjs", "sources": ["../../../src/splitter/style/SplitterStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/splitter';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => ['p-splitter p-component', 'p-splitter-' + props.layout],\n    gutter: 'p-splitter-gutter',\n    gutterHandle: 'p-splitter-gutter-handle'\n};\n\nexport default BaseStyle.extend({\n    name: 'splitter',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "props", "layout", "gutter", "gutterHandle", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAA,OAAO,CAAC,wBAAwB,EAAE,aAAa,GAAGA,KAAK,CAACC,MAAM,CAAC;AAAA,GAAA;AAC7EC,EAAAA,MAAM,EAAE,mBAAmB;AAC3BC,EAAAA,YAAY,EAAE;AAClB,CAAC;AAED,oBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,UAAU;AAChBC,EAAAA,KAAK,EAALA,KAAK;AACLV,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}