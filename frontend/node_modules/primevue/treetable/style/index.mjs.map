{"version": 3, "file": "index.mjs", "sources": ["../../../src/treetable/style/TreeTableStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/treetable';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-treetable p-component',\n        {\n            'p-treetable-hoverable': props.rowHover || instance.rowSelectionMode,\n            'p-treetable-resizable': props.resizableColumns,\n            'p-treetable-resizable-fit': props.resizableColumns && props.columnResizeMode === 'fit',\n            'p-treetable-scrollable': props.scrollable,\n            'p-treetable-flex-scrollable': props.scrollable && props.scrollHeight === 'flex',\n            'p-treetable-gridlines': props.showGridlines,\n            'p-treetable-sm': props.size === 'small',\n            'p-treetable-lg': props.size === 'large'\n        }\n    ],\n    loading: 'p-treetable-loading', //TODO: required?\n    mask: 'p-treetable-mask p-overlay-mask',\n    loadingIcon: 'p-treetable-loading-icon',\n    header: 'p-treetable-header',\n    paginator: ({ position }) => 'p-treetable-paginator-' + position,\n    tableContainer: 'p-treetable-table-container',\n    table: ({ props }) => [\n        'p-treetable-table',\n        {\n            'p-treetable-scrollable-table': props.scrollable,\n            'p-treetable-resizable-table': props.resizableColumns,\n            'p-treetable-resizable-table-fit': props.resizableColumns && props.columnResizeMode === 'fit'\n        }\n    ],\n    thead: 'p-treetable-thead',\n    headerCell: ({ instance, props }) => [\n        'p-treetable-header-cell',\n        {\n            'p-treetable-sortable-column': instance.columnProp('sortable'),\n            'p-treetable-resizable-column': props.resizableColumns,\n            'p-treetable-column-sorted': instance.columnProp('sortable') ? instance.isColumnSorted() : false,\n            'p-treetable-frozen-column': instance.columnProp('frozen')\n        }\n    ],\n    columnResizer: 'p-treetable-column-resizer',\n    columnHeaderContent: 'p-treetable-column-header-content',\n    columnTitle: 'p-treetable-column-title',\n    sortIcon: 'p-treetable-sort-icon',\n    pcSortBadge: 'p-treetable-sort-badge',\n    tbody: 'p-treetable-tbody',\n    row: ({ props, instance }) => [\n        {\n            'p-treetable-row-selected': instance.selected,\n            'p-treetable-contextmenu-row-selected': props.contextMenuSelection && instance.isSelectedWithContextMenu\n        }\n    ],\n    bodyCell: ({ instance }) => [\n        {\n            'p-treetable-frozen-column': instance.columnProp('frozen')\n        }\n    ],\n    bodyCellContent: ({ instance }) => [\n        'p-treetable-body-cell-content',\n        {\n            'p-treetable-body-cell-content-expander': instance.columnProp('expander')\n        }\n    ],\n    nodeToggleButton: 'p-treetable-node-toggle-button',\n    nodeToggleIcon: 'p-treetable-node-toggle-icon',\n    pcNodeCheckbox: 'p-treetable-node-checkbox',\n    emptyMessage: 'p-treetable-empty-message',\n    tfoot: 'p-treetable-tfoot',\n    footerCell: ({ instance }) => [\n        {\n            'p-treetable-frozen-column': instance.columnProp('frozen')\n        }\n    ],\n    footer: 'p-treetable-footer',\n    columnResizeIndicator: 'p-treetable-column-resize-indicator'\n};\n\nconst inlineStyles = {\n    tableContainer: { overflow: 'auto' },\n    thead: { position: 'sticky' },\n    tfoot: { position: 'sticky' }\n};\n\nexport default BaseStyle.extend({\n    name: 'treetable',\n    style,\n    classes,\n    inlineStyles\n});\n"], "names": ["classes", "root", "_ref", "instance", "props", "rowHover", "rowSelectionMode", "resizableColumns", "columnResizeMode", "scrollable", "scrollHeight", "showGridlines", "size", "loading", "mask", "loadingIcon", "header", "paginator", "_ref2", "position", "tableContainer", "table", "_ref3", "thead", "headerCell", "_ref4", "columnProp", "isColumnSorted", "columnResizer", "column<PERSON>eader<PERSON>ontent", "columnTitle", "sortIcon", "pcSortBadge", "tbody", "row", "_ref5", "selected", "contextMenuSelection", "isSelectedWithContextMenu", "bodyCell", "_ref6", "body<PERSON>ell<PERSON><PERSON>nt", "_ref7", "nodeToggleButton", "nodeToggleIcon", "pcNodeCheckbox", "emptyMessage", "tfoot", "<PERSON><PERSON><PERSON><PERSON>", "_ref8", "footer", "columnResizeIndicator", "inlineStyles", "overflow", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAA,OAAO,CAC3B,yBAAyB,EACzB;AACI,MAAA,uBAAuB,EAAEA,KAAK,CAACC,QAAQ,IAAIF,QAAQ,CAACG,gBAAgB;MACpE,uBAAuB,EAAEF,KAAK,CAACG,gBAAgB;MAC/C,2BAA2B,EAAEH,KAAK,CAACG,gBAAgB,IAAIH,KAAK,CAACI,gBAAgB,KAAK,KAAK;MACvF,wBAAwB,EAAEJ,KAAK,CAACK,UAAU;MAC1C,6BAA6B,EAAEL,KAAK,CAACK,UAAU,IAAIL,KAAK,CAACM,YAAY,KAAK,MAAM;MAChF,uBAAuB,EAAEN,KAAK,CAACO,aAAa;AAC5C,MAAA,gBAAgB,EAAEP,KAAK,CAACQ,IAAI,KAAK,OAAO;AACxC,MAAA,gBAAgB,EAAER,KAAK,CAACQ,IAAI,KAAK;AACrC,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,OAAO,EAAE,qBAAqB;AAAE;AAChCC,EAAAA,IAAI,EAAE,iCAAiC;AACvCC,EAAAA,WAAW,EAAE,0BAA0B;AACvCC,EAAAA,MAAM,EAAE,oBAAoB;AAC5BC,EAAAA,SAAS,EAAE,SAAXA,SAASA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,KAAA,CAARC,QAAQ;IAAA,OAAO,wBAAwB,GAAGA,QAAQ;AAAA,GAAA;AAChEC,EAAAA,cAAc,EAAE,6BAA6B;AAC7CC,EAAAA,KAAK,EAAE,SAAPA,KAAKA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKlB,KAAK,GAAAkB,KAAA,CAALlB,KAAK;IAAA,OAAO,CAClB,mBAAmB,EACnB;MACI,8BAA8B,EAAEA,KAAK,CAACK,UAAU;MAChD,6BAA6B,EAAEL,KAAK,CAACG,gBAAgB;MACrD,iCAAiC,EAAEH,KAAK,CAACG,gBAAgB,IAAIH,KAAK,CAACI,gBAAgB,KAAK;AAC5F,KAAC,CACJ;AAAA,GAAA;AACDe,EAAAA,KAAK,EAAE,mBAAmB;AAC1BC,EAAAA,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKtB,QAAQ,GAAAsB,KAAA,CAARtB,QAAQ;MAAEC,KAAK,GAAAqB,KAAA,CAALrB,KAAK;IAAA,OAAO,CACjC,yBAAyB,EACzB;AACI,MAAA,6BAA6B,EAAED,QAAQ,CAACuB,UAAU,CAAC,UAAU,CAAC;MAC9D,8BAA8B,EAAEtB,KAAK,CAACG,gBAAgB;AACtD,MAAA,2BAA2B,EAAEJ,QAAQ,CAACuB,UAAU,CAAC,UAAU,CAAC,GAAGvB,QAAQ,CAACwB,cAAc,EAAE,GAAG,KAAK;AAChG,MAAA,2BAA2B,EAAExB,QAAQ,CAACuB,UAAU,CAAC,QAAQ;AAC7D,KAAC,CACJ;AAAA,GAAA;AACDE,EAAAA,aAAa,EAAE,4BAA4B;AAC3CC,EAAAA,mBAAmB,EAAE,mCAAmC;AACxDC,EAAAA,WAAW,EAAE,0BAA0B;AACvCC,EAAAA,QAAQ,EAAE,uBAAuB;AACjCC,EAAAA,WAAW,EAAE,wBAAwB;AACrCC,EAAAA,KAAK,EAAE,mBAAmB;AAC1BC,EAAAA,GAAG,EAAE,SAALA,GAAGA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAK/B,KAAK,GAAA+B,KAAA,CAAL/B,KAAK;MAAED,QAAQ,GAAAgC,KAAA,CAARhC,QAAQ;AAAA,IAAA,OAAO,CAC1B;MACI,0BAA0B,EAAEA,QAAQ,CAACiC,QAAQ;AAC7C,MAAA,sCAAsC,EAAEhC,KAAK,CAACiC,oBAAoB,IAAIlC,QAAQ,CAACmC;AACnF,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,QAAQ,EAAE,SAAVA,QAAQA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKrC,QAAQ,GAAAqC,KAAA,CAARrC,QAAQ;AAAA,IAAA,OAAO,CACxB;AACI,MAAA,2BAA2B,EAAEA,QAAQ,CAACuB,UAAU,CAAC,QAAQ;AAC7D,KAAC,CACJ;AAAA,GAAA;AACDe,EAAAA,eAAe,EAAE,SAAjBA,eAAeA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAKvC,QAAQ,GAAAuC,KAAA,CAARvC,QAAQ;IAAA,OAAO,CAC/B,+BAA+B,EAC/B;AACI,MAAA,wCAAwC,EAAEA,QAAQ,CAACuB,UAAU,CAAC,UAAU;AAC5E,KAAC,CACJ;AAAA,GAAA;AACDiB,EAAAA,gBAAgB,EAAE,gCAAgC;AAClDC,EAAAA,cAAc,EAAE,8BAA8B;AAC9CC,EAAAA,cAAc,EAAE,2BAA2B;AAC3CC,EAAAA,YAAY,EAAE,2BAA2B;AACzCC,EAAAA,KAAK,EAAE,mBAAmB;AAC1BC,EAAAA,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA,EAAA;AAAA,IAAA,IAAK9C,QAAQ,GAAA8C,KAAA,CAAR9C,QAAQ;AAAA,IAAA,OAAO,CAC1B;AACI,MAAA,2BAA2B,EAAEA,QAAQ,CAACuB,UAAU,CAAC,QAAQ;AAC7D,KAAC,CACJ;AAAA,GAAA;AACDwB,EAAAA,MAAM,EAAE,oBAAoB;AAC5BC,EAAAA,qBAAqB,EAAE;AAC3B,CAAC;AAED,IAAMC,YAAY,GAAG;AACjBhC,EAAAA,cAAc,EAAE;AAAEiC,IAAAA,QAAQ,EAAE;GAAQ;AACpC9B,EAAAA,KAAK,EAAE;AAAEJ,IAAAA,QAAQ,EAAE;GAAU;AAC7B4B,EAAAA,KAAK,EAAE;AAAE5B,IAAAA,QAAQ,EAAE;AAAS;AAChC,CAAC;AAED,qBAAemC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,WAAW;AACjBC,EAAAA,KAAK,EAALA,KAAK;AACLzD,EAAAA,OAAO,EAAPA,OAAO;AACPoD,EAAAA,YAAY,EAAZA;AACJ,CAAC,CAAC;;;;"}