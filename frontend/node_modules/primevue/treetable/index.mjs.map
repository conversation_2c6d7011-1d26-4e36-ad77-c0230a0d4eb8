{"version": 3, "file": "index.mjs", "sources": ["../../src/treetable/BaseTreeTable.vue", "../../src/treetable/FooterCell.vue", "../../src/treetable/FooterCell.vue?vue&type=template&id=2533b3ba&lang.js", "../../src/treetable/HeaderCell.vue", "../../src/treetable/HeaderCell.vue?vue&type=template&id=443165a8&lang.js", "../../src/treetable/BodyCell.vue", "../../src/treetable/BodyCell.vue?vue&type=template&id=a4d989b8&lang.js", "../../src/treetable/TreeTableRow.vue", "../../src/treetable/TreeTableRow.vue?vue&type=template&id=631929fa&lang.js", "../../src/treetable/TreeTable.vue", "../../src/treetable/TreeTable.vue?vue&type=template&id=1c850030&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TreeTableStyle from 'primevue/treetable/style';\n\nexport default {\n    name: 'BaseTreeTable',\n    extends: BaseComponent,\n    props: {\n        value: {\n            type: null,\n            default: null\n        },\n        dataKey: {\n            type: [String, Function],\n            default: 'key'\n        },\n        expandedKeys: {\n            type: null,\n            default: null\n        },\n        selectionKeys: {\n            type: null,\n            default: null\n        },\n        selectionMode: {\n            type: String,\n            default: null\n        },\n        metaKeySelection: {\n            type: Boolean,\n            default: false\n        },\n        contextMenu: {\n            type: Boolean,\n            default: false\n        },\n        contextMenuSelection: {\n            type: Object,\n            default: null\n        },\n        rows: {\n            type: Number,\n            default: 0\n        },\n        first: {\n            type: Number,\n            default: 0\n        },\n        totalRecords: {\n            type: Number,\n            default: 0\n        },\n        paginator: {\n            type: Boolean,\n            default: false\n        },\n        paginatorPosition: {\n            type: String,\n            default: 'bottom'\n        },\n        alwaysShowPaginator: {\n            type: Boolean,\n            default: true\n        },\n        paginatorTemplate: {\n            type: String,\n            default: 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown'\n        },\n        pageLinkSize: {\n            type: Number,\n            default: 5\n        },\n        rowsPerPageOptions: {\n            type: Array,\n            default: null\n        },\n        currentPageReportTemplate: {\n            type: String,\n            default: '({currentPage} of {totalPages})'\n        },\n        lazy: {\n            type: Boolean,\n            default: false\n        },\n        loading: {\n            type: Boolean,\n            default: false\n        },\n        loadingIcon: {\n            type: String,\n            default: undefined\n        },\n        loadingMode: {\n            type: String,\n            default: 'mask'\n        },\n        rowHover: {\n            type: Boolean,\n            default: false\n        },\n        autoLayout: {\n            type: Boolean,\n            default: false\n        },\n        sortField: {\n            type: [String, Function],\n            default: null\n        },\n        sortOrder: {\n            type: Number,\n            default: null\n        },\n        defaultSortOrder: {\n            type: Number,\n            default: 1\n        },\n        multiSortMeta: {\n            type: Array,\n            default: null\n        },\n        sortMode: {\n            type: String,\n            default: 'single'\n        },\n        removableSort: {\n            type: Boolean,\n            default: false\n        },\n        filters: {\n            type: Object,\n            default: null\n        },\n        filterMode: {\n            type: String,\n            default: 'lenient'\n        },\n        filterLocale: {\n            type: String,\n            default: undefined\n        },\n        resizableColumns: {\n            type: Boolean,\n            default: false\n        },\n        columnResizeMode: {\n            type: String,\n            default: 'fit'\n        },\n        indentation: {\n            type: Number,\n            default: 1\n        },\n        showGridlines: {\n            type: Boolean,\n            default: false\n        },\n        scrollable: {\n            type: Boolean,\n            default: false\n        },\n        scrollHeight: {\n            type: String,\n            default: null\n        },\n        size: {\n            type: String,\n            default: null\n        },\n        tableStyle: {\n            type: null,\n            default: null\n        },\n        tableClass: {\n            type: [String, Object],\n            default: null\n        },\n        tableProps: {\n            type: Object,\n            default: null\n        }\n    },\n    style: TreeTableStyle,\n    provide() {\n        return {\n            $pcTreeTable: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <td :style=\"containerStyle\" :class=\"containerClass\" role=\"cell\" v-bind=\"{ ...getColumnPT('root'), ...getColumnPT('footerCell') }\" :data-p-frozen-column=\"columnProp('frozen')\">\n        <component v-if=\"column.children && column.children.footer\" :is=\"column.children.footer\" :column=\"column\" />\n        <span v-if=\"columnProp('footer')\" :class=\"cx('columnFooter')\" v-bind=\"getColumnPT('columnFooter')\">{{ columnProp('footer') }}</span>\n    </td>\n</template>\n\n<script>\nimport { getNextElementSibling, getOuterWidth, getPreviousElementSibling } from '@primeuix/utils/dom';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'FooterCell',\n    hostName: 'TreeTable',\n    extends: BaseComponent,\n    props: {\n        column: {\n            type: Object,\n            default: null\n        },\n        index: {\n            type: Number,\n            default: null\n        }\n    },\n    data() {\n        return {\n            styleObject: {}\n        };\n    },\n    mounted() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    updated() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    methods: {\n        columnProp(prop) {\n            return getVNodeProp(this.column, prop);\n        },\n        getColumnPT(key) {\n            const columnMetaData = {\n                props: this.column.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    index: this.index,\n                    frozen: this.columnProp('frozen'),\n                    size: this.$parentInstance?.size\n                }\n            };\n\n            return mergeProps(this.ptm(`column.${key}`, { column: columnMetaData }), this.ptm(`column.${key}`, columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));\n        },\n        getColumnProp() {\n            return this.column.props && this.column.props.pt ? this.column.props.pt : undefined;\n        },\n        updateStickyPosition() {\n            if (this.columnProp('frozen')) {\n                let align = this.columnProp('alignFrozen');\n\n                if (align === 'right') {\n                    let pos = 0;\n                    let next = getNextElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (next) {\n                        pos = getOuterWidth(next) + parseFloat(next.style['inset-inline-end'] || 0);\n                    }\n\n                    this.styleObject.insetInlineEnd = pos + 'px';\n                } else {\n                    let pos = 0;\n                    let prev = getPreviousElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (prev) {\n                        pos = getOuterWidth(prev) + parseFloat(prev.style['inset-inline-start'] || 0);\n                    }\n\n                    this.styleObject.insetInlineStart = pos + 'px';\n                }\n            }\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.columnProp('footerClass'), this.columnProp('class'), this.cx('footerCell')];\n        },\n        containerStyle() {\n            let bodyStyle = this.columnProp('footerStyle');\n            let columnStyle = this.columnProp('style');\n\n            return this.columnProp('frozen') ? [columnStyle, bodyStyle, this.styleObject] : [columnStyle, bodyStyle];\n        }\n    }\n};\n</script>\n", "<template>\n    <td :style=\"containerStyle\" :class=\"containerClass\" role=\"cell\" v-bind=\"{ ...getColumnPT('root'), ...getColumnPT('footerCell') }\" :data-p-frozen-column=\"columnProp('frozen')\">\n        <component v-if=\"column.children && column.children.footer\" :is=\"column.children.footer\" :column=\"column\" />\n        <span v-if=\"columnProp('footer')\" :class=\"cx('columnFooter')\" v-bind=\"getColumnPT('columnFooter')\">{{ columnProp('footer') }}</span>\n    </td>\n</template>\n\n<script>\nimport { getNextElementSibling, getOuterWidth, getPreviousElementSibling } from '@primeuix/utils/dom';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'FooterCell',\n    hostName: 'TreeTable',\n    extends: BaseComponent,\n    props: {\n        column: {\n            type: Object,\n            default: null\n        },\n        index: {\n            type: Number,\n            default: null\n        }\n    },\n    data() {\n        return {\n            styleObject: {}\n        };\n    },\n    mounted() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    updated() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    methods: {\n        columnProp(prop) {\n            return getVNodeProp(this.column, prop);\n        },\n        getColumnPT(key) {\n            const columnMetaData = {\n                props: this.column.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    index: this.index,\n                    frozen: this.columnProp('frozen'),\n                    size: this.$parentInstance?.size\n                }\n            };\n\n            return mergeProps(this.ptm(`column.${key}`, { column: columnMetaData }), this.ptm(`column.${key}`, columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));\n        },\n        getColumnProp() {\n            return this.column.props && this.column.props.pt ? this.column.props.pt : undefined;\n        },\n        updateStickyPosition() {\n            if (this.columnProp('frozen')) {\n                let align = this.columnProp('alignFrozen');\n\n                if (align === 'right') {\n                    let pos = 0;\n                    let next = getNextElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (next) {\n                        pos = getOuterWidth(next) + parseFloat(next.style['inset-inline-end'] || 0);\n                    }\n\n                    this.styleObject.insetInlineEnd = pos + 'px';\n                } else {\n                    let pos = 0;\n                    let prev = getPreviousElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (prev) {\n                        pos = getOuterWidth(prev) + parseFloat(prev.style['inset-inline-start'] || 0);\n                    }\n\n                    this.styleObject.insetInlineStart = pos + 'px';\n                }\n            }\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.columnProp('footerClass'), this.columnProp('class'), this.cx('footerCell')];\n        },\n        containerStyle() {\n            let bodyStyle = this.columnProp('footerStyle');\n            let columnStyle = this.columnProp('style');\n\n            return this.columnProp('frozen') ? [columnStyle, bodyStyle, this.styleObject] : [columnStyle, bodyStyle];\n        }\n    }\n};\n</script>\n", "<template>\n    <th\n        :class=\"containerClass\"\n        :style=\"[containerStyle]\"\n        @click=\"onClick\"\n        @keydown=\"onKeyDown\"\n        :tabindex=\"columnProp('sortable') ? '0' : null\"\n        :aria-sort=\"ariaSort\"\n        role=\"columnheader\"\n        v-bind=\"{ ...getColumnPT('root'), ...getColumnPT('headerCell') }\"\n        :data-p-sortable-column=\"columnProp('sortable')\"\n        :data-p-resizable-column=\"resizableColumns\"\n        :data-p-sorted=\"isColumnSorted()\"\n        :data-p-frozen-column=\"columnProp('frozen')\"\n    >\n        <span v-if=\"resizableColumns && !columnProp('frozen')\" :class=\"cx('columnResizer')\" @mousedown=\"onResizeStart\" v-bind=\"getColumnPT('columnResizer')\"></span>\n        <div :class=\"cx('columnHeaderContent')\" v-bind=\"getColumnPT('columnHeaderContent')\">\n            <component v-if=\"column.children && column.children.header\" :is=\"column.children.header\" :column=\"column\" />\n            <span v-if=\"columnProp('header')\" :class=\"cx('columnTitle')\" v-bind=\"getColumnPT('columnTitle')\">{{ columnProp('header') }}</span>\n            <span v-if=\"columnProp('sortable')\" v-bind=\"getColumnPT('sort')\">\n                <component :is=\"(column.children && column.children.sorticon) || sortableColumnIcon\" :sorted=\"sortState.sorted\" :sortOrder=\"sortState.sortOrder\" :class=\"cx('sortIcon')\" v-bind=\"getColumnPT('sortIcon')\" />\n            </span>\n            <Badge v-if=\"isMultiSorted()\" :class=\"cx('pcSortBadge')\" v-bind=\"getColumnPT('pcSortBadge')\" :value=\"getMultiSortMetaIndex() + 1\" size=\"small\" />\n        </div>\n    </th>\n</template>\n\n<script>\nimport { getAttribute, getIndex, getNextElementSibling, getOuterWidth, getPreviousElementSibling } from '@primeuix/utils/dom';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport SortAltIcon from '@primevue/icons/sortalt';\nimport SortAmountDownIcon from '@primevue/icons/sortamountdown';\nimport SortAmountUpAltIcon from '@primevue/icons/sortamountupalt';\nimport Badge from 'primevue/badge';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'HeaderCell',\n    hostName: 'TreeTable',\n    extends: BaseComponent,\n    emits: ['column-click', 'column-resizestart'],\n    props: {\n        column: {\n            type: Object,\n            default: null\n        },\n        resizableColumns: {\n            type: Boolean,\n            default: false\n        },\n        sortField: {\n            type: [String, Function],\n            default: null\n        },\n        sortOrder: {\n            type: Number,\n            default: null\n        },\n        multiSortMeta: {\n            type: Array,\n            default: null\n        },\n        sortMode: {\n            type: String,\n            default: 'single'\n        },\n        index: {\n            type: Number,\n            default: null\n        }\n    },\n    data() {\n        return {\n            styleObject: {}\n        };\n    },\n    mounted() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    updated() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    methods: {\n        columnProp(prop) {\n            return getVNodeProp(this.column, prop);\n        },\n        getColumnPT(key) {\n            const columnMetaData = {\n                props: this.column.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    index: this.index,\n                    sorted: this.isColumnSorted(),\n                    frozen: this.$parentInstance.scrollable && this.columnProp('frozen'),\n                    resizable: this.resizableColumns,\n                    scrollable: this.$parentInstance.scrollable,\n                    showGridlines: this.$parentInstance.showGridlines,\n                    size: this.$parentInstance?.size\n                }\n            };\n\n            return mergeProps(this.ptm(`column.${key}`, { column: columnMetaData }), this.ptm(`column.${key}`, columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));\n        },\n        getColumnProp() {\n            return this.column.props && this.column.props.pt ? this.column.props.pt : undefined; //@todo:\n        },\n        updateStickyPosition() {\n            if (this.columnProp('frozen')) {\n                let align = this.columnProp('alignFrozen');\n\n                if (align === 'right') {\n                    let pos = 0;\n                    let next = getNextElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (next) {\n                        pos = getOuterWidth(next) + parseFloat(next.style['inset-inline-end'] || 0);\n                    }\n\n                    this.styleObject.insetInlineEnd = pos + 'px';\n                } else {\n                    let pos = 0;\n                    let prev = getPreviousElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (prev) {\n                        pos = getOuterWidth(prev) + parseFloat(prev.style['inset-inline-start'] || 0);\n                    }\n\n                    this.styleObject.insetInlineStart = pos + 'px';\n                }\n\n                let filterRow = this.$el.parentElement.nextElementSibling;\n\n                if (filterRow) {\n                    let index = getIndex(this.$el);\n\n                    filterRow.children[index].style['inset-inline-start'] = this.styleObject['inset-inline-start'];\n                    filterRow.children[index].style['inset-inline-end'] = this.styleObject['inset-inline-end'];\n                }\n            }\n        },\n        onClick(event) {\n            this.$emit('column-click', { originalEvent: event, column: this.column });\n        },\n        onKeyDown(event) {\n            if ((event.code === 'Enter' || event.code === 'NumpadEnter' || event.code === 'Space') && event.currentTarget.nodeName === 'TH' && getAttribute(event.currentTarget, 'data-p-sortable-column')) {\n                this.$emit('column-click', { originalEvent: event, column: this.column });\n\n                event.preventDefault();\n            }\n        },\n        onResizeStart(event) {\n            this.$emit('column-resizestart', event);\n        },\n        getMultiSortMetaIndex() {\n            let index = -1;\n\n            for (let i = 0; i < this.multiSortMeta.length; i++) {\n                let meta = this.multiSortMeta[i];\n\n                if (meta.field === this.columnProp('field') || meta.field === this.columnProp('sortField')) {\n                    index = i;\n                    break;\n                }\n            }\n\n            return index;\n        },\n        isMultiSorted() {\n            return this.columnProp('sortable') && this.getMultiSortMetaIndex() > -1;\n        },\n        isColumnSorted() {\n            return this.sortMode === 'single' ? this.sortField && (this.sortField === this.columnProp('field') || this.sortField === this.columnProp('sortField')) : this.isMultiSorted();\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.columnProp('headerClass'), this.columnProp('class'), this.cx('headerCell')];\n        },\n        containerStyle() {\n            let headerStyle = this.columnProp('headerStyle');\n            let columnStyle = this.columnProp('style');\n\n            return this.columnProp('frozen') ? [columnStyle, headerStyle, this.styleObject] : [columnStyle, headerStyle];\n        },\n        sortState() {\n            let sorted = false;\n            let sortOrder = null;\n\n            if (this.sortMode === 'single') {\n                sorted = this.sortField && (this.sortField === this.columnProp('field') || this.sortField === this.columnProp('sortField'));\n                sortOrder = sorted ? this.sortOrder : 0;\n            } else if (this.sortMode === 'multiple') {\n                let metaIndex = this.getMultiSortMetaIndex();\n\n                if (metaIndex > -1) {\n                    sorted = true;\n                    sortOrder = this.multiSortMeta[metaIndex].order;\n                }\n            }\n\n            return {\n                sorted,\n                sortOrder\n            };\n        },\n        sortableColumnIcon() {\n            const { sorted, sortOrder } = this.sortState;\n\n            if (!sorted) return SortAltIcon;\n            else if (sorted && sortOrder > 0) return SortAmountUpAltIcon;\n            else if (sorted && sortOrder < 0) return SortAmountDownIcon;\n\n            return null;\n        },\n        ariaSort() {\n            if (this.columnProp('sortable')) {\n                const { sorted, sortOrder } = this.sortState;\n\n                if (sorted && sortOrder < 0) return 'descending';\n                else if (sorted && sortOrder > 0) return 'ascending';\n                else return 'none';\n            } else {\n                return null;\n            }\n        }\n    },\n    components: {\n        Badge,\n        SortAltIcon: SortAltIcon,\n        SortAmountUpAltIcon: SortAmountUpAltIcon,\n        SortAmountDownIcon: SortAmountDownIcon\n    }\n};\n</script>\n", "<template>\n    <th\n        :class=\"containerClass\"\n        :style=\"[containerStyle]\"\n        @click=\"onClick\"\n        @keydown=\"onKeyDown\"\n        :tabindex=\"columnProp('sortable') ? '0' : null\"\n        :aria-sort=\"ariaSort\"\n        role=\"columnheader\"\n        v-bind=\"{ ...getColumnPT('root'), ...getColumnPT('headerCell') }\"\n        :data-p-sortable-column=\"columnProp('sortable')\"\n        :data-p-resizable-column=\"resizableColumns\"\n        :data-p-sorted=\"isColumnSorted()\"\n        :data-p-frozen-column=\"columnProp('frozen')\"\n    >\n        <span v-if=\"resizableColumns && !columnProp('frozen')\" :class=\"cx('columnResizer')\" @mousedown=\"onResizeStart\" v-bind=\"getColumnPT('columnResizer')\"></span>\n        <div :class=\"cx('columnHeaderContent')\" v-bind=\"getColumnPT('columnHeaderContent')\">\n            <component v-if=\"column.children && column.children.header\" :is=\"column.children.header\" :column=\"column\" />\n            <span v-if=\"columnProp('header')\" :class=\"cx('columnTitle')\" v-bind=\"getColumnPT('columnTitle')\">{{ columnProp('header') }}</span>\n            <span v-if=\"columnProp('sortable')\" v-bind=\"getColumnPT('sort')\">\n                <component :is=\"(column.children && column.children.sorticon) || sortableColumnIcon\" :sorted=\"sortState.sorted\" :sortOrder=\"sortState.sortOrder\" :class=\"cx('sortIcon')\" v-bind=\"getColumnPT('sortIcon')\" />\n            </span>\n            <Badge v-if=\"isMultiSorted()\" :class=\"cx('pcSortBadge')\" v-bind=\"getColumnPT('pcSortBadge')\" :value=\"getMultiSortMetaIndex() + 1\" size=\"small\" />\n        </div>\n    </th>\n</template>\n\n<script>\nimport { getAttribute, getIndex, getNextElementSibling, getOuterWidth, getPreviousElementSibling } from '@primeuix/utils/dom';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport SortAltIcon from '@primevue/icons/sortalt';\nimport SortAmountDownIcon from '@primevue/icons/sortamountdown';\nimport SortAmountUpAltIcon from '@primevue/icons/sortamountupalt';\nimport Badge from 'primevue/badge';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'HeaderCell',\n    hostName: 'TreeTable',\n    extends: BaseComponent,\n    emits: ['column-click', 'column-resizestart'],\n    props: {\n        column: {\n            type: Object,\n            default: null\n        },\n        resizableColumns: {\n            type: Boolean,\n            default: false\n        },\n        sortField: {\n            type: [String, Function],\n            default: null\n        },\n        sortOrder: {\n            type: Number,\n            default: null\n        },\n        multiSortMeta: {\n            type: Array,\n            default: null\n        },\n        sortMode: {\n            type: String,\n            default: 'single'\n        },\n        index: {\n            type: Number,\n            default: null\n        }\n    },\n    data() {\n        return {\n            styleObject: {}\n        };\n    },\n    mounted() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    updated() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    methods: {\n        columnProp(prop) {\n            return getVNodeProp(this.column, prop);\n        },\n        getColumnPT(key) {\n            const columnMetaData = {\n                props: this.column.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    index: this.index,\n                    sorted: this.isColumnSorted(),\n                    frozen: this.$parentInstance.scrollable && this.columnProp('frozen'),\n                    resizable: this.resizableColumns,\n                    scrollable: this.$parentInstance.scrollable,\n                    showGridlines: this.$parentInstance.showGridlines,\n                    size: this.$parentInstance?.size\n                }\n            };\n\n            return mergeProps(this.ptm(`column.${key}`, { column: columnMetaData }), this.ptm(`column.${key}`, columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));\n        },\n        getColumnProp() {\n            return this.column.props && this.column.props.pt ? this.column.props.pt : undefined; //@todo:\n        },\n        updateStickyPosition() {\n            if (this.columnProp('frozen')) {\n                let align = this.columnProp('alignFrozen');\n\n                if (align === 'right') {\n                    let pos = 0;\n                    let next = getNextElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (next) {\n                        pos = getOuterWidth(next) + parseFloat(next.style['inset-inline-end'] || 0);\n                    }\n\n                    this.styleObject.insetInlineEnd = pos + 'px';\n                } else {\n                    let pos = 0;\n                    let prev = getPreviousElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (prev) {\n                        pos = getOuterWidth(prev) + parseFloat(prev.style['inset-inline-start'] || 0);\n                    }\n\n                    this.styleObject.insetInlineStart = pos + 'px';\n                }\n\n                let filterRow = this.$el.parentElement.nextElementSibling;\n\n                if (filterRow) {\n                    let index = getIndex(this.$el);\n\n                    filterRow.children[index].style['inset-inline-start'] = this.styleObject['inset-inline-start'];\n                    filterRow.children[index].style['inset-inline-end'] = this.styleObject['inset-inline-end'];\n                }\n            }\n        },\n        onClick(event) {\n            this.$emit('column-click', { originalEvent: event, column: this.column });\n        },\n        onKeyDown(event) {\n            if ((event.code === 'Enter' || event.code === 'NumpadEnter' || event.code === 'Space') && event.currentTarget.nodeName === 'TH' && getAttribute(event.currentTarget, 'data-p-sortable-column')) {\n                this.$emit('column-click', { originalEvent: event, column: this.column });\n\n                event.preventDefault();\n            }\n        },\n        onResizeStart(event) {\n            this.$emit('column-resizestart', event);\n        },\n        getMultiSortMetaIndex() {\n            let index = -1;\n\n            for (let i = 0; i < this.multiSortMeta.length; i++) {\n                let meta = this.multiSortMeta[i];\n\n                if (meta.field === this.columnProp('field') || meta.field === this.columnProp('sortField')) {\n                    index = i;\n                    break;\n                }\n            }\n\n            return index;\n        },\n        isMultiSorted() {\n            return this.columnProp('sortable') && this.getMultiSortMetaIndex() > -1;\n        },\n        isColumnSorted() {\n            return this.sortMode === 'single' ? this.sortField && (this.sortField === this.columnProp('field') || this.sortField === this.columnProp('sortField')) : this.isMultiSorted();\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.columnProp('headerClass'), this.columnProp('class'), this.cx('headerCell')];\n        },\n        containerStyle() {\n            let headerStyle = this.columnProp('headerStyle');\n            let columnStyle = this.columnProp('style');\n\n            return this.columnProp('frozen') ? [columnStyle, headerStyle, this.styleObject] : [columnStyle, headerStyle];\n        },\n        sortState() {\n            let sorted = false;\n            let sortOrder = null;\n\n            if (this.sortMode === 'single') {\n                sorted = this.sortField && (this.sortField === this.columnProp('field') || this.sortField === this.columnProp('sortField'));\n                sortOrder = sorted ? this.sortOrder : 0;\n            } else if (this.sortMode === 'multiple') {\n                let metaIndex = this.getMultiSortMetaIndex();\n\n                if (metaIndex > -1) {\n                    sorted = true;\n                    sortOrder = this.multiSortMeta[metaIndex].order;\n                }\n            }\n\n            return {\n                sorted,\n                sortOrder\n            };\n        },\n        sortableColumnIcon() {\n            const { sorted, sortOrder } = this.sortState;\n\n            if (!sorted) return SortAltIcon;\n            else if (sorted && sortOrder > 0) return SortAmountUpAltIcon;\n            else if (sorted && sortOrder < 0) return SortAmountDownIcon;\n\n            return null;\n        },\n        ariaSort() {\n            if (this.columnProp('sortable')) {\n                const { sorted, sortOrder } = this.sortState;\n\n                if (sorted && sortOrder < 0) return 'descending';\n                else if (sorted && sortOrder > 0) return 'ascending';\n                else return 'none';\n            } else {\n                return null;\n            }\n        }\n    },\n    components: {\n        Badge,\n        SortAltIcon: SortAltIcon,\n        SortAmountUpAltIcon: SortAmountUpAltIcon,\n        SortAmountDownIcon: SortAmountDownIcon\n    }\n};\n</script>\n", "<template>\n    <td :style=\"containerStyle\" :class=\"containerClass\" role=\"cell\" v-bind=\"{ ...getColumnPT('root'), ...getColumnPT('bodyCell') }\" :data-p-frozen-column=\"columnProp('frozen')\">\n        <div :class=\"cx('bodyCellContent')\" v-bind=\"getColumnPT('bodyCellContent')\">\n            <button v-if=\"columnProp('expander')\" v-ripple type=\"button\" :class=\"cx('nodeToggleButton')\" @click=\"toggle\" :style=\"togglerStyle\" tabindex=\"-1\" v-bind=\"getColumnPT('nodeToggleButton')\" data-pc-group-section=\"rowactionbutton\">\n                <template v-if=\"node.loading && loadingMode === 'icon'\">\n                    <component v-if=\"templates['nodetoggleicon']\" :is=\"templates['nodetoggleicon']\" />\n                    <!-- TODO: Deprecated since v4.0-->\n                    <component v-if=\"templates['nodetogglericon']\" :is=\"templates['nodetogglericon']\" />\n                    <SpinnerIcon v-else spin v-bind=\"ptm('nodetoggleicon')\" />\n                </template>\n                <template v-else>\n                    <component v-if=\"column.children && column.children.rowtoggleicon\" :is=\"column.children.rowtoggleicon\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <component v-else-if=\"templates['nodetoggleicon']\" :is=\"templates['nodetoggleicon']\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <!-- TODO: Deprecated since v4.0-->\n                    <component v-else-if=\"column.children && column.children.rowtogglericon\" :is=\"column.children.rowtogglericon\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <component v-else-if=\"expanded\" :is=\"node.expandedIcon ? 'span' : 'ChevronDownIcon'\" :class=\"cx('nodeToggleIcon')\" v-bind=\"getColumnPT('nodeToggleIcon')\" />\n                    <component v-else :is=\"node.collapsedIcon ? 'span' : 'ChevronRightIcon'\" :class=\"cx('nodeToggleIcon')\" v-bind=\"getColumnPT('nodeToggleIcon')\" />\n                </template>\n            </button>\n            <Checkbox\n                v-if=\"checkboxSelectionMode && columnProp('expander')\"\n                :modelValue=\"checked\"\n                :binary=\"true\"\n                :class=\"cx('pcNodeCheckbox')\"\n                :disabled=\"node.selectable === false\"\n                @change=\"toggleCheckbox\"\n                :tabindex=\"-1\"\n                :indeterminate=\"partialChecked\"\n                :unstyled=\"unstyled\"\n                :pt=\"getColumnCheckboxPT('pcNodeCheckbox')\"\n                :data-p-partialchecked=\"partialChecked\"\n            >\n                <template #icon=\"slotProps\">\n                    <component v-if=\"templates['checkboxicon']\" :is=\"templates['checkboxicon']\" :checked=\"slotProps.checked\" :partialChecked=\"partialChecked\" :class=\"slotProps.class\" />\n                </template>\n            </Checkbox>\n            <component v-if=\"column.children && column.children.body\" :is=\"column.children.body\" :node=\"node\" :column=\"column\" />\n            <template v-else>{{ resolveFieldData(node.data, columnProp('field')) }}</template>\n        </div>\n    </td>\n</template>\n\n<script>\nimport { getNextElementSibling, getOuterWidth, getPreviousElementSibling } from '@primeuix/utils/dom';\nimport { resolveFieldData } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport CheckIcon from '@primevue/icons/check';\nimport ChevronDownIcon from '@primevue/icons/chevrondown';\nimport ChevronRightIcon from '@primevue/icons/chevronright';\nimport MinusIcon from '@primevue/icons/minus';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport Checkbox from 'primevue/checkbox';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'BodyCell',\n    hostName: 'TreeTable',\n    extends: BaseComponent,\n    emits: ['node-toggle', 'checkbox-toggle'],\n    props: {\n        node: {\n            type: Object,\n            default: null\n        },\n        column: {\n            type: Object,\n            default: null\n        },\n        level: {\n            type: Number,\n            default: 0\n        },\n        indentation: {\n            type: Number,\n            default: 1\n        },\n        leaf: {\n            type: Boolean,\n            default: false\n        },\n        expanded: {\n            type: Boolean,\n            default: false\n        },\n        selectionMode: {\n            type: String,\n            default: null\n        },\n        checked: {\n            type: Boolean,\n            default: false\n        },\n        partialChecked: {\n            type: Boolean,\n            default: false\n        },\n        templates: {\n            type: Object,\n            default: null\n        },\n        index: {\n            type: Number,\n            default: null\n        },\n        loadingMode: {\n            type: String,\n            default: 'mask'\n        }\n    },\n    data() {\n        return {\n            styleObject: {}\n        };\n    },\n    mounted() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    updated() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    methods: {\n        toggle() {\n            this.$emit('node-toggle', this.node);\n        },\n        columnProp(prop) {\n            return getVNodeProp(this.column, prop);\n        },\n        getColumnPT(key) {\n            const columnMetaData = {\n                props: this.column.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    index: this.index,\n                    selectable: this.$parentInstance.rowHover || this.$parentInstance.rowSelectionMode,\n                    selected: this.$parent.selected,\n                    frozen: this.columnProp('frozen'),\n                    scrollable: this.$parentInstance.scrollable,\n                    showGridlines: this.$parentInstance.showGridlines,\n                    size: this.$parentInstance?.size,\n                    node: this.node\n                }\n            };\n\n            return mergeProps(this.ptm(`column.${key}`, { column: columnMetaData }), this.ptm(`column.${key}`, columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));\n        },\n        getColumnProp() {\n            return this.column.props && this.column.props.pt ? this.column.props.pt : undefined; //@todo\n        },\n        getColumnCheckboxPT(key) {\n            const columnMetaData = {\n                props: this.column.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    checked: this.checked,\n                    partialChecked: this.partialChecked,\n                    node: this.node\n                }\n            };\n\n            return mergeProps(this.ptm(`column.${key}`, { column: columnMetaData }), this.ptm(`column.${key}`, columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));\n        },\n        updateStickyPosition() {\n            if (this.columnProp('frozen')) {\n                let align = this.columnProp('alignFrozen');\n\n                if (align === 'right') {\n                    let pos = 0;\n                    let next = getNextElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (next) {\n                        pos = getOuterWidth(next) + parseFloat(next.style['inset-inline-end'] || 0);\n                    }\n\n                    this.styleObject.insetInlineEnd = pos + 'px';\n                } else {\n                    let pos = 0;\n                    let prev = getPreviousElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (prev) {\n                        pos = getOuterWidth(prev) + parseFloat(prev.style['inset-inline-start'] || 0);\n                    }\n\n                    this.styleObject.insetInlineStart = pos + 'px';\n                }\n            }\n        },\n        resolveFieldData(rowData, field) {\n            return resolveFieldData(rowData, field);\n        },\n        toggleCheckbox() {\n            this.$emit('checkbox-toggle');\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.columnProp('bodyClass'), this.columnProp('class'), this.cx('bodyCell')];\n        },\n        containerStyle() {\n            let bodyStyle = this.columnProp('bodyStyle');\n            let columnStyle = this.columnProp('style');\n\n            return this.columnProp('frozen') ? [columnStyle, bodyStyle, this.styleObject] : [columnStyle, bodyStyle];\n        },\n        togglerStyle() {\n            return {\n                marginLeft: this.level * this.indentation + 'rem',\n                visibility: this.leaf ? 'hidden' : 'visible'\n            };\n        },\n        checkboxSelectionMode() {\n            return this.selectionMode === 'checkbox';\n        }\n    },\n    components: {\n        Checkbox,\n        ChevronRightIcon,\n        ChevronDownIcon,\n        CheckIcon,\n        MinusIcon,\n        SpinnerIcon\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <td :style=\"containerStyle\" :class=\"containerClass\" role=\"cell\" v-bind=\"{ ...getColumnPT('root'), ...getColumnPT('bodyCell') }\" :data-p-frozen-column=\"columnProp('frozen')\">\n        <div :class=\"cx('bodyCellContent')\" v-bind=\"getColumnPT('bodyCellContent')\">\n            <button v-if=\"columnProp('expander')\" v-ripple type=\"button\" :class=\"cx('nodeToggleButton')\" @click=\"toggle\" :style=\"togglerStyle\" tabindex=\"-1\" v-bind=\"getColumnPT('nodeToggleButton')\" data-pc-group-section=\"rowactionbutton\">\n                <template v-if=\"node.loading && loadingMode === 'icon'\">\n                    <component v-if=\"templates['nodetoggleicon']\" :is=\"templates['nodetoggleicon']\" />\n                    <!-- TODO: Deprecated since v4.0-->\n                    <component v-if=\"templates['nodetogglericon']\" :is=\"templates['nodetogglericon']\" />\n                    <SpinnerIcon v-else spin v-bind=\"ptm('nodetoggleicon')\" />\n                </template>\n                <template v-else>\n                    <component v-if=\"column.children && column.children.rowtoggleicon\" :is=\"column.children.rowtoggleicon\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <component v-else-if=\"templates['nodetoggleicon']\" :is=\"templates['nodetoggleicon']\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <!-- TODO: Deprecated since v4.0-->\n                    <component v-else-if=\"column.children && column.children.rowtogglericon\" :is=\"column.children.rowtogglericon\" :node=\"node\" :expanded=\"expanded\" :class=\"cx('nodeToggleIcon')\" />\n                    <component v-else-if=\"expanded\" :is=\"node.expandedIcon ? 'span' : 'ChevronDownIcon'\" :class=\"cx('nodeToggleIcon')\" v-bind=\"getColumnPT('nodeToggleIcon')\" />\n                    <component v-else :is=\"node.collapsedIcon ? 'span' : 'ChevronRightIcon'\" :class=\"cx('nodeToggleIcon')\" v-bind=\"getColumnPT('nodeToggleIcon')\" />\n                </template>\n            </button>\n            <Checkbox\n                v-if=\"checkboxSelectionMode && columnProp('expander')\"\n                :modelValue=\"checked\"\n                :binary=\"true\"\n                :class=\"cx('pcNodeCheckbox')\"\n                :disabled=\"node.selectable === false\"\n                @change=\"toggleCheckbox\"\n                :tabindex=\"-1\"\n                :indeterminate=\"partialChecked\"\n                :unstyled=\"unstyled\"\n                :pt=\"getColumnCheckboxPT('pcNodeCheckbox')\"\n                :data-p-partialchecked=\"partialChecked\"\n            >\n                <template #icon=\"slotProps\">\n                    <component v-if=\"templates['checkboxicon']\" :is=\"templates['checkboxicon']\" :checked=\"slotProps.checked\" :partialChecked=\"partialChecked\" :class=\"slotProps.class\" />\n                </template>\n            </Checkbox>\n            <component v-if=\"column.children && column.children.body\" :is=\"column.children.body\" :node=\"node\" :column=\"column\" />\n            <template v-else>{{ resolveFieldData(node.data, columnProp('field')) }}</template>\n        </div>\n    </td>\n</template>\n\n<script>\nimport { getNextElementSibling, getOuterWidth, getPreviousElementSibling } from '@primeuix/utils/dom';\nimport { resolveFieldData } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport CheckIcon from '@primevue/icons/check';\nimport ChevronDownIcon from '@primevue/icons/chevrondown';\nimport ChevronRightIcon from '@primevue/icons/chevronright';\nimport MinusIcon from '@primevue/icons/minus';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport Checkbox from 'primevue/checkbox';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\n\nexport default {\n    name: 'BodyCell',\n    hostName: 'TreeTable',\n    extends: BaseComponent,\n    emits: ['node-toggle', 'checkbox-toggle'],\n    props: {\n        node: {\n            type: Object,\n            default: null\n        },\n        column: {\n            type: Object,\n            default: null\n        },\n        level: {\n            type: Number,\n            default: 0\n        },\n        indentation: {\n            type: Number,\n            default: 1\n        },\n        leaf: {\n            type: Boolean,\n            default: false\n        },\n        expanded: {\n            type: Boolean,\n            default: false\n        },\n        selectionMode: {\n            type: String,\n            default: null\n        },\n        checked: {\n            type: Boolean,\n            default: false\n        },\n        partialChecked: {\n            type: Boolean,\n            default: false\n        },\n        templates: {\n            type: Object,\n            default: null\n        },\n        index: {\n            type: Number,\n            default: null\n        },\n        loadingMode: {\n            type: String,\n            default: 'mask'\n        }\n    },\n    data() {\n        return {\n            styleObject: {}\n        };\n    },\n    mounted() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    updated() {\n        if (this.columnProp('frozen')) {\n            this.updateStickyPosition();\n        }\n    },\n    methods: {\n        toggle() {\n            this.$emit('node-toggle', this.node);\n        },\n        columnProp(prop) {\n            return getVNodeProp(this.column, prop);\n        },\n        getColumnPT(key) {\n            const columnMetaData = {\n                props: this.column.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    index: this.index,\n                    selectable: this.$parentInstance.rowHover || this.$parentInstance.rowSelectionMode,\n                    selected: this.$parent.selected,\n                    frozen: this.columnProp('frozen'),\n                    scrollable: this.$parentInstance.scrollable,\n                    showGridlines: this.$parentInstance.showGridlines,\n                    size: this.$parentInstance?.size,\n                    node: this.node\n                }\n            };\n\n            return mergeProps(this.ptm(`column.${key}`, { column: columnMetaData }), this.ptm(`column.${key}`, columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));\n        },\n        getColumnProp() {\n            return this.column.props && this.column.props.pt ? this.column.props.pt : undefined; //@todo\n        },\n        getColumnCheckboxPT(key) {\n            const columnMetaData = {\n                props: this.column.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    checked: this.checked,\n                    partialChecked: this.partialChecked,\n                    node: this.node\n                }\n            };\n\n            return mergeProps(this.ptm(`column.${key}`, { column: columnMetaData }), this.ptm(`column.${key}`, columnMetaData), this.ptmo(this.getColumnProp(), key, columnMetaData));\n        },\n        updateStickyPosition() {\n            if (this.columnProp('frozen')) {\n                let align = this.columnProp('alignFrozen');\n\n                if (align === 'right') {\n                    let pos = 0;\n                    let next = getNextElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (next) {\n                        pos = getOuterWidth(next) + parseFloat(next.style['inset-inline-end'] || 0);\n                    }\n\n                    this.styleObject.insetInlineEnd = pos + 'px';\n                } else {\n                    let pos = 0;\n                    let prev = getPreviousElementSibling(this.$el, '[data-p-frozen-column=\"true\"]');\n\n                    if (prev) {\n                        pos = getOuterWidth(prev) + parseFloat(prev.style['inset-inline-start'] || 0);\n                    }\n\n                    this.styleObject.insetInlineStart = pos + 'px';\n                }\n            }\n        },\n        resolveFieldData(rowData, field) {\n            return resolveFieldData(rowData, field);\n        },\n        toggleCheckbox() {\n            this.$emit('checkbox-toggle');\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.columnProp('bodyClass'), this.columnProp('class'), this.cx('bodyCell')];\n        },\n        containerStyle() {\n            let bodyStyle = this.columnProp('bodyStyle');\n            let columnStyle = this.columnProp('style');\n\n            return this.columnProp('frozen') ? [columnStyle, bodyStyle, this.styleObject] : [columnStyle, bodyStyle];\n        },\n        togglerStyle() {\n            return {\n                marginLeft: this.level * this.indentation + 'rem',\n                visibility: this.leaf ? 'hidden' : 'visible'\n            };\n        },\n        checkboxSelectionMode() {\n            return this.selectionMode === 'checkbox';\n        }\n    },\n    components: {\n        Checkbox,\n        ChevronRightIcon,\n        ChevronDownIcon,\n        CheckIcon,\n        MinusIcon,\n        SpinnerIcon\n    },\n    directives: {\n        ripple: Ripple\n    }\n};\n</script>\n", "<template>\n    <tr\n        ref=\"node\"\n        :class=\"containerClass\"\n        :style=\"node.style\"\n        :tabindex=\"tabindex\"\n        role=\"row\"\n        :aria-expanded=\"node.children && node.children.length ? expanded : undefined\"\n        :aria-level=\"level + 1\"\n        :aria-setsize=\"ariaSetSize\"\n        :aria-posinset=\"ariaPosInset\"\n        :aria-selected=\"getAriaSelected\"\n        :aria-checked=\"checked || undefined\"\n        @click=\"onClick\"\n        @keydown=\"onKeyDown\"\n        @touchend=\"onTouchEnd\"\n        @contextmenu=\"onRowRightClick\"\n        v-bind=\"ptm('row', ptmOptions)\"\n        :data-p-selected=\"selected\"\n        :data-p-selected-contextmenu=\"contextMenuSelection && isSelectedWithContextMenu\"\n    >\n        <template v-for=\"(col, i) of columns\" :key=\"columnProp(col, 'columnKey') || columnProp(col, 'field') || i\">\n            <TTBodyCell\n                v-if=\"!columnProp(col, 'hidden')\"\n                :column=\"col\"\n                :node=\"node\"\n                :level=\"level\"\n                :leaf=\"leaf\"\n                :indentation=\"indentation\"\n                :expanded=\"expanded\"\n                :selectionMode=\"selectionMode\"\n                :checked=\"checked\"\n                :partialChecked=\"partialChecked\"\n                :templates=\"templates\"\n                @node-toggle=\"$emit('node-toggle', $event)\"\n                @checkbox-toggle=\"toggleCheckbox\"\n                :index=\"i\"\n                :loadingMode=\"loadingMode\"\n                :unstyled=\"unstyled\"\n                :pt=\"pt\"\n            ></TTBodyCell>\n        </template>\n    </tr>\n    <template v-if=\"expanded && node.children && node.children.length\">\n        <TreeTableRow\n            v-for=\"childNode of node.children\"\n            :key=\"nodeKey(childNode)\"\n            :dataKey=\"dataKey\"\n            :columns=\"columns\"\n            :node=\"childNode\"\n            :parentNode=\"node\"\n            :level=\"level + 1\"\n            :expandedKeys=\"expandedKeys\"\n            :selectionMode=\"selectionMode\"\n            :selectionKeys=\"selectionKeys\"\n            :contextMenu=\"contextMenu\"\n            :contextMenuSelection=\"contextMenuSelection\"\n            :indentation=\"indentation\"\n            :ariaPosInset=\"node.children.indexOf(childNode) + 1\"\n            :ariaSetSize=\"node.children.length\"\n            :templates=\"templates\"\n            @node-toggle=\"$emit('node-toggle', $event)\"\n            @node-click=\"$emit('node-click', $event)\"\n            @row-rightclick=\"$emit('row-rightclick', $event)\"\n            @checkbox-change=\"onCheckboxChange\"\n            :unstyled=\"unstyled\"\n            :pt=\"pt\"\n        />\n    </template>\n</template>\n\n<script>\nimport { find, findSingle, focus, getAttribute, isClickable } from '@primeuix/utils/dom';\nimport { equals, resolveFieldData } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport BodyCell from './BodyCell.vue';\n\nexport default {\n    name: 'TreeTableRow',\n    hostName: 'TreeTable',\n    extends: BaseComponent,\n    emits: ['node-click', 'node-toggle', 'checkbox-change', 'nodeClick', 'nodeToggle', 'checkboxChange', 'row-rightclick', 'rowRightclick'],\n    props: {\n        node: {\n            type: null,\n            default: null\n        },\n        dataKey: {\n            type: [String, Function],\n            default: 'key'\n        },\n        parentNode: {\n            type: null,\n            default: null\n        },\n        columns: {\n            type: null,\n            default: null\n        },\n        expandedKeys: {\n            type: null,\n            default: null\n        },\n        selectionKeys: {\n            type: null,\n            default: null\n        },\n        selectionMode: {\n            type: String,\n            default: null\n        },\n        level: {\n            type: Number,\n            default: 0\n        },\n        indentation: {\n            type: Number,\n            default: 1\n        },\n        tabindex: {\n            type: Number,\n            default: -1\n        },\n        ariaSetSize: {\n            type: Number,\n            default: null\n        },\n        ariaPosInset: {\n            type: Number,\n            default: null\n        },\n        loadingMode: {\n            type: String,\n            default: 'mask'\n        },\n        templates: {\n            type: Object,\n            default: null\n        },\n        contextMenu: {\n            type: Boolean,\n            default: false\n        },\n        contextMenuSelection: {\n            type: Object,\n            default: null\n        }\n    },\n    nodeTouched: false,\n    methods: {\n        columnProp(col, prop) {\n            return getVNodeProp(col, prop);\n        },\n        toggle() {\n            this.$emit('node-toggle', this.node);\n        },\n        onClick(event) {\n            if (isClickable(event.target) || getAttribute(event.target, 'data-pc-section') === 'nodetogglebutton' || getAttribute(event.target, 'data-pc-section') === 'nodetoggleicon' || event.target.tagName === 'path') {\n                return;\n            }\n\n            this.setTabIndexForSelectionMode(event, this.nodeTouched);\n\n            this.$emit('node-click', {\n                originalEvent: event,\n                nodeTouched: this.nodeTouched,\n                node: this.node\n            });\n            this.nodeTouched = false;\n        },\n        onRowRightClick(event) {\n            this.$emit('row-rightclick', {\n                originalEvent: event,\n                node: this.node\n            });\n        },\n        onTouchEnd() {\n            this.nodeTouched = true;\n        },\n        nodeKey(node) {\n            return resolveFieldData(node, this.dataKey);\n        },\n        onKeyDown(event, item) {\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeftKey(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRightKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                case 'Space':\n                    if (!isClickable(event.target)) {\n                        this.onEnterKey(event, item);\n                    }\n\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            const nextElementSibling = event.currentTarget.nextElementSibling;\n\n            nextElementSibling && this.focusRowChange(event.currentTarget, nextElementSibling);\n\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            const previousElementSibling = event.currentTarget.previousElementSibling;\n\n            previousElementSibling && this.focusRowChange(event.currentTarget, previousElementSibling);\n\n            event.preventDefault();\n        },\n        onArrowRightKey(event) {\n            const ishiddenIcon = findSingle(event.currentTarget, 'button').style.visibility === 'hidden';\n            const togglerElement = findSingle(this.$refs.node, '[data-pc-section=\"nodetogglebutton\"]');\n\n            if (ishiddenIcon) return;\n\n            !this.expanded && togglerElement.click();\n\n            this.$nextTick(() => {\n                this.onArrowDownKey(event);\n            });\n\n            event.preventDefault();\n        },\n        onArrowLeftKey(event) {\n            if (this.level === 0 && !this.expanded) {\n                return;\n            }\n\n            const currentTarget = event.currentTarget;\n            const ishiddenIcon = findSingle(currentTarget, 'button').style.visibility === 'hidden';\n            const togglerElement = findSingle(currentTarget, '[data-pc-section=\"nodetogglebutton\"]');\n\n            if (this.expanded && !ishiddenIcon) {\n                togglerElement.click();\n\n                return;\n            }\n\n            const target = this.findBeforeClickableNode(currentTarget);\n\n            target && this.focusRowChange(currentTarget, target);\n        },\n        onHomeKey(event) {\n            const findFirstElement = findSingle(event.currentTarget.parentElement, `tr[aria-level=\"${this.level + 1}\"]`);\n\n            findFirstElement && focus(findFirstElement);\n\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            const nodes = find(event.currentTarget.parentElement, `tr[aria-level=\"${this.level + 1}\"]`);\n            const findFirstElement = nodes[nodes.length - 1];\n\n            focus(findFirstElement);\n\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            event.preventDefault();\n            this.setTabIndexForSelectionMode(event, this.nodeTouched);\n\n            if (this.selectionMode === 'checkbox') {\n                this.toggleCheckbox();\n\n                return;\n            }\n\n            this.$emit('node-click', {\n                originalEvent: event,\n                nodeTouched: this.nodeTouched,\n                node: this.node\n            });\n\n            this.nodeTouched = false;\n        },\n        onTabKey() {\n            const rows = [...find(this.$refs.node.parentElement, 'tr')];\n            const hasSelectedRow = rows.some((row) => getAttribute(row, 'data-p-selected') || row.getAttribute('aria-checked') === 'true');\n\n            rows.forEach((row) => {\n                row.tabIndex = -1;\n            });\n\n            if (hasSelectedRow) {\n                const selectedNodes = rows.filter((node) => getAttribute(node, 'data-p-selected') || node.getAttribute('aria-checked') === 'true');\n\n                selectedNodes[0].tabIndex = 0;\n\n                return;\n            }\n\n            rows[0].tabIndex = 0;\n        },\n        focusRowChange(firstFocusableRow, currentFocusedRow) {\n            firstFocusableRow.tabIndex = '-1';\n            currentFocusedRow.tabIndex = '0';\n            focus(currentFocusedRow);\n        },\n        findBeforeClickableNode(node) {\n            const prevNode = node.previousElementSibling;\n\n            if (prevNode) {\n                const prevNodeButton = prevNode.querySelector('button');\n\n                if (prevNodeButton && prevNodeButton.style.visibility !== 'hidden') {\n                    return prevNode;\n                }\n\n                return this.findBeforeClickableNode(prevNode);\n            }\n\n            return null;\n        },\n        toggleCheckbox() {\n            let _selectionKeys = this.selectionKeys ? { ...this.selectionKeys } : {};\n            const _check = !this.checked;\n\n            this.propagateDown(this.node, _check, _selectionKeys);\n\n            this.$emit('checkbox-change', {\n                node: this.node,\n                check: _check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        propagateDown(node, check, selectionKeys) {\n            if (check) selectionKeys[this.nodeKey(node)] = { checked: true, partialChecked: false };\n            else delete selectionKeys[this.nodeKey(node)];\n\n            if (node.children && node.children.length) {\n                for (let child of node.children) {\n                    this.propagateDown(child, check, selectionKeys);\n                }\n            }\n        },\n        propagateUp(event) {\n            let check = event.check;\n            let _selectionKeys = { ...event.selectionKeys };\n            let checkedChildCount = 0;\n            let childPartialSelected = false;\n\n            for (let child of this.node.children) {\n                if (_selectionKeys[this.nodeKey(child)] && _selectionKeys[this.nodeKey(child)].checked) checkedChildCount++;\n                else if (_selectionKeys[this.nodeKey(child)] && _selectionKeys[this.nodeKey(child)].partialChecked) childPartialSelected = true;\n            }\n\n            if (check && checkedChildCount === this.node.children.length) {\n                _selectionKeys[this.nodeKey(this.node)] = { checked: true, partialChecked: false };\n            } else {\n                if (!check) {\n                    delete _selectionKeys[this.nodeKey(this.node)];\n                }\n\n                if (childPartialSelected || (checkedChildCount > 0 && checkedChildCount !== this.node.children.length)) _selectionKeys[this.nodeKey(this.node)] = { checked: false, partialChecked: true };\n                else _selectionKeys[this.nodeKey(this.node)] = { checked: false, partialChecked: false };\n            }\n\n            this.$emit('checkbox-change', {\n                node: event.node,\n                check: event.check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        onCheckboxChange(event) {\n            let check = event.check;\n            let _selectionKeys = { ...event.selectionKeys };\n            let checkedChildCount = 0;\n            let childPartialSelected = false;\n\n            for (let child of this.node.children) {\n                if (_selectionKeys[this.nodeKey(child)] && _selectionKeys[this.nodeKey(child)].checked) checkedChildCount++;\n                else if (_selectionKeys[this.nodeKey(child)] && _selectionKeys[this.nodeKey(child)].partialChecked) childPartialSelected = true;\n            }\n\n            if (check && checkedChildCount === this.node.children.length) {\n                _selectionKeys[this.nodeKey(this.node)] = { checked: true, partialChecked: false };\n            } else {\n                if (!check) {\n                    delete _selectionKeys[this.nodeKey(this.node)];\n                }\n\n                if (childPartialSelected || (checkedChildCount > 0 && checkedChildCount !== this.node.children.length)) _selectionKeys[this.nodeKey(this.node)] = { checked: false, partialChecked: true };\n                else _selectionKeys[this.nodeKey(this.node)] = { checked: false, partialChecked: false };\n            }\n\n            this.$emit('checkbox-change', {\n                node: event.node,\n                check: event.check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        setTabIndexForSelectionMode(event, nodeTouched) {\n            if (this.selectionMode !== null) {\n                const elements = [...find(this.$refs.node.parentElement, 'tr')];\n\n                event.currentTarget.tabIndex = nodeTouched === false ? -1 : 0;\n\n                if (elements.every((element) => element.tabIndex === -1)) {\n                    elements[0].tabIndex = 0;\n                }\n            }\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.node.styleClass, this.cx('row')];\n        },\n        expanded() {\n            return this.expandedKeys && this.expandedKeys[this.nodeKey(this.node)] === true;\n        },\n        leaf() {\n            return this.node.leaf === false ? false : !(this.node.children && this.node.children.length);\n        },\n        selected() {\n            return this.selectionMode && this.selectionKeys ? this.selectionKeys[this.nodeKey(this.node)] === true : false;\n        },\n        isSelectedWithContextMenu() {\n            if (this.node && this.contextMenuSelection) {\n                return equals(this.node, this.contextMenuSelection, this.dataKey);\n            }\n\n            return false;\n        },\n        checked() {\n            return this.selectionKeys ? this.selectionKeys[this.nodeKey(this.node)] && this.selectionKeys[this.nodeKey(this.node)].checked : false;\n        },\n        partialChecked() {\n            return this.selectionKeys ? this.selectionKeys[this.nodeKey(this.node)] && this.selectionKeys[this.nodeKey(this.node)].partialChecked : false;\n        },\n        getAriaSelected() {\n            return this.selectionMode === 'single' || this.selectionMode === 'multiple' ? this.selected : null;\n        },\n        ptmOptions() {\n            return {\n                context: {\n                    selectable: this.$parentInstance.rowHover || this.$parentInstance.rowSelectionMode,\n                    selected: this.selected,\n                    scrollable: this.$parentInstance.scrollable\n                }\n            };\n        }\n    },\n    components: {\n        TTBodyCell: BodyCell\n    }\n};\n</script>\n", "<template>\n    <tr\n        ref=\"node\"\n        :class=\"containerClass\"\n        :style=\"node.style\"\n        :tabindex=\"tabindex\"\n        role=\"row\"\n        :aria-expanded=\"node.children && node.children.length ? expanded : undefined\"\n        :aria-level=\"level + 1\"\n        :aria-setsize=\"ariaSetSize\"\n        :aria-posinset=\"ariaPosInset\"\n        :aria-selected=\"getAriaSelected\"\n        :aria-checked=\"checked || undefined\"\n        @click=\"onClick\"\n        @keydown=\"onKeyDown\"\n        @touchend=\"onTouchEnd\"\n        @contextmenu=\"onRowRightClick\"\n        v-bind=\"ptm('row', ptmOptions)\"\n        :data-p-selected=\"selected\"\n        :data-p-selected-contextmenu=\"contextMenuSelection && isSelectedWithContextMenu\"\n    >\n        <template v-for=\"(col, i) of columns\" :key=\"columnProp(col, 'columnKey') || columnProp(col, 'field') || i\">\n            <TTBodyCell\n                v-if=\"!columnProp(col, 'hidden')\"\n                :column=\"col\"\n                :node=\"node\"\n                :level=\"level\"\n                :leaf=\"leaf\"\n                :indentation=\"indentation\"\n                :expanded=\"expanded\"\n                :selectionMode=\"selectionMode\"\n                :checked=\"checked\"\n                :partialChecked=\"partialChecked\"\n                :templates=\"templates\"\n                @node-toggle=\"$emit('node-toggle', $event)\"\n                @checkbox-toggle=\"toggleCheckbox\"\n                :index=\"i\"\n                :loadingMode=\"loadingMode\"\n                :unstyled=\"unstyled\"\n                :pt=\"pt\"\n            ></TTBodyCell>\n        </template>\n    </tr>\n    <template v-if=\"expanded && node.children && node.children.length\">\n        <TreeTableRow\n            v-for=\"childNode of node.children\"\n            :key=\"nodeKey(childNode)\"\n            :dataKey=\"dataKey\"\n            :columns=\"columns\"\n            :node=\"childNode\"\n            :parentNode=\"node\"\n            :level=\"level + 1\"\n            :expandedKeys=\"expandedKeys\"\n            :selectionMode=\"selectionMode\"\n            :selectionKeys=\"selectionKeys\"\n            :contextMenu=\"contextMenu\"\n            :contextMenuSelection=\"contextMenuSelection\"\n            :indentation=\"indentation\"\n            :ariaPosInset=\"node.children.indexOf(childNode) + 1\"\n            :ariaSetSize=\"node.children.length\"\n            :templates=\"templates\"\n            @node-toggle=\"$emit('node-toggle', $event)\"\n            @node-click=\"$emit('node-click', $event)\"\n            @row-rightclick=\"$emit('row-rightclick', $event)\"\n            @checkbox-change=\"onCheckboxChange\"\n            :unstyled=\"unstyled\"\n            :pt=\"pt\"\n        />\n    </template>\n</template>\n\n<script>\nimport { find, findSingle, focus, getAttribute, isClickable } from '@primeuix/utils/dom';\nimport { equals, resolveFieldData } from '@primeuix/utils/object';\nimport BaseComponent from '@primevue/core/basecomponent';\nimport { getVNodeProp } from '@primevue/core/utils';\nimport BodyCell from './BodyCell.vue';\n\nexport default {\n    name: 'TreeTableRow',\n    hostName: 'TreeTable',\n    extends: BaseComponent,\n    emits: ['node-click', 'node-toggle', 'checkbox-change', 'nodeClick', 'nodeToggle', 'checkboxChange', 'row-rightclick', 'rowRightclick'],\n    props: {\n        node: {\n            type: null,\n            default: null\n        },\n        dataKey: {\n            type: [String, Function],\n            default: 'key'\n        },\n        parentNode: {\n            type: null,\n            default: null\n        },\n        columns: {\n            type: null,\n            default: null\n        },\n        expandedKeys: {\n            type: null,\n            default: null\n        },\n        selectionKeys: {\n            type: null,\n            default: null\n        },\n        selectionMode: {\n            type: String,\n            default: null\n        },\n        level: {\n            type: Number,\n            default: 0\n        },\n        indentation: {\n            type: Number,\n            default: 1\n        },\n        tabindex: {\n            type: Number,\n            default: -1\n        },\n        ariaSetSize: {\n            type: Number,\n            default: null\n        },\n        ariaPosInset: {\n            type: Number,\n            default: null\n        },\n        loadingMode: {\n            type: String,\n            default: 'mask'\n        },\n        templates: {\n            type: Object,\n            default: null\n        },\n        contextMenu: {\n            type: Boolean,\n            default: false\n        },\n        contextMenuSelection: {\n            type: Object,\n            default: null\n        }\n    },\n    nodeTouched: false,\n    methods: {\n        columnProp(col, prop) {\n            return getVNodeProp(col, prop);\n        },\n        toggle() {\n            this.$emit('node-toggle', this.node);\n        },\n        onClick(event) {\n            if (isClickable(event.target) || getAttribute(event.target, 'data-pc-section') === 'nodetogglebutton' || getAttribute(event.target, 'data-pc-section') === 'nodetoggleicon' || event.target.tagName === 'path') {\n                return;\n            }\n\n            this.setTabIndexForSelectionMode(event, this.nodeTouched);\n\n            this.$emit('node-click', {\n                originalEvent: event,\n                nodeTouched: this.nodeTouched,\n                node: this.node\n            });\n            this.nodeTouched = false;\n        },\n        onRowRightClick(event) {\n            this.$emit('row-rightclick', {\n                originalEvent: event,\n                node: this.node\n            });\n        },\n        onTouchEnd() {\n            this.nodeTouched = true;\n        },\n        nodeKey(node) {\n            return resolveFieldData(node, this.dataKey);\n        },\n        onKeyDown(event, item) {\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeftKey(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRightKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                case 'Space':\n                    if (!isClickable(event.target)) {\n                        this.onEnterKey(event, item);\n                    }\n\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onArrowDownKey(event) {\n            const nextElementSibling = event.currentTarget.nextElementSibling;\n\n            nextElementSibling && this.focusRowChange(event.currentTarget, nextElementSibling);\n\n            event.preventDefault();\n        },\n        onArrowUpKey(event) {\n            const previousElementSibling = event.currentTarget.previousElementSibling;\n\n            previousElementSibling && this.focusRowChange(event.currentTarget, previousElementSibling);\n\n            event.preventDefault();\n        },\n        onArrowRightKey(event) {\n            const ishiddenIcon = findSingle(event.currentTarget, 'button').style.visibility === 'hidden';\n            const togglerElement = findSingle(this.$refs.node, '[data-pc-section=\"nodetogglebutton\"]');\n\n            if (ishiddenIcon) return;\n\n            !this.expanded && togglerElement.click();\n\n            this.$nextTick(() => {\n                this.onArrowDownKey(event);\n            });\n\n            event.preventDefault();\n        },\n        onArrowLeftKey(event) {\n            if (this.level === 0 && !this.expanded) {\n                return;\n            }\n\n            const currentTarget = event.currentTarget;\n            const ishiddenIcon = findSingle(currentTarget, 'button').style.visibility === 'hidden';\n            const togglerElement = findSingle(currentTarget, '[data-pc-section=\"nodetogglebutton\"]');\n\n            if (this.expanded && !ishiddenIcon) {\n                togglerElement.click();\n\n                return;\n            }\n\n            const target = this.findBeforeClickableNode(currentTarget);\n\n            target && this.focusRowChange(currentTarget, target);\n        },\n        onHomeKey(event) {\n            const findFirstElement = findSingle(event.currentTarget.parentElement, `tr[aria-level=\"${this.level + 1}\"]`);\n\n            findFirstElement && focus(findFirstElement);\n\n            event.preventDefault();\n        },\n        onEndKey(event) {\n            const nodes = find(event.currentTarget.parentElement, `tr[aria-level=\"${this.level + 1}\"]`);\n            const findFirstElement = nodes[nodes.length - 1];\n\n            focus(findFirstElement);\n\n            event.preventDefault();\n        },\n        onEnterKey(event) {\n            event.preventDefault();\n            this.setTabIndexForSelectionMode(event, this.nodeTouched);\n\n            if (this.selectionMode === 'checkbox') {\n                this.toggleCheckbox();\n\n                return;\n            }\n\n            this.$emit('node-click', {\n                originalEvent: event,\n                nodeTouched: this.nodeTouched,\n                node: this.node\n            });\n\n            this.nodeTouched = false;\n        },\n        onTabKey() {\n            const rows = [...find(this.$refs.node.parentElement, 'tr')];\n            const hasSelectedRow = rows.some((row) => getAttribute(row, 'data-p-selected') || row.getAttribute('aria-checked') === 'true');\n\n            rows.forEach((row) => {\n                row.tabIndex = -1;\n            });\n\n            if (hasSelectedRow) {\n                const selectedNodes = rows.filter((node) => getAttribute(node, 'data-p-selected') || node.getAttribute('aria-checked') === 'true');\n\n                selectedNodes[0].tabIndex = 0;\n\n                return;\n            }\n\n            rows[0].tabIndex = 0;\n        },\n        focusRowChange(firstFocusableRow, currentFocusedRow) {\n            firstFocusableRow.tabIndex = '-1';\n            currentFocusedRow.tabIndex = '0';\n            focus(currentFocusedRow);\n        },\n        findBeforeClickableNode(node) {\n            const prevNode = node.previousElementSibling;\n\n            if (prevNode) {\n                const prevNodeButton = prevNode.querySelector('button');\n\n                if (prevNodeButton && prevNodeButton.style.visibility !== 'hidden') {\n                    return prevNode;\n                }\n\n                return this.findBeforeClickableNode(prevNode);\n            }\n\n            return null;\n        },\n        toggleCheckbox() {\n            let _selectionKeys = this.selectionKeys ? { ...this.selectionKeys } : {};\n            const _check = !this.checked;\n\n            this.propagateDown(this.node, _check, _selectionKeys);\n\n            this.$emit('checkbox-change', {\n                node: this.node,\n                check: _check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        propagateDown(node, check, selectionKeys) {\n            if (check) selectionKeys[this.nodeKey(node)] = { checked: true, partialChecked: false };\n            else delete selectionKeys[this.nodeKey(node)];\n\n            if (node.children && node.children.length) {\n                for (let child of node.children) {\n                    this.propagateDown(child, check, selectionKeys);\n                }\n            }\n        },\n        propagateUp(event) {\n            let check = event.check;\n            let _selectionKeys = { ...event.selectionKeys };\n            let checkedChildCount = 0;\n            let childPartialSelected = false;\n\n            for (let child of this.node.children) {\n                if (_selectionKeys[this.nodeKey(child)] && _selectionKeys[this.nodeKey(child)].checked) checkedChildCount++;\n                else if (_selectionKeys[this.nodeKey(child)] && _selectionKeys[this.nodeKey(child)].partialChecked) childPartialSelected = true;\n            }\n\n            if (check && checkedChildCount === this.node.children.length) {\n                _selectionKeys[this.nodeKey(this.node)] = { checked: true, partialChecked: false };\n            } else {\n                if (!check) {\n                    delete _selectionKeys[this.nodeKey(this.node)];\n                }\n\n                if (childPartialSelected || (checkedChildCount > 0 && checkedChildCount !== this.node.children.length)) _selectionKeys[this.nodeKey(this.node)] = { checked: false, partialChecked: true };\n                else _selectionKeys[this.nodeKey(this.node)] = { checked: false, partialChecked: false };\n            }\n\n            this.$emit('checkbox-change', {\n                node: event.node,\n                check: event.check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        onCheckboxChange(event) {\n            let check = event.check;\n            let _selectionKeys = { ...event.selectionKeys };\n            let checkedChildCount = 0;\n            let childPartialSelected = false;\n\n            for (let child of this.node.children) {\n                if (_selectionKeys[this.nodeKey(child)] && _selectionKeys[this.nodeKey(child)].checked) checkedChildCount++;\n                else if (_selectionKeys[this.nodeKey(child)] && _selectionKeys[this.nodeKey(child)].partialChecked) childPartialSelected = true;\n            }\n\n            if (check && checkedChildCount === this.node.children.length) {\n                _selectionKeys[this.nodeKey(this.node)] = { checked: true, partialChecked: false };\n            } else {\n                if (!check) {\n                    delete _selectionKeys[this.nodeKey(this.node)];\n                }\n\n                if (childPartialSelected || (checkedChildCount > 0 && checkedChildCount !== this.node.children.length)) _selectionKeys[this.nodeKey(this.node)] = { checked: false, partialChecked: true };\n                else _selectionKeys[this.nodeKey(this.node)] = { checked: false, partialChecked: false };\n            }\n\n            this.$emit('checkbox-change', {\n                node: event.node,\n                check: event.check,\n                selectionKeys: _selectionKeys\n            });\n        },\n        setTabIndexForSelectionMode(event, nodeTouched) {\n            if (this.selectionMode !== null) {\n                const elements = [...find(this.$refs.node.parentElement, 'tr')];\n\n                event.currentTarget.tabIndex = nodeTouched === false ? -1 : 0;\n\n                if (elements.every((element) => element.tabIndex === -1)) {\n                    elements[0].tabIndex = 0;\n                }\n            }\n        }\n    },\n    computed: {\n        containerClass() {\n            return [this.node.styleClass, this.cx('row')];\n        },\n        expanded() {\n            return this.expandedKeys && this.expandedKeys[this.nodeKey(this.node)] === true;\n        },\n        leaf() {\n            return this.node.leaf === false ? false : !(this.node.children && this.node.children.length);\n        },\n        selected() {\n            return this.selectionMode && this.selectionKeys ? this.selectionKeys[this.nodeKey(this.node)] === true : false;\n        },\n        isSelectedWithContextMenu() {\n            if (this.node && this.contextMenuSelection) {\n                return equals(this.node, this.contextMenuSelection, this.dataKey);\n            }\n\n            return false;\n        },\n        checked() {\n            return this.selectionKeys ? this.selectionKeys[this.nodeKey(this.node)] && this.selectionKeys[this.nodeKey(this.node)].checked : false;\n        },\n        partialChecked() {\n            return this.selectionKeys ? this.selectionKeys[this.nodeKey(this.node)] && this.selectionKeys[this.nodeKey(this.node)].partialChecked : false;\n        },\n        getAriaSelected() {\n            return this.selectionMode === 'single' || this.selectionMode === 'multiple' ? this.selected : null;\n        },\n        ptmOptions() {\n            return {\n                context: {\n                    selectable: this.$parentInstance.rowHover || this.$parentInstance.rowSelectionMode,\n                    selected: this.selected,\n                    scrollable: this.$parentInstance.scrollable\n                }\n            };\n        }\n    },\n    components: {\n        TTBodyCell: BodyCell\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" data-scrollselectors=\".p-treetable-scrollable-body\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n        <div v-if=\"loading && loadingMode === 'mask'\" :class=\"cx('loading')\" v-bind=\"ptm('loading')\">\n            <div :class=\"cx('mask')\" v-bind=\"ptm('mask')\">\n                <slot name=\"loadingicon\" :class=\"cx('loadingIcon')\">\n                    <component :is=\"loadingIcon ? 'span' : 'SpinnerIcon'\" spin :class=\"[cx('loadingIcon'), loadingIcon]\" v-bind=\"ptm('loadingIcon')\" />\n                </slot>\n            </div>\n        </div>\n        <div v-if=\"$slots.header\" :class=\"cx('header')\" v-bind=\"ptm('header')\">\n            <slot name=\"header\"></slot>\n        </div>\n        <TTPaginator\n            v-if=\"paginatorTop\"\n            :rows=\"d_rows\"\n            :first=\"d_first\"\n            :totalRecords=\"totalRecordsLength\"\n            :pageLinkSize=\"pageLinkSize\"\n            :template=\"paginatorTemplate\"\n            :rowsPerPageOptions=\"rowsPerPageOptions\"\n            :currentPageReportTemplate=\"currentPageReportTemplate\"\n            :class=\"cx('pcPaginator', { position: 'top' })\"\n            @page=\"onPage($event)\"\n            :alwaysShow=\"alwaysShowPaginator\"\n            :unstyled=\"unstyled\"\n            :pt=\"ptm('pcPaginator')\"\n        >\n            <template v-if=\"$slots.paginatorcontainer\" #container=\"slotProps\">\n                <slot\n                    name=\"paginatorcontainer\"\n                    :first=\"slotProps.first\"\n                    :last=\"slotProps.last\"\n                    :rows=\"slotProps.rows\"\n                    :page=\"slotProps.page\"\n                    :pageCount=\"slotProps.pageCount\"\n                    :totalRecords=\"slotProps.totalRecords\"\n                    :firstPageCallback=\"slotProps.firstPageCallback\"\n                    :lastPageCallback=\"slotProps.lastPageCallback\"\n                    :prevPageCallback=\"slotProps.prevPageCallback\"\n                    :nextPageCallback=\"slotProps.nextPageCallback\"\n                    :rowChangeCallback=\"slotProps.rowChangeCallback\"\n                ></slot>\n            </template>\n            <template v-if=\"$slots.paginatorstart\" #start>\n                <slot name=\"paginatorstart\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorend\" #end>\n                <slot name=\"paginatorend\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorfirstpagelinkicon\" #firstpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorfirstpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorprevpagelinkicon\" #prevpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorprevpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatornextpagelinkicon\" #nextpagelinkicon=\"slotProps\">\n                <slot name=\"paginatornextpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorlastpagelinkicon\" #lastpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorlastpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorjumptopagedropdownicon\" #jumptopagedropdownicon=\"slotProps\">\n                <slot name=\"paginatorjumptopagedropdownicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorrowsperpagedropdownicon\" #rowsperpagedropdownicon=\"slotProps\">\n                <slot name=\"paginatorrowsperpagedropdownicon\" :class=\"slotProps.class\"></slot>\n            </template>\n        </TTPaginator>\n        <div :class=\"cx('tableContainer')\" :style=\"[sx('tableContainer'), { maxHeight: scrollHeight }]\" v-bind=\"ptm('tableContainer')\">\n            <table ref=\"table\" role=\"table\" :class=\"[cx('table'), tableClass]\" :style=\"tableStyle\" v-bind=\"{ ...tableProps, ...ptm('table') }\">\n                <thead :class=\"cx('thead')\" :style=\"sx('thead')\" role=\"rowgroup\" v-bind=\"ptm('thead')\">\n                    <tr role=\"row\" v-bind=\"ptm('headerRow')\">\n                        <template v-for=\"(col, i) of columns\" :key=\"columnProp(col, 'columnKey') || columnProp(col, 'field') || i\">\n                            <TTHeaderCell\n                                v-if=\"!columnProp(col, 'hidden')\"\n                                :column=\"col\"\n                                :resizableColumns=\"resizableColumns\"\n                                :sortField=\"d_sortField\"\n                                :sortOrder=\"d_sortOrder\"\n                                :multiSortMeta=\"d_multiSortMeta\"\n                                :sortMode=\"sortMode\"\n                                @column-click=\"onColumnHeaderClick($event)\"\n                                @column-resizestart=\"onColumnResizeStart($event)\"\n                                :index=\"i\"\n                                :unstyled=\"unstyled\"\n                                :pt=\"pt\"\n                            ></TTHeaderCell>\n                        </template>\n                    </tr>\n                    <tr v-if=\"hasColumnFilter()\" v-bind=\"ptm('headerRow')\">\n                        <template v-for=\"(col, i) of columns\" :key=\"columnProp(col, 'columnKey') || columnProp(col, 'field') || i\">\n                            <th v-if=\"!columnProp(col, 'hidden')\" :class=\"getFilterColumnHeaderClass(col)\" :style=\"[columnProp(col, 'style'), columnProp(col, 'filterHeaderStyle')]\" v-bind=\"ptm('headerCell', ptHeaderCellOptions(col))\">\n                                <component v-if=\"col.children && col.children.filter\" :is=\"col.children.filter\" :column=\"col\" :index=\"i\" />\n                            </th>\n                        </template>\n                    </tr>\n                </thead>\n                <tbody :class=\"cx('tbody')\" role=\"rowgroup\" v-bind=\"ptm('tbody')\">\n                    <template v-if=\"!empty\">\n                        <TTRow\n                            v-for=\"(node, index) of dataToRender\"\n                            :key=\"nodeKey(node)\"\n                            :dataKey=\"dataKey\"\n                            :columns=\"columns\"\n                            :node=\"node\"\n                            :level=\"0\"\n                            :expandedKeys=\"d_expandedKeys\"\n                            :indentation=\"indentation\"\n                            :selectionMode=\"selectionMode\"\n                            :selectionKeys=\"selectionKeys\"\n                            :ariaSetSize=\"dataToRender.length\"\n                            :ariaPosInset=\"index + 1\"\n                            :tabindex=\"setTabindex(node, index)\"\n                            :loadingMode=\"loadingMode\"\n                            :contextMenu=\"contextMenu\"\n                            :contextMenuSelection=\"contextMenuSelection\"\n                            :templates=\"$slots\"\n                            @node-toggle=\"onNodeToggle\"\n                            @node-click=\"onNodeClick\"\n                            @checkbox-change=\"onCheckboxChange\"\n                            @row-rightclick=\"onRowRightClick($event)\"\n                            :unstyled=\"unstyled\"\n                            :pt=\"pt\"\n                        ></TTRow>\n                    </template>\n                    <tr v-else :class=\"cx('emptyMessage')\" v-bind=\"ptm('emptyMessage')\">\n                        <td :colspan=\"columns.length\" v-bind=\"ptm('emptyMessageCell')\">\n                            <slot name=\"empty\"></slot>\n                        </td>\n                    </tr>\n                </tbody>\n                <tfoot v-if=\"hasFooter\" :class=\"cx('tfoot')\" :style=\"sx('tfoot')\" role=\"rowgroup\" v-bind=\"ptm('tfoot')\">\n                    <tr role=\"row\" v-bind=\"ptm('footerRow')\">\n                        <template v-for=\"(col, i) of columns\" :key=\"columnProp(col, 'columnKey') || columnProp(col, 'field') || i\">\n                            <TTFooterCell v-if=\"!columnProp(col, 'hidden')\" :column=\"col\" :index=\"i\" :unstyled=\"unstyled\" :pt=\"pt\"></TTFooterCell>\n                        </template>\n                    </tr>\n                </tfoot>\n            </table>\n        </div>\n        <TTPaginator\n            v-if=\"paginatorBottom\"\n            :rows=\"d_rows\"\n            :first=\"d_first\"\n            :totalRecords=\"totalRecordsLength\"\n            :pageLinkSize=\"pageLinkSize\"\n            :template=\"paginatorTemplate\"\n            :rowsPerPageOptions=\"rowsPerPageOptions\"\n            :currentPageReportTemplate=\"currentPageReportTemplate\"\n            :class=\"cx('pcPaginator', { position: 'bottom' })\"\n            @page=\"onPage($event)\"\n            :alwaysShow=\"alwaysShowPaginator\"\n            :unstyled=\"unstyled\"\n            :pt=\"ptm('pcPaginator')\"\n        >\n            <template v-if=\"$slots.paginatorcontainer\" #container=\"slotProps\">\n                <slot\n                    name=\"paginatorcontainer\"\n                    :first=\"slotProps.first\"\n                    :last=\"slotProps.last\"\n                    :rows=\"slotProps.rows\"\n                    :page=\"slotProps.page\"\n                    :pageCount=\"slotProps.pageCount\"\n                    :totalRecords=\"slotProps.totalRecords\"\n                    :firstPageCallback=\"slotProps.firstPageCallback\"\n                    :lastPageCallback=\"slotProps.lastPageCallback\"\n                    :prevPageCallback=\"slotProps.prevPageCallback\"\n                    :nextPageCallback=\"slotProps.nextPageCallback\"\n                    :rowChangeCallback=\"slotProps.rowChangeCallback\"\n                ></slot>\n            </template>\n            <template v-if=\"$slots.paginatorstart\" #start>\n                <slot name=\"paginatorstart\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorend\" #end>\n                <slot name=\"paginatorend\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorfirstpagelinkicon\" #firstpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorfirstpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorprevpagelinkicon\" #prevpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorprevpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatornextpagelinkicon\" #nextpagelinkicon=\"slotProps\">\n                <slot name=\"paginatornextpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorlastpagelinkicon\" #lastpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorlastpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorjumptopagedropdownicon\" #jumptopagedropdownicon=\"slotProps\">\n                <slot name=\"paginatorjumptopagedropdownicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorrowsperpagedropdownicon\" #rowsperpagedropdownicon=\"slotProps\">\n                <slot name=\"paginatorrowsperpagedropdownicon\" :class=\"slotProps.class\"></slot>\n            </template>\n        </TTPaginator>\n        <div v-if=\"$slots.footer\" :class=\"cx('footer')\" v-bind=\"ptm('footer')\">\n            <slot name=\"footer\"></slot>\n        </div>\n        <div ref=\"resizeHelper\" :class=\"cx('columnResizeIndicator')\" style=\"display: none\" v-bind=\"ptm('columnResizeIndicator')\"></div>\n    </div>\n</template>\n\n<script>\nimport { addStyle, clearSelection, find, getAttribute, getIndex, getOffset, getOuterWidth, isRTL, setAttribute } from '@primeuix/utils/dom';\nimport { localeComparator, resolveFieldData, sort } from '@primeuix/utils/object';\nimport { FilterService } from '@primevue/core/api';\nimport { getVNodeProp, HelperSet } from '@primevue/core/utils';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport Paginator from 'primevue/paginator';\nimport BaseTreeTable from './BaseTreeTable.vue';\nimport FooterCell from './FooterCell.vue';\nimport HeaderCell from './HeaderCell.vue';\nimport TreeTableRow from './TreeTableRow.vue';\n\nexport default {\n    name: 'TreeTable',\n    extends: BaseTreeTable,\n    inheritAttrs: false,\n    emits: [\n        'node-expand',\n        'node-collapse',\n        'update:expandedKeys',\n        'update:selectionKeys',\n        'node-select',\n        'node-unselect',\n        'update:first',\n        'update:rows',\n        'page',\n        'update:sortField',\n        'update:sortOrder',\n        'update:multiSortMeta',\n        'sort',\n        'filter',\n        'column-resize-end',\n        'update:contextMenuSelection',\n        'row-contextmenu'\n    ],\n    provide() {\n        return {\n            $columns: this.d_columns\n        };\n    },\n    data() {\n        return {\n            d_expandedKeys: this.expandedKeys || {},\n            d_first: this.first,\n            d_rows: this.rows,\n            d_sortField: this.sortField,\n            d_sortOrder: this.sortOrder,\n            d_multiSortMeta: this.multiSortMeta ? [...this.multiSortMeta] : [],\n            hasASelectedNode: false,\n            d_columns: new HelperSet({ type: 'Column' })\n        };\n    },\n    documentColumnResizeListener: null,\n    documentColumnResizeEndListener: null,\n    lastResizeHelperX: null,\n    resizeColumnElement: null,\n    watch: {\n        expandedKeys(newValue) {\n            this.d_expandedKeys = newValue;\n        },\n        first(newValue) {\n            this.d_first = newValue;\n        },\n        rows(newValue) {\n            this.d_rows = newValue;\n        },\n        sortField(newValue) {\n            this.d_sortField = newValue;\n        },\n        sortOrder(newValue) {\n            this.d_sortOrder = newValue;\n        },\n        multiSortMeta(newValue) {\n            this.d_multiSortMeta = newValue;\n        }\n    },\n    beforeUnmount() {\n        this.destroyStyleElement();\n        this.d_columns.clear();\n    },\n    methods: {\n        columnProp(col, prop) {\n            return getVNodeProp(col, prop);\n        },\n        ptHeaderCellOptions(column) {\n            return {\n                context: {\n                    frozen: this.columnProp(column, 'frozen')\n                }\n            };\n        },\n        onNodeToggle(node) {\n            const key = this.nodeKey(node);\n\n            if (this.d_expandedKeys[key]) {\n                delete this.d_expandedKeys[key];\n                this.$emit('node-collapse', node);\n            } else {\n                this.d_expandedKeys[key] = true;\n                this.$emit('node-expand', node);\n            }\n\n            this.d_expandedKeys = { ...this.d_expandedKeys };\n            this.$emit('update:expandedKeys', this.d_expandedKeys);\n        },\n        onNodeClick(event) {\n            if (this.rowSelectionMode && event.node.selectable !== false) {\n                const metaSelection = event.nodeTouched ? false : this.metaKeySelection;\n                const _selectionKeys = metaSelection ? this.handleSelectionWithMetaKey(event) : this.handleSelectionWithoutMetaKey(event);\n\n                this.$emit('update:selectionKeys', _selectionKeys);\n            }\n        },\n        nodeKey(node) {\n            return resolveFieldData(node, this.dataKey);\n        },\n        handleSelectionWithMetaKey(event) {\n            const originalEvent = event.originalEvent;\n            const node = event.node;\n            const nodeKey = this.nodeKey(node);\n            const metaKey = originalEvent.metaKey || originalEvent.ctrlKey;\n            const selected = this.isNodeSelected(node);\n            let _selectionKeys;\n\n            if (selected && metaKey) {\n                if (this.isSingleSelectionMode()) {\n                    _selectionKeys = {};\n                } else {\n                    _selectionKeys = { ...this.selectionKeys };\n                    delete _selectionKeys[nodeKey];\n                }\n\n                this.$emit('node-unselect', node);\n            } else {\n                if (this.isSingleSelectionMode()) {\n                    _selectionKeys = {};\n                } else if (this.isMultipleSelectionMode()) {\n                    _selectionKeys = !metaKey ? {} : this.selectionKeys ? { ...this.selectionKeys } : {};\n                }\n\n                _selectionKeys[nodeKey] = true;\n                this.$emit('node-select', node);\n            }\n\n            return _selectionKeys;\n        },\n        handleSelectionWithoutMetaKey(event) {\n            const node = event.node;\n            const nodeKey = this.nodeKey(node);\n            const selected = this.isNodeSelected(node);\n            let _selectionKeys;\n\n            if (this.isSingleSelectionMode()) {\n                if (selected) {\n                    _selectionKeys = {};\n                    this.$emit('node-unselect', node);\n                } else {\n                    _selectionKeys = {};\n                    _selectionKeys[nodeKey] = true;\n                    this.$emit('node-select', node);\n                }\n            } else {\n                if (selected) {\n                    _selectionKeys = { ...this.selectionKeys };\n                    delete _selectionKeys[nodeKey];\n\n                    this.$emit('node-unselect', node);\n                } else {\n                    _selectionKeys = this.selectionKeys ? { ...this.selectionKeys } : {};\n                    _selectionKeys[nodeKey] = true;\n\n                    this.$emit('node-select', node);\n                }\n            }\n\n            return _selectionKeys;\n        },\n        onCheckboxChange(event) {\n            this.$emit('update:selectionKeys', event.selectionKeys);\n\n            if (event.check) this.$emit('node-select', event.node);\n            else this.$emit('node-unselect', event.node);\n        },\n        onRowRightClick(event) {\n            if (this.contextMenu) {\n                clearSelection();\n                event.originalEvent.target.focus();\n            }\n\n            this.$emit('update:contextMenuSelection', event.node);\n            this.$emit('row-contextmenu', event);\n        },\n        isSingleSelectionMode() {\n            return this.selectionMode === 'single';\n        },\n        isMultipleSelectionMode() {\n            return this.selectionMode === 'multiple';\n        },\n        onPage(event) {\n            this.d_first = event.first;\n            this.d_rows = event.rows;\n\n            let pageEvent = this.createLazyLoadEvent(event);\n\n            pageEvent.pageCount = event.pageCount;\n            pageEvent.page = event.page;\n\n            this.d_expandedKeys = {};\n            this.$emit('update:expandedKeys', this.d_expandedKeys);\n            this.$emit('update:first', this.d_first);\n            this.$emit('update:rows', this.d_rows);\n            this.$emit('page', pageEvent);\n        },\n        resetPage() {\n            this.d_first = 0;\n            this.$emit('update:first', this.d_first);\n        },\n        getFilterColumnHeaderClass(column) {\n            return [this.cx('headerCell', { column }), this.columnProp(column, 'filterHeaderClass')];\n        },\n        onColumnHeaderClick(e) {\n            let event = e.originalEvent;\n            let column = e.column;\n\n            if (this.columnProp(column, 'sortable')) {\n                const targetNode = event.target;\n                const columnField = this.columnProp(column, 'sortField') || this.columnProp(column, 'field');\n\n                if (\n                    getAttribute(targetNode, 'data-p-sortable-column') === true ||\n                    getAttribute(targetNode, 'data-pc-section') === 'columntitle' ||\n                    getAttribute(targetNode, 'data-pc-section') === 'columnheadercontent' ||\n                    getAttribute(targetNode, 'data-pc-section') === 'sorticon' ||\n                    getAttribute(targetNode.parentElement, 'data-pc-section') === 'sorticon' ||\n                    getAttribute(targetNode.parentElement.parentElement, 'data-pc-section') === 'sorticon' ||\n                    targetNode.closest('[data-p-sortable-column=\"true\"]')\n                ) {\n                    clearSelection();\n\n                    if (this.sortMode === 'single') {\n                        if (this.d_sortField === columnField) {\n                            if (this.removableSort && this.d_sortOrder * -1 === this.defaultSortOrder) {\n                                this.d_sortOrder = null;\n                                this.d_sortField = null;\n                            } else {\n                                this.d_sortOrder = this.d_sortOrder * -1;\n                            }\n                        } else {\n                            this.d_sortOrder = this.defaultSortOrder;\n                            this.d_sortField = columnField;\n                        }\n\n                        this.$emit('update:sortField', this.d_sortField);\n                        this.$emit('update:sortOrder', this.d_sortOrder);\n                        this.resetPage();\n                    } else if (this.sortMode === 'multiple') {\n                        let metaKey = event.metaKey || event.ctrlKey;\n\n                        if (!metaKey) {\n                            this.d_multiSortMeta = this.d_multiSortMeta.filter((meta) => meta.field === columnField);\n                        }\n\n                        this.addMultiSortField(columnField);\n                        this.$emit('update:multiSortMeta', this.d_multiSortMeta);\n                    }\n\n                    this.$emit('sort', this.createLazyLoadEvent(event));\n                }\n            }\n        },\n        addMultiSortField(field) {\n            let index = this.d_multiSortMeta.findIndex((meta) => meta.field === field);\n\n            if (index >= 0) {\n                if (this.removableSort && this.d_multiSortMeta[index].order * -1 === this.defaultSortOrder) this.d_multiSortMeta.splice(index, 1);\n                else this.d_multiSortMeta[index] = { field: field, order: this.d_multiSortMeta[index].order * -1 };\n            } else {\n                this.d_multiSortMeta.push({ field: field, order: this.defaultSortOrder });\n            }\n\n            this.d_multiSortMeta = [...this.d_multiSortMeta];\n        },\n        sortSingle(nodes) {\n            return this.sortNodesSingle(nodes);\n        },\n        sortNodesSingle(nodes) {\n            let _nodes = [...nodes];\n            const comparer = localeComparator();\n\n            _nodes.sort((node1, node2) => {\n                const value1 = resolveFieldData(node1.data, this.d_sortField);\n                const value2 = resolveFieldData(node2.data, this.d_sortField);\n\n                return sort(value1, value2, this.d_sortOrder, comparer);\n            });\n\n            return _nodes;\n        },\n        sortMultiple(nodes) {\n            return this.sortNodesMultiple(nodes);\n        },\n        sortNodesMultiple(nodes) {\n            let _nodes = [...nodes];\n\n            _nodes.sort((node1, node2) => {\n                return this.multisortField(node1, node2, 0);\n            });\n\n            return _nodes;\n        },\n        multisortField(node1, node2, index) {\n            const value1 = resolveFieldData(node1.data, this.d_multiSortMeta[index].field);\n            const value2 = resolveFieldData(node2.data, this.d_multiSortMeta[index].field);\n            const comparer = localeComparator();\n\n            if (value1 === value2) {\n                return this.d_multiSortMeta.length - 1 > index ? this.multisortField(node1, node2, index + 1) : 0;\n            }\n\n            return sort(value1, value2, this.d_multiSortMeta[index].order, comparer);\n        },\n        filter(value) {\n            let filteredNodes = [];\n            const strict = this.filterMode === 'strict';\n\n            for (let node of value) {\n                let copyNode = { ...node };\n                let localMatch = true;\n                let globalMatch = false;\n\n                for (let j = 0; j < this.columns.length; j++) {\n                    let col = this.columns[j];\n                    let filterField = this.columnProp(col, 'filterField') || this.columnProp(col, 'field');\n\n                    //local\n                    if (Object.prototype.hasOwnProperty.call(this.filters, filterField)) {\n                        let filterMatchMode = this.columnProp(col, 'filterMatchMode') || 'startsWith';\n                        let filterValue = this.filters[filterField];\n                        let filterConstraint = FilterService.filters[filterMatchMode];\n                        let paramsWithoutNode = { filterField, filterValue, filterConstraint, strict };\n\n                        if (\n                            (strict && !(this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode))) ||\n                            (!strict && !(this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode)))\n                        ) {\n                            localMatch = false;\n                        }\n\n                        if (!localMatch) {\n                            break;\n                        }\n                    }\n\n                    //global\n                    if (this.hasGlobalFilter() && !globalMatch) {\n                        let copyNodeForGlobal = { ...copyNode };\n                        let filterValue = this.filters['global'];\n                        let filterConstraint = FilterService.filters['contains'];\n                        let globalFilterParamsWithoutNode = { filterField, filterValue, filterConstraint, strict };\n\n                        if (\n                            (strict && (this.findFilteredNodes(copyNodeForGlobal, globalFilterParamsWithoutNode) || this.isFilterMatched(copyNodeForGlobal, globalFilterParamsWithoutNode))) ||\n                            (!strict && (this.isFilterMatched(copyNodeForGlobal, globalFilterParamsWithoutNode) || this.findFilteredNodes(copyNodeForGlobal, globalFilterParamsWithoutNode)))\n                        ) {\n                            globalMatch = true;\n                            copyNode = copyNodeForGlobal;\n                        }\n                    }\n                }\n\n                let matches = localMatch;\n\n                if (this.hasGlobalFilter()) {\n                    matches = localMatch && globalMatch;\n                }\n\n                if (matches) {\n                    filteredNodes.push(copyNode);\n                }\n            }\n\n            let filterEvent = this.createLazyLoadEvent(event);\n\n            filterEvent.filteredValue = filteredNodes;\n            this.$emit('filter', filterEvent);\n\n            return filteredNodes;\n        },\n        findFilteredNodes(node, paramsWithoutNode) {\n            if (node) {\n                let matched = false;\n\n                if (node.children) {\n                    let childNodes = [...node.children];\n\n                    node.children = [];\n\n                    for (let childNode of childNodes) {\n                        let copyChildNode = { ...childNode };\n\n                        if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n                            matched = true;\n                            node.children.push(copyChildNode);\n                        }\n                    }\n                }\n\n                if (matched) {\n                    return true;\n                }\n            }\n        },\n        isFilterMatched(node, { filterField, filterValue, filterConstraint, strict }) {\n            let matched = false;\n            let dataFieldValue = resolveFieldData(node.data, filterField);\n\n            if (filterConstraint(dataFieldValue, filterValue, this.filterLocale)) {\n                matched = true;\n            }\n\n            if (!matched || (strict && !this.isNodeLeaf(node))) {\n                matched = this.findFilteredNodes(node, { filterField, filterValue, filterConstraint, strict }) || matched;\n            }\n\n            return matched;\n        },\n        isNodeSelected(node) {\n            return this.selectionMode && this.selectionKeys ? this.selectionKeys[this.nodeKey(node)] === true : false;\n        },\n        isNodeLeaf(node) {\n            return node.leaf === false ? false : !(node.children && node.children.length);\n        },\n        createLazyLoadEvent(event) {\n            let filterMatchModes;\n\n            if (this.hasFilters()) {\n                filterMatchModes = {};\n                this.columns.forEach((col) => {\n                    if (this.columnProp(col, 'field')) {\n                        filterMatchModes[col.props.field] = this.columnProp(col, 'filterMatchMode');\n                    }\n                });\n            }\n\n            return {\n                originalEvent: event,\n                first: this.d_first,\n                rows: this.d_rows,\n                sortField: this.d_sortField,\n                sortOrder: this.d_sortOrder,\n                multiSortMeta: this.d_multiSortMeta,\n                filters: this.filters,\n                filterMatchModes: filterMatchModes\n            };\n        },\n        onColumnResizeStart(event) {\n            let containerLeft = getOffset(this.$el).left;\n\n            this.resizeColumnElement = event.target.parentElement;\n            this.columnResizing = true;\n            this.lastResizeHelperX = event.pageX - containerLeft + this.$el.scrollLeft;\n\n            this.bindColumnResizeEvents();\n        },\n        onColumnResize(event) {\n            let containerLeft = getOffset(this.$el).left;\n\n            this.$el.setAttribute('data-p-unselectable-text', 'true');\n            !this.isUnstyled && addStyle(this.$el, { 'user-select': 'none' });\n            this.$refs.resizeHelper.style.height = this.$el.offsetHeight + 'px';\n            this.$refs.resizeHelper.style.top = 0 + 'px';\n            this.$refs.resizeHelper.style.left = event.pageX - containerLeft + this.$el.scrollLeft + 'px';\n\n            this.$refs.resizeHelper.style.display = 'block';\n        },\n        onColumnResizeEnd() {\n            let delta = isRTL(this.$el) ? this.lastResizeHelperX - this.$refs.resizeHelper.offsetLeft : this.$refs.resizeHelper.offsetLeft - this.lastResizeHelperX;\n            let columnWidth = this.resizeColumnElement.offsetWidth;\n            let newColumnWidth = columnWidth + delta;\n            let minWidth = this.resizeColumnElement.style.minWidth || 15;\n\n            if (columnWidth + delta > parseInt(minWidth, 10)) {\n                if (this.columnResizeMode === 'fit') {\n                    let nextColumn = this.resizeColumnElement.nextElementSibling;\n                    let nextColumnWidth = nextColumn.offsetWidth - delta;\n\n                    if (newColumnWidth > 15 && nextColumnWidth > 15) {\n                        this.resizeTableCells(newColumnWidth, nextColumnWidth);\n                    }\n                } else if (this.columnResizeMode === 'expand') {\n                    const tableWidth = this.$refs.table.offsetWidth + delta + 'px';\n\n                    const updateTableWidth = (el) => {\n                        !!el && (el.style.width = el.style.minWidth = tableWidth);\n                    };\n\n                    // Reasoning: resize table cells before updating the table width so that it can use existing computed cell widths and adjust only the one column.\n                    this.resizeTableCells(newColumnWidth);\n                    updateTableWidth(this.$refs.table);\n                }\n\n                this.$emit('column-resize-end', {\n                    element: this.resizeColumnElement,\n                    delta: delta\n                });\n            }\n\n            this.$refs.resizeHelper.style.display = 'none';\n            this.resizeColumn = null;\n            this.$el.removeAttribute('data-p-unselectable-text');\n            !this.isUnstyled && (this.$el.style['user-select'] = '');\n\n            this.unbindColumnResizeEvents();\n        },\n        resizeTableCells(newColumnWidth, nextColumnWidth) {\n            let colIndex = getIndex(this.resizeColumnElement);\n            let widths = [];\n            let headers = find(this.$refs.table, 'thead[data-pc-section=\"thead\"] > tr > th');\n\n            headers.forEach((header) => widths.push(getOuterWidth(header)));\n\n            this.destroyStyleElement();\n            this.createStyleElement();\n\n            let innerHTML = '';\n            let selector = `[data-pc-name=\"treetable\"][${this.$attrSelector}] > [data-pc-section=\"tablecontainer\"] > table[data-pc-section=\"table\"]`;\n\n            widths.forEach((width, index) => {\n                let colWidth = index === colIndex ? newColumnWidth : nextColumnWidth && index === colIndex + 1 ? nextColumnWidth : width;\n                let style = `width: ${colWidth}px !important; max-width: ${colWidth}px !important`;\n\n                innerHTML += `\n                    ${selector} > thead[data-pc-section=\"thead\"] > tr > th:nth-child(${index + 1}),\n                    ${selector} > tbody[data-pc-section=\"tbody\"] > tr > td:nth-child(${index + 1}),\n                    ${selector} > tfoot[data-pc-section=\"tfoot\"] > tr > td:nth-child(${index + 1}) {\n                        ${style}\n                    }\n                `;\n            });\n\n            this.styleElement.innerHTML = innerHTML;\n        },\n        bindColumnResizeEvents() {\n            if (!this.documentColumnResizeListener) {\n                this.documentColumnResizeListener = document.addEventListener('mousemove', (event) => {\n                    if (this.columnResizing) {\n                        this.onColumnResize(event);\n                    }\n                });\n            }\n\n            if (!this.documentColumnResizeEndListener) {\n                this.documentColumnResizeEndListener = document.addEventListener('mouseup', () => {\n                    if (this.columnResizing) {\n                        this.columnResizing = false;\n                        this.onColumnResizeEnd();\n                    }\n                });\n            }\n        },\n        unbindColumnResizeEvents() {\n            if (this.documentColumnResizeListener) {\n                document.removeEventListener('document', this.documentColumnResizeListener);\n                this.documentColumnResizeListener = null;\n            }\n\n            if (this.documentColumnResizeEndListener) {\n                document.removeEventListener('document', this.documentColumnResizeEndListener);\n                this.documentColumnResizeEndListener = null;\n            }\n        },\n        onColumnKeyDown(event, col) {\n            if ((event.code === 'Enter' || event.code === 'NumpadEnter') && event.currentTarget.nodeName === 'TH' && getAttribute(event.currentTarget, 'data-p-sortable-column')) {\n                this.onColumnHeaderClick(event, col);\n            }\n        },\n        hasColumnFilter() {\n            if (this.columns) {\n                for (let col of this.columns) {\n                    if (col.children && col.children.filter) {\n                        return true;\n                    }\n                }\n            }\n\n            return false;\n        },\n        hasFilters() {\n            return this.filters && Object.keys(this.filters).length > 0 && this.filters.constructor === Object;\n        },\n        hasGlobalFilter() {\n            return this.filters && Object.prototype.hasOwnProperty.call(this.filters, 'global');\n        },\n        getItemLabel(node) {\n            return node.data.name;\n        },\n        createStyleElement() {\n            this.styleElement = document.createElement('style');\n            this.styleElement.type = 'text/css';\n            setAttribute(this.styleElement, 'nonce', this.$primevue?.config?.csp?.nonce);\n            document.head.appendChild(this.styleElement);\n        },\n        destroyStyleElement() {\n            if (this.styleElement) {\n                document.head.removeChild(this.styleElement);\n                this.styleElement = null;\n            }\n        },\n        setTabindex(node, index) {\n            if (this.isNodeSelected(node)) {\n                this.hasASelectedNode = true;\n\n                return 0;\n            }\n\n            if (this.selectionMode) {\n                if (!this.isNodeSelected(node) && index === 0 && !this.hasASelectedNode) return 0;\n            } else if (!this.selectionMode && index === 0) {\n                return 0;\n            }\n\n            return -1;\n        }\n    },\n    computed: {\n        columns() {\n            return this.d_columns.get(this);\n        },\n        processedData() {\n            if (this.lazy) {\n                return this.value;\n            } else {\n                if (this.value && this.value.length) {\n                    let data = this.value;\n\n                    if (this.sorted) {\n                        if (this.sortMode === 'single') data = this.sortSingle(data);\n                        else if (this.sortMode === 'multiple') data = this.sortMultiple(data);\n                    }\n\n                    if (this.hasFilters()) {\n                        data = this.filter(data);\n                    }\n\n                    return data;\n                } else {\n                    return null;\n                }\n            }\n        },\n        dataToRender() {\n            const data = this.processedData;\n\n            if (this.paginator) {\n                const first = this.lazy ? 0 : this.d_first;\n\n                return data.slice(first, first + this.d_rows);\n            } else {\n                return data;\n            }\n        },\n        empty() {\n            const data = this.processedData;\n\n            return !data || data.length === 0;\n        },\n        sorted() {\n            return this.d_sortField || (this.d_multiSortMeta && this.d_multiSortMeta.length > 0);\n        },\n        hasFooter() {\n            let hasFooter = false;\n\n            for (let col of this.columns) {\n                if (this.columnProp(col, 'footer') || (col.children && col.children.footer)) {\n                    hasFooter = true;\n                    break;\n                }\n            }\n\n            return hasFooter;\n        },\n        paginatorTop() {\n            return this.paginator && (this.paginatorPosition !== 'bottom' || this.paginatorPosition === 'both');\n        },\n        paginatorBottom() {\n            return this.paginator && (this.paginatorPosition !== 'top' || this.paginatorPosition === 'both');\n        },\n        singleSelectionMode() {\n            return this.selectionMode && this.selectionMode === 'single';\n        },\n        multipleSelectionMode() {\n            return this.selectionMode && this.selectionMode === 'multiple';\n        },\n        rowSelectionMode() {\n            return this.singleSelectionMode || this.multipleSelectionMode;\n        },\n        totalRecordsLength() {\n            if (this.lazy) {\n                return this.totalRecords;\n            } else {\n                const data = this.processedData;\n\n                return data ? data.length : 0;\n            }\n        }\n    },\n    components: {\n        TTRow: TreeTableRow,\n        TTPaginator: Paginator,\n        TTHeaderCell: HeaderCell,\n        TTFooterCell: FooterCell,\n        SpinnerIcon: SpinnerIcon\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" data-scrollselectors=\".p-treetable-scrollable-body\" v-bind=\"ptmi('root')\">\n        <slot></slot>\n        <div v-if=\"loading && loadingMode === 'mask'\" :class=\"cx('loading')\" v-bind=\"ptm('loading')\">\n            <div :class=\"cx('mask')\" v-bind=\"ptm('mask')\">\n                <slot name=\"loadingicon\" :class=\"cx('loadingIcon')\">\n                    <component :is=\"loadingIcon ? 'span' : 'SpinnerIcon'\" spin :class=\"[cx('loadingIcon'), loadingIcon]\" v-bind=\"ptm('loadingIcon')\" />\n                </slot>\n            </div>\n        </div>\n        <div v-if=\"$slots.header\" :class=\"cx('header')\" v-bind=\"ptm('header')\">\n            <slot name=\"header\"></slot>\n        </div>\n        <TTPaginator\n            v-if=\"paginatorTop\"\n            :rows=\"d_rows\"\n            :first=\"d_first\"\n            :totalRecords=\"totalRecordsLength\"\n            :pageLinkSize=\"pageLinkSize\"\n            :template=\"paginatorTemplate\"\n            :rowsPerPageOptions=\"rowsPerPageOptions\"\n            :currentPageReportTemplate=\"currentPageReportTemplate\"\n            :class=\"cx('pcPaginator', { position: 'top' })\"\n            @page=\"onPage($event)\"\n            :alwaysShow=\"alwaysShowPaginator\"\n            :unstyled=\"unstyled\"\n            :pt=\"ptm('pcPaginator')\"\n        >\n            <template v-if=\"$slots.paginatorcontainer\" #container=\"slotProps\">\n                <slot\n                    name=\"paginatorcontainer\"\n                    :first=\"slotProps.first\"\n                    :last=\"slotProps.last\"\n                    :rows=\"slotProps.rows\"\n                    :page=\"slotProps.page\"\n                    :pageCount=\"slotProps.pageCount\"\n                    :totalRecords=\"slotProps.totalRecords\"\n                    :firstPageCallback=\"slotProps.firstPageCallback\"\n                    :lastPageCallback=\"slotProps.lastPageCallback\"\n                    :prevPageCallback=\"slotProps.prevPageCallback\"\n                    :nextPageCallback=\"slotProps.nextPageCallback\"\n                    :rowChangeCallback=\"slotProps.rowChangeCallback\"\n                ></slot>\n            </template>\n            <template v-if=\"$slots.paginatorstart\" #start>\n                <slot name=\"paginatorstart\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorend\" #end>\n                <slot name=\"paginatorend\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorfirstpagelinkicon\" #firstpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorfirstpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorprevpagelinkicon\" #prevpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorprevpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatornextpagelinkicon\" #nextpagelinkicon=\"slotProps\">\n                <slot name=\"paginatornextpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorlastpagelinkicon\" #lastpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorlastpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorjumptopagedropdownicon\" #jumptopagedropdownicon=\"slotProps\">\n                <slot name=\"paginatorjumptopagedropdownicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorrowsperpagedropdownicon\" #rowsperpagedropdownicon=\"slotProps\">\n                <slot name=\"paginatorrowsperpagedropdownicon\" :class=\"slotProps.class\"></slot>\n            </template>\n        </TTPaginator>\n        <div :class=\"cx('tableContainer')\" :style=\"[sx('tableContainer'), { maxHeight: scrollHeight }]\" v-bind=\"ptm('tableContainer')\">\n            <table ref=\"table\" role=\"table\" :class=\"[cx('table'), tableClass]\" :style=\"tableStyle\" v-bind=\"{ ...tableProps, ...ptm('table') }\">\n                <thead :class=\"cx('thead')\" :style=\"sx('thead')\" role=\"rowgroup\" v-bind=\"ptm('thead')\">\n                    <tr role=\"row\" v-bind=\"ptm('headerRow')\">\n                        <template v-for=\"(col, i) of columns\" :key=\"columnProp(col, 'columnKey') || columnProp(col, 'field') || i\">\n                            <TTHeaderCell\n                                v-if=\"!columnProp(col, 'hidden')\"\n                                :column=\"col\"\n                                :resizableColumns=\"resizableColumns\"\n                                :sortField=\"d_sortField\"\n                                :sortOrder=\"d_sortOrder\"\n                                :multiSortMeta=\"d_multiSortMeta\"\n                                :sortMode=\"sortMode\"\n                                @column-click=\"onColumnHeaderClick($event)\"\n                                @column-resizestart=\"onColumnResizeStart($event)\"\n                                :index=\"i\"\n                                :unstyled=\"unstyled\"\n                                :pt=\"pt\"\n                            ></TTHeaderCell>\n                        </template>\n                    </tr>\n                    <tr v-if=\"hasColumnFilter()\" v-bind=\"ptm('headerRow')\">\n                        <template v-for=\"(col, i) of columns\" :key=\"columnProp(col, 'columnKey') || columnProp(col, 'field') || i\">\n                            <th v-if=\"!columnProp(col, 'hidden')\" :class=\"getFilterColumnHeaderClass(col)\" :style=\"[columnProp(col, 'style'), columnProp(col, 'filterHeaderStyle')]\" v-bind=\"ptm('headerCell', ptHeaderCellOptions(col))\">\n                                <component v-if=\"col.children && col.children.filter\" :is=\"col.children.filter\" :column=\"col\" :index=\"i\" />\n                            </th>\n                        </template>\n                    </tr>\n                </thead>\n                <tbody :class=\"cx('tbody')\" role=\"rowgroup\" v-bind=\"ptm('tbody')\">\n                    <template v-if=\"!empty\">\n                        <TTRow\n                            v-for=\"(node, index) of dataToRender\"\n                            :key=\"nodeKey(node)\"\n                            :dataKey=\"dataKey\"\n                            :columns=\"columns\"\n                            :node=\"node\"\n                            :level=\"0\"\n                            :expandedKeys=\"d_expandedKeys\"\n                            :indentation=\"indentation\"\n                            :selectionMode=\"selectionMode\"\n                            :selectionKeys=\"selectionKeys\"\n                            :ariaSetSize=\"dataToRender.length\"\n                            :ariaPosInset=\"index + 1\"\n                            :tabindex=\"setTabindex(node, index)\"\n                            :loadingMode=\"loadingMode\"\n                            :contextMenu=\"contextMenu\"\n                            :contextMenuSelection=\"contextMenuSelection\"\n                            :templates=\"$slots\"\n                            @node-toggle=\"onNodeToggle\"\n                            @node-click=\"onNodeClick\"\n                            @checkbox-change=\"onCheckboxChange\"\n                            @row-rightclick=\"onRowRightClick($event)\"\n                            :unstyled=\"unstyled\"\n                            :pt=\"pt\"\n                        ></TTRow>\n                    </template>\n                    <tr v-else :class=\"cx('emptyMessage')\" v-bind=\"ptm('emptyMessage')\">\n                        <td :colspan=\"columns.length\" v-bind=\"ptm('emptyMessageCell')\">\n                            <slot name=\"empty\"></slot>\n                        </td>\n                    </tr>\n                </tbody>\n                <tfoot v-if=\"hasFooter\" :class=\"cx('tfoot')\" :style=\"sx('tfoot')\" role=\"rowgroup\" v-bind=\"ptm('tfoot')\">\n                    <tr role=\"row\" v-bind=\"ptm('footerRow')\">\n                        <template v-for=\"(col, i) of columns\" :key=\"columnProp(col, 'columnKey') || columnProp(col, 'field') || i\">\n                            <TTFooterCell v-if=\"!columnProp(col, 'hidden')\" :column=\"col\" :index=\"i\" :unstyled=\"unstyled\" :pt=\"pt\"></TTFooterCell>\n                        </template>\n                    </tr>\n                </tfoot>\n            </table>\n        </div>\n        <TTPaginator\n            v-if=\"paginatorBottom\"\n            :rows=\"d_rows\"\n            :first=\"d_first\"\n            :totalRecords=\"totalRecordsLength\"\n            :pageLinkSize=\"pageLinkSize\"\n            :template=\"paginatorTemplate\"\n            :rowsPerPageOptions=\"rowsPerPageOptions\"\n            :currentPageReportTemplate=\"currentPageReportTemplate\"\n            :class=\"cx('pcPaginator', { position: 'bottom' })\"\n            @page=\"onPage($event)\"\n            :alwaysShow=\"alwaysShowPaginator\"\n            :unstyled=\"unstyled\"\n            :pt=\"ptm('pcPaginator')\"\n        >\n            <template v-if=\"$slots.paginatorcontainer\" #container=\"slotProps\">\n                <slot\n                    name=\"paginatorcontainer\"\n                    :first=\"slotProps.first\"\n                    :last=\"slotProps.last\"\n                    :rows=\"slotProps.rows\"\n                    :page=\"slotProps.page\"\n                    :pageCount=\"slotProps.pageCount\"\n                    :totalRecords=\"slotProps.totalRecords\"\n                    :firstPageCallback=\"slotProps.firstPageCallback\"\n                    :lastPageCallback=\"slotProps.lastPageCallback\"\n                    :prevPageCallback=\"slotProps.prevPageCallback\"\n                    :nextPageCallback=\"slotProps.nextPageCallback\"\n                    :rowChangeCallback=\"slotProps.rowChangeCallback\"\n                ></slot>\n            </template>\n            <template v-if=\"$slots.paginatorstart\" #start>\n                <slot name=\"paginatorstart\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorend\" #end>\n                <slot name=\"paginatorend\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorfirstpagelinkicon\" #firstpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorfirstpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorprevpagelinkicon\" #prevpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorprevpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatornextpagelinkicon\" #nextpagelinkicon=\"slotProps\">\n                <slot name=\"paginatornextpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorlastpagelinkicon\" #lastpagelinkicon=\"slotProps\">\n                <slot name=\"paginatorlastpagelinkicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorjumptopagedropdownicon\" #jumptopagedropdownicon=\"slotProps\">\n                <slot name=\"paginatorjumptopagedropdownicon\" :class=\"slotProps.class\"></slot>\n            </template>\n            <template v-if=\"$slots.paginatorrowsperpagedropdownicon\" #rowsperpagedropdownicon=\"slotProps\">\n                <slot name=\"paginatorrowsperpagedropdownicon\" :class=\"slotProps.class\"></slot>\n            </template>\n        </TTPaginator>\n        <div v-if=\"$slots.footer\" :class=\"cx('footer')\" v-bind=\"ptm('footer')\">\n            <slot name=\"footer\"></slot>\n        </div>\n        <div ref=\"resizeHelper\" :class=\"cx('columnResizeIndicator')\" style=\"display: none\" v-bind=\"ptm('columnResizeIndicator')\"></div>\n    </div>\n</template>\n\n<script>\nimport { addStyle, clearSelection, find, getAttribute, getIndex, getOffset, getOuterWidth, isRTL, setAttribute } from '@primeuix/utils/dom';\nimport { localeComparator, resolveFieldData, sort } from '@primeuix/utils/object';\nimport { FilterService } from '@primevue/core/api';\nimport { getVNodeProp, HelperSet } from '@primevue/core/utils';\nimport SpinnerIcon from '@primevue/icons/spinner';\nimport Paginator from 'primevue/paginator';\nimport BaseTreeTable from './BaseTreeTable.vue';\nimport FooterCell from './FooterCell.vue';\nimport HeaderCell from './HeaderCell.vue';\nimport TreeTableRow from './TreeTableRow.vue';\n\nexport default {\n    name: 'TreeTable',\n    extends: BaseTreeTable,\n    inheritAttrs: false,\n    emits: [\n        'node-expand',\n        'node-collapse',\n        'update:expandedKeys',\n        'update:selectionKeys',\n        'node-select',\n        'node-unselect',\n        'update:first',\n        'update:rows',\n        'page',\n        'update:sortField',\n        'update:sortOrder',\n        'update:multiSortMeta',\n        'sort',\n        'filter',\n        'column-resize-end',\n        'update:contextMenuSelection',\n        'row-contextmenu'\n    ],\n    provide() {\n        return {\n            $columns: this.d_columns\n        };\n    },\n    data() {\n        return {\n            d_expandedKeys: this.expandedKeys || {},\n            d_first: this.first,\n            d_rows: this.rows,\n            d_sortField: this.sortField,\n            d_sortOrder: this.sortOrder,\n            d_multiSortMeta: this.multiSortMeta ? [...this.multiSortMeta] : [],\n            hasASelectedNode: false,\n            d_columns: new HelperSet({ type: 'Column' })\n        };\n    },\n    documentColumnResizeListener: null,\n    documentColumnResizeEndListener: null,\n    lastResizeHelperX: null,\n    resizeColumnElement: null,\n    watch: {\n        expandedKeys(newValue) {\n            this.d_expandedKeys = newValue;\n        },\n        first(newValue) {\n            this.d_first = newValue;\n        },\n        rows(newValue) {\n            this.d_rows = newValue;\n        },\n        sortField(newValue) {\n            this.d_sortField = newValue;\n        },\n        sortOrder(newValue) {\n            this.d_sortOrder = newValue;\n        },\n        multiSortMeta(newValue) {\n            this.d_multiSortMeta = newValue;\n        }\n    },\n    beforeUnmount() {\n        this.destroyStyleElement();\n        this.d_columns.clear();\n    },\n    methods: {\n        columnProp(col, prop) {\n            return getVNodeProp(col, prop);\n        },\n        ptHeaderCellOptions(column) {\n            return {\n                context: {\n                    frozen: this.columnProp(column, 'frozen')\n                }\n            };\n        },\n        onNodeToggle(node) {\n            const key = this.nodeKey(node);\n\n            if (this.d_expandedKeys[key]) {\n                delete this.d_expandedKeys[key];\n                this.$emit('node-collapse', node);\n            } else {\n                this.d_expandedKeys[key] = true;\n                this.$emit('node-expand', node);\n            }\n\n            this.d_expandedKeys = { ...this.d_expandedKeys };\n            this.$emit('update:expandedKeys', this.d_expandedKeys);\n        },\n        onNodeClick(event) {\n            if (this.rowSelectionMode && event.node.selectable !== false) {\n                const metaSelection = event.nodeTouched ? false : this.metaKeySelection;\n                const _selectionKeys = metaSelection ? this.handleSelectionWithMetaKey(event) : this.handleSelectionWithoutMetaKey(event);\n\n                this.$emit('update:selectionKeys', _selectionKeys);\n            }\n        },\n        nodeKey(node) {\n            return resolveFieldData(node, this.dataKey);\n        },\n        handleSelectionWithMetaKey(event) {\n            const originalEvent = event.originalEvent;\n            const node = event.node;\n            const nodeKey = this.nodeKey(node);\n            const metaKey = originalEvent.metaKey || originalEvent.ctrlKey;\n            const selected = this.isNodeSelected(node);\n            let _selectionKeys;\n\n            if (selected && metaKey) {\n                if (this.isSingleSelectionMode()) {\n                    _selectionKeys = {};\n                } else {\n                    _selectionKeys = { ...this.selectionKeys };\n                    delete _selectionKeys[nodeKey];\n                }\n\n                this.$emit('node-unselect', node);\n            } else {\n                if (this.isSingleSelectionMode()) {\n                    _selectionKeys = {};\n                } else if (this.isMultipleSelectionMode()) {\n                    _selectionKeys = !metaKey ? {} : this.selectionKeys ? { ...this.selectionKeys } : {};\n                }\n\n                _selectionKeys[nodeKey] = true;\n                this.$emit('node-select', node);\n            }\n\n            return _selectionKeys;\n        },\n        handleSelectionWithoutMetaKey(event) {\n            const node = event.node;\n            const nodeKey = this.nodeKey(node);\n            const selected = this.isNodeSelected(node);\n            let _selectionKeys;\n\n            if (this.isSingleSelectionMode()) {\n                if (selected) {\n                    _selectionKeys = {};\n                    this.$emit('node-unselect', node);\n                } else {\n                    _selectionKeys = {};\n                    _selectionKeys[nodeKey] = true;\n                    this.$emit('node-select', node);\n                }\n            } else {\n                if (selected) {\n                    _selectionKeys = { ...this.selectionKeys };\n                    delete _selectionKeys[nodeKey];\n\n                    this.$emit('node-unselect', node);\n                } else {\n                    _selectionKeys = this.selectionKeys ? { ...this.selectionKeys } : {};\n                    _selectionKeys[nodeKey] = true;\n\n                    this.$emit('node-select', node);\n                }\n            }\n\n            return _selectionKeys;\n        },\n        onCheckboxChange(event) {\n            this.$emit('update:selectionKeys', event.selectionKeys);\n\n            if (event.check) this.$emit('node-select', event.node);\n            else this.$emit('node-unselect', event.node);\n        },\n        onRowRightClick(event) {\n            if (this.contextMenu) {\n                clearSelection();\n                event.originalEvent.target.focus();\n            }\n\n            this.$emit('update:contextMenuSelection', event.node);\n            this.$emit('row-contextmenu', event);\n        },\n        isSingleSelectionMode() {\n            return this.selectionMode === 'single';\n        },\n        isMultipleSelectionMode() {\n            return this.selectionMode === 'multiple';\n        },\n        onPage(event) {\n            this.d_first = event.first;\n            this.d_rows = event.rows;\n\n            let pageEvent = this.createLazyLoadEvent(event);\n\n            pageEvent.pageCount = event.pageCount;\n            pageEvent.page = event.page;\n\n            this.d_expandedKeys = {};\n            this.$emit('update:expandedKeys', this.d_expandedKeys);\n            this.$emit('update:first', this.d_first);\n            this.$emit('update:rows', this.d_rows);\n            this.$emit('page', pageEvent);\n        },\n        resetPage() {\n            this.d_first = 0;\n            this.$emit('update:first', this.d_first);\n        },\n        getFilterColumnHeaderClass(column) {\n            return [this.cx('headerCell', { column }), this.columnProp(column, 'filterHeaderClass')];\n        },\n        onColumnHeaderClick(e) {\n            let event = e.originalEvent;\n            let column = e.column;\n\n            if (this.columnProp(column, 'sortable')) {\n                const targetNode = event.target;\n                const columnField = this.columnProp(column, 'sortField') || this.columnProp(column, 'field');\n\n                if (\n                    getAttribute(targetNode, 'data-p-sortable-column') === true ||\n                    getAttribute(targetNode, 'data-pc-section') === 'columntitle' ||\n                    getAttribute(targetNode, 'data-pc-section') === 'columnheadercontent' ||\n                    getAttribute(targetNode, 'data-pc-section') === 'sorticon' ||\n                    getAttribute(targetNode.parentElement, 'data-pc-section') === 'sorticon' ||\n                    getAttribute(targetNode.parentElement.parentElement, 'data-pc-section') === 'sorticon' ||\n                    targetNode.closest('[data-p-sortable-column=\"true\"]')\n                ) {\n                    clearSelection();\n\n                    if (this.sortMode === 'single') {\n                        if (this.d_sortField === columnField) {\n                            if (this.removableSort && this.d_sortOrder * -1 === this.defaultSortOrder) {\n                                this.d_sortOrder = null;\n                                this.d_sortField = null;\n                            } else {\n                                this.d_sortOrder = this.d_sortOrder * -1;\n                            }\n                        } else {\n                            this.d_sortOrder = this.defaultSortOrder;\n                            this.d_sortField = columnField;\n                        }\n\n                        this.$emit('update:sortField', this.d_sortField);\n                        this.$emit('update:sortOrder', this.d_sortOrder);\n                        this.resetPage();\n                    } else if (this.sortMode === 'multiple') {\n                        let metaKey = event.metaKey || event.ctrlKey;\n\n                        if (!metaKey) {\n                            this.d_multiSortMeta = this.d_multiSortMeta.filter((meta) => meta.field === columnField);\n                        }\n\n                        this.addMultiSortField(columnField);\n                        this.$emit('update:multiSortMeta', this.d_multiSortMeta);\n                    }\n\n                    this.$emit('sort', this.createLazyLoadEvent(event));\n                }\n            }\n        },\n        addMultiSortField(field) {\n            let index = this.d_multiSortMeta.findIndex((meta) => meta.field === field);\n\n            if (index >= 0) {\n                if (this.removableSort && this.d_multiSortMeta[index].order * -1 === this.defaultSortOrder) this.d_multiSortMeta.splice(index, 1);\n                else this.d_multiSortMeta[index] = { field: field, order: this.d_multiSortMeta[index].order * -1 };\n            } else {\n                this.d_multiSortMeta.push({ field: field, order: this.defaultSortOrder });\n            }\n\n            this.d_multiSortMeta = [...this.d_multiSortMeta];\n        },\n        sortSingle(nodes) {\n            return this.sortNodesSingle(nodes);\n        },\n        sortNodesSingle(nodes) {\n            let _nodes = [...nodes];\n            const comparer = localeComparator();\n\n            _nodes.sort((node1, node2) => {\n                const value1 = resolveFieldData(node1.data, this.d_sortField);\n                const value2 = resolveFieldData(node2.data, this.d_sortField);\n\n                return sort(value1, value2, this.d_sortOrder, comparer);\n            });\n\n            return _nodes;\n        },\n        sortMultiple(nodes) {\n            return this.sortNodesMultiple(nodes);\n        },\n        sortNodesMultiple(nodes) {\n            let _nodes = [...nodes];\n\n            _nodes.sort((node1, node2) => {\n                return this.multisortField(node1, node2, 0);\n            });\n\n            return _nodes;\n        },\n        multisortField(node1, node2, index) {\n            const value1 = resolveFieldData(node1.data, this.d_multiSortMeta[index].field);\n            const value2 = resolveFieldData(node2.data, this.d_multiSortMeta[index].field);\n            const comparer = localeComparator();\n\n            if (value1 === value2) {\n                return this.d_multiSortMeta.length - 1 > index ? this.multisortField(node1, node2, index + 1) : 0;\n            }\n\n            return sort(value1, value2, this.d_multiSortMeta[index].order, comparer);\n        },\n        filter(value) {\n            let filteredNodes = [];\n            const strict = this.filterMode === 'strict';\n\n            for (let node of value) {\n                let copyNode = { ...node };\n                let localMatch = true;\n                let globalMatch = false;\n\n                for (let j = 0; j < this.columns.length; j++) {\n                    let col = this.columns[j];\n                    let filterField = this.columnProp(col, 'filterField') || this.columnProp(col, 'field');\n\n                    //local\n                    if (Object.prototype.hasOwnProperty.call(this.filters, filterField)) {\n                        let filterMatchMode = this.columnProp(col, 'filterMatchMode') || 'startsWith';\n                        let filterValue = this.filters[filterField];\n                        let filterConstraint = FilterService.filters[filterMatchMode];\n                        let paramsWithoutNode = { filterField, filterValue, filterConstraint, strict };\n\n                        if (\n                            (strict && !(this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode))) ||\n                            (!strict && !(this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode)))\n                        ) {\n                            localMatch = false;\n                        }\n\n                        if (!localMatch) {\n                            break;\n                        }\n                    }\n\n                    //global\n                    if (this.hasGlobalFilter() && !globalMatch) {\n                        let copyNodeForGlobal = { ...copyNode };\n                        let filterValue = this.filters['global'];\n                        let filterConstraint = FilterService.filters['contains'];\n                        let globalFilterParamsWithoutNode = { filterField, filterValue, filterConstraint, strict };\n\n                        if (\n                            (strict && (this.findFilteredNodes(copyNodeForGlobal, globalFilterParamsWithoutNode) || this.isFilterMatched(copyNodeForGlobal, globalFilterParamsWithoutNode))) ||\n                            (!strict && (this.isFilterMatched(copyNodeForGlobal, globalFilterParamsWithoutNode) || this.findFilteredNodes(copyNodeForGlobal, globalFilterParamsWithoutNode)))\n                        ) {\n                            globalMatch = true;\n                            copyNode = copyNodeForGlobal;\n                        }\n                    }\n                }\n\n                let matches = localMatch;\n\n                if (this.hasGlobalFilter()) {\n                    matches = localMatch && globalMatch;\n                }\n\n                if (matches) {\n                    filteredNodes.push(copyNode);\n                }\n            }\n\n            let filterEvent = this.createLazyLoadEvent(event);\n\n            filterEvent.filteredValue = filteredNodes;\n            this.$emit('filter', filterEvent);\n\n            return filteredNodes;\n        },\n        findFilteredNodes(node, paramsWithoutNode) {\n            if (node) {\n                let matched = false;\n\n                if (node.children) {\n                    let childNodes = [...node.children];\n\n                    node.children = [];\n\n                    for (let childNode of childNodes) {\n                        let copyChildNode = { ...childNode };\n\n                        if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n                            matched = true;\n                            node.children.push(copyChildNode);\n                        }\n                    }\n                }\n\n                if (matched) {\n                    return true;\n                }\n            }\n        },\n        isFilterMatched(node, { filterField, filterValue, filterConstraint, strict }) {\n            let matched = false;\n            let dataFieldValue = resolveFieldData(node.data, filterField);\n\n            if (filterConstraint(dataFieldValue, filterValue, this.filterLocale)) {\n                matched = true;\n            }\n\n            if (!matched || (strict && !this.isNodeLeaf(node))) {\n                matched = this.findFilteredNodes(node, { filterField, filterValue, filterConstraint, strict }) || matched;\n            }\n\n            return matched;\n        },\n        isNodeSelected(node) {\n            return this.selectionMode && this.selectionKeys ? this.selectionKeys[this.nodeKey(node)] === true : false;\n        },\n        isNodeLeaf(node) {\n            return node.leaf === false ? false : !(node.children && node.children.length);\n        },\n        createLazyLoadEvent(event) {\n            let filterMatchModes;\n\n            if (this.hasFilters()) {\n                filterMatchModes = {};\n                this.columns.forEach((col) => {\n                    if (this.columnProp(col, 'field')) {\n                        filterMatchModes[col.props.field] = this.columnProp(col, 'filterMatchMode');\n                    }\n                });\n            }\n\n            return {\n                originalEvent: event,\n                first: this.d_first,\n                rows: this.d_rows,\n                sortField: this.d_sortField,\n                sortOrder: this.d_sortOrder,\n                multiSortMeta: this.d_multiSortMeta,\n                filters: this.filters,\n                filterMatchModes: filterMatchModes\n            };\n        },\n        onColumnResizeStart(event) {\n            let containerLeft = getOffset(this.$el).left;\n\n            this.resizeColumnElement = event.target.parentElement;\n            this.columnResizing = true;\n            this.lastResizeHelperX = event.pageX - containerLeft + this.$el.scrollLeft;\n\n            this.bindColumnResizeEvents();\n        },\n        onColumnResize(event) {\n            let containerLeft = getOffset(this.$el).left;\n\n            this.$el.setAttribute('data-p-unselectable-text', 'true');\n            !this.isUnstyled && addStyle(this.$el, { 'user-select': 'none' });\n            this.$refs.resizeHelper.style.height = this.$el.offsetHeight + 'px';\n            this.$refs.resizeHelper.style.top = 0 + 'px';\n            this.$refs.resizeHelper.style.left = event.pageX - containerLeft + this.$el.scrollLeft + 'px';\n\n            this.$refs.resizeHelper.style.display = 'block';\n        },\n        onColumnResizeEnd() {\n            let delta = isRTL(this.$el) ? this.lastResizeHelperX - this.$refs.resizeHelper.offsetLeft : this.$refs.resizeHelper.offsetLeft - this.lastResizeHelperX;\n            let columnWidth = this.resizeColumnElement.offsetWidth;\n            let newColumnWidth = columnWidth + delta;\n            let minWidth = this.resizeColumnElement.style.minWidth || 15;\n\n            if (columnWidth + delta > parseInt(minWidth, 10)) {\n                if (this.columnResizeMode === 'fit') {\n                    let nextColumn = this.resizeColumnElement.nextElementSibling;\n                    let nextColumnWidth = nextColumn.offsetWidth - delta;\n\n                    if (newColumnWidth > 15 && nextColumnWidth > 15) {\n                        this.resizeTableCells(newColumnWidth, nextColumnWidth);\n                    }\n                } else if (this.columnResizeMode === 'expand') {\n                    const tableWidth = this.$refs.table.offsetWidth + delta + 'px';\n\n                    const updateTableWidth = (el) => {\n                        !!el && (el.style.width = el.style.minWidth = tableWidth);\n                    };\n\n                    // Reasoning: resize table cells before updating the table width so that it can use existing computed cell widths and adjust only the one column.\n                    this.resizeTableCells(newColumnWidth);\n                    updateTableWidth(this.$refs.table);\n                }\n\n                this.$emit('column-resize-end', {\n                    element: this.resizeColumnElement,\n                    delta: delta\n                });\n            }\n\n            this.$refs.resizeHelper.style.display = 'none';\n            this.resizeColumn = null;\n            this.$el.removeAttribute('data-p-unselectable-text');\n            !this.isUnstyled && (this.$el.style['user-select'] = '');\n\n            this.unbindColumnResizeEvents();\n        },\n        resizeTableCells(newColumnWidth, nextColumnWidth) {\n            let colIndex = getIndex(this.resizeColumnElement);\n            let widths = [];\n            let headers = find(this.$refs.table, 'thead[data-pc-section=\"thead\"] > tr > th');\n\n            headers.forEach((header) => widths.push(getOuterWidth(header)));\n\n            this.destroyStyleElement();\n            this.createStyleElement();\n\n            let innerHTML = '';\n            let selector = `[data-pc-name=\"treetable\"][${this.$attrSelector}] > [data-pc-section=\"tablecontainer\"] > table[data-pc-section=\"table\"]`;\n\n            widths.forEach((width, index) => {\n                let colWidth = index === colIndex ? newColumnWidth : nextColumnWidth && index === colIndex + 1 ? nextColumnWidth : width;\n                let style = `width: ${colWidth}px !important; max-width: ${colWidth}px !important`;\n\n                innerHTML += `\n                    ${selector} > thead[data-pc-section=\"thead\"] > tr > th:nth-child(${index + 1}),\n                    ${selector} > tbody[data-pc-section=\"tbody\"] > tr > td:nth-child(${index + 1}),\n                    ${selector} > tfoot[data-pc-section=\"tfoot\"] > tr > td:nth-child(${index + 1}) {\n                        ${style}\n                    }\n                `;\n            });\n\n            this.styleElement.innerHTML = innerHTML;\n        },\n        bindColumnResizeEvents() {\n            if (!this.documentColumnResizeListener) {\n                this.documentColumnResizeListener = document.addEventListener('mousemove', (event) => {\n                    if (this.columnResizing) {\n                        this.onColumnResize(event);\n                    }\n                });\n            }\n\n            if (!this.documentColumnResizeEndListener) {\n                this.documentColumnResizeEndListener = document.addEventListener('mouseup', () => {\n                    if (this.columnResizing) {\n                        this.columnResizing = false;\n                        this.onColumnResizeEnd();\n                    }\n                });\n            }\n        },\n        unbindColumnResizeEvents() {\n            if (this.documentColumnResizeListener) {\n                document.removeEventListener('document', this.documentColumnResizeListener);\n                this.documentColumnResizeListener = null;\n            }\n\n            if (this.documentColumnResizeEndListener) {\n                document.removeEventListener('document', this.documentColumnResizeEndListener);\n                this.documentColumnResizeEndListener = null;\n            }\n        },\n        onColumnKeyDown(event, col) {\n            if ((event.code === 'Enter' || event.code === 'NumpadEnter') && event.currentTarget.nodeName === 'TH' && getAttribute(event.currentTarget, 'data-p-sortable-column')) {\n                this.onColumnHeaderClick(event, col);\n            }\n        },\n        hasColumnFilter() {\n            if (this.columns) {\n                for (let col of this.columns) {\n                    if (col.children && col.children.filter) {\n                        return true;\n                    }\n                }\n            }\n\n            return false;\n        },\n        hasFilters() {\n            return this.filters && Object.keys(this.filters).length > 0 && this.filters.constructor === Object;\n        },\n        hasGlobalFilter() {\n            return this.filters && Object.prototype.hasOwnProperty.call(this.filters, 'global');\n        },\n        getItemLabel(node) {\n            return node.data.name;\n        },\n        createStyleElement() {\n            this.styleElement = document.createElement('style');\n            this.styleElement.type = 'text/css';\n            setAttribute(this.styleElement, 'nonce', this.$primevue?.config?.csp?.nonce);\n            document.head.appendChild(this.styleElement);\n        },\n        destroyStyleElement() {\n            if (this.styleElement) {\n                document.head.removeChild(this.styleElement);\n                this.styleElement = null;\n            }\n        },\n        setTabindex(node, index) {\n            if (this.isNodeSelected(node)) {\n                this.hasASelectedNode = true;\n\n                return 0;\n            }\n\n            if (this.selectionMode) {\n                if (!this.isNodeSelected(node) && index === 0 && !this.hasASelectedNode) return 0;\n            } else if (!this.selectionMode && index === 0) {\n                return 0;\n            }\n\n            return -1;\n        }\n    },\n    computed: {\n        columns() {\n            return this.d_columns.get(this);\n        },\n        processedData() {\n            if (this.lazy) {\n                return this.value;\n            } else {\n                if (this.value && this.value.length) {\n                    let data = this.value;\n\n                    if (this.sorted) {\n                        if (this.sortMode === 'single') data = this.sortSingle(data);\n                        else if (this.sortMode === 'multiple') data = this.sortMultiple(data);\n                    }\n\n                    if (this.hasFilters()) {\n                        data = this.filter(data);\n                    }\n\n                    return data;\n                } else {\n                    return null;\n                }\n            }\n        },\n        dataToRender() {\n            const data = this.processedData;\n\n            if (this.paginator) {\n                const first = this.lazy ? 0 : this.d_first;\n\n                return data.slice(first, first + this.d_rows);\n            } else {\n                return data;\n            }\n        },\n        empty() {\n            const data = this.processedData;\n\n            return !data || data.length === 0;\n        },\n        sorted() {\n            return this.d_sortField || (this.d_multiSortMeta && this.d_multiSortMeta.length > 0);\n        },\n        hasFooter() {\n            let hasFooter = false;\n\n            for (let col of this.columns) {\n                if (this.columnProp(col, 'footer') || (col.children && col.children.footer)) {\n                    hasFooter = true;\n                    break;\n                }\n            }\n\n            return hasFooter;\n        },\n        paginatorTop() {\n            return this.paginator && (this.paginatorPosition !== 'bottom' || this.paginatorPosition === 'both');\n        },\n        paginatorBottom() {\n            return this.paginator && (this.paginatorPosition !== 'top' || this.paginatorPosition === 'both');\n        },\n        singleSelectionMode() {\n            return this.selectionMode && this.selectionMode === 'single';\n        },\n        multipleSelectionMode() {\n            return this.selectionMode && this.selectionMode === 'multiple';\n        },\n        rowSelectionMode() {\n            return this.singleSelectionMode || this.multipleSelectionMode;\n        },\n        totalRecordsLength() {\n            if (this.lazy) {\n                return this.totalRecords;\n            } else {\n                const data = this.processedData;\n\n                return data ? data.length : 0;\n            }\n        }\n    },\n    components: {\n        TTRow: TreeTableRow,\n        TTPaginator: Paginator,\n        TTHeaderCell: HeaderCell,\n        TTFooterCell: FooterCell,\n        SpinnerIcon: SpinnerIcon\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "value", "type", "dataKey", "String", "Function", "expandedKeys", "<PERSON><PERSON><PERSON><PERSON>", "selectionMode", "metaKeySelection", "Boolean", "contextMenu", "contextMenuSelection", "Object", "rows", "Number", "first", "totalRecords", "paginator", "paginatorPosition", "alwaysShowPaginator", "paginatorTemplate", "pageLinkSize", "rowsPerPageOptions", "Array", "currentPageReportTemplate", "lazy", "loading", "loadingIcon", "undefined", "loadingMode", "rowHover", "autoLayout", "sortField", "sortOrder", "defaultSortOrder", "multiSortMeta", "sortMode", "removableSort", "filters", "filterMode", "filterLocale", "resizableColumns", "columnResizeMode", "indentation", "showGridlines", "scrollable", "scrollHeight", "size", "tableStyle", "tableClass", "tableProps", "style", "TreeTableStyle", "provide", "$pcTreeTable", "$parentInstance", "hostName", "column", "index", "data", "styleObject", "mounted", "columnProp", "updateStickyPosition", "updated", "methods", "prop", "getVNodeProp", "getColumnPT", "key", "_this$$parentInstance", "columnMetaData", "parent", "instance", "$props", "state", "$data", "context", "frozen", "mergeProps", "ptm", "concat", "ptmo", "getColumnProp", "pt", "align", "pos", "next", "getNextElementSibling", "$el", "getOuterWidth", "parseFloat", "insetInlineEnd", "prev", "getPreviousElementSibling", "insetInlineStart", "computed", "containerClass", "cx", "containerStyle", "bodyStyle", "columnStyle", "_openBlock", "_createElementBlock", "_mergeProps", "$options", "role", "_objectSpread", "children", "footer", "_createBlock", "_resolveDynamicComponent", "_ctx", "emits", "sorted", "isColumnSorted", "resizable", "filterRow", "parentElement", "nextElement<PERSON><PERSON>ling", "getIndex", "onClick", "event", "$emit", "originalEvent", "onKeyDown", "code", "currentTarget", "nodeName", "getAttribute", "preventDefault", "onResizeStart", "getMultiSortMetaIndex", "i", "length", "meta", "field", "isMultiSorted", "headerStyle", "sortState", "metaIndex", "order", "sortableColumnIcon", "_this$sortState", "SortAltIcon", "SortAmountUpAltIcon", "SortAmountDownIcon", "ariaSort", "_this$sortState2", "components", "Badge", "apply", "arguments", "onKeydown", "tabindex", "onMousedown", "_createElementVNode", "header", "sorticon", "_component_Badge", "node", "level", "leaf", "expanded", "checked", "partialChecked", "templates", "toggle", "selectable", "rowSelectionMode", "selected", "$parent", "getColumnCheckboxPT", "resolveFieldData", "rowData", "toggleCheckbox", "toggler<PERSON><PERSON>le", "marginLeft", "visibility", "checkboxSelectionMode", "Checkbox", "ChevronRightIcon", "ChevronDownIcon", "CheckIcon", "MinusIcon", "SpinnerIcon", "directives", "ripple", "<PERSON><PERSON><PERSON>", "_withDirectives", "_Fragment", "_component_SpinnerIcon", "spin", "rowtoggleicon", "row<PERSON><PERSON><PERSON><PERSON>", "expandedIcon", "collapsedIcon", "_component_Checkbox", "modelValue", "binary", "disabled", "onChange", "indeterminate", "unstyled", "icon", "_withCtx", "slotProps", "_normalizeClass", "body", "_createTextVNode", "_toDisplayString", "parentNode", "columns", "ariaSetSize", "ariaPosInset", "nodeTouched", "col", "isClickable", "target", "tagName", "setTabIndexForSelectionMode", "onRowRightClick", "onTouchEnd", "nodeKey", "item", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onArrowRightKey", "onHomeKey", "onEndKey", "onEnterKey", "onTabKey", "focusRowChange", "previousElementSibling", "_this", "ishiddenIcon", "findSingle", "toggler<PERSON><PERSON>", "$refs", "click", "$nextTick", "findBeforeClickableNode", "findFirstElement", "focus", "nodes", "find", "_toConsumableArray", "hasSelectedRow", "some", "row", "for<PERSON>ach", "tabIndex", "selectedNodes", "filter", "firstFocusableRow", "currentFocusedRow", "prevNode", "prevNodeButton", "querySelector", "_selectionKeys", "_check", "propagateDown", "check", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "child", "err", "e", "f", "propagateUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>ount", "childPartialSelected", "_iterator2", "_step2", "onCheckboxChange", "_iterator3", "_step3", "elements", "every", "element", "styleClass", "isSelectedWithContextMenu", "equals", "getAriaSelected", "ptmOptions", "TTBodyCell", "BodyCell", "ref", "onTouchend", "onContextmenu", "_renderList", "_component_TTBodyCell", "onNodeToggle", "_cache", "$event", "onCheckboxToggle", "childNode", "_component_TreeTableRow", "indexOf", "onNodeClick", "onRowRightclick", "BaseTreeTable", "inheritAttrs", "$columns", "d_columns", "d_expandedKeys", "d_first", "d_rows", "d_sortField", "d_sortOrder", "d_multiSortMeta", "hasASelectedNode", "HelperSet", "documentColumnResizeListener", "documentColumnResizeEndListener", "lastResizeHelperX", "resizeColumnElement", "watch", "newValue", "beforeUnmount", "destroyStyleElement", "clear", "ptHeaderCellOptions", "metaSelection", "handleSelectionWithMetaKey", "handleSelectionWithoutMetaKey", "metaKey", "ctrl<PERSON>ey", "isNodeSelected", "isSingleSelectionMode", "isMultipleSelectionMode", "clearSelection", "onPage", "pageEvent", "createLazyLoadEvent", "pageCount", "page", "resetPage", "getFilterColumnHeaderClass", "onColumnHeaderClick", "targetNode", "columnField", "closest", "addMultiSortField", "findIndex", "splice", "push", "sortSingle", "sortNodesSingle", "_nodes", "comparer", "localeComparator", "sort", "node1", "node2", "value1", "value2", "sortMultiple", "sortNodesMultiple", "_this2", "multisortField", "filteredNodes", "strict", "copyNode", "localMatch", "globalMatch", "j", "filterField", "prototype", "hasOwnProperty", "call", "filterMatchMode", "filterValue", "filterConstraint", "FilterService", "paramsWithoutNode", "findFilteredNodes", "isFilterMatched", "hasGlobalFilter", "copyNodeForGlobal", "globalFilterParamsWithoutNode", "matches", "filterEvent", "filteredValue", "matched", "childNodes", "copyChildNode", "_ref", "dataFieldValue", "isNodeLeaf", "_this3", "filterMatchModes", "hasFilters", "onColumnResizeStart", "containerLeft", "getOffset", "left", "columnResizing", "pageX", "scrollLeft", "bindColumnResizeEvents", "onColumnResize", "setAttribute", "isUnstyled", "addStyle", "resizeHelper", "height", "offsetHeight", "top", "display", "onColumnResizeEnd", "delta", "isRTL", "offsetLeft", "columnWidth", "offsetWidth", "newColumnWidth", "min<PERSON><PERSON><PERSON>", "parseInt", "nextColumn", "nextColumnWidth", "resizeTableCells", "tableWidth", "table", "updateTableWidth", "el", "width", "resizeColumn", "removeAttribute", "unbindColumnResizeEvents", "colIndex", "widths", "headers", "createStyleElement", "innerHTML", "selector", "$attrSelector", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styleElement", "_this4", "document", "addEventListener", "removeEventListener", "onColumnKeyDown", "hasColumnFilter", "keys", "constructor", "getItemLabel", "_this$$primevue", "createElement", "$primevue", "config", "csp", "nonce", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setTabindex", "get", "processedData", "dataToRender", "slice", "empty", "<PERSON><PERSON><PERSON>er", "_iterator4", "_step4", "paginatorTop", "paginatorBottom", "singleSelectionMode", "multipleSelectionMode", "totalRecordsLength", "TTRow", "TreeTableRow", "TTPaginator", "Paginator", "TTHeaderCell", "<PERSON><PERSON><PERSON><PERSON>", "TTFooterCell", "<PERSON><PERSON><PERSON>ell", "ptmi", "_renderSlot", "$slots", "_component_TTPaginator", "template", "position", "alwaysShow", "paginatorcontainer", "fn", "last", "firstPageCallback", "lastPageCallback", "prevPageCallback", "nextPageCallback", "row<PERSON><PERSON>e<PERSON>allback", "paginatorstart", "paginatorend", "paginatorfirstpagelinkicon", "paginatorprevpagelinkicon", "paginatornextpagelinkicon", "paginatorlastpagelinkicon", "paginatorjumptopagedropdownicon", "paginatorrowsperpagedropdownicon", "sx", "maxHeight", "_component_TTHeaderCell", "onColumnClick", "onColumnResizestart", "_component_TTRow", "colspan", "_component_TTFooterCell"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,eAAe;AACrB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,KAAK,EAAE;AACHC,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDC,IAAAA,OAAO,EAAE;AACLD,MAAAA,IAAI,EAAE,CAACE,MAAM,EAAEC,QAAQ,CAAC;MACxB,SAAS,EAAA;KACZ;AACDC,IAAAA,YAAY,EAAE;AACVJ,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDK,IAAAA,aAAa,EAAE;AACXL,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDM,IAAAA,aAAa,EAAE;AACXN,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDK,IAAAA,gBAAgB,EAAE;AACdP,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,WAAW,EAAE;AACTT,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDE,IAAAA,oBAAoB,EAAE;AAClBV,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,IAAI,EAAE;AACFZ,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,KAAK,EAAE;AACHd,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDE,IAAAA,YAAY,EAAE;AACVf,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDG,IAAAA,SAAS,EAAE;AACPhB,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDS,IAAAA,iBAAiB,EAAE;AACfjB,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDgB,IAAAA,mBAAmB,EAAE;AACjBlB,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDW,IAAAA,iBAAiB,EAAE;AACfnB,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDkB,IAAAA,YAAY,EAAE;AACVpB,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDQ,IAAAA,kBAAkB,EAAE;AAChBrB,MAAAA,IAAI,EAAEsB,KAAK;MACX,SAAS,EAAA;KACZ;AACDC,IAAAA,yBAAyB,EAAE;AACvBvB,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDsB,IAAAA,IAAI,EAAE;AACFxB,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDiB,IAAAA,OAAO,EAAE;AACLzB,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDkB,IAAAA,WAAW,EAAE;AACT1B,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAASyB,EAAAA;KACZ;AACDC,IAAAA,WAAW,EAAE;AACT5B,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACD2B,IAAAA,QAAQ,EAAE;AACN7B,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDsB,IAAAA,UAAU,EAAE;AACR9B,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDuB,IAAAA,SAAS,EAAE;AACP/B,MAAAA,IAAI,EAAE,CAACE,MAAM,EAAEC,QAAQ,CAAC;MACxB,SAAS,EAAA;KACZ;AACD6B,IAAAA,SAAS,EAAE;AACPhC,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDoB,IAAAA,gBAAgB,EAAE;AACdjC,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDqB,IAAAA,aAAa,EAAE;AACXlC,MAAAA,IAAI,EAAEsB,KAAK;MACX,SAAS,EAAA;KACZ;AACDa,IAAAA,QAAQ,EAAE;AACNnC,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDkC,IAAAA,aAAa,EAAE;AACXpC,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACD6B,IAAAA,OAAO,EAAE;AACLrC,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;KACZ;AACD2B,IAAAA,UAAU,EAAE;AACRtC,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDqC,IAAAA,YAAY,EAAE;AACVvC,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAASyB,EAAAA;KACZ;AACDa,IAAAA,gBAAgB,EAAE;AACdxC,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDiC,IAAAA,gBAAgB,EAAE;AACdzC,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDwC,IAAAA,WAAW,EAAE;AACT1C,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACD8B,IAAAA,aAAa,EAAE;AACX3C,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDoC,IAAAA,UAAU,EAAE;AACR5C,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDqC,IAAAA,YAAY,EAAE;AACV7C,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACD4C,IAAAA,IAAI,EAAE;AACF9C,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACD6C,IAAAA,UAAU,EAAE;AACR/C,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDgD,IAAAA,UAAU,EAAE;AACRhD,MAAAA,IAAI,EAAE,CAACE,MAAM,EAAES,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDsC,IAAAA,UAAU,EAAE;AACRjD,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDuC,EAAAA,KAAK,EAAEC,cAAc;EACrBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,YAAY,EAAE,IAAI;AAClBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;AC/KD,eAAe;AACX1D,EAAAA,IAAI,EAAE,YAAY;AAClB2D,EAAAA,QAAQ,EAAE,WAAW;AACrB,EAAA,SAAA,EAAS1D,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACH0D,IAAAA,MAAM,EAAE;AACJxD,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;KACZ;AACD8C,IAAAA,KAAK,EAAE;AACHzD,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;AACb;GACH;EACD6C,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHC,MAAAA,WAAW,EAAE;KAChB;GACJ;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,IAAA,IAAI,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAACC,oBAAoB,EAAE;AAC/B;GACH;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,IAAA,IAAI,IAAI,CAACF,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAACC,oBAAoB,EAAE;AAC/B;GACH;AACDE,EAAAA,OAAO,EAAE;AACLH,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACI,IAAI,EAAE;AACb,MAAA,OAAOC,YAAY,CAAC,IAAI,CAACV,MAAM,EAAES,IAAI,CAAC;KACzC;AACDE,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACC,GAAG,EAAE;AAAA,MAAA,IAAAC,qBAAA;AACb,MAAA,IAAMC,iBAAiB;AACnBxE,QAAAA,KAAK,EAAE,IAAI,CAAC0D,MAAM,CAAC1D,KAAK;AACxByE,QAAAA,MAAM,EAAE;AACJC,UAAAA,QAAQ,EAAE,IAAI;UACd1E,KAAK,EAAE,IAAI,CAAC2E,MAAM;UAClBC,KAAK,EAAE,IAAI,CAACC;SACf;AACDC,QAAAA,OAAO,EAAE;UACLnB,KAAK,EAAE,IAAI,CAACA,KAAK;AACjBoB,UAAAA,MAAM,EAAE,IAAI,CAAChB,UAAU,CAAC,QAAQ,CAAC;UACjCf,IAAI,EAAA,CAAAuB,qBAAA,GAAE,IAAI,CAACf,eAAe,MAAAe,IAAAA,IAAAA,qBAAA,KAApBA,MAAAA,GAAAA,MAAAA,GAAAA,qBAAA,CAAsBvB;AAChC;OACH;MAED,OAAOgC,UAAU,CAAC,IAAI,CAACC,GAAG,CAAAC,SAAAA,CAAAA,MAAA,CAAWZ,GAAG,CAAI,EAAA;AAAEZ,QAAAA,MAAM,EAAEc;OAAgB,CAAC,EAAE,IAAI,CAACS,GAAG,CAAAC,SAAAA,CAAAA,MAAA,CAAWZ,GAAG,CAAIE,EAAAA,cAAc,CAAC,EAAE,IAAI,CAACW,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE,EAAEd,GAAG,EAAEE,cAAc,CAAC,CAAC;KAC5K;IACDY,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,OAAO,IAAI,CAAC1B,MAAM,CAAC1D,KAAI,IAAK,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAACqF,KAAK,IAAI,CAAC3B,MAAM,CAAC1D,KAAK,CAACqF,EAAG,GAAExD,SAAS;KACtF;IACDmC,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;AACnB,MAAA,IAAI,IAAI,CAACD,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC3B,QAAA,IAAIuB,KAAM,GAAE,IAAI,CAACvB,UAAU,CAAC,aAAa,CAAC;QAE1C,IAAIuB,KAAM,KAAI,OAAO,EAAE;UACnB,IAAIC,GAAE,GAAI,CAAC;UACX,IAAIC,IAAK,GAAEC,qBAAqB,CAAC,IAAI,CAACC,GAAG,EAAE,+BAA+B,CAAC;AAE3E,UAAA,IAAIF,IAAI,EAAE;AACND,YAAAA,GAAE,GAAII,aAAa,CAACH,IAAI,IAAII,UAAU,CAACJ,IAAI,CAACpC,KAAK,CAAC,kBAAkB,CAAA,IAAK,CAAC,CAAC;AAC/E;AAEA,UAAA,IAAI,CAACS,WAAW,CAACgC,cAAe,GAAEN,GAAE,GAAI,IAAI;AAChD,SAAE,MAAK;UACH,IAAIA,IAAE,GAAI,CAAC;UACX,IAAIO,IAAK,GAAEC,yBAAyB,CAAC,IAAI,CAACL,GAAG,EAAE,+BAA+B,CAAC;AAE/E,UAAA,IAAII,IAAI,EAAE;AACNP,YAAAA,IAAE,GAAII,aAAa,CAACG,IAAI,CAAA,GAAIF,UAAU,CAACE,IAAI,CAAC1C,KAAK,CAAC,oBAAoB,CAAA,IAAK,CAAC,CAAC;AACjF;AAEA,UAAA,IAAI,CAACS,WAAW,CAACmC,gBAAiB,GAAET,IAAE,GAAI,IAAI;AAClD;AACJ;AACJ;GACH;AACDU,EAAAA,QAAQ,EAAE;IACNC,cAAc,EAAA,SAAdA,cAAcA,GAAG;MACb,OAAO,CAAC,IAAI,CAACnC,UAAU,CAAC,aAAa,CAAC,EAAE,IAAI,CAACA,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,CAACoC,EAAE,CAAC,YAAY,CAAC,CAAC;KAC3F;IACDC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,IAAIC,SAAQ,GAAI,IAAI,CAACtC,UAAU,CAAC,aAAa,CAAC;AAC9C,MAAA,IAAIuC,WAAU,GAAI,IAAI,CAACvC,UAAU,CAAC,OAAO,CAAC;MAE1C,OAAO,IAAI,CAACA,UAAU,CAAC,QAAQ,CAAE,GAAE,CAACuC,WAAW,EAAED,SAAS,EAAE,IAAI,CAACxC,WAAW,CAAA,GAAI,CAACyC,WAAW,EAAED,SAAS,CAAC;AAC5G;AACJ;AACJ,CAAC;;;;;;;;;;ECtGG,OAAAE,SAAA,EAAA,EAAAC,kBAAA,CAGI,MAHJC,UAGI,CAAA;IAHCrD,KAAK,EAAEsD,QAAc,CAAAN,cAAA;IAAG,OAAOM,EAAAA,QAAc,CAAAR,cAAA;AAAES,IAAAA,IAAI,EAAC;AAAoB,GAAA,EAAAC,eAAA,CAAAA,eAAA,CAAAF,EAAAA,EAAAA,QAAA,CAAArC,WAAW,WAAaqC,QAAW,CAAArC,WAAA,CAAA,YAAA,CAAA,CAAA,EAAA;AAAmB,IAAA,sBAAoB,EAAEqC,QAAU,CAAA3C,UAAA,CAAA,QAAA;OAC9IY,MAAA,CAAAjB,MAAM,CAACmD,YAAYlC,aAAM,CAACkC,QAAQ,CAACC,MAAM,IAA1DP,SAAA,EAAA,EAAAQ,WAAA,CAA2GC,uBAA1C,CAAArC,MAAA,CAAAjB,MAAM,CAACmD,QAAQ,CAACC,MAAM,CAAA,EAAA;;IAAGpD,MAAM,EAAEiB,MAAM,CAAAjB;2DAC5FgD,QAAU,CAAA3C,UAAA,CAAA,QAAA,CAAA,IAAtBwC,SAAA,EAAA,EAAAC,kBAAA,CAAmI,QAAnIC,UAAmI,CAAA;;AAAhG,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,cAAA;AAA0B,GAAA,EAAAO,QAAA,CAAArC,WAAW,mCAAqBqC,QAAU,CAAA3C,UAAA,CAAA,QAAA,CAAA,CAAA,EAAA,EAAA,CAAA;;;;;ACkCxH,eAAe;AACXjE,EAAAA,IAAI,EAAE,YAAY;AAClB2D,EAAAA,QAAQ,EAAE,WAAW;AACrB,EAAA,SAAA,EAAS1D,aAAa;AACtBmH,EAAAA,KAAK,EAAE,CAAC,cAAc,EAAE,oBAAoB,CAAC;AAC7ClH,EAAAA,KAAK,EAAE;AACH0D,IAAAA,MAAM,EAAE;AACJxD,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;KACZ;AACD6B,IAAAA,gBAAgB,EAAE;AACdxC,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDuB,IAAAA,SAAS,EAAE;AACP/B,MAAAA,IAAI,EAAE,CAACE,MAAM,EAAEC,QAAQ,CAAC;MACxB,SAAS,EAAA;KACZ;AACD6B,IAAAA,SAAS,EAAE;AACPhC,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDqB,IAAAA,aAAa,EAAE;AACXlC,MAAAA,IAAI,EAAEsB,KAAK;MACX,SAAS,EAAA;KACZ;AACDa,IAAAA,QAAQ,EAAE;AACNnC,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDuD,IAAAA,KAAK,EAAE;AACHzD,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;AACb;GACH;EACD6C,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHC,MAAAA,WAAW,EAAE;KAChB;GACJ;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,IAAA,IAAI,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAACC,oBAAoB,EAAE;AAC/B;GACH;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,IAAA,IAAI,IAAI,CAACF,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAACC,oBAAoB,EAAE;AAC/B;GACH;AACDE,EAAAA,OAAO,EAAE;AACLH,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACI,IAAI,EAAE;AACb,MAAA,OAAOC,YAAY,CAAC,IAAI,CAACV,MAAM,EAAES,IAAI,CAAC;KACzC;AACDE,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACC,GAAG,EAAE;AAAA,MAAA,IAAAC,qBAAA;AACb,MAAA,IAAMC,iBAAiB;AACnBxE,QAAAA,KAAK,EAAE,IAAI,CAAC0D,MAAM,CAAC1D,KAAK;AACxByE,QAAAA,MAAM,EAAE;AACJC,UAAAA,QAAQ,EAAE,IAAI;UACd1E,KAAK,EAAE,IAAI,CAAC2E,MAAM;UAClBC,KAAK,EAAE,IAAI,CAACC;SACf;AACDC,QAAAA,OAAO,EAAE;UACLnB,KAAK,EAAE,IAAI,CAACA,KAAK;AACjBwD,UAAAA,MAAM,EAAE,IAAI,CAACC,cAAc,EAAE;AAC7BrC,UAAAA,MAAM,EAAE,IAAI,CAACvB,eAAe,CAACV,UAAS,IAAK,IAAI,CAACiB,UAAU,CAAC,QAAQ,CAAC;UACpEsD,SAAS,EAAE,IAAI,CAAC3E,gBAAgB;AAChCI,UAAAA,UAAU,EAAE,IAAI,CAACU,eAAe,CAACV,UAAU;AAC3CD,UAAAA,aAAa,EAAE,IAAI,CAACW,eAAe,CAACX,aAAa;UACjDG,IAAI,EAAA,CAAAuB,qBAAA,GAAE,IAAI,CAACf,eAAe,MAAAe,IAAAA,IAAAA,qBAAA,KAApBA,MAAAA,GAAAA,MAAAA,GAAAA,qBAAA,CAAsBvB;AAChC;OACH;MAED,OAAOgC,UAAU,CAAC,IAAI,CAACC,GAAG,CAAAC,SAAAA,CAAAA,MAAA,CAAWZ,GAAG,CAAI,EAAA;AAAEZ,QAAAA,MAAM,EAAEc;OAAgB,CAAC,EAAE,IAAI,CAACS,GAAG,CAAAC,SAAAA,CAAAA,MAAA,CAAWZ,GAAG,CAAIE,EAAAA,cAAc,CAAC,EAAE,IAAI,CAACW,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE,EAAEd,GAAG,EAAEE,cAAc,CAAC,CAAC;KAC5K;IACDY,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,OAAO,IAAI,CAAC1B,MAAM,CAAC1D,SAAS,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAACqF,KAAK,IAAI,CAAC3B,MAAM,CAAC1D,KAAK,CAACqF,KAAKxD,SAAS;KACtF;IACDmC,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;AACnB,MAAA,IAAI,IAAI,CAACD,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC3B,QAAA,IAAIuB,KAAM,GAAE,IAAI,CAACvB,UAAU,CAAC,aAAa,CAAC;QAE1C,IAAIuB,KAAM,KAAI,OAAO,EAAE;UACnB,IAAIC,GAAE,GAAI,CAAC;UACX,IAAIC,IAAK,GAAEC,qBAAqB,CAAC,IAAI,CAACC,GAAG,EAAE,+BAA+B,CAAC;AAE3E,UAAA,IAAIF,IAAI,EAAE;AACND,YAAAA,GAAE,GAAII,aAAa,CAACH,IAAI,IAAII,UAAU,CAACJ,IAAI,CAACpC,KAAK,CAAC,kBAAkB,CAAA,IAAK,CAAC,CAAC;AAC/E;AAEA,UAAA,IAAI,CAACS,WAAW,CAACgC,cAAe,GAAEN,GAAE,GAAI,IAAI;AAChD,SAAE,MAAK;UACH,IAAIA,IAAE,GAAI,CAAC;UACX,IAAIO,IAAK,GAAEC,yBAAyB,CAAC,IAAI,CAACL,GAAG,EAAE,+BAA+B,CAAC;AAE/E,UAAA,IAAII,IAAI,EAAE;AACNP,YAAAA,IAAE,GAAII,aAAa,CAACG,IAAI,CAAA,GAAIF,UAAU,CAACE,IAAI,CAAC1C,KAAK,CAAC,oBAAoB,CAAA,IAAK,CAAC,CAAC;AACjF;AAEA,UAAA,IAAI,CAACS,WAAW,CAACmC,gBAAiB,GAAET,IAAE,GAAI,IAAI;AAClD;QAEA,IAAI+B,SAAU,GAAE,IAAI,CAAC5B,GAAG,CAAC6B,aAAa,CAACC,kBAAkB;AAEzD,QAAA,IAAIF,SAAS,EAAE;AACX,UAAA,IAAI3D,KAAM,GAAE8D,QAAQ,CAAC,IAAI,CAAC/B,GAAG,CAAC;AAE9B4B,UAAAA,SAAS,CAACT,QAAQ,CAAClD,KAAK,CAAC,CAACP,KAAK,CAAC,oBAAoB,CAAE,GAAE,IAAI,CAACS,WAAW,CAAC,oBAAoB,CAAC;AAC9FyD,UAAAA,SAAS,CAACT,QAAQ,CAAClD,KAAK,CAAC,CAACP,KAAK,CAAC,kBAAkB,CAAE,GAAE,IAAI,CAACS,WAAW,CAAC,kBAAkB,CAAC;AAC9F;AACJ;KACH;AACD6D,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,KAAK,EAAE;AACX,MAAA,IAAI,CAACC,KAAK,CAAC,cAAc,EAAE;AAAEC,QAAAA,aAAa,EAAEF,KAAK;QAAEjE,MAAM,EAAE,IAAI,CAACA;AAAO,OAAC,CAAC;KAC5E;AACDoE,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACH,KAAK,EAAE;AACb,MAAA,IAAI,CAACA,KAAK,CAACI,IAAG,KAAM,OAAQ,IAAGJ,KAAK,CAACI,IAAG,KAAM,aAAc,IAAGJ,KAAK,CAACI,IAAG,KAAM,OAAO,KAAKJ,KAAK,CAACK,aAAa,CAACC,QAAO,KAAM,IAAK,IAAGC,YAAY,CAACP,KAAK,CAACK,aAAa,EAAE,wBAAwB,CAAC,EAAE;AAC5L,QAAA,IAAI,CAACJ,KAAK,CAAC,cAAc,EAAE;AAAEC,UAAAA,aAAa,EAAEF,KAAK;UAAEjE,MAAM,EAAE,IAAI,CAACA;AAAO,SAAC,CAAC;QAEzEiE,KAAK,CAACQ,cAAc,EAAE;AAC1B;KACH;AACDC,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACT,KAAK,EAAE;AACjB,MAAA,IAAI,CAACC,KAAK,CAAC,oBAAoB,EAAED,KAAK,CAAC;KAC1C;IACDU,qBAAqB,EAAA,SAArBA,qBAAqBA,GAAG;MACpB,IAAI1E,KAAI,GAAI,EAAE;AAEd,MAAA,KAAK,IAAI2E,IAAI,CAAC,EAAEA,CAAA,GAAI,IAAI,CAAClG,aAAa,CAACmG,MAAM,EAAED,CAAC,EAAE,EAAE;AAChD,QAAA,IAAIE,IAAK,GAAE,IAAI,CAACpG,aAAa,CAACkG,CAAC,CAAC;QAEhC,IAAIE,IAAI,CAACC,KAAM,KAAI,IAAI,CAAC1E,UAAU,CAAC,OAAO,KAAKyE,IAAI,CAACC,KAAI,KAAM,IAAI,CAAC1E,UAAU,CAAC,WAAW,CAAC,EAAE;AACxFJ,UAAAA,KAAI,GAAI2E,CAAC;AACT,UAAA;AACJ;AACJ;AAEA,MAAA,OAAO3E,KAAK;KACf;IACD+E,aAAa,EAAA,SAAbA,aAAaA,GAAG;AACZ,MAAA,OAAO,IAAI,CAAC3E,UAAU,CAAC,UAAU,CAAA,IAAK,IAAI,CAACsE,qBAAqB,EAAG,GAAE,EAAE;KAC1E;IACDjB,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,OAAO,IAAI,CAAC/E,QAAO,KAAM,QAAS,GAAE,IAAI,CAACJ,SAAU,KAAI,IAAI,CAACA,cAAc,IAAI,CAAC8B,UAAU,CAAC,OAAO,CAAA,IAAK,IAAI,CAAC9B,SAAU,KAAI,IAAI,CAAC8B,UAAU,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC2E,aAAa,EAAE;AACjL;GACH;AACDzC,EAAAA,QAAQ,EAAE;IACNC,cAAc,EAAA,SAAdA,cAAcA,GAAG;MACb,OAAO,CAAC,IAAI,CAACnC,UAAU,CAAC,aAAa,CAAC,EAAE,IAAI,CAACA,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,CAACoC,EAAE,CAAC,YAAY,CAAC,CAAC;KAC3F;IACDC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,IAAIuC,cAAc,IAAI,CAAC5E,UAAU,CAAC,aAAa,CAAC;AAChD,MAAA,IAAIuC,WAAU,GAAI,IAAI,CAACvC,UAAU,CAAC,OAAO,CAAC;MAE1C,OAAO,IAAI,CAACA,UAAU,CAAC,QAAQ,IAAI,CAACuC,WAAW,EAAEqC,WAAW,EAAE,IAAI,CAAC9E,WAAW,CAAE,GAAE,CAACyC,WAAW,EAAEqC,WAAW,CAAC;KAC/G;IACDC,SAAS,EAAA,SAATA,SAASA,GAAG;MACR,IAAIzB,MAAK,GAAI,KAAK;MAClB,IAAIjF,SAAU,GAAE,IAAI;AAEpB,MAAA,IAAI,IAAI,CAACG,QAAS,KAAI,QAAQ,EAAE;QAC5B8E,MAAK,GAAI,IAAI,CAAClF,cAAc,IAAI,CAACA,SAAU,KAAI,IAAI,CAAC8B,UAAU,CAAC,OAAO,CAAE,IAAG,IAAI,CAAC9B,SAAQ,KAAM,IAAI,CAAC8B,UAAU,CAAC,WAAW,CAAC,CAAC;AAC3H7B,QAAAA,SAAQ,GAAIiF,MAAK,GAAI,IAAI,CAACjF,SAAQ,GAAI,CAAC;AAC3C,OAAA,MAAO,IAAI,IAAI,CAACG,QAAO,KAAM,UAAU,EAAE;AACrC,QAAA,IAAIwG,YAAY,IAAI,CAACR,qBAAqB,EAAE;AAE5C,QAAA,IAAIQ,SAAQ,GAAI,EAAE,EAAE;AAChB1B,UAAAA,SAAS,IAAI;UACbjF,SAAU,GAAE,IAAI,CAACE,aAAa,CAACyG,SAAS,CAAC,CAACC,KAAK;AACnD;AACJ;MAEA,OAAO;AACH3B,QAAAA,MAAM,EAANA,MAAM;AACNjF,QAAAA,SAAQ,EAARA;OACH;KACJ;IACD6G,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AACjB,MAAA,IAAAC,eAAA,GAA8B,IAAI,CAACJ,SAAS;QAApCzB,MAAM,GAAA6B,eAAA,CAAN7B,MAAM;QAAEjF,4BAAAA;MAEhB,IAAI,CAACiF,MAAM,EAAE,OAAO8B,WAAW,CAAA,KAC1B,IAAI9B,UAAUjF,SAAQ,GAAI,CAAC,EAAE,OAAOgH,mBAAmB,CAAA,KACvD,IAAI/B,UAAUjF,SAAU,GAAE,CAAC,EAAE,OAAOiH,kBAAkB;AAE3D,MAAA,OAAO,IAAI;KACd;IACDC,QAAQ,EAAA,SAARA,QAAQA,GAAG;AACP,MAAA,IAAI,IAAI,CAACrF,UAAU,CAAC,UAAU,CAAC,EAAE;AAC7B,QAAA,IAAAsF,gBAAA,GAA8B,IAAI,CAACT,SAAS;UAApCzB,MAAM,GAAAkC,gBAAA,CAANlC,MAAM;UAAEjF,6BAAAA;QAEhB,IAAIiF,MAAK,IAAKjF,SAAQ,GAAI,CAAC,EAAE,OAAO,YAAY,CAAA,KAC3C,IAAIiF,MAAK,IAAKjF,SAAQ,GAAI,CAAC,EAAE,OAAO,WAAW,CAAA,KAC/C,OAAO,MAAM;AACtB,OAAE,MAAK;AACH,QAAA,OAAO,IAAI;AACf;AACJ;GACH;AACDoH,EAAAA,UAAU,EAAE;AACRC,IAAAA,KAAK,EAALA,KAAK;AACLN,IAAAA,WAAW,EAAEA,WAAW;AACxBC,IAAAA,mBAAmB,EAAEA,mBAAmB;AACxCC,IAAAA,kBAAkB,EAAEA;AACxB;AACJ,CAAC;;;;;;;;;;;EChPG,OAAA5C,SAAA,EAAA,EAAAC,kBAAA,CAuBI,MAvBJC,UAuBI,CAAA;IAtBC,OAAOC,EAAAA,QAAc,CAAAR,cAAA;AACrB9C,IAAAA,KAAK,GAAGsD,QAAc,CAAAN,cAAA,CAAA;IACtBsB,OAAK;aAAEhB,QAAO,CAAAgB,OAAA,IAAAhB,QAAA,CAAAgB,OAAA,CAAA8B,KAAA,CAAA9C,QAAA,EAAA+C,SAAA,CAAA;AAAA,KAAA,CAAA;IACdC,SAAO;aAAEhD,QAAS,CAAAoB,SAAA,IAAApB,QAAA,CAAAoB,SAAA,CAAA0B,KAAA,CAAA9C,QAAA,EAAA+C,SAAA,CAAA;AAAA,KAAA,CAAA;IAClBE,QAAQ,EAAEjD,QAAU,CAAA3C,UAAA,CAAA,UAAA,CAAA,GAAA,GAAA,GAAA,IAAA;IACpB,WAAS,EAAE2C,QAAQ,CAAA0C,QAAA;AACpBzC,IAAAA,IAAI,EAAC;AACQ,GAAA,EAAAC,eAAA,CAAAA,eAAA,CAAAF,EAAAA,EAAAA,QAAA,CAAArC,WAAW,WAAaqC,QAAW,CAAArC,WAAA,CAAA,YAAA,CAAA,CAAA,EAAA;AAC/C,IAAA,wBAAsB,EAAEqC,QAAU,CAAA3C,UAAA,CAAA,UAAA,CAAA;IAClC,yBAAuB,EAAEY,MAAgB,CAAAjC,gBAAA;AACzC,IAAA,eAAa,EAAEgE,QAAc,CAAAU,cAAA,EAAA;AAC7B,IAAA,sBAAoB,EAAEV,QAAU,CAAA3C,UAAA,CAAA,QAAA;OAErBY,MAAA,CAAAjC,gBAAiB,KAAIgE,QAAU,CAAA3C,UAAA,CAAA,QAAA,CAAA,IAA3CwC,SAAA,EAAA,EAAAC,kBAAA,CAA2J,QAA3JC,UAA2J,CAAA;;AAAnG,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,eAAA,CAAA;IAAoByD,WAAS;aAAElD,QAAa,CAAA0B,aAAA,IAAA1B,QAAA,CAAA0B,aAAA,CAAAoB,KAAA,CAAA9C,QAAA,EAAA+C,SAAA,CAAA;KAAA;KAAU/C,QAAW,CAAArC,WAAA,CAAA,eAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,kCAClIwF,kBAAA,CAOK,OAPLpD,UAOK,CAAA;AAPC,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,qBAAA;KAAiCO,QAAW,CAAArC,WAAA,CAAA,qBAAA,CAAA,CAAA,EAAA,CACtCM,MAAA,CAAAjB,MAAM,CAACmD,YAAYlC,aAAM,CAACkC,QAAQ,CAACiD,MAAM,IAA1DvD,SAAA,EAAA,EAAAQ,WAAA,CAA2GC,uBAA1C,CAAArC,MAAA,CAAAjB,MAAM,CAACmD,QAAQ,CAACiD,MAAM,CAAA,EAAA;;IAAGpG,MAAM,EAAEiB,MAAM,CAAAjB;2DAC5FgD,QAAU,CAAA3C,UAAA,CAAA,QAAA,CAAA,IAAtBwC,SAAA,EAAA,EAAAC,kBAAA,CAAiI,QAAjIC,UAAiI,CAAA;;AAA9F,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,aAAA;GAAyB,EAAAO,QAAA,CAAArC,WAAW,kCAAoBqC,QAAU,CAAA3C,UAAA,CAAA,QAAA,CAAA,CAAA,EAAA,EAAA,CAAA,kCAClG2C,QAAU,CAAA3C,UAAA,CAAA,UAAA,CAAA,IAAtBwC,SAAA,EAAA,EAAAC,kBAAA,CAEM;;KAFsCE,QAAW,CAAArC,WAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,EACnDkC,SAAA,EAAA,EAAAQ,WAAA,CAA2MC,uBAA1L,CAAArC,MAAA,CAAAjB,MAAM,CAACmD,QAAS,IAAGlC,MAAA,CAAAjB,MAAM,CAACmD,QAAQ,CAACkD,QAAQ,IAAKrD,QAAA,CAAAqC,kBAAkB,GAAnFtC,UAA2M,CAAA;AAArHU,IAAAA,MAAM,EAAET,QAAS,CAAAkC,SAAA,CAACzB,MAAM;AAAGjF,IAAAA,SAAS,EAAEwE,QAAS,CAAAkC,SAAA,CAAC1G,SAAS;AAAG,IAAA,OAAA,EAAO+E,IAAE,CAAAd,EAAA,CAAA,UAAA;KAAsBO,QAAW,CAAArC,WAAA,CAAA,UAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,QAAA,EAAA,WAAA,EAAA,OAAA,CAAA,CAAA,yCAEnLqC,QAAa,CAAAgC,aAAA,EAAA,IAA1BnC,SAAA,EAAA,EAAAQ,WAAA,CAAgJiD,kBAAhJvD,UAAgJ,CAAA;;AAAjH,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,aAAA;KAAyBO,QAAW,CAAArC,WAAA,CAAA,aAAA,CAAA,EAAA;AAAkBpE,IAAAA,KAAK,EAAEyG,QAAqB,CAAA2B,qBAAA,EAAA,GAAA,CAAA;AAAQrF,IAAAA,IAAI,EAAC;;;;;;ACkCnJ,eAAe;AACXlD,EAAAA,IAAI,EAAE,UAAU;AAChB2D,EAAAA,QAAQ,EAAE,WAAW;AACrB,EAAA,SAAA,EAAS1D,aAAa;AACtBmH,EAAAA,KAAK,EAAE,CAAC,aAAa,EAAE,iBAAiB,CAAC;AACzClH,EAAAA,KAAK,EAAE;AACHiK,IAAAA,IAAI,EAAE;AACF/J,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;KACZ;AACD6C,IAAAA,MAAM,EAAE;AACJxD,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;KACZ;AACDqJ,IAAAA,KAAK,EAAE;AACHhK,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACD6B,IAAAA,WAAW,EAAE;AACT1C,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDoJ,IAAAA,IAAI,EAAE;AACFjK,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACD0J,IAAAA,QAAQ,EAAE;AACNlK,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDF,IAAAA,aAAa,EAAE;AACXN,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDiK,IAAAA,OAAO,EAAE;AACLnK,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACD4J,IAAAA,cAAc,EAAE;AACZpK,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACD6J,IAAAA,SAAS,EAAE;AACPrK,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;KACZ;AACD8C,IAAAA,KAAK,EAAE;AACHzD,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDe,IAAAA,WAAW,EAAE;AACT5B,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;AACb;GACH;EACDwD,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHC,MAAAA,WAAW,EAAE;KAChB;GACJ;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,IAAA,IAAI,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAACC,oBAAoB,EAAE;AAC/B;GACH;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,IAAA,IAAI,IAAI,CAACF,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAACC,oBAAoB,EAAE;AAC/B;GACH;AACDE,EAAAA,OAAO,EAAE;IACLsG,MAAM,EAAA,SAANA,MAAMA,GAAG;MACL,IAAI,CAAC5C,KAAK,CAAC,aAAa,EAAE,IAAI,CAACqC,IAAI,CAAC;KACvC;AACDlG,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACI,IAAI,EAAE;AACb,MAAA,OAAOC,YAAY,CAAC,IAAI,CAACV,MAAM,EAAES,IAAI,CAAC;KACzC;AACDE,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACC,GAAG,EAAE;AAAA,MAAA,IAAAC,qBAAA;AACb,MAAA,IAAMC,iBAAiB;AACnBxE,QAAAA,KAAK,EAAE,IAAI,CAAC0D,MAAM,CAAC1D,KAAK;AACxByE,QAAAA,MAAM,EAAE;AACJC,UAAAA,QAAQ,EAAE,IAAI;UACd1E,KAAK,EAAE,IAAI,CAAC2E,MAAM;UAClBC,KAAK,EAAE,IAAI,CAACC;SACf;AACDC,QAAAA,OAAO,EAAE;UACLnB,KAAK,EAAE,IAAI,CAACA,KAAK;UACjB8G,UAAU,EAAE,IAAI,CAACjH,eAAe,CAACzB,QAAS,IAAG,IAAI,CAACyB,eAAe,CAACkH,gBAAgB;AAClFC,UAAAA,QAAQ,EAAE,IAAI,CAACC,OAAO,CAACD,QAAQ;AAC/B5F,UAAAA,MAAM,EAAE,IAAI,CAAChB,UAAU,CAAC,QAAQ,CAAC;AACjCjB,UAAAA,UAAU,EAAE,IAAI,CAACU,eAAe,CAACV,UAAU;AAC3CD,UAAAA,aAAa,EAAE,IAAI,CAACW,eAAe,CAACX,aAAa;UACjDG,IAAI,EAAA,CAAAuB,qBAAA,GAAE,IAAI,CAACf,eAAe,MAAA,IAAA,IAAAe,qBAAA,KAAA,MAAA,GAAA,MAAA,GAApBA,qBAAA,CAAsBvB,IAAI;UAChCiH,IAAI,EAAE,IAAI,CAACA;AACf;OACH;MAED,OAAOjF,UAAU,CAAC,IAAI,CAACC,GAAG,CAAAC,SAAAA,CAAAA,MAAA,CAAWZ,GAAG,CAAI,EAAA;AAAEZ,QAAAA,MAAM,EAAEc;OAAgB,CAAC,EAAE,IAAI,CAACS,GAAG,CAAAC,SAAAA,CAAAA,MAAA,CAAWZ,GAAG,CAAIE,EAAAA,cAAc,CAAC,EAAE,IAAI,CAACW,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE,EAAEd,GAAG,EAAEE,cAAc,CAAC,CAAC;KAC5K;IACDY,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,OAAO,IAAI,CAAC1B,MAAM,CAAC1D,SAAS,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAACqF,EAAG,GAAE,IAAI,CAAC3B,MAAM,CAAC1D,KAAK,CAACqF,EAAG,GAAExD,SAAS,CAAE;KACxF;AACDgJ,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAACvG,GAAG,EAAE;AACrB,MAAA,IAAME,iBAAiB;AACnBxE,QAAAA,KAAK,EAAE,IAAI,CAAC0D,MAAM,CAAC1D,KAAK;AACxByE,QAAAA,MAAM,EAAE;AACJC,UAAAA,QAAQ,EAAE,IAAI;UACd1E,KAAK,EAAE,IAAI,CAAC2E,MAAM;UAClBC,KAAK,EAAE,IAAI,CAACC;SACf;AACDC,QAAAA,OAAO,EAAE;UACLuF,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBC,cAAc,EAAE,IAAI,CAACA,cAAc;UACnCL,IAAI,EAAE,IAAI,CAACA;AACf;OACH;MAED,OAAOjF,UAAU,CAAC,IAAI,CAACC,GAAG,CAAAC,SAAAA,CAAAA,MAAA,CAAWZ,GAAG,CAAI,EAAA;AAAEZ,QAAAA,MAAM,EAAEc;OAAgB,CAAC,EAAE,IAAI,CAACS,GAAG,CAAAC,SAAAA,CAAAA,MAAA,CAAWZ,GAAG,CAAIE,EAAAA,cAAc,CAAC,EAAE,IAAI,CAACW,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE,EAAEd,GAAG,EAAEE,cAAc,CAAC,CAAC;KAC5K;IACDR,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;AACnB,MAAA,IAAI,IAAI,CAACD,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC3B,QAAA,IAAIuB,KAAM,GAAE,IAAI,CAACvB,UAAU,CAAC,aAAa,CAAC;QAE1C,IAAIuB,KAAM,KAAI,OAAO,EAAE;UACnB,IAAIC,GAAE,GAAI,CAAC;UACX,IAAIC,IAAK,GAAEC,qBAAqB,CAAC,IAAI,CAACC,GAAG,EAAE,+BAA+B,CAAC;AAE3E,UAAA,IAAIF,IAAI,EAAE;AACND,YAAAA,GAAE,GAAII,aAAa,CAACH,IAAI,IAAII,UAAU,CAACJ,IAAI,CAACpC,KAAK,CAAC,kBAAkB,CAAA,IAAK,CAAC,CAAC;AAC/E;AAEA,UAAA,IAAI,CAACS,WAAW,CAACgC,cAAe,GAAEN,GAAE,GAAI,IAAI;AAChD,SAAE,MAAK;UACH,IAAIA,IAAE,GAAI,CAAC;UACX,IAAIO,IAAK,GAAEC,yBAAyB,CAAC,IAAI,CAACL,GAAG,EAAE,+BAA+B,CAAC;AAE/E,UAAA,IAAII,IAAI,EAAE;AACNP,YAAAA,IAAE,GAAII,aAAa,CAACG,IAAI,CAAA,GAAIF,UAAU,CAACE,IAAI,CAAC1C,KAAK,CAAC,oBAAoB,CAAA,IAAK,CAAC,CAAC;AACjF;AAEA,UAAA,IAAI,CAACS,WAAW,CAACmC,gBAAiB,GAAET,IAAE,GAAI,IAAI;AAClD;AACJ;KACH;AACDuF,IAAAA,gBAAgB,WAAhBA,kBAAgBA,CAACC,OAAO,EAAEtC,KAAK,EAAE;AAC7B,MAAA,OAAOqC,gBAAgB,CAACC,OAAO,EAAEtC,KAAK,CAAC;KAC1C;IACDuC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,IAAI,CAACpD,KAAK,CAAC,iBAAiB,CAAC;AACjC;GACH;AACD3B,EAAAA,QAAQ,EAAE;IACNC,cAAc,EAAA,SAAdA,cAAcA,GAAG;MACb,OAAO,CAAC,IAAI,CAACnC,UAAU,CAAC,WAAW,CAAC,EAAE,IAAI,CAACA,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,CAACoC,EAAE,CAAC,UAAU,CAAC,CAAC;KACvF;IACDC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,IAAIC,YAAY,IAAI,CAACtC,UAAU,CAAC,WAAW,CAAC;AAC5C,MAAA,IAAIuC,WAAU,GAAI,IAAI,CAACvC,UAAU,CAAC,OAAO,CAAC;MAE1C,OAAO,IAAI,CAACA,UAAU,CAAC,QAAQ,CAAE,GAAE,CAACuC,WAAW,EAAED,SAAS,EAAE,IAAI,CAACxC,WAAW,CAAA,GAAI,CAACyC,WAAW,EAAED,SAAS,CAAC;KAC3G;IACD4E,YAAY,EAAA,SAAZA,YAAYA,GAAG;MACX,OAAO;QACHC,UAAU,EAAE,IAAI,CAAChB,KAAM,GAAE,IAAI,CAACtH,WAAY,GAAE,KAAK;AACjDuI,QAAAA,UAAU,EAAE,IAAI,CAAChB,IAAK,GAAE,QAAO,GAAI;OACtC;KACJ;IACDiB,qBAAqB,EAAA,SAArBA,qBAAqBA,GAAG;AACpB,MAAA,OAAO,IAAI,CAAC5K,aAAc,KAAI,UAAU;AAC5C;GACH;AACD8I,EAAAA,UAAU,EAAE;AACR+B,IAAAA,QAAQ,EAARA,QAAQ;AACRC,IAAAA,gBAAgB,EAAhBA,gBAAgB;AAChBC,IAAAA,eAAe,EAAfA,eAAe;AACfC,IAAAA,SAAS,EAATA,SAAS;AACTC,IAAAA,SAAS,EAATA,SAAS;AACTC,IAAAA,WAAU,EAAVA;GACH;AACDC,EAAAA,UAAU,EAAE;AACRC,IAAAA,MAAM,EAAEC;AACZ;AACJ,CAAC;;;;;;;;;;;;;EC7OG,OAAAtF,SAAA,EAAA,EAAAC,kBAAA,CAsCI,MAtCJC,UAsCI,CAAA;IAtCCrD,KAAK,EAAEsD,QAAc,CAAAN,cAAA;IAAG,OAAOM,EAAAA,QAAc,CAAAR,cAAA;AAAES,IAAAA,IAAI,EAAC;AAAoB,GAAA,EAAAC,eAAA,CAAAA,eAAA,CAAAF,EAAAA,EAAAA,QAAA,CAAArC,WAAW,WAAaqC,QAAW,CAAArC,WAAA,CAAA,UAAA,CAAA,CAAA,EAAA;AAAiB,IAAA,sBAAoB,EAAEqC,QAAU,CAAA3C,UAAA,CAAA,QAAA;OAC7J8F,kBAAA,CAoCK,OApCLpD,UAoCK,CAAA;AApCC,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,iBAAA;KAA6BO,QAAW,CAAArC,WAAA,CAAA,iBAAA,CAAA,CAAA,EAAA,CACrCqC,QAAU,CAAA3C,UAAA,CAAA,UAAA,CAAA,GAAxB+H,cAAA,EAAAvF,SAAA,EAAA,EAAAC,kBAAA,CAeQ,UAfRC,UAeQ,CAAA;;AAfuCvG,IAAAA,IAAI,EAAC;AAAU,IAAA,OAAA,EAAO+G,IAAE,CAAAd,EAAA,CAAA,kBAAA,CAAA;IAAuBuB,OAAK;aAAEhB,QAAM,CAAA8D,MAAA,IAAA9D,QAAA,CAAA8D,MAAA,CAAAhB,KAAA,CAAA9C,QAAA,EAAA+C,SAAA,CAAA;AAAA,KAAA,CAAA;IAAGrG,KAAK,EAAEsD,QAAY,CAAAuE,YAAA;AAAEtB,IAAAA,QAAQ,EAAC;KAAajD,QAAW,CAAArC,WAAA,CAAA,kBAAA,CAAA,EAAA;AAAsB,IAAA,uBAAqB,EAAC;GAAiB,CAAA,EAAA,CAC7MM,MAAI,CAAAsF,IAAA,CAACtI,OAAQ,IAAGgD,MAAY,CAAA7C,WAAA,KAAA,MAAA,iBAA5C0E,kBAKU,CAAAuF,QAAA,EAAA;AAAAzH,IAAAA,GAAA,EAAA;GAAA,EAAA,CAJWK,MAAS,CAAA4F,SAAA,CAAA,gBAAA,CAAA,IAA1BhE,SAAA,EAAA,EAAAQ,WAAA,CAAiFC,wBAA9BrC,MAAS,CAAA4F,SAAA,CAAA,gBAAA,CAAA,CAAA,EAAA;AAAAjG,IAAAA,GAAA,EAAA;AAAA,GAAA,CAAA,kCAE3CK,MAAS,CAAA4F,SAAA,CAAA,iBAAA,CAAA,IAA1BhE,SAAA,EAAA,EAAAQ,WAAA,CAAmFC,wBAA/BrC,MAAS,CAAA4F,SAAA,CAAA,iBAAA,CAAA,CAAA,EAAA;AAAAjG,IAAAA,GAAA,EAAA;GAAA,CAAA,KAC7DiC,SAAA,EAAA,EAAAQ,WAAA,CAAyDiF,wBAAzDvF,UAAyD,CAAA;;AAArCwF,IAAAA,IAAG,EAAH;KAAahF,IAAG,CAAAhC,GAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,CAAA,wBAExCuB,kBAOU,CAAAuF,QAAA,EAAA;AAAAzH,IAAAA,GAAA,EAAA;AAAA,GAAA,EAAA,CANWK,MAAA,CAAAjB,MAAM,CAACmD,QAAO,IAAKlC,aAAM,CAACkC,QAAQ,CAACqF,aAAa,IAAjE3F,SAAA,EAAA,EAAAQ,WAAA,CAAwKC,uBAAhG,CAAArC,MAAA,CAAAjB,MAAM,CAACmD,QAAQ,CAACqF,aAAa,CAAA,EAAA;;IAAGjC,IAAI,EAAEtF,MAAI,CAAAsF,IAAA;IAAGG,QAAQ,EAAEzF,MAAQ,CAAAyF,QAAA;AAAG,IAAA,OAAA,iBAAOnD,IAAE,CAAAd,EAAA,CAAA,gBAAA,CAAA;gDAC7HxB,MAAS,CAAA4F,SAAA,CAAA,gBAAA,CAAA,IAA/BhE,SAAA,EAAA,EAAAQ,WAAA,CAAsJC,wBAA9FrC,MAAS,CAAA4F,SAAA,CAAA,gBAAA,CAAA,CAAA,EAAA;;IAAqBN,IAAI,EAAEtF,MAAI,CAAAsF,IAAA;IAAGG,QAAQ,EAAEzF,MAAQ,CAAAyF,QAAA;AAAG,IAAA,OAAA,iBAAOnD,IAAE,CAAAd,EAAA,CAAA,gBAAA,CAAA;gDAE3GxB,MAAA,CAAAjB,MAAM,CAACmD,YAAYlC,aAAM,CAACkC,QAAQ,CAACsF,cAAc,IAAvE5F,SAAA,EAAA,EAAAQ,WAAA,CAA+KC,uBAAjG,CAAArC,MAAA,CAAAjB,MAAM,CAACmD,QAAQ,CAACsF,cAAc,CAAA,EAAA;;IAAGlC,IAAI,EAAEtF,MAAI,CAAAsF,IAAA;IAAGG,QAAQ,EAAEzF,MAAQ,CAAAyF,QAAA;AAAG,IAAA,OAAA,iBAAOnD,IAAE,CAAAd,EAAA,CAAA,gBAAA,CAAA;gDACpIxB,MAAQ,CAAAyF,QAAA,IAA9B7D,SAAA,EAAA,EAAAQ,WAAA,CAA2JC,uBAAtH,CAAArC,MAAA,CAAAsF,IAAI,CAACmC,4CAA1C3F,UAA2J,CAAA;;AAArE,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,gBAAA;KAA4BO,QAAW,CAAArC,WAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,KACtIkC,SAAA,EAAA,EAAAQ,WAAA,CAA+IC,uBAAxH,CAAArC,MAAA,CAAAsF,IAAI,CAACoC,aAAY,iCAAxC5F,UAA+I,CAAA;;AAArE,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,gBAAA;KAA4BO,QAAW,CAAArC,WAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA,uEAIxHqC,QAAA,CAAA0E,qBAAsB,IAAG1E,QAAU,CAAA3C,UAAA,CAAA,UAAA,CAAA,iBAD7CgD,WAgBU,CAAAuF,mBAAA,EAAA;;IAdLC,UAAU,EAAE5H,MAAO,CAAA0F,OAAA;AACnBmC,IAAAA,MAAM,EAAE,IAAI;IACZ,wBAAOvF,IAAE,CAAAd,EAAA,CAAA,gBAAA,CAAA,CAAA;AACTsG,IAAAA,QAAQ,EAAE9H,MAAI,CAAAsF,IAAA,CAACQ,UAAS,KAAA,KAAA;IACxBiC,QAAM,EAAEhG,QAAc,CAAAsE,cAAA;IACtBrB,QAAQ,EAAE,EAAE;IACZgD,aAAa,EAAEhI,MAAc,CAAA2F,cAAA;IAC7BsC,QAAQ,EAAE3F,IAAQ,CAAA2F,QAAA;AAClBvH,IAAAA,EAAE,EAAEqB,QAAmB,CAAAmE,mBAAA,CAAA,gBAAA,CAAA;IACvB,uBAAqB,EAAElG,MAAc,CAAA2F;;AAE3BuC,IAAAA,IAAI,EAAAC,OAAA,CACX,UAAoKC,SAD9I,EAAA;MAAA,OAAA,CACLpI,MAAS,CAAA4F,SAAA,CAAA,cAAA,CAAA,IAA1BhE,SAAA,EAAA,EAAAQ,WAAA,CAAoKC,wBAAnHrC,MAAS,CAAA4F,SAAA,CAAA,cAAA,CAAA,CAAA,EAAA;;QAAmBF,OAAO,EAAE0C,SAAS,CAAC1C,OAAO;QAAGC,cAAc,EAAE3F,MAAc,CAAA2F,cAAA;QAAG,OAAK0C,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;sJAGxJpI,MAAA,CAAAjB,MAAM,CAACmD,QAAO,IAAKlC,aAAM,CAACkC,QAAQ,CAACoG,IAAI,IAAxD1G,SAAA,EAAA,EAAAQ,WAAA,CAAoHC,uBAArD,CAAArC,MAAA,CAAAjB,MAAM,CAACmD,QAAQ,CAACoG,IAAI,CAAA,EAAA;;IAAGhD,IAAI,EAAEtF,MAAI,CAAAsF,IAAA;IAAGvG,MAAM,EAAEiB,MAAM,CAAAjB;mDACjH8C,kBAAiF,CAAAuF,QAAA,EAAA;AAAAzH,IAAAA,GAAA,EAAA;AAAA,GAAA,EAAA,CAA7D4I,eAAA,CAAAC,eAAA,CAAAzG,QAAA,CAAAoE,gBAAgB,CAACnG,MAAA,CAAAsF,IAAI,CAACrG,IAAI,EAAE8C,QAAU,CAAA3C,UAAA,CAAA,OAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;ACyCtE,eAAe;AACXjE,EAAAA,IAAI,EAAE,cAAc;AACpB2D,EAAAA,QAAQ,EAAE,WAAW;AACrB,EAAA,SAAA,EAAS1D,aAAa;AACtBmH,EAAAA,KAAK,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,CAAC;AACvIlH,EAAAA,KAAK,EAAE;AACHiK,IAAAA,IAAI,EAAE;AACF/J,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDC,IAAAA,OAAO,EAAE;AACLD,MAAAA,IAAI,EAAE,CAACE,MAAM,EAAEC,QAAQ,CAAC;MACxB,SAAS,EAAA;KACZ;AACD+M,IAAAA,UAAU,EAAE;AACRlN,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDmN,IAAAA,OAAO,EAAE;AACLnN,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDI,IAAAA,YAAY,EAAE;AACVJ,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDK,IAAAA,aAAa,EAAE;AACXL,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDM,IAAAA,aAAa,EAAE;AACXN,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACD8J,IAAAA,KAAK,EAAE;AACHhK,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACD6B,IAAAA,WAAW,EAAE;AACT1C,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACD4I,IAAAA,QAAQ,EAAE;AACNzJ,MAAAA,IAAI,EAAEa,MAAM;AACZ,MAAA,SAAA,EAAS;KACZ;AACDuM,IAAAA,WAAW,EAAE;AACTpN,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDwM,IAAAA,YAAY,EAAE;AACVrN,MAAAA,IAAI,EAAEa,MAAM;MACZ,SAAS,EAAA;KACZ;AACDe,IAAAA,WAAW,EAAE;AACT5B,MAAAA,IAAI,EAAEE,MAAM;MACZ,SAAS,EAAA;KACZ;AACDmK,IAAAA,SAAS,EAAE;AACPrK,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;KACZ;AACDF,IAAAA,WAAW,EAAE;AACTT,MAAAA,IAAI,EAAEQ,OAAO;MACb,SAAS,EAAA;KACZ;AACDE,IAAAA,oBAAoB,EAAE;AAClBV,MAAAA,IAAI,EAAEW,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACD2M,EAAAA,WAAW,EAAE,KAAK;AAClBtJ,EAAAA,OAAO,EAAE;AACLH,IAAAA,UAAU,WAAVA,UAAUA,CAAC0J,GAAG,EAAEtJ,IAAI,EAAE;AAClB,MAAA,OAAOC,YAAY,CAACqJ,GAAG,EAAEtJ,IAAI,CAAC;KACjC;IACDqG,MAAM,EAAA,SAANA,MAAMA,GAAG;MACL,IAAI,CAAC5C,KAAK,CAAC,aAAa,EAAE,IAAI,CAACqC,IAAI,CAAC;KACvC;AACDvC,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,KAAK,EAAE;AACX,MAAA,IAAI+F,WAAW,CAAC/F,KAAK,CAACgG,MAAM,CAAE,IAAGzF,YAAY,CAACP,KAAK,CAACgG,MAAM,EAAE,iBAAiB,CAAA,KAAM,kBAAmB,IAAGzF,YAAY,CAACP,KAAK,CAACgG,MAAM,EAAE,iBAAiB,CAAA,KAAM,oBAAoBhG,KAAK,CAACgG,MAAM,CAACC,OAAQ,KAAI,MAAM,EAAE;AAC5M,QAAA;AACJ;MAEA,IAAI,CAACC,2BAA2B,CAAClG,KAAK,EAAE,IAAI,CAAC6F,WAAW,CAAC;AAEzD,MAAA,IAAI,CAAC5F,KAAK,CAAC,YAAY,EAAE;AACrBC,QAAAA,aAAa,EAAEF,KAAK;QACpB6F,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BvD,IAAI,EAAE,IAAI,CAACA;AACf,OAAC,CAAC;MACF,IAAI,CAACuD,WAAY,GAAE,KAAK;KAC3B;AACDM,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAACnG,KAAK,EAAE;AACnB,MAAA,IAAI,CAACC,KAAK,CAAC,gBAAgB,EAAE;AACzBC,QAAAA,aAAa,EAAEF,KAAK;QACpBsC,IAAI,EAAE,IAAI,CAACA;AACf,OAAC,CAAC;KACL;IACD8D,UAAU,EAAA,SAAVA,UAAUA,GAAG;MACT,IAAI,CAACP,WAAU,GAAI,IAAI;KAC1B;AACDQ,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAAC/D,IAAI,EAAE;AACV,MAAA,OAAOa,gBAAgB,CAACb,IAAI,EAAE,IAAI,CAAC9J,OAAO,CAAC;KAC9C;AACD2H,IAAAA,SAAS,WAATA,SAASA,CAACH,KAAK,EAAEsG,IAAI,EAAE;MACnB,QAAQtG,KAAK,CAACI,IAAI;AACd,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACmG,cAAc,CAACvG,KAAK,CAAC;AAC1B,UAAA;AAEJ,QAAA,KAAK,SAAS;AACV,UAAA,IAAI,CAACwG,YAAY,CAACxG,KAAK,CAAC;AACxB,UAAA;AAEJ,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACyG,cAAc,CAACzG,KAAK,CAAC;AAC1B,UAAA;AAEJ,QAAA,KAAK,YAAY;AACb,UAAA,IAAI,CAAC0G,eAAe,CAAC1G,KAAK,CAAC;AAC3B,UAAA;AAEJ,QAAA,KAAK,MAAM;AACP,UAAA,IAAI,CAAC2G,SAAS,CAAC3G,KAAK,CAAC;AACrB,UAAA;AAEJ,QAAA,KAAK,KAAK;AACN,UAAA,IAAI,CAAC4G,QAAQ,CAAC5G,KAAK,CAAC;AACpB,UAAA;AAEJ,QAAA,KAAK,OAAO;AACZ,QAAA,KAAK,aAAa;AAClB,QAAA,KAAK,OAAO;AACR,UAAA,IAAI,CAAC+F,WAAW,CAAC/F,KAAK,CAACgG,MAAM,CAAC,EAAE;AAC5B,YAAA,IAAI,CAACa,UAAU,CAAC7G,KAAK,EAAEsG,IAAI,CAAC;AAChC;AAEA,UAAA;AAEJ,QAAA,KAAK,KAAK;AACN,UAAA,IAAI,CAACQ,QAAQ,CAAC9G,KAAK,CAAC;AACpB,UAAA;AAIR;KACH;AACDuG,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACvG,KAAK,EAAE;AAClB,MAAA,IAAMH,kBAAiB,GAAIG,KAAK,CAACK,aAAa,CAACR,kBAAkB;MAEjEA,kBAAmB,IAAG,IAAI,CAACkH,cAAc,CAAC/G,KAAK,CAACK,aAAa,EAAER,kBAAkB,CAAC;MAElFG,KAAK,CAACQ,cAAc,EAAE;KACzB;AACDgG,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACxG,KAAK,EAAE;AAChB,MAAA,IAAMgH,sBAAuB,GAAEhH,KAAK,CAACK,aAAa,CAAC2G,sBAAsB;MAEzEA,sBAAuB,IAAG,IAAI,CAACD,cAAc,CAAC/G,KAAK,CAACK,aAAa,EAAE2G,sBAAsB,CAAC;MAE1FhH,KAAK,CAACQ,cAAc,EAAE;KACzB;AACDkG,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAAC1G,KAAK,EAAE;AAAA,MAAA,IAAAiH,KAAA,GAAA,IAAA;AACnB,MAAA,IAAMC,YAAa,GAAEC,UAAU,CAACnH,KAAK,CAACK,aAAa,EAAE,QAAQ,CAAC,CAAC5E,KAAK,CAAC+H,UAAW,KAAI,QAAQ;MAC5F,IAAM4D,iBAAiBD,UAAU,CAAC,IAAI,CAACE,KAAK,CAAC/E,IAAI,EAAE,sCAAsC,CAAC;AAE1F,MAAA,IAAI4E,YAAY,EAAE;MAElB,CAAC,IAAI,CAACzE,QAAO,IAAK2E,cAAc,CAACE,KAAK,EAAE;MAExC,IAAI,CAACC,SAAS,CAAC,YAAM;AACjBN,QAAAA,KAAI,CAACV,cAAc,CAACvG,KAAK,CAAC;AAC9B,OAAC,CAAC;MAEFA,KAAK,CAACQ,cAAc,EAAE;KACzB;AACDiG,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAACzG,KAAK,EAAE;MAClB,IAAI,IAAI,CAACuC,KAAM,KAAI,CAAA,IAAK,CAAC,IAAI,CAACE,QAAQ,EAAE;AACpC,QAAA;AACJ;AAEA,MAAA,IAAMpC,aAAc,GAAEL,KAAK,CAACK,aAAa;AACzC,MAAA,IAAM6G,eAAeC,UAAU,CAAC9G,aAAa,EAAE,QAAQ,CAAC,CAAC5E,KAAK,CAAC+H,UAAW,KAAI,QAAQ;AACtF,MAAA,IAAM4D,cAAe,GAAED,UAAU,CAAC9G,aAAa,EAAE,sCAAsC,CAAC;AAExF,MAAA,IAAI,IAAI,CAACoC,QAAO,IAAK,CAACyE,YAAY,EAAE;QAChCE,cAAc,CAACE,KAAK,EAAE;AAEtB,QAAA;AACJ;AAEA,MAAA,IAAMtB,MAAK,GAAI,IAAI,CAACwB,uBAAuB,CAACnH,aAAa,CAAC;MAE1D2F,UAAU,IAAI,CAACe,cAAc,CAAC1G,aAAa,EAAE2F,MAAM,CAAC;KACvD;AACDW,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAAC3G,KAAK,EAAE;AACb,MAAA,IAAMyH,gBAAe,GAAIN,UAAU,CAACnH,KAAK,CAACK,aAAa,CAACT,aAAa,EAAArC,kBAAAA,CAAAA,MAAA,CAAoB,IAAI,CAACgF,KAAM,GAAE,CAAC,QAAI,CAAC;AAE5GkF,MAAAA,gBAAiB,IAAGC,KAAK,CAACD,gBAAgB,CAAC;MAE3CzH,KAAK,CAACQ,cAAc,EAAE;KACzB;AACDoG,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAAC5G,KAAK,EAAE;AACZ,MAAA,IAAM2H,KAAM,GAAEC,IAAI,CAAC5H,KAAK,CAACK,aAAa,CAACT,aAAa,EAAArC,kBAAAA,CAAAA,MAAA,CAAoB,IAAI,CAACgF,QAAQ,CAAC,QAAI,CAAC;MAC3F,IAAMkF,gBAAe,GAAIE,KAAK,CAACA,KAAK,CAAC/G,MAAO,GAAE,CAAC,CAAC;MAEhD8G,KAAK,CAACD,gBAAgB,CAAC;MAEvBzH,KAAK,CAACQ,cAAc,EAAE;KACzB;AACDqG,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAAC7G,KAAK,EAAE;MACdA,KAAK,CAACQ,cAAc,EAAE;MACtB,IAAI,CAAC0F,2BAA2B,CAAClG,KAAK,EAAE,IAAI,CAAC6F,WAAW,CAAC;AAEzD,MAAA,IAAI,IAAI,CAAChN,aAAY,KAAM,UAAU,EAAE;QACnC,IAAI,CAACwK,cAAc,EAAE;AAErB,QAAA;AACJ;AAEA,MAAA,IAAI,CAACpD,KAAK,CAAC,YAAY,EAAE;AACrBC,QAAAA,aAAa,EAAEF,KAAK;QACpB6F,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BvD,IAAI,EAAE,IAAI,CAACA;AACf,OAAC,CAAC;MAEF,IAAI,CAACuD,WAAY,GAAE,KAAK;KAC3B;IACDiB,QAAQ,EAAA,SAARA,QAAQA,GAAG;AACP,MAAA,IAAM3N,IAAG,GAAA0O,oBAAA,CAAQD,IAAI,CAAC,IAAI,CAACP,KAAK,CAAC/E,IAAI,CAAC1C,aAAa,EAAE,IAAI,CAAC,CAAC;AAC3D,MAAA,IAAMkI,cAAe,GAAE3O,IAAI,CAAC4O,IAAI,CAAC,UAACC,GAAG,EAAA;AAAA,QAAA,OAAKzH,YAAY,CAACyH,GAAG,EAAE,iBAAiB,CAAA,IAAKA,GAAG,CAACzH,YAAY,CAAC,cAAc,CAAE,KAAI,MAAM;OAAC,CAAA;AAE9HpH,MAAAA,IAAI,CAAC8O,OAAO,CAAC,UAACD,GAAG,EAAK;AAClBA,QAAAA,GAAG,CAACE,QAAS,GAAE,EAAE;AACrB,OAAC,CAAC;AAEF,MAAA,IAAIJ,cAAc,EAAE;AAChB,QAAA,IAAMK,aAAc,GAAEhP,IAAI,CAACiP,MAAM,CAAC,UAAC9F,IAAI,EAAA;AAAA,UAAA,OAAK/B,YAAY,CAAC+B,IAAI,EAAE,iBAAiB,CAAA,IAAKA,IAAI,CAAC/B,YAAY,CAAC,cAAc,CAAE,KAAI,MAAM;SAAC,CAAA;AAElI4H,QAAAA,aAAa,CAAC,CAAC,CAAC,CAACD,QAAS,GAAE,CAAC;AAE7B,QAAA;AACJ;AAEA/O,MAAAA,IAAI,CAAC,CAAC,CAAC,CAAC+O,QAAO,GAAI,CAAC;KACvB;AACDnB,IAAAA,cAAc,WAAdA,cAAcA,CAACsB,iBAAiB,EAAEC,iBAAiB,EAAE;MACjDD,iBAAiB,CAACH,QAAS,GAAE,IAAI;MACjCI,iBAAiB,CAACJ,QAAS,GAAE,GAAG;MAChCR,KAAK,CAACY,iBAAiB,CAAC;KAC3B;AACDd,IAAAA,uBAAuB,EAAvBA,SAAAA,uBAAuBA,CAAClF,IAAI,EAAE;AAC1B,MAAA,IAAMiG,QAAS,GAAEjG,IAAI,CAAC0E,sBAAsB;AAE5C,MAAA,IAAIuB,QAAQ,EAAE;AACV,QAAA,IAAMC,cAAa,GAAID,QAAQ,CAACE,aAAa,CAAC,QAAQ,CAAC;QAEvD,IAAID,cAAa,IAAKA,cAAc,CAAC/M,KAAK,CAAC+H,UAAW,KAAI,QAAQ,EAAE;AAChE,UAAA,OAAO+E,QAAQ;AACnB;AAEA,QAAA,OAAO,IAAI,CAACf,uBAAuB,CAACe,QAAQ,CAAC;AACjD;AAEA,MAAA,OAAO,IAAI;KACd;IACDlF,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,IAAIqF,cAAa,GAAI,IAAI,CAAC9P,aAAc,GAAAqG,eAAA,CAAA,EAAA,EAAO,IAAI,CAACrG,aAAY,CAAA,GAAM,EAAE;AACxE,MAAA,IAAM+P,MAAO,GAAE,CAAC,IAAI,CAACjG,OAAO;MAE5B,IAAI,CAACkG,aAAa,CAAC,IAAI,CAACtG,IAAI,EAAEqG,MAAM,EAAED,cAAc,CAAC;AAErD,MAAA,IAAI,CAACzI,KAAK,CAAC,iBAAiB,EAAE;QAC1BqC,IAAI,EAAE,IAAI,CAACA,IAAI;AACfuG,QAAAA,KAAK,EAAEF,MAAM;AACb/P,QAAAA,aAAa,EAAE8P;AACnB,OAAC,CAAC;KACL;IACDE,aAAa,EAAA,SAAbA,aAAaA,CAACtG,IAAI,EAAEuG,KAAK,EAAEjQ,aAAa,EAAE;MACtC,IAAIiQ,KAAK,EAAEjQ,aAAa,CAAC,IAAI,CAACyN,OAAO,CAAC/D,IAAI,CAAC,IAAI;AAAEI,QAAAA,OAAO,EAAE,IAAI;AAAEC,QAAAA,cAAc,EAAE;OAAO,CAAA,KAClF,OAAO/J,aAAa,CAAC,IAAI,CAACyN,OAAO,CAAC/D,IAAI,CAAC,CAAC;MAE7C,IAAIA,IAAI,CAACpD,QAAO,IAAKoD,IAAI,CAACpD,QAAQ,CAAC0B,MAAM,EAAE;AAAA,QAAA,IAAAkI,SAAA,GAAAC,4BAAA,CACrBzG,IAAI,CAACpD,QAAQ,CAAA;UAAA8J,KAAA;AAAA,QAAA,IAAA;UAA/B,KAAAF,SAAA,CAAAG,CAAA,EAAAD,EAAAA,CAAAA,CAAAA,KAAA,GAAAF,SAAA,CAAAI,CAAA,EAAAC,EAAAA,IAAA,GAAiC;AAAA,YAAA,IAAxBC,KAAI,GAAAJ,KAAA,CAAA1Q,KAAA;YACT,IAAI,CAACsQ,aAAa,CAACQ,KAAK,EAAEP,KAAK,EAAEjQ,aAAa,CAAC;AACnD;AAAA,SAAA,CAAA,OAAAyQ,GAAA,EAAA;UAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA,CAAA;AAAA,SAAA,SAAA;AAAAP,UAAAA,SAAA,CAAAS,CAAA,EAAA;AAAA;AACJ;KACH;AACDC,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACxJ,KAAK,EAAE;AACf,MAAA,IAAI6I,QAAQ7I,KAAK,CAAC6I,KAAK;AACvB,MAAA,IAAIH,cAAe,GAAAzJ,eAAA,KAAOe,KAAK,CAACpH,cAAe;MAC/C,IAAI6Q,iBAAgB,GAAI,CAAC;MACzB,IAAIC,oBAAqB,GAAE,KAAK;MAAA,IAAAC,UAAA,GAAAZ,4BAAA,CAEd,IAAI,CAACzG,IAAI,CAACpD,QAAQ,CAAA;QAAA0K,MAAA;AAAA,MAAA,IAAA;QAApC,KAAAD,UAAA,CAAAV,CAAA,EAAAW,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAAT,CAAA,EAAAC,EAAAA,IAAA,GAAsC;AAAA,UAAA,IAA7BC,KAAM,GAAAQ,MAAA,CAAAtR,KAAA;UACX,IAAIoQ,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC+C,KAAK,CAAC,CAAA,IAAKV,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC+C,KAAK,CAAC,CAAC,CAAC1G,OAAO,EAAE+G,iBAAiB,EAAE,CAAA,KACtG,IAAIf,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC+C,KAAK,CAAC,CAAA,IAAKV,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC+C,KAAK,CAAC,CAAC,CAACzG,cAAc,EAAE+G,oBAAmB,GAAI,IAAI;AACnI;AAAA,OAAA,CAAA,OAAAL,GAAA,EAAA;QAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA,CAAA;AAAA,OAAA,SAAA;AAAAM,QAAAA,UAAA,CAAAJ,CAAA,EAAA;AAAA;MAEA,IAAIV,KAAM,IAAGY,iBAAkB,KAAI,IAAI,CAACnH,IAAI,CAACpD,QAAQ,CAAC0B,MAAM,EAAE;QAC1D8H,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAA,GAAI;AAAEI,UAAAA,OAAO,EAAE,IAAI;AAAEC,UAAAA,cAAc,EAAE;SAAO;AACtF,OAAE,MAAK;QACH,IAAI,CAACkG,KAAK,EAAE;UACR,OAAOH,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAC;AAClD;QAEA,IAAIoH,wBAAyBD,iBAAgB,GAAI,CAAE,IAAGA,iBAAgB,KAAM,IAAI,CAACnH,IAAI,CAACpD,QAAQ,CAAC0B,MAAO,EAAE8H,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAE,GAAE;AAAEI,UAAAA,OAAO,EAAE,KAAK;AAAEC,UAAAA,cAAc,EAAE;AAAK,SAAC,CAAA,KACrL+F,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,IAAI;AAAEI,UAAAA,OAAO,EAAE,KAAK;AAAEC,UAAAA,cAAc,EAAE;SAAO;AAC5F;AAEA,MAAA,IAAI,CAAC1C,KAAK,CAAC,iBAAiB,EAAE;QAC1BqC,IAAI,EAAEtC,KAAK,CAACsC,IAAI;QAChBuG,KAAK,EAAE7I,KAAK,CAAC6I,KAAK;AAClBjQ,QAAAA,aAAa,EAAE8P;AACnB,OAAC,CAAC;KACL;AACDmB,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAC7J,KAAK,EAAE;AACpB,MAAA,IAAI6I,QAAQ7I,KAAK,CAAC6I,KAAK;AACvB,MAAA,IAAIH,cAAe,GAAAzJ,eAAA,KAAOe,KAAK,CAACpH,cAAe;MAC/C,IAAI6Q,iBAAgB,GAAI,CAAC;MACzB,IAAIC,oBAAqB,GAAE,KAAK;MAAA,IAAAI,UAAA,GAAAf,4BAAA,CAEd,IAAI,CAACzG,IAAI,CAACpD,QAAQ,CAAA;QAAA6K,MAAA;AAAA,MAAA,IAAA;QAApC,KAAAD,UAAA,CAAAb,CAAA,EAAAc,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAAZ,CAAA,EAAAC,EAAAA,IAAA,GAAsC;AAAA,UAAA,IAA7BC,KAAM,GAAAW,MAAA,CAAAzR,KAAA;UACX,IAAIoQ,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC+C,KAAK,CAAC,CAAA,IAAKV,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC+C,KAAK,CAAC,CAAC,CAAC1G,OAAO,EAAE+G,iBAAiB,EAAE,CAAA,KACtG,IAAIf,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC+C,KAAK,CAAC,CAAA,IAAKV,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC+C,KAAK,CAAC,CAAC,CAACzG,cAAc,EAAE+G,oBAAmB,GAAI,IAAI;AACnI;AAAA,OAAA,CAAA,OAAAL,GAAA,EAAA;QAAAS,UAAA,CAAAR,CAAA,CAAAD,GAAA,CAAA;AAAA,OAAA,SAAA;AAAAS,QAAAA,UAAA,CAAAP,CAAA,EAAA;AAAA;MAEA,IAAIV,KAAM,IAAGY,iBAAkB,KAAI,IAAI,CAACnH,IAAI,CAACpD,QAAQ,CAAC0B,MAAM,EAAE;QAC1D8H,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAA,GAAI;AAAEI,UAAAA,OAAO,EAAE,IAAI;AAAEC,UAAAA,cAAc,EAAE;SAAO;AACtF,OAAE,MAAK;QACH,IAAI,CAACkG,KAAK,EAAE;UACR,OAAOH,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAC;AAClD;QAEA,IAAIoH,wBAAyBD,iBAAgB,GAAI,CAAE,IAAGA,iBAAgB,KAAM,IAAI,CAACnH,IAAI,CAACpD,QAAQ,CAAC0B,MAAO,EAAE8H,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAE,GAAE;AAAEI,UAAAA,OAAO,EAAE,KAAK;AAAEC,UAAAA,cAAc,EAAE;AAAK,SAAC,CAAA,KACrL+F,cAAc,CAAC,IAAI,CAACrC,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,IAAI;AAAEI,UAAAA,OAAO,EAAE,KAAK;AAAEC,UAAAA,cAAc,EAAE;SAAO;AAC5F;AAEA,MAAA,IAAI,CAAC1C,KAAK,CAAC,iBAAiB,EAAE;QAC1BqC,IAAI,EAAEtC,KAAK,CAACsC,IAAI;QAChBuG,KAAK,EAAE7I,KAAK,CAAC6I,KAAK;AAClBjQ,QAAAA,aAAa,EAAE8P;AACnB,OAAC,CAAC;KACL;AACDxC,IAAAA,2BAA2B,WAA3BA,2BAA2BA,CAAClG,KAAK,EAAE6F,WAAW,EAAE;AAC5C,MAAA,IAAI,IAAI,CAAChN,aAAc,KAAI,IAAI,EAAE;AAC7B,QAAA,IAAMmR,QAAS,GAAAnC,oBAAA,CAAMD,IAAI,CAAC,IAAI,CAACP,KAAK,CAAC/E,IAAI,CAAC1C,aAAa,EAAE,IAAI,CAAC,CAAC;AAE/DI,QAAAA,KAAK,CAACK,aAAa,CAAC6H,QAAS,GAAErC,WAAU,KAAM,QAAQ,EAAG,GAAE,CAAC;AAE7D,QAAA,IAAImE,QAAQ,CAACC,KAAK,CAAC,UAACC,OAAO,EAAA;AAAA,UAAA,OAAKA,OAAO,CAAChC,QAAS,KAAI,EAAE;AAAA,SAAA,CAAC,EAAE;AACtD8B,UAAAA,QAAQ,CAAC,CAAC,CAAC,CAAC9B,QAAO,GAAI,CAAC;AAC5B;AACJ;AACJ;GACH;AACD5J,EAAAA,QAAQ,EAAE;IACNC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,OAAO,CAAC,IAAI,CAAC+D,IAAI,CAAC6H,UAAU,EAAE,IAAI,CAAC3L,EAAE,CAAC,KAAK,CAAC,CAAC;KAChD;IACDiE,QAAQ,EAAA,SAARA,QAAQA,GAAG;AACP,MAAA,OAAO,IAAI,CAAC9J,YAAa,IAAG,IAAI,CAACA,YAAY,CAAC,IAAI,CAAC0N,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAA,KAAM,IAAI;KAClF;IACDE,IAAI,EAAA,SAAJA,IAAIA,GAAG;MACH,OAAO,IAAI,CAACF,IAAI,CAACE,IAAG,KAAM,QAAQ,KAAM,GAAE,EAAE,IAAI,CAACF,IAAI,CAACpD,QAAS,IAAG,IAAI,CAACoD,IAAI,CAACpD,QAAQ,CAAC0B,MAAM,CAAC;KAC/F;IACDoC,QAAQ,EAAA,SAARA,QAAQA,GAAG;MACP,OAAO,IAAI,CAACnK,aAAY,IAAK,IAAI,CAACD,gBAAgB,IAAI,CAACA,aAAa,CAAC,IAAI,CAACyN,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAE,KAAI,IAAG,GAAI,KAAK;KACjH;IACD8H,yBAAyB,EAAA,SAAzBA,yBAAyBA,GAAG;AACxB,MAAA,IAAI,IAAI,CAAC9H,QAAQ,IAAI,CAACrJ,oBAAoB,EAAE;AACxC,QAAA,OAAOoR,MAAM,CAAC,IAAI,CAAC/H,IAAI,EAAE,IAAI,CAACrJ,oBAAoB,EAAE,IAAI,CAACT,OAAO,CAAC;AACrE;AAEA,MAAA,OAAO,KAAK;KACf;IACDkK,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,MAAA,OAAO,IAAI,CAAC9J,aAAc,GAAE,IAAI,CAACA,aAAa,CAAC,IAAI,CAACyN,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAE,IAAG,IAAI,CAAC1J,aAAa,CAAC,IAAI,CAACyN,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAC,CAACI,OAAM,GAAI,KAAK;KACzI;IACDC,cAAc,EAAA,SAAdA,cAAcA,GAAG;AACb,MAAA,OAAO,IAAI,CAAC/J,aAAY,GAAI,IAAI,CAACA,aAAa,CAAC,IAAI,CAACyN,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAE,IAAG,IAAI,CAAC1J,aAAa,CAAC,IAAI,CAACyN,OAAO,CAAC,IAAI,CAAC/D,IAAI,CAAC,CAAC,CAACK,cAAa,GAAI,KAAK;KAChJ;IACD2H,eAAe,EAAA,SAAfA,eAAeA,GAAG;AACd,MAAA,OAAO,IAAI,CAACzR,kBAAkB,YAAY,IAAI,CAACA,aAAc,KAAI,aAAa,IAAI,CAACmK,QAAO,GAAI,IAAI;KACrG;IACDuH,UAAU,EAAA,SAAVA,UAAUA,GAAG;MACT,OAAO;AACHpN,QAAAA,OAAO,EAAE;UACL2F,UAAU,EAAE,IAAI,CAACjH,eAAe,CAACzB,QAAS,IAAG,IAAI,CAACyB,eAAe,CAACkH,gBAAgB;UAClFC,QAAQ,EAAE,IAAI,CAACA,QAAQ;AACvB7H,UAAAA,UAAU,EAAE,IAAI,CAACU,eAAe,CAACV;AACrC;OACH;AACL;GACH;AACDwG,EAAAA,UAAU,EAAE;AACR6I,IAAAA,UAAU,EAAEC;AAChB;AACJ,CAAC;;;;;;0DC3dGvI,kBAAA,CAyCI,MAzCJpD,UAyCI,CAAA;AAxCA4L,IAAAA,GAAG,EAAC,MAAK;IACR,OAAO3L,EAAAA,QAAc,CAAAR,cAAA;AACrB9C,IAAAA,KAAK,EAAEuB,MAAI,CAAAsF,IAAA,CAAC7G,KAAK;IACjBuG,QAAQ,EAAEhF,MAAQ,CAAAgF,QAAA;AACnBhD,IAAAA,IAAI,EAAC,KAAI;AACR,IAAA,eAAa,EAAEhC,MAAA,CAAAsF,IAAI,CAACpD,QAAS,IAAGlC,MAAA,CAAAsF,IAAI,CAACpD,QAAQ,CAAC0B,SAAS7B,QAAA,CAAA0D,WAAWvI,SAAS;AAC3E,IAAA,YAAU,EAAE8C,MAAI,CAAAuF,KAAA,GAAA,CAAA;IAChB,cAAY,EAAEvF,MAAW,CAAA2I,WAAA;IACzB,eAAa,EAAE3I,MAAY,CAAA4I,YAAA;IAC3B,eAAa,EAAE7G,QAAe,CAAAuL,eAAA;AAC9B,IAAA,cAAY,EAAEvL,QAAM,CAAA2D,OAAA,IAAKxI,SAAS;IAClC6F,OAAK;aAAEhB,QAAO,CAAAgB,OAAA,IAAAhB,QAAA,CAAAgB,OAAA,CAAA8B,KAAA,CAAA9C,QAAA,EAAA+C,SAAA,CAAA;AAAA,KAAA,CAAA;IACdC,SAAO;aAAEhD,QAAS,CAAAoB,SAAA,IAAApB,QAAA,CAAAoB,SAAA,CAAA0B,KAAA,CAAA9C,QAAA,EAAA+C,SAAA,CAAA;AAAA,KAAA,CAAA;IAClB6I,UAAQ;aAAE5L,QAAU,CAAAqH,UAAA,IAAArH,QAAA,CAAAqH,UAAA,CAAAvE,KAAA,CAAA9C,QAAA,EAAA+C,SAAA,CAAA;AAAA,KAAA,CAAA;IACpB8I,aAAW;aAAE7L,QAAe,CAAAoH,eAAA,IAAApH,QAAA,CAAAoH,eAAA,CAAAtE,KAAA,CAAA9C,QAAA,EAAA+C,SAAA,CAAA;KAAA;GACrB,EAAAxC,IAAA,CAAAhC,GAAG,QAAQyB,QAAU,CAAAwL,UAAA,CAAA,EAAA;IAC5B,iBAAe,EAAExL,QAAQ,CAAAiE,QAAA;AACzB,IAAA,6BAA2B,EAAEhG,MAAmB,CAAA/D,oBAAA,IAAK8F,QAAyB,CAAAqL;QAE/ExL,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAoBUuF,QApBmB,EAAA,IAAA,EAAAyG,UAAA,CAAA7N,MAAA,CAAA0I,OAAO,EAAlB,UAAAI,GAAG,EAAEnF,CAAC,EAAA;;AAAoBhE,MAAAA,GAAA,EAAAoC,QAAA,CAAA3C,UAAU,CAAC0J,GAAG,EAAA,WAAA,CAAA,IAAkB/G,mBAAU,CAAC+G,GAAG,cAAcnF;QAEzF,CAAA5B,QAAA,CAAA3C,UAAU,CAAC0J,GAAG,EAAA,QAAA,CAAA,iBADzB1G,WAkBa,CAAA0L,qBAAA,EAAA;;AAhBR/O,MAAAA,MAAM,EAAE+J,GAAG;MACXxD,IAAI,EAAEtF,MAAI,CAAAsF,IAAA;MACVC,KAAK,EAAEvF,MAAK,CAAAuF,KAAA;MACZC,IAAI,EAAEzD,QAAI,CAAAyD,IAAA;MACVvH,WAAW,EAAE+B,MAAW,CAAA/B,WAAA;MACxBwH,QAAQ,EAAE1D,QAAQ,CAAA0D,QAAA;MAClB5J,aAAa,EAAEmE,MAAa,CAAAnE,aAAA;MAC5B6J,OAAO,EAAE3D,QAAO,CAAA2D,OAAA;MAChBC,cAAc,EAAE5D,QAAc,CAAA4D,cAAA;MAC9BC,SAAS,EAAE5F,MAAS,CAAA4F,SAAA;AACpBmI,MAAAA,YAAW,EAAAC,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,QAAA,OAAE3L,IAAK,CAAAW,KAAA,CAAA,aAAA,EAAgBgL,MAAM,CAAA;AAAA,OAAA,CAAA;MACxCC,gBAAe,EAAEnM,QAAc,CAAAsE,cAAA;AAC/BrH,MAAAA,KAAK,EAAE2E,CAAC;MACRxG,WAAW,EAAE6C,MAAW,CAAA7C,WAAA;MACxB8K,QAAQ,EAAE3F,IAAQ,CAAA2F,QAAA;MAClBvH,EAAE,EAAE4B,IAAE,CAAA5B;;iCAIHqB,QAAS,CAAA0D,QAAA,IAAGzF,WAAI,CAACkC,QAAO,IAAKlC,MAAI,CAAAsF,IAAA,CAACpD,QAAQ,CAAC0B,MAAM,IAC7DhC,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAuBCuF,QAtBuB,EAAA;AAAAzH,IAAAA,GAAA,EAAA;GAAA,EAAAkO,UAAA,CAAA7N,MAAA,CAAAsF,IAAI,CAACpD,QAAQ,YAA1BiM,SAAU,EAAA;wBADrB/L,WAuBC,CAAAgM,uBAAA,EAAA;AArBIzO,MAAAA,GAAG,EAAEoC,QAAO,CAAAsH,OAAA,CAAC8E,SAAS,CAAA;MACtB3S,OAAO,EAAEwE,MAAO,CAAAxE,OAAA;MAChBkN,OAAO,EAAE1I,MAAO,CAAA0I,OAAA;AAChBpD,MAAAA,IAAI,EAAE6I,SAAS;MACf1F,UAAU,EAAEzI,MAAI,CAAAsF,IAAA;AAChBC,MAAAA,KAAK,EAAEvF,MAAI,CAAAuF,KAAA,GAAA,CAAA;MACX5J,YAAY,EAAEqE,MAAY,CAAArE,YAAA;MAC1BE,aAAa,EAAEmE,MAAa,CAAAnE,aAAA;MAC5BD,aAAa,EAAEoE,MAAa,CAAApE,aAAA;MAC5BI,WAAW,EAAEgE,MAAW,CAAAhE,WAAA;MACxBC,oBAAoB,EAAE+D,MAAoB,CAAA/D,oBAAA;MAC1CgC,WAAW,EAAE+B,MAAW,CAAA/B,WAAA;AACxB2K,MAAAA,YAAY,EAAE5I,MAAI,CAAAsF,IAAA,CAACpD,QAAQ,CAACmM,OAAO,CAACF,SAAS,CAAA,GAAA,CAAA;AAC7CxF,MAAAA,WAAW,EAAE3I,MAAA,CAAAsF,IAAI,CAACpD,QAAQ,CAAC0B,MAAM;MACjCgC,SAAS,EAAE5F,MAAS,CAAA4F,SAAA;AACpBmI,MAAAA,YAAW,EAAAC,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,QAAA,OAAE3L,IAAK,CAAAW,KAAA,CAAA,aAAA,EAAgBgL,MAAM,CAAA;AAAA,OAAA,CAAA;AACxCK,MAAAA,WAAU,EAAAN,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,QAAA,OAAE3L,IAAK,CAAAW,KAAA,CAAA,YAAA,EAAegL,MAAM,CAAA;AAAA,OAAA,CAAA;AACtCM,MAAAA,eAAc,EAAAP,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,QAAA,OAAE3L,IAAK,CAAAW,KAAA,CAAA,gBAAA,EAAmBgL,MAAM,CAAA;AAAA,OAAA,CAAA;MAC9CpB,gBAAe,EAAE9K,QAAgB,CAAA8K,gBAAA;MACjC5E,QAAQ,EAAE3F,IAAQ,CAAA2F,QAAA;MAClBvH,EAAE,EAAE4B,IAAE,CAAA5B;;;;;;;;;;;;;;;;;;;;ACsJnB,aAAe;AACXvF,EAAAA,IAAI,EAAE,WAAW;AACjB,EAAA,SAAA,EAASqT,QAAa;AACtBC,EAAAA,YAAY,EAAE,KAAK;AACnBlM,EAAAA,KAAK,EAAE,CACH,aAAa,EACb,eAAe,EACf,qBAAqB,EACrB,sBAAsB,EACtB,aAAa,EACb,eAAe,EACf,cAAc,EACd,aAAa,EACb,MAAM,EACN,kBAAkB,EAClB,kBAAkB,EAClB,sBAAsB,EACtB,MAAM,EACN,QAAQ,EACR,mBAAmB,EACnB,6BAA6B,EAC7B,iBAAgB,CACnB;EACD5D,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;MACH+P,QAAQ,EAAE,IAAI,CAACC;KAClB;GACJ;EACD1P,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACH2P,MAAAA,cAAc,EAAE,IAAI,CAACjT,YAAW,IAAK,EAAE;MACvCkT,OAAO,EAAE,IAAI,CAACxS,KAAK;MACnByS,MAAM,EAAE,IAAI,CAAC3S,IAAI;MACjB4S,WAAW,EAAE,IAAI,CAACzR,SAAS;MAC3B0R,WAAW,EAAE,IAAI,CAACzR,SAAS;MAC3B0R,eAAe,EAAE,IAAI,CAACxR,aAAY,GAAAoN,kBAAA,CAAQ,IAAI,CAACpN,aAAa,CAAA,GAAI,EAAE;AAClEyR,MAAAA,gBAAgB,EAAE,KAAK;MACvBP,SAAS,EAAE,IAAIQ,SAAS,CAAC;AAAE5T,QAAAA,IAAI,EAAE;OAAU;KAC9C;GACJ;AACD6T,EAAAA,4BAA4B,EAAE,IAAI;AAClCC,EAAAA,+BAA+B,EAAE,IAAI;AACrCC,EAAAA,iBAAiB,EAAE,IAAI;AACvBC,EAAAA,mBAAmB,EAAE,IAAI;AACzBC,EAAAA,KAAK,EAAE;AACH7T,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAAC8T,QAAQ,EAAE;MACnB,IAAI,CAACb,cAAa,GAAIa,QAAQ;KACjC;AACDpT,IAAAA,KAAK,EAALA,SAAAA,KAAKA,CAACoT,QAAQ,EAAE;MACZ,IAAI,CAACZ,OAAQ,GAAEY,QAAQ;KAC1B;AACDtT,IAAAA,IAAI,EAAJA,SAAAA,IAAIA,CAACsT,QAAQ,EAAE;MACX,IAAI,CAACX,MAAO,GAAEW,QAAQ;KACzB;AACDnS,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACmS,QAAQ,EAAE;MAChB,IAAI,CAACV,WAAY,GAAEU,QAAQ;KAC9B;AACDlS,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACkS,QAAQ,EAAE;MAChB,IAAI,CAACT,WAAY,GAAES,QAAQ;KAC9B;AACDhS,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACgS,QAAQ,EAAE;MACpB,IAAI,CAACR,eAAc,GAAIQ,QAAQ;AACnC;GACH;EACDC,aAAa,EAAA,SAAbA,aAAaA,GAAG;IACZ,IAAI,CAACC,mBAAmB,EAAE;AAC1B,IAAA,IAAI,CAAChB,SAAS,CAACiB,KAAK,EAAE;GACzB;AACDrQ,EAAAA,OAAO,EAAE;AACLH,IAAAA,UAAU,WAAVA,UAAUA,CAAC0J,GAAG,EAAEtJ,IAAI,EAAE;AAClB,MAAA,OAAOC,YAAY,CAACqJ,GAAG,EAAEtJ,IAAI,CAAC;KACjC;AACDqQ,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAAC9Q,MAAM,EAAE;MACxB,OAAO;AACHoB,QAAAA,OAAO,EAAE;AACLC,UAAAA,MAAM,EAAE,IAAI,CAAChB,UAAU,CAACL,MAAM,EAAE,QAAQ;AAC5C;OACH;KACJ;AACDgP,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACzI,IAAI,EAAE;AACf,MAAA,IAAM3F,GAAI,GAAE,IAAI,CAAC0J,OAAO,CAAC/D,IAAI,CAAC;AAE9B,MAAA,IAAI,IAAI,CAACsJ,cAAc,CAACjP,GAAG,CAAC,EAAE;AAC1B,QAAA,OAAO,IAAI,CAACiP,cAAc,CAACjP,GAAG,CAAC;AAC/B,QAAA,IAAI,CAACsD,KAAK,CAAC,eAAe,EAAEqC,IAAI,CAAC;AACrC,OAAE,MAAK;AACH,QAAA,IAAI,CAACsJ,cAAc,CAACjP,GAAG,CAAA,GAAI,IAAI;AAC/B,QAAA,IAAI,CAACsD,KAAK,CAAC,aAAa,EAAEqC,IAAI,CAAC;AACnC;MAEA,IAAI,CAACsJ,cAAa,GAAA3M,eAAA,KAAS,IAAI,CAAC2M,eAAgB;MAChD,IAAI,CAAC3L,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC2L,cAAc,CAAC;KACzD;AACDN,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACtL,KAAK,EAAE;MACf,IAAI,IAAI,CAAC+C,gBAAe,IAAK/C,KAAK,CAACsC,IAAI,CAACQ,UAAS,KAAM,KAAK,EAAE;QAC1D,IAAMgK,aAAc,GAAE9M,KAAK,CAAC6F,WAAU,GAAI,KAAI,GAAI,IAAI,CAAC/M,gBAAgB;AACvE,QAAA,IAAM4P,cAAe,GAAEoE,aAAY,GAAI,IAAI,CAACC,0BAA0B,CAAC/M,KAAK,IAAI,IAAI,CAACgN,6BAA6B,CAAChN,KAAK,CAAC;AAEzH,QAAA,IAAI,CAACC,KAAK,CAAC,sBAAsB,EAAEyI,cAAc,CAAC;AACtD;KACH;AACDrC,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAAC/D,IAAI,EAAE;AACV,MAAA,OAAOa,gBAAgB,CAACb,IAAI,EAAE,IAAI,CAAC9J,OAAO,CAAC;KAC9C;AACDuU,IAAAA,0BAA0B,EAA1BA,SAAAA,0BAA0BA,CAAC/M,KAAK,EAAE;AAC9B,MAAA,IAAME,aAAc,GAAEF,KAAK,CAACE,aAAa;AACzC,MAAA,IAAMoC,IAAK,GAAEtC,KAAK,CAACsC,IAAI;AACvB,MAAA,IAAM+D,OAAQ,GAAE,IAAI,CAACA,OAAO,CAAC/D,IAAI,CAAC;MAClC,IAAM2K,OAAQ,GAAE/M,aAAa,CAAC+M,OAAQ,IAAG/M,aAAa,CAACgN,OAAO;AAC9D,MAAA,IAAMlK,QAAS,GAAE,IAAI,CAACmK,cAAc,CAAC7K,IAAI,CAAC;AAC1C,MAAA,IAAIoG,cAAc;MAElB,IAAI1F,YAAYiK,OAAO,EAAE;AACrB,QAAA,IAAI,IAAI,CAACG,qBAAqB,EAAE,EAAE;UAC9B1E,cAAe,GAAE,EAAE;AACvB,SAAE,MAAK;AACHA,UAAAA,cAAa,GAAAzJ,eAAA,CAAA,EAAA,EAAS,IAAI,CAACrG,cAAe;UAC1C,OAAO8P,cAAc,CAACrC,OAAO,CAAC;AAClC;AAEA,QAAA,IAAI,CAACpG,KAAK,CAAC,eAAe,EAAEqC,IAAI,CAAC;AACrC,OAAE,MAAK;AACH,QAAA,IAAI,IAAI,CAAC8K,qBAAqB,EAAE,EAAE;UAC9B1E,cAAe,GAAE,EAAE;AACvB,SAAA,MAAO,IAAI,IAAI,CAAC2E,uBAAuB,EAAE,EAAE;AACvC3E,UAAAA,cAAa,GAAI,CAACuE,OAAM,GAAI,EAAC,GAAI,IAAI,CAACrU,aAAY,GAAAqG,eAAA,CAAS,EAAA,EAAA,IAAI,CAACrG,aAAY,CAAA,GAAM,EAAE;AACxF;AAEA8P,QAAAA,cAAc,CAACrC,OAAO,CAAA,GAAI,IAAI;AAC9B,QAAA,IAAI,CAACpG,KAAK,CAAC,aAAa,EAAEqC,IAAI,CAAC;AACnC;AAEA,MAAA,OAAOoG,cAAc;KACxB;AACDsE,IAAAA,6BAA6B,EAA7BA,SAAAA,6BAA6BA,CAAChN,KAAK,EAAE;AACjC,MAAA,IAAMsC,IAAK,GAAEtC,KAAK,CAACsC,IAAI;AACvB,MAAA,IAAM+D,OAAQ,GAAE,IAAI,CAACA,OAAO,CAAC/D,IAAI,CAAC;AAClC,MAAA,IAAMU,QAAS,GAAE,IAAI,CAACmK,cAAc,CAAC7K,IAAI,CAAC;AAC1C,MAAA,IAAIoG,cAAc;AAElB,MAAA,IAAI,IAAI,CAAC0E,qBAAqB,EAAE,EAAE;AAC9B,QAAA,IAAIpK,QAAQ,EAAE;UACV0F,cAAe,GAAE,EAAE;AACnB,UAAA,IAAI,CAACzI,KAAK,CAAC,eAAe,EAAEqC,IAAI,CAAC;AACrC,SAAE,MAAK;UACHoG,cAAe,GAAE,EAAE;AACnBA,UAAAA,cAAc,CAACrC,OAAO,CAAA,GAAI,IAAI;AAC9B,UAAA,IAAI,CAACpG,KAAK,CAAC,aAAa,EAAEqC,IAAI,CAAC;AACnC;AACJ,OAAE,MAAK;AACH,QAAA,IAAIU,QAAQ,EAAE;AACV0F,UAAAA,cAAa,GAAAzJ,eAAA,CAAA,EAAA,EAAS,IAAI,CAACrG,cAAe;UAC1C,OAAO8P,cAAc,CAACrC,OAAO,CAAC;AAE9B,UAAA,IAAI,CAACpG,KAAK,CAAC,eAAe,EAAEqC,IAAI,CAAC;AACrC,SAAE,MAAK;AACHoG,UAAAA,cAAa,GAAI,IAAI,CAAC9P,aAAY,GAAAqG,eAAA,CAAS,EAAA,EAAA,IAAI,CAACrG,aAAc,CAAI,GAAA,EAAE;AACpE8P,UAAAA,cAAc,CAACrC,OAAO,CAAA,GAAI,IAAI;AAE9B,UAAA,IAAI,CAACpG,KAAK,CAAC,aAAa,EAAEqC,IAAI,CAAC;AACnC;AACJ;AAEA,MAAA,OAAOoG,cAAc;KACxB;AACDmB,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAAC7J,KAAK,EAAE;MACpB,IAAI,CAACC,KAAK,CAAC,sBAAsB,EAAED,KAAK,CAACpH,aAAa,CAAC;MAEvD,IAAIoH,KAAK,CAAC6I,KAAK,EAAE,IAAI,CAAC5I,KAAK,CAAC,aAAa,EAAED,KAAK,CAACsC,IAAI,CAAC,CAAA,KACjD,IAAI,CAACrC,KAAK,CAAC,eAAe,EAAED,KAAK,CAACsC,IAAI,CAAC;KAC/C;AACD6D,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAACnG,KAAK,EAAE;MACnB,IAAI,IAAI,CAAChH,WAAW,EAAE;AAClBsU,QAAAA,cAAc,EAAE;AAChBtN,QAAAA,KAAK,CAACE,aAAa,CAAC8F,MAAM,CAAC0B,KAAK,EAAE;AACtC;MAEA,IAAI,CAACzH,KAAK,CAAC,6BAA6B,EAAED,KAAK,CAACsC,IAAI,CAAC;AACrD,MAAA,IAAI,CAACrC,KAAK,CAAC,iBAAiB,EAAED,KAAK,CAAC;KACvC;IACDoN,qBAAqB,EAAA,SAArBA,qBAAqBA,GAAG;AACpB,MAAA,OAAO,IAAI,CAACvU,kBAAkB,QAAQ;KACzC;IACDwU,uBAAuB,EAAA,SAAvBA,uBAAuBA,GAAG;AACtB,MAAA,OAAO,IAAI,CAACxU,aAAc,KAAI,UAAU;KAC3C;AACD0U,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACvN,KAAK,EAAE;AACV,MAAA,IAAI,CAAC6L,OAAM,GAAI7L,KAAK,CAAC3G,KAAK;AAC1B,MAAA,IAAI,CAACyS,MAAK,GAAI9L,KAAK,CAAC7G,IAAI;AAExB,MAAA,IAAIqU,SAAU,GAAE,IAAI,CAACC,mBAAmB,CAACzN,KAAK,CAAC;AAE/CwN,MAAAA,SAAS,CAACE,SAAQ,GAAI1N,KAAK,CAAC0N,SAAS;AACrCF,MAAAA,SAAS,CAACG,IAAK,GAAE3N,KAAK,CAAC2N,IAAI;AAE3B,MAAA,IAAI,CAAC/B,cAAe,GAAE,EAAE;MACxB,IAAI,CAAC3L,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC2L,cAAc,CAAC;MACtD,IAAI,CAAC3L,KAAK,CAAC,cAAc,EAAE,IAAI,CAAC4L,OAAO,CAAC;MACxC,IAAI,CAAC5L,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC6L,MAAM,CAAC;AACtC,MAAA,IAAI,CAAC7L,KAAK,CAAC,MAAM,EAAEuN,SAAS,CAAC;KAChC;IACDI,SAAS,EAAA,SAATA,SAASA,GAAG;MACR,IAAI,CAAC/B,OAAQ,GAAE,CAAC;MAChB,IAAI,CAAC5L,KAAK,CAAC,cAAc,EAAE,IAAI,CAAC4L,OAAO,CAAC;KAC3C;AACDgC,IAAAA,0BAA0B,EAA1BA,SAAAA,0BAA0BA,CAAC9R,MAAM,EAAE;AAC/B,MAAA,OAAO,CAAC,IAAI,CAACyC,EAAE,CAAC,YAAY,EAAE;AAAEzC,QAAAA,MAAK,EAALA;OAAQ,CAAC,EAAE,IAAI,CAACK,UAAU,CAACL,MAAM,EAAE,mBAAmB,CAAC,CAAC;KAC3F;AACD+R,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAACxE,CAAC,EAAE;AACnB,MAAA,IAAItJ,KAAI,GAAIsJ,CAAC,CAACpJ,aAAa;AAC3B,MAAA,IAAInE,MAAK,GAAIuN,CAAC,CAACvN,MAAM;MAErB,IAAI,IAAI,CAACK,UAAU,CAACL,MAAM,EAAE,UAAU,CAAC,EAAE;AACrC,QAAA,IAAMgS,aAAa/N,KAAK,CAACgG,MAAM;AAC/B,QAAA,IAAMgI,WAAY,GAAE,IAAI,CAAC5R,UAAU,CAACL,MAAM,EAAE,WAAW,CAAE,IAAG,IAAI,CAACK,UAAU,CAACL,MAAM,EAAE,OAAO,CAAC;AAE5F,QAAA,IACIwE,YAAY,CAACwN,UAAU,EAAE,wBAAwB,CAAE,KAAI,IAAG,IAC1DxN,YAAY,CAACwN,UAAU,EAAE,iBAAiB,CAAA,KAAM,aAAY,IAC5DxN,YAAY,CAACwN,UAAU,EAAE,iBAAiB,CAAE,KAAI,yBAChDxN,YAAY,CAACwN,UAAU,EAAE,iBAAiB,CAAA,KAAM,UAAS,IACzDxN,YAAY,CAACwN,UAAU,CAACnO,aAAa,EAAE,iBAAiB,CAAE,KAAI,cAC9DW,YAAY,CAACwN,UAAU,CAACnO,aAAa,CAACA,aAAa,EAAE,iBAAiB,CAAE,KAAI,cAC5EmO,UAAU,CAACE,OAAO,CAAC,iCAAiC,CAAA,EACtD;AACEX,UAAAA,cAAc,EAAE;AAEhB,UAAA,IAAI,IAAI,CAAC5S,QAAS,KAAI,QAAQ,EAAE;AAC5B,YAAA,IAAI,IAAI,CAACqR,WAAU,KAAMiC,WAAW,EAAE;AAClC,cAAA,IAAI,IAAI,CAACrT,aAAc,IAAG,IAAI,CAACqR,WAAU,GAAI,EAAG,KAAI,IAAI,CAACxR,gBAAgB,EAAE;gBACvE,IAAI,CAACwR,WAAU,GAAI,IAAI;gBACvB,IAAI,CAACD,WAAU,GAAI,IAAI;AAC3B,eAAE,MAAK;gBACH,IAAI,CAACC,cAAc,IAAI,CAACA,WAAY,GAAE,EAAE;AAC5C;AACJ,aAAE,MAAK;AACH,cAAA,IAAI,CAACA,WAAU,GAAI,IAAI,CAACxR,gBAAgB;cACxC,IAAI,CAACuR,WAAY,GAAEiC,WAAW;AAClC;YAEA,IAAI,CAAC/N,KAAK,CAAC,kBAAkB,EAAE,IAAI,CAAC8L,WAAW,CAAC;YAChD,IAAI,CAAC9L,KAAK,CAAC,kBAAkB,EAAE,IAAI,CAAC+L,WAAW,CAAC;YAChD,IAAI,CAAC4B,SAAS,EAAE;AACpB,WAAA,MAAO,IAAI,IAAI,CAAClT,QAAO,KAAM,UAAU,EAAE;YACrC,IAAIuS,OAAQ,GAAEjN,KAAK,CAACiN,OAAM,IAAKjN,KAAK,CAACkN,OAAO;YAE5C,IAAI,CAACD,OAAO,EAAE;cACV,IAAI,CAAChB,eAAc,GAAI,IAAI,CAACA,eAAe,CAAC7D,MAAM,CAAC,UAACvH,IAAI,EAAA;AAAA,gBAAA,OAAKA,IAAI,CAACC,KAAI,KAAMkN,WAAW;eAAC,CAAA;AAC5F;AAEA,YAAA,IAAI,CAACE,iBAAiB,CAACF,WAAW,CAAC;YACnC,IAAI,CAAC/N,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACgM,eAAe,CAAC;AAC5D;UAEA,IAAI,CAAChM,KAAK,CAAC,MAAM,EAAE,IAAI,CAACwN,mBAAmB,CAACzN,KAAK,CAAC,CAAC;AACvD;AACJ;KACH;AACDkO,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAACpN,KAAK,EAAE;MACrB,IAAI9E,KAAM,GAAE,IAAI,CAACiQ,eAAe,CAACkC,SAAS,CAAC,UAACtN,IAAI,EAAA;AAAA,QAAA,OAAKA,IAAI,CAACC,KAAM,KAAIA,KAAK;OAAC,CAAA;MAE1E,IAAI9E,KAAM,IAAG,CAAC,EAAE;AACZ,QAAA,IAAI,IAAI,CAACrB,aAAY,IAAK,IAAI,CAACsR,eAAe,CAACjQ,KAAK,CAAC,CAACmF,KAAI,GAAI,EAAG,KAAI,IAAI,CAAC3G,gBAAgB,EAAE,IAAI,CAACyR,eAAe,CAACmC,MAAM,CAACpS,KAAK,EAAE,CAAC,CAAC,CAAA,KAC5H,IAAI,CAACiQ,eAAe,CAACjQ,KAAK,CAAE,GAAE;AAAE8E,UAAAA,KAAK,EAAEA,KAAK;UAAEK,KAAK,EAAE,IAAI,CAAC8K,eAAe,CAACjQ,KAAK,CAAC,CAACmF,QAAQ;SAAI;AACtG,OAAE,MAAK;AACH,QAAA,IAAI,CAAC8K,eAAe,CAACoC,IAAI,CAAC;AAAEvN,UAAAA,KAAK,EAAEA,KAAK;UAAEK,KAAK,EAAE,IAAI,CAAC3G;AAAiB,SAAC,CAAC;AAC7E;MAEA,IAAI,CAACyR,eAAc,GAAApE,kBAAA,CAAQ,IAAI,CAACoE,eAAe,CAAC;KACnD;AACDqC,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAAC3G,KAAK,EAAE;AACd,MAAA,OAAO,IAAI,CAAC4G,eAAe,CAAC5G,KAAK,CAAC;KACrC;AACD4G,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAAC5G,KAAK,EAAE;AAAA,MAAA,IAAAV,KAAA,GAAA,IAAA;AACnB,MAAA,IAAIuH,MAAO,GAAA3G,kBAAA,CAAMF,KAAK,CAAC;AACvB,MAAA,IAAM8G,QAAO,GAAIC,gBAAgB,EAAE;AAEnCF,MAAAA,MAAM,CAACG,IAAI,CAAC,UAACC,KAAK,EAAEC,KAAK,EAAK;QAC1B,IAAMC,MAAK,GAAI3L,gBAAgB,CAACyL,KAAK,CAAC3S,IAAI,EAAEgL,KAAI,CAAC8E,WAAW,CAAC;QAC7D,IAAMgD,MAAK,GAAI5L,gBAAgB,CAAC0L,KAAK,CAAC5S,IAAI,EAAEgL,KAAI,CAAC8E,WAAW,CAAC;QAE7D,OAAO4C,IAAI,CAACG,MAAM,EAAEC,MAAM,EAAE9H,KAAI,CAAC+E,WAAW,EAAEyC,QAAQ,CAAC;AAC3D,OAAC,CAAC;AAEF,MAAA,OAAOD,MAAM;KAChB;AACDQ,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACrH,KAAK,EAAE;AAChB,MAAA,OAAO,IAAI,CAACsH,iBAAiB,CAACtH,KAAK,CAAC;KACvC;AACDsH,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAACtH,KAAK,EAAE;AAAA,MAAA,IAAAuH,MAAA,GAAA,IAAA;AACrB,MAAA,IAAIV,MAAO,GAAA3G,kBAAA,CAAMF,KAAK,CAAC;AAEvB6G,MAAAA,MAAM,CAACG,IAAI,CAAC,UAACC,KAAK,EAAEC,KAAK,EAAK;QAC1B,OAAOK,MAAI,CAACC,cAAc,CAACP,KAAK,EAAEC,KAAK,EAAE,CAAC,CAAC;AAC/C,OAAC,CAAC;AAEF,MAAA,OAAOL,MAAM;KAChB;IACDW,cAAc,EAAA,SAAdA,cAAcA,CAACP,KAAK,EAAEC,KAAK,EAAE7S,KAAK,EAAE;AAChC,MAAA,IAAM8S,SAAS3L,gBAAgB,CAACyL,KAAK,CAAC3S,IAAI,EAAE,IAAI,CAACgQ,eAAe,CAACjQ,KAAK,CAAC,CAAC8E,KAAK,CAAC;AAC9E,MAAA,IAAMiO,SAAS5L,gBAAgB,CAAC0L,KAAK,CAAC5S,IAAI,EAAE,IAAI,CAACgQ,eAAe,CAACjQ,KAAK,CAAC,CAAC8E,KAAK,CAAC;AAC9E,MAAA,IAAM2N,QAAO,GAAIC,gBAAgB,EAAE;MAEnC,IAAII,MAAK,KAAMC,MAAM,EAAE;QACnB,OAAO,IAAI,CAAC9C,eAAe,CAACrL,MAAK,GAAI,CAAE,GAAE5E,KAAM,GAAE,IAAI,CAACmT,cAAc,CAACP,KAAK,EAAEC,KAAK,EAAE7S,KAAI,GAAI,CAAC,CAAA,GAAI,CAAC;AACrG;AAEA,MAAA,OAAO2S,IAAI,CAACG,MAAM,EAAEC,MAAM,EAAE,IAAI,CAAC9C,eAAe,CAACjQ,KAAK,CAAC,CAACmF,KAAK,EAAEsN,QAAQ,CAAC;KAC3E;AACDrG,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAAC9P,KAAK,EAAE;MACV,IAAI8W,aAAc,GAAE,EAAE;AACtB,MAAA,IAAMC,MAAK,GAAI,IAAI,CAACxU,eAAe,QAAQ;AAAA,MAAA,IAAAiO,SAAA,GAAAC,0BAAA,CAE1BzQ,KAAK,CAAA;QAAA0Q,KAAA;AAAA,MAAA,IAAA;QAAtB,KAAAF,SAAA,CAAAG,CAAA,EAAAD,EAAAA,CAAAA,CAAAA,KAAA,GAAAF,SAAA,CAAAI,CAAA,EAAAC,EAAAA,IAAA,GAAwB;AAAA,UAAA,IAAf7G,IAAG,GAAA0G,KAAA,CAAA1Q,KAAA;AACR,UAAA,IAAIgX,+BAAgBhN,KAAM;UAC1B,IAAIiN,UAAW,GAAE,IAAI;UACrB,IAAIC,WAAY,GAAE,KAAK;AAEvB,UAAA,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAE,GAAE,IAAI,CAAC/J,OAAO,CAAC9E,MAAM,EAAE6O,CAAC,EAAE,EAAE;AAC1C,YAAA,IAAI3J,MAAM,IAAI,CAACJ,OAAO,CAAC+J,CAAC,CAAC;AACzB,YAAA,IAAIC,WAAU,GAAI,IAAI,CAACtT,UAAU,CAAC0J,GAAG,EAAE,aAAa,CAAA,IAAK,IAAI,CAAC1J,UAAU,CAAC0J,GAAG,EAAE,OAAO,CAAC;;AAEtF;AACA,YAAA,IAAI5M,MAAM,CAACyW,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAACjV,OAAO,EAAE8U,WAAW,CAAC,EAAE;cACjE,IAAII,eAAgB,GAAE,IAAI,CAAC1T,UAAU,CAAC0J,GAAG,EAAE,iBAAiB,CAAA,IAAK,YAAY;AAC7E,cAAA,IAAIiK,WAAU,GAAI,IAAI,CAACnV,OAAO,CAAC8U,WAAW,CAAC;AAC3C,cAAA,IAAIM,gBAAe,GAAIC,aAAa,CAACrV,OAAO,CAACkV,eAAe,CAAC;AAC7D,cAAA,IAAII,iBAAkB,GAAE;AAAER,gBAAAA,WAAW,EAAXA,WAAW;AAAEK,gBAAAA,WAAW,EAAXA,WAAW;AAAEC,gBAAAA,gBAAgB,EAAhBA,gBAAgB;AAAEX,gBAAAA,QAAAA;eAAQ;cAE9E,IACKA,UAAU,EAAE,IAAI,CAACc,iBAAiB,CAACb,QAAQ,EAAEY,iBAAiB,CAAE,IAAG,IAAI,CAACE,eAAe,CAACd,QAAQ,EAAEY,iBAAiB,CAAC,CAAC,IACrH,CAACb,MAAO,IAAG,EAAE,IAAI,CAACe,eAAe,CAACd,QAAQ,EAAEY,iBAAiB,CAAE,IAAG,IAAI,CAACC,iBAAiB,CAACb,QAAQ,EAAEY,iBAAiB,CAAC,CAAC,EACzH;AACEX,gBAAAA,UAAS,GAAI,KAAK;AACtB;cAEA,IAAI,CAACA,UAAU,EAAE;AACb,gBAAA;AACJ;AACJ;;AAEA;YACA,IAAI,IAAI,CAACc,eAAe,EAAC,IAAK,CAACb,WAAW,EAAE;AACxC,cAAA,IAAIc,iBAAkB,GAAArR,eAAA,CAAA,EAAA,EAAOqQ,SAAU;AACvC,cAAA,IAAIS,YAAY,GAAE,IAAI,CAACnV,OAAO,CAAC,QAAQ,CAAC;AACxC,cAAA,IAAIoV,iBAAe,GAAIC,aAAa,CAACrV,OAAO,CAAC,UAAU,CAAC;AACxD,cAAA,IAAI2V,6BAA4B,GAAI;AAAEb,gBAAAA,WAAW,EAAXA,WAAW;AAAEK,gBAAAA,WAAW,EAAXA,YAAW;AAAEC,gBAAAA,gBAAgB,EAAhBA,iBAAgB;AAAEX,gBAAAA,QAAAA;eAAQ;AAE1F,cAAA,IACKA,MAAO,KAAI,IAAI,CAACc,iBAAiB,CAACG,iBAAiB,EAAEC,6BAA6B,CAAA,IAAK,IAAI,CAACH,eAAe,CAACE,iBAAiB,EAAEC,6BAA6B,CAAC,CAAC,IAC9J,CAAClB,MAAK,KAAM,IAAI,CAACe,eAAe,CAACE,iBAAiB,EAAEC,6BAA6B,CAAE,IAAG,IAAI,CAACJ,iBAAiB,CAACG,iBAAiB,EAAEC,6BAA6B,CAAC,CAAC,EAClK;AACEf,gBAAAA,WAAY,GAAE,IAAI;AAClBF,gBAAAA,QAAS,GAAEgB,iBAAiB;AAChC;AACJ;AACJ;UAEA,IAAIE,OAAM,GAAIjB,UAAU;AAExB,UAAA,IAAI,IAAI,CAACc,eAAe,EAAE,EAAE;YACxBG,OAAM,GAAIjB,UAAW,IAAGC,WAAW;AACvC;AAEA,UAAA,IAAIgB,OAAO,EAAE;AACTpB,YAAAA,aAAa,CAACf,IAAI,CAACiB,QAAQ,CAAC;AAChC;AACJ;AAAA,OAAA,CAAA,OAAAjG,GAAA,EAAA;QAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA,CAAA;AAAA,OAAA,SAAA;AAAAP,QAAAA,SAAA,CAAAS,CAAA,EAAA;AAAA;AAEA,MAAA,IAAIkH,WAAU,GAAI,IAAI,CAAChD,mBAAmB,CAACzN,KAAK,CAAC;MAEjDyQ,WAAW,CAACC,aAAc,GAAEtB,aAAa;AACzC,MAAA,IAAI,CAACnP,KAAK,CAAC,QAAQ,EAAEwQ,WAAW,CAAC;AAEjC,MAAA,OAAOrB,aAAa;KACvB;AACDe,IAAAA,iBAAiB,WAAjBA,iBAAiBA,CAAC7N,IAAI,EAAE4N,iBAAiB,EAAE;AACvC,MAAA,IAAI5N,IAAI,EAAE;QACN,IAAIqO,OAAM,GAAI,KAAK;QAEnB,IAAIrO,IAAI,CAACpD,QAAQ,EAAE;AACf,UAAA,IAAI0R,UAAS,GAAA/I,kBAAA,CAAQvF,IAAI,CAACpD,QAAQ,CAAC;UAEnCoD,IAAI,CAACpD,QAAS,GAAE,EAAE;AAAA,UAAA,IAAAyK,UAAA,GAAAZ,0BAAA,CAEI6H,UAAU,CAAA;YAAAhH,MAAA;AAAA,UAAA,IAAA;YAAhC,KAAAD,UAAA,CAAAV,CAAA,EAAAW,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAAT,CAAA,EAAAC,EAAAA,IAAA,GAAkC;AAAA,cAAA,IAAzBgC,SAAQ,GAAAvB,MAAA,CAAAtR,KAAA;AACb,cAAA,IAAIuY,aAAc,GAAA5R,eAAA,CAAA,EAAA,EAAOkM,UAAW;cAEpC,IAAI,IAAI,CAACiF,eAAe,CAACS,aAAa,EAAEX,iBAAiB,CAAC,EAAE;AACxDS,gBAAAA,UAAU,IAAI;AACdrO,gBAAAA,IAAI,CAACpD,QAAQ,CAACmP,IAAI,CAACwC,aAAa,CAAC;AACrC;AACJ;AAAA,WAAA,CAAA,OAAAxH,GAAA,EAAA;YAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA,CAAA;AAAA,WAAA,SAAA;AAAAM,YAAAA,UAAA,CAAAJ,CAAA,EAAA;AAAA;AACJ;AAEA,QAAA,IAAIoH,OAAO,EAAE;AACT,UAAA,OAAO,IAAI;AACf;AACJ;KACH;AACDP,IAAAA,eAAe,WAAfA,eAAeA,CAAC9N,IAAI,EAAAwO,IAAA,EAA0D;AAAA,MAAA,IAAtDpB,WAAW,GAAAoB,IAAA,CAAXpB,WAAW;QAAEK,WAAW,GAAAe,IAAA,CAAXf,WAAW;QAAEC,gBAAgB,GAAAc,IAAA,CAAhBd,gBAAgB;QAAEX,MAAK,GAAAyB,IAAA,CAALzB,MAAK;MACrE,IAAIsB,OAAM,GAAI,KAAK;MACnB,IAAII,cAAe,GAAE5N,gBAAgB,CAACb,IAAI,CAACrG,IAAI,EAAEyT,WAAW,CAAC;MAE7D,IAAIM,gBAAgB,CAACe,cAAc,EAAEhB,WAAW,EAAE,IAAI,CAACjV,YAAY,CAAC,EAAE;AAClE6V,QAAAA,UAAU,IAAI;AAClB;AAEA,MAAA,IAAI,CAACA,OAAM,IAAMtB,MAAK,IAAK,CAAC,IAAI,CAAC2B,UAAU,CAAC1O,IAAI,CAAE,EAAE;AAChDqO,QAAAA,OAAQ,GAAE,IAAI,CAACR,iBAAiB,CAAC7N,IAAI,EAAE;AAAEoN,UAAAA,WAAW,EAAXA,WAAW;AAAEK,UAAAA,WAAW,EAAXA,WAAW;AAAEC,UAAAA,gBAAgB,EAAhBA,gBAAgB;AAAEX,UAAAA,QAAAA;SAAQ,CAAA,IAAKsB,OAAO;AAC7G;AAEA,MAAA,OAAOA,OAAO;KACjB;AACDxD,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC7K,IAAI,EAAE;MACjB,OAAO,IAAI,CAACzJ,iBAAiB,IAAI,CAACD,aAAY,GAAI,IAAI,CAACA,aAAa,CAAC,IAAI,CAACyN,OAAO,CAAC/D,IAAI,CAAC,CAAE,KAAI,IAAG,GAAI,KAAK;KAC5G;AACD0O,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAAC1O,IAAI,EAAE;AACb,MAAA,OAAOA,IAAI,CAACE,IAAK,KAAI,QAAQ,KAAI,GAAI,EAAEF,IAAI,CAACpD,QAAS,IAAGoD,IAAI,CAACpD,QAAQ,CAAC0B,MAAM,CAAC;KAChF;AACD6M,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAACzN,KAAK,EAAE;AAAA,MAAA,IAAAiR,MAAA,GAAA,IAAA;AACvB,MAAA,IAAIC,gBAAgB;AAEpB,MAAA,IAAI,IAAI,CAACC,UAAU,EAAE,EAAE;QACnBD,gBAAiB,GAAE,EAAE;AACrB,QAAA,IAAI,CAACxL,OAAO,CAACuC,OAAO,CAAC,UAACnC,GAAG,EAAK;UAC1B,IAAImL,MAAI,CAAC7U,UAAU,CAAC0J,GAAG,EAAE,OAAO,CAAC,EAAE;AAC/BoL,YAAAA,gBAAgB,CAACpL,GAAG,CAACzN,KAAK,CAACyI,KAAK,CAAA,GAAImQ,MAAI,CAAC7U,UAAU,CAAC0J,GAAG,EAAE,iBAAiB,CAAC;AAC/E;AACJ,SAAC,CAAC;AACN;MAEA,OAAO;AACH5F,QAAAA,aAAa,EAAEF,KAAK;QACpB3G,KAAK,EAAE,IAAI,CAACwS,OAAO;QACnB1S,IAAI,EAAE,IAAI,CAAC2S,MAAM;QACjBxR,SAAS,EAAE,IAAI,CAACyR,WAAW;QAC3BxR,SAAS,EAAE,IAAI,CAACyR,WAAW;QAC3BvR,aAAa,EAAE,IAAI,CAACwR,eAAe;QACnCrR,OAAO,EAAE,IAAI,CAACA,OAAO;AACrBsW,QAAAA,gBAAgB,EAAEA;OACrB;KACJ;AACDE,IAAAA,mBAAmB,EAAnBA,SAAAA,mBAAmBA,CAACpR,KAAK,EAAE;MACvB,IAAIqR,aAAc,GAAEC,SAAS,CAAC,IAAI,CAACvT,GAAG,CAAC,CAACwT,IAAI;AAE5C,MAAA,IAAI,CAAChF,mBAAoB,GAAEvM,KAAK,CAACgG,MAAM,CAACpG,aAAa;MACrD,IAAI,CAAC4R,iBAAiB,IAAI;AAC1B,MAAA,IAAI,CAAClF,iBAAgB,GAAItM,KAAK,CAACyR,KAAI,GAAIJ,aAAY,GAAI,IAAI,CAACtT,GAAG,CAAC2T,UAAU;MAE1E,IAAI,CAACC,sBAAsB,EAAE;KAChC;AACDC,IAAAA,cAAc,EAAdA,SAAAA,cAAcA,CAAC5R,KAAK,EAAE;MAClB,IAAIqR,aAAc,GAAEC,SAAS,CAAC,IAAI,CAACvT,GAAG,CAAC,CAACwT,IAAI;MAE5C,IAAI,CAACxT,GAAG,CAAC8T,YAAY,CAAC,0BAA0B,EAAE,MAAM,CAAC;MACzD,CAAC,IAAI,CAACC,UAAW,IAAGC,QAAQ,CAAC,IAAI,CAAChU,GAAG,EAAE;AAAE,QAAA,aAAa,EAAE;AAAO,OAAC,CAAC;AACjE,MAAA,IAAI,CAACsJ,KAAK,CAAC2K,YAAY,CAACvW,KAAK,CAACwW,MAAO,GAAE,IAAI,CAAClU,GAAG,CAACmU,YAAa,GAAE,IAAI;MACnE,IAAI,CAAC7K,KAAK,CAAC2K,YAAY,CAACvW,KAAK,CAAC0W,MAAM,CAAA,GAAI,IAAI;MAC5C,IAAI,CAAC9K,KAAK,CAAC2K,YAAY,CAACvW,KAAK,CAAC8V,OAAOvR,KAAK,CAACyR,KAAM,GAAEJ,aAAc,GAAE,IAAI,CAACtT,GAAG,CAAC2T,UAAS,GAAI,IAAI;MAE7F,IAAI,CAACrK,KAAK,CAAC2K,YAAY,CAACvW,KAAK,CAAC2W,OAAM,GAAI,OAAO;KAClD;IACDC,iBAAiB,EAAA,SAAjBA,iBAAiBA,GAAG;AAChB,MAAA,IAAIC,KAAM,GAAEC,KAAK,CAAC,IAAI,CAACxU,GAAG,CAAE,GAAE,IAAI,CAACuO,iBAAgB,GAAI,IAAI,CAACjF,KAAK,CAAC2K,YAAY,CAACQ,UAAS,GAAI,IAAI,CAACnL,KAAK,CAAC2K,YAAY,CAACQ,UAAW,GAAE,IAAI,CAAClG,iBAAiB;AACvJ,MAAA,IAAImG,WAAY,GAAE,IAAI,CAAClG,mBAAmB,CAACmG,WAAW;AACtD,MAAA,IAAIC,cAAa,GAAIF,cAAcH,KAAK;MACxC,IAAIM,QAAO,GAAI,IAAI,CAACrG,mBAAmB,CAAC9Q,KAAK,CAACmX,QAAS,IAAG,EAAE;MAE5D,IAAIH,WAAY,GAAEH,KAAM,GAAEO,QAAQ,CAACD,QAAQ,EAAE,EAAE,CAAC,EAAE;AAC9C,QAAA,IAAI,IAAI,CAAC5X,gBAAiB,KAAI,KAAK,EAAE;AACjC,UAAA,IAAI8X,UAAS,GAAI,IAAI,CAACvG,mBAAmB,CAAC1M,kBAAkB;AAC5D,UAAA,IAAIkT,kBAAkBD,UAAU,CAACJ,WAAU,GAAIJ,KAAK;AAEpD,UAAA,IAAIK,cAAa,GAAI,EAAC,IAAKI,kBAAkB,EAAE,EAAE;AAC7C,YAAA,IAAI,CAACC,gBAAgB,CAACL,cAAc,EAAEI,eAAe,CAAC;AAC1D;AACJ,SAAA,MAAO,IAAI,IAAI,CAAC/X,qBAAqB,QAAQ,EAAE;AAC3C,UAAA,IAAMiY,UAAW,GAAE,IAAI,CAAC5L,KAAK,CAAC6L,KAAK,CAACR,WAAU,GAAIJ,KAAI,GAAI,IAAI;AAE9D,UAAA,IAAMa,gBAAe,GAAI,SAAnBA,gBAAeA,CAAKC,EAAE,EAAK;AAC7B,YAAA,CAAC,CAACA,EAAG,KAAIA,EAAE,CAAC3X,KAAK,CAAC4X,KAAI,GAAID,EAAE,CAAC3X,KAAK,CAACmX,WAAWK,UAAU,CAAC;WAC5D;;AAED;AACA,UAAA,IAAI,CAACD,gBAAgB,CAACL,cAAc,CAAC;AACrCQ,UAAAA,gBAAgB,CAAC,IAAI,CAAC9L,KAAK,CAAC6L,KAAK,CAAC;AACtC;AAEA,QAAA,IAAI,CAACjT,KAAK,CAAC,mBAAmB,EAAE;UAC5BiK,OAAO,EAAE,IAAI,CAACqC,mBAAmB;AACjC+F,UAAAA,KAAK,EAAEA;AACX,SAAC,CAAC;AACN;MAEA,IAAI,CAACjL,KAAK,CAAC2K,YAAY,CAACvW,KAAK,CAAC2W,UAAU,MAAM;MAC9C,IAAI,CAACkB,YAAW,GAAI,IAAI;AACxB,MAAA,IAAI,CAACvV,GAAG,CAACwV,eAAe,CAAC,0BAA0B,CAAC;AACpD,MAAA,CAAC,IAAI,CAACzB,UAAW,KAAI,IAAI,CAAC/T,GAAG,CAACtC,KAAK,CAAC,aAAa,CAAE,GAAE,EAAE,CAAC;MAExD,IAAI,CAAC+X,wBAAwB,EAAE;KAClC;AACDR,IAAAA,gBAAgB,WAAhBA,gBAAgBA,CAACL,cAAc,EAAEI,eAAe,EAAE;AAC9C,MAAA,IAAIU,QAAO,GAAI3T,QAAQ,CAAC,IAAI,CAACyM,mBAAmB,CAAC;MACjD,IAAImH,MAAK,GAAI,EAAE;MACf,IAAIC,OAAM,GAAI/L,IAAI,CAAC,IAAI,CAACP,KAAK,CAAC6L,KAAK,EAAE,0CAA0C,CAAC;AAEhFS,MAAAA,OAAO,CAAC1L,OAAO,CAAC,UAAC9F,MAAM,EAAA;QAAA,OAAKuR,MAAM,CAACrF,IAAI,CAACrQ,aAAa,CAACmE,MAAM,CAAC,CAAC;OAAC,CAAA;MAE/D,IAAI,CAACwK,mBAAmB,EAAE;MAC1B,IAAI,CAACiH,kBAAkB,EAAE;MAEzB,IAAIC,SAAU,GAAE,EAAE;AAClB,MAAA,IAAIC,QAAO,GAAAvW,+BAAAA,CAAAA,MAAA,CAAkC,IAAI,CAACwW,aAAa,EAAyE,6EAAA,CAAA;AAExIL,MAAAA,MAAM,CAACzL,OAAO,CAAC,UAACoL,KAAK,EAAErX,KAAK,EAAK;AAC7B,QAAA,IAAIgY,QAAO,GAAIhY,KAAI,KAAMyX,WAAWd,cAAa,GAAII,eAAc,IAAK/W,KAAI,KAAMyX,WAAW,CAAA,GAAIV,eAAc,GAAIM,KAAK;QACxH,IAAI5X,KAAI,aAAA8B,MAAA,CAAcyW,QAAQ,EAAAzW,4BAAAA,CAAAA,CAAAA,MAAA,CAA6ByW,QAAQ,EAAe,eAAA,CAAA;AAElFH,QAAAA,SAAU,IAAAtW,wBAAAA,CAAAA,MAAA,CACJuW,QAAQ,8DAAAvW,MAAA,CAAyDvB,KAAI,GAAI,CAAC,EAAAuB,0BAAAA,CAAAA,CAAAA,MAAA,CAC1EuW,QAAQ,8DAAAvW,MAAA,CAAyDvB,KAAI,GAAI,CAAC,EAAA,0BAAA,CAAA,CAAAuB,MAAA,CAC1EuW,QAAQ,EAAAvW,0DAAAA,CAAAA,CAAAA,MAAA,CAAyDvB,KAAI,GAAI,CAAC,EAAA,+BAAA,CAAA,CAAAuB,MAAA,CACtE9B,KAAK,EAEd,2CAAA,CAAA;AACL,OAAC,CAAC;AAEF,MAAA,IAAI,CAACwY,YAAY,CAACJ,SAAU,GAAEA,SAAS;KAC1C;IACDlC,sBAAsB,EAAA,SAAtBA,sBAAsBA,GAAG;AAAA,MAAA,IAAAuC,MAAA,GAAA,IAAA;AACrB,MAAA,IAAI,CAAC,IAAI,CAAC9H,4BAA4B,EAAE;QACpC,IAAI,CAACA,4BAA6B,GAAE+H,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,UAACpU,KAAK,EAAK;UAClF,IAAIkU,MAAI,CAAC1C,cAAc,EAAE;AACrB0C,YAAAA,MAAI,CAACtC,cAAc,CAAC5R,KAAK,CAAC;AAC9B;AACJ,SAAC,CAAC;AACN;AAEA,MAAA,IAAI,CAAC,IAAI,CAACqM,+BAA+B,EAAE;QACvC,IAAI,CAACA,kCAAkC8H,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,YAAM;UAC9E,IAAIF,MAAI,CAAC1C,cAAc,EAAE;YACrB0C,MAAI,CAAC1C,cAAa,GAAI,KAAK;YAC3B0C,MAAI,CAAC7B,iBAAiB,EAAE;AAC5B;AACJ,SAAC,CAAC;AACN;KACH;IACDmB,wBAAwB,EAAA,SAAxBA,wBAAwBA,GAAG;MACvB,IAAI,IAAI,CAACpH,4BAA4B,EAAE;QACnC+H,QAAQ,CAACE,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACjI,4BAA4B,CAAC;QAC3E,IAAI,CAACA,4BAA6B,GAAE,IAAI;AAC5C;MAEA,IAAI,IAAI,CAACC,+BAA+B,EAAE;QACtC8H,QAAQ,CAACE,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAChI,+BAA+B,CAAC;QAC9E,IAAI,CAACA,+BAA8B,GAAI,IAAI;AAC/C;KACH;AACDiI,IAAAA,eAAe,WAAfA,eAAeA,CAACtU,KAAK,EAAE8F,GAAG,EAAE;AACxB,MAAA,IAAI,CAAC9F,KAAK,CAACI,IAAK,KAAI,OAAM,IAAKJ,KAAK,CAACI,IAAK,KAAI,aAAa,KAAKJ,KAAK,CAACK,aAAa,CAACC,QAAS,KAAI,IAAK,IAAGC,YAAY,CAACP,KAAK,CAACK,aAAa,EAAE,wBAAwB,CAAC,EAAE;AAClK,QAAA,IAAI,CAACyN,mBAAmB,CAAC9N,KAAK,EAAE8F,GAAG,CAAC;AACxC;KACH;IACDyO,eAAe,EAAA,SAAfA,eAAeA,GAAG;MACd,IAAI,IAAI,CAAC7O,OAAO,EAAE;AAAA,QAAA,IAAAoE,UAAA,GAAAf,0BAAA,CACE,IAAI,CAACrD,OAAO,CAAA;UAAAqE,MAAA;AAAA,QAAA,IAAA;UAA5B,KAAAD,UAAA,CAAAb,CAAA,EAAAc,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAAZ,CAAA,EAAAC,EAAAA,IAAA,GAA8B;AAAA,YAAA,IAArBrD,GAAE,GAAAiE,MAAA,CAAAzR,KAAA;YACP,IAAIwN,GAAG,CAAC5G,QAAO,IAAK4G,GAAG,CAAC5G,QAAQ,CAACkJ,MAAM,EAAE;AACrC,cAAA,OAAO,IAAI;AACf;AACJ;AAAA,SAAA,CAAA,OAAAiB,GAAA,EAAA;UAAAS,UAAA,CAAAR,CAAA,CAAAD,GAAA,CAAA;AAAA,SAAA,SAAA;AAAAS,UAAAA,UAAA,CAAAP,CAAA,EAAA;AAAA;AACJ;AAEA,MAAA,OAAO,KAAK;KACf;IACD4H,UAAU,EAAA,SAAVA,UAAUA,GAAG;MACT,OAAO,IAAI,CAACvW,OAAM,IAAK1B,MAAM,CAACsb,IAAI,CAAC,IAAI,CAAC5Z,OAAO,CAAC,CAACgG,MAAK,GAAI,CAAE,IAAG,IAAI,CAAChG,OAAO,CAAC6Z,WAAY,KAAIvb,MAAM;KACrG;IACDmX,eAAe,EAAA,SAAfA,eAAeA,GAAG;AACd,MAAA,OAAO,IAAI,CAACzV,OAAQ,IAAG1B,MAAM,CAACyW,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAACjV,OAAO,EAAE,QAAQ,CAAC;KACtF;AACD8Z,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACpS,IAAI,EAAE;AACf,MAAA,OAAOA,IAAI,CAACrG,IAAI,CAAC9D,IAAI;KACxB;IACDyb,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;AAAA,MAAA,IAAAe,eAAA;MACjB,IAAI,CAACV,YAAW,GAAIE,QAAQ,CAACS,aAAa,CAAC,OAAO,CAAC;AACnD,MAAA,IAAI,CAACX,YAAY,CAAC1b,IAAG,GAAI,UAAU;AACnCsZ,MAAAA,YAAY,CAAC,IAAI,CAACoC,YAAY,EAAE,OAAO,EAAA,CAAAU,eAAA,GAAE,IAAI,CAACE,SAAS,MAAAF,IAAAA,IAAAA,eAAA,gBAAAA,eAAA,GAAdA,eAAA,CAAgBG,MAAM,MAAA,IAAA,IAAAH,eAAA,KAAA,MAAA,IAAA,CAAAA,eAAA,GAAtBA,eAAA,CAAwBI,GAAG,cAAAJ,eAAA,KAAA,MAAA,GAAA,MAAA,GAA3BA,eAAA,CAA6BK,KAAK,CAAC;MAC5Eb,QAAQ,CAACc,IAAI,CAACC,WAAW,CAAC,IAAI,CAACjB,YAAY,CAAC;KAC/C;IACDtH,mBAAmB,EAAA,SAAnBA,mBAAmBA,GAAG;MAClB,IAAI,IAAI,CAACsH,YAAY,EAAE;QACnBE,QAAQ,CAACc,IAAI,CAACE,WAAW,CAAC,IAAI,CAAClB,YAAY,CAAC;QAC5C,IAAI,CAACA,YAAW,GAAI,IAAI;AAC5B;KACH;AACDmB,IAAAA,WAAW,WAAXA,WAAWA,CAAC9S,IAAI,EAAEtG,KAAK,EAAE;AACrB,MAAA,IAAI,IAAI,CAACmR,cAAc,CAAC7K,IAAI,CAAC,EAAE;QAC3B,IAAI,CAAC4J,mBAAmB,IAAI;AAE5B,QAAA,OAAO,CAAC;AACZ;MAEA,IAAI,IAAI,CAACrT,aAAa,EAAE;AACpB,QAAA,IAAI,CAAC,IAAI,CAACsU,cAAc,CAAC7K,IAAI,KAAKtG,KAAI,KAAM,CAAA,IAAK,CAAC,IAAI,CAACkQ,gBAAgB,EAAE,OAAO,CAAC;OACrF,MAAO,IAAI,CAAC,IAAI,CAACrT,iBAAiBmD,KAAI,KAAM,CAAC,EAAE;AAC3C,QAAA,OAAO,CAAC;AACZ;AAEA,MAAA,OAAO,EAAE;AACb;GACH;AACDsC,EAAAA,QAAQ,EAAE;IACNoH,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,MAAA,OAAO,IAAI,CAACiG,SAAS,CAAC0J,GAAG,CAAC,IAAI,CAAC;KAClC;IACDC,aAAa,EAAA,SAAbA,aAAaA,GAAG;MACZ,IAAI,IAAI,CAACvb,IAAI,EAAE;QACX,OAAO,IAAI,CAACzB,KAAK;AACrB,OAAE,MAAK;QACH,IAAI,IAAI,CAACA,KAAI,IAAK,IAAI,CAACA,KAAK,CAACsI,MAAM,EAAE;AACjC,UAAA,IAAI3E,OAAO,IAAI,CAAC3D,KAAK;UAErB,IAAI,IAAI,CAACkH,MAAM,EAAE;AACb,YAAA,IAAI,IAAI,CAAC9E,QAAO,KAAM,QAAQ,EAAEuB,IAAG,GAAI,IAAI,CAACqS,UAAU,CAACrS,IAAI,CAAC,CAAA,KACvD,IAAI,IAAI,CAACvB,QAAS,KAAI,UAAU,EAAEuB,IAAK,GAAE,IAAI,CAAC+S,YAAY,CAAC/S,IAAI,CAAC;AACzE;AAEA,UAAA,IAAI,IAAI,CAACkV,UAAU,EAAE,EAAE;AACnBlV,YAAAA,IAAK,GAAE,IAAI,CAACmM,MAAM,CAACnM,IAAI,CAAC;AAC5B;AAEA,UAAA,OAAOA,IAAI;AACf,SAAE,MAAK;AACH,UAAA,OAAO,IAAI;AACf;AACJ;KACH;IACDsZ,YAAY,EAAA,SAAZA,YAAYA,GAAG;AACX,MAAA,IAAMtZ,IAAK,GAAE,IAAI,CAACqZ,aAAa;MAE/B,IAAI,IAAI,CAAC/b,SAAS,EAAE;QAChB,IAAMF,KAAI,GAAI,IAAI,CAACU,IAAK,GAAE,CAAE,GAAE,IAAI,CAAC8R,OAAO;QAE1C,OAAO5P,IAAI,CAACuZ,KAAK,CAACnc,KAAK,EAAEA,KAAI,GAAI,IAAI,CAACyS,MAAM,CAAC;AACjD,OAAE,MAAK;AACH,QAAA,OAAO7P,IAAI;AACf;KACH;IACDwZ,KAAK,EAAA,SAALA,KAAKA,GAAG;AACJ,MAAA,IAAMxZ,IAAK,GAAE,IAAI,CAACqZ,aAAa;AAE/B,MAAA,OAAO,CAACrZ,IAAK,IAAGA,IAAI,CAAC2E,MAAO,KAAI,CAAC;KACpC;IACDpB,MAAM,EAAA,SAANA,MAAMA,GAAG;AACL,MAAA,OAAO,IAAI,CAACuM,WAAY,IAAI,IAAI,CAACE,eAAc,IAAK,IAAI,CAACA,eAAe,CAACrL,MAAO,GAAE,CAAE;KACvF;IACD8U,SAAS,EAAA,SAATA,SAASA,GAAG;MACR,IAAIA,YAAY,KAAK;AAAA,MAAA,IAAAC,UAAA,GAAA5M,0BAAA,CAEL,IAAI,CAACrD,OAAO,CAAA;QAAAkQ,MAAA;AAAA,MAAA,IAAA;QAA5B,KAAAD,UAAA,CAAA1M,CAAA,EAAA2M,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAAzM,CAAA,EAAAC,EAAAA,IAAA,GAA8B;AAAA,UAAA,IAArBrD,GAAE,GAAA8P,MAAA,CAAAtd,KAAA;AACP,UAAA,IAAI,IAAI,CAAC8D,UAAU,CAAC0J,GAAG,EAAE,QAAQ,CAAA,IAAMA,GAAG,CAAC5G,QAAS,IAAG4G,GAAG,CAAC5G,QAAQ,CAACC,MAAO,EAAE;AACzEuW,YAAAA,SAAQ,GAAI,IAAI;AAChB,YAAA;AACJ;AACJ;AAAA,OAAA,CAAA,OAAArM,GAAA,EAAA;QAAAsM,UAAA,CAAArM,CAAA,CAAAD,GAAA,CAAA;AAAA,OAAA,SAAA;AAAAsM,QAAAA,UAAA,CAAApM,CAAA,EAAA;AAAA;AAEA,MAAA,OAAOmM,SAAS;KACnB;IACDG,YAAY,EAAA,SAAZA,YAAYA,GAAG;AACX,MAAA,OAAO,IAAI,CAACtc,cAAc,IAAI,CAACC,iBAAgB,KAAM,QAAO,IAAK,IAAI,CAACA,sBAAsB,MAAM,CAAC;KACtG;IACDsc,eAAe,EAAA,SAAfA,eAAeA,GAAG;AACd,MAAA,OAAO,IAAI,CAACvc,SAAU,KAAI,IAAI,CAACC,iBAAgB,KAAM,KAAM,IAAG,IAAI,CAACA,sBAAsB,MAAM,CAAC;KACnG;IACDuc,mBAAmB,EAAA,SAAnBA,mBAAmBA,GAAG;MAClB,OAAO,IAAI,CAACld,aAAc,IAAG,IAAI,CAACA,aAAY,KAAM,QAAQ;KAC/D;IACDmd,qBAAqB,EAAA,SAArBA,qBAAqBA,GAAG;MACpB,OAAO,IAAI,CAACnd,aAAY,IAAK,IAAI,CAACA,kBAAkB,UAAU;KACjE;IACDkK,gBAAgB,EAAA,SAAhBA,gBAAgBA,GAAG;AACf,MAAA,OAAO,IAAI,CAACgT,uBAAuB,IAAI,CAACC,qBAAqB;KAChE;IACDC,kBAAkB,EAAA,SAAlBA,kBAAkBA,GAAG;MACjB,IAAI,IAAI,CAAClc,IAAI,EAAE;QACX,OAAO,IAAI,CAACT,YAAY;AAC5B,OAAE,MAAK;AACH,QAAA,IAAM2C,IAAK,GAAE,IAAI,CAACqZ,aAAa;AAE/B,QAAA,OAAOrZ,IAAG,GAAIA,IAAI,CAAC2E,MAAO,GAAE,CAAC;AACjC;AACJ;GACH;AACDe,EAAAA,UAAU,EAAE;AACRuU,IAAAA,KAAK,EAAEC,QAAY;AACnBC,IAAAA,WAAW,EAAEC,SAAS;AACtBC,IAAAA,YAAY,EAAEC,QAAU;AACxBC,IAAAA,YAAY,EAAEC,QAAU;AACxB1S,IAAAA,WAAW,EAAEA;AACjB;AACJ,CAAC;;;;;;;;;;;;;;ECp5BG,OAAAnF,SAAA,EAAA,EAAAC,kBAAA,CAwMK,OAxMLC,UAwMK,CAAA;AAxMC,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,MAAA,CAAA;AAAU,IAAA,sBAAoB,EAAC;KAAuCc,IAAI,CAAAoX,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACrFC,UAAY,CAAArX,IAAA,CAAAsX,MAAA,EAAA,SAAA,CAAA,EACDtX,IAAA,CAAAtF,OAAM,IAAKsF,IAAY,CAAAnF,WAAA,KAAA,MAAA,IAAlCyE,SAAA,EAAA,EAAAC,kBAAA,CAMK,OANLC,UAMK,CAAA;;AAN0C,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,SAAA;KAAqBc,IAAG,CAAAhC,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAC5E4E,kBAAA,CAIK,OAJLpD,UAIK,CAAA;AAJC,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,MAAA;KAAkBc,IAAG,CAAAhC,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAChCqZ,UAEM,CAAArX,IAAA,CAAAsX,MAAA,EAAA,aAAA,EAAA;AAFoB,IAAA,OAAA,iBAAOtX,IAAE,CAAAd,EAAA,CAAA,aAAA,CAAA;KAAnC,YAAA;AAAA,IAAA,OAEM,eADFY,WAAkI,CAAAC,uBAAA,CAAlHC,IAAY,CAAArF,WAAA,GAAA,MAAA,GAAA,aAAA,CAAA,EAA5B6E,UAAkI,CAAA;AAA5EwF,MAAAA,IAAG,EAAH,EAAG;MAAG,OAAK,EAAA,CAAGhF,IAAE,CAAAd,EAAA,CAAA,aAAA,CAAA,EAAiBc,IAAW,CAAArF,WAAA;OAAWqF,IAAG,CAAAhC,GAAA,CAAA,aAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA;kDAIjHgC,IAAA,CAAAsX,MAAM,CAACzU,MAAM,IAAxBvD,SAAA,EAAA,EAAAC,kBAAA,CAEK,OAFLC,UAEK,CAAA;;AAFsB,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,QAAA;KAAoBc,IAAG,CAAAhC,GAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CACvDqZ,UAA0B,CAAArX,IAAA,CAAAsX,MAAA,EAAA,QAAA,CAAA,wCAGpB7X,QAAY,CAAA8W,YAAA,iBADtBzW,WAuDa,CAAAyX,sBAAA,EAAA;;IArDR1d,IAAI,EAAE+D,KAAM,CAAA4O,MAAA;IACZzS,KAAK,EAAE6D,KAAO,CAAA2O,OAAA;IACdvS,YAAY,EAAEyF,QAAkB,CAAAkX,kBAAA;IAChCtc,YAAY,EAAE2F,IAAY,CAAA3F,YAAA;IAC1Bmd,QAAQ,EAAExX,IAAiB,CAAA5F,iBAAA;IAC3BE,kBAAkB,EAAE0F,IAAkB,CAAA1F,kBAAA;IACtCE,yBAAyB,EAAEwF,IAAyB,CAAAxF,yBAAA;AACpD,IAAA,OAAA,iBAAOwF,IAAE,CAAAd,EAAA,CAAA,aAAA,EAAA;AAAAuY,MAAAA,QAAA,EAAA;AAAA,KAAA,CAAA,CAAA;AACTxJ,IAAAA,MAAI,EAAAvC,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,MAAA,OAAElM,QAAM,CAAAwO,MAAA,CAACtC,MAAM,CAAA;AAAA,KAAA,CAAA;IACnB+L,UAAU,EAAE1X,IAAmB,CAAA7F,mBAAA;IAC/BwL,QAAQ,EAAE3F,IAAQ,CAAA2F,QAAA;AAClBvH,IAAAA,EAAE,EAAE4B,IAAG,CAAAhC,GAAA,CAAA,aAAA;;;MAEQgC,IAAA,CAAAsX,MAAM,CAACK,kBAAkB;UAAG,WAAS;AACjDC,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADmDC,SAAS,EAAA;MAAA,OAAA,CAC5DuR,UAaO,CAAArX,IAAA,CAAAsX,MAAA,EAAA,oBAAA,EAAA;QAXFvd,KAAK,EAAE+L,SAAS,CAAC/L,KAAK;QACtB8d,IAAI,EAAE/R,SAAS,CAAC+R,IAAI;QACpBhe,IAAI,EAAEiM,SAAS,CAACjM,IAAI;QACpBwU,IAAI,EAAEvI,SAAS,CAACuI,IAAI;QACpBD,SAAS,EAAEtI,SAAS,CAACsI,SAAS;QAC9BpU,YAAY,EAAE8L,SAAS,CAAC9L,YAAY;QACpC8d,iBAAiB,EAAEhS,SAAS,CAACgS,iBAAiB;QAC9CC,gBAAgB,EAAEjS,SAAS,CAACiS,gBAAgB;QAC5CC,gBAAgB,EAAElS,SAAS,CAACkS,gBAAgB;QAC5CC,gBAAgB,EAAEnS,SAAS,CAACmS,gBAAgB;QAC5CC,iBAAiB,EAAEpS,SAAS,CAACoS;;;;iBAGtBlY,IAAA,CAAAsX,MAAM,CAACa,cAAc;UAAG,OAAK;gBACzC,YAAA;MAAA,OAAkC,CAAlCd,UAAkC,CAAArX,IAAA,CAAAsX,MAAA,EAAA,gBAAA,CAAA;;;iBAEtBtX,IAAA,CAAAsX,MAAM,CAACc,YAAY;UAAG,KAAG;gBACrC,YAAA;MAAA,OAAgC,CAAhCf,UAAgC,CAAArX,IAAA,CAAAsX,MAAA,EAAA,cAAA,CAAA;;;iBAEpBtX,IAAA,CAAAsX,MAAM,CAACe,0BAA0B;UAAG,mBAAiB;AACjET,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADmEC,SAAS,EAAA;MAAA,OAAA,CAC5EuR,UAAuE,CAAArX,IAAA,CAAAsX,MAAA,EAAA,4BAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAEnD9F,IAAA,CAAAsX,MAAM,CAACgB,yBAAyB;UAAG,kBAAgB;AAC/DV,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADiEC,SAAS,EAAA;MAAA,OAAA,CAC1EuR,UAAsE,CAAArX,IAAA,CAAAsX,MAAA,EAAA,2BAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAElD9F,IAAA,CAAAsX,MAAM,CAACiB,yBAAyB;UAAG,kBAAgB;AAC/DX,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADiEC,SAAS,EAAA;MAAA,OAAA,CAC1EuR,UAAsE,CAAArX,IAAA,CAAAsX,MAAA,EAAA,2BAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAElD9F,IAAA,CAAAsX,MAAM,CAACkB,yBAAyB;UAAG,kBAAgB;AAC/DZ,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADiEC,SAAS,EAAA;MAAA,OAAA,CAC1EuR,UAAsE,CAAArX,IAAA,CAAAsX,MAAA,EAAA,2BAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAElD9F,IAAA,CAAAsX,MAAM,CAACmB,+BAA+B;UAAG,wBAAsB;AAC3Eb,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UAD6EC,SAAS,EAAA;MAAA,OAAA,CACtFuR,UAA4E,CAAArX,IAAA,CAAAsX,MAAA,EAAA,iCAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAExD9F,IAAA,CAAAsX,MAAM,CAACoB,gCAAgC;UAAG,yBAAuB;AAC7Ed,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UAD+EC,SAAS,EAAA;MAAA,OAAA,CACxFuR,UAA6E,CAAArX,IAAA,CAAAsX,MAAA,EAAA,kCAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;qNAG7ElD,kBAAA,CAuEK,OAvELpD,UAuEK,CAAA;AAvEC,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,gBAAA,CAAA;IAAqB/C,KAAK,EAAA,CAAG6D,IAAE,CAAA2Y,EAAA,CAAA,gBAAA,CAAA,EAAA;MAAAC,SAAA,EAAiC5Y,IAAa,CAAAlE;KAAA;KAAYkE,IAAG,CAAAhC,GAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,CACvG4E,kBAAA,CAqEO,SArEPpD,UAqEO,CAAA;AArEA4L,IAAAA,GAAG,EAAC,OAAM;AAAE1L,IAAAA,IAAI,EAAC,OAAM;IAAG,OAAK,EAAA,CAAGM,IAAE,CAAAd,EAAA,CAAA,OAAA,CAAA,EAAWc,IAAU,CAAA/D,UAAA,CAAA;IAAIE,KAAK,EAAE6D,IAAU,CAAAhE;GAAe,EAAA2D,aAAA,CAAAA,aAAA,CAAA,EAAA,EAAAK,IAAA,CAAA9D,UAAU,CAAK8D,EAAAA,IAAG,CAAAhC,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,EAAA,CAClH4E,kBAAA,CA0BO,SA1BPpD,UA0BO,CAAA;AA1BC,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,OAAA,CAAA;AAAY/C,IAAAA,KAAK,EAAE6D,IAAE,CAAA2Y,EAAA,CAAA,OAAA,CAAA;AAAWjZ,IAAAA,IAAI,EAAC;KAAmBM,IAAG,CAAAhC,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CACxE4E,kBAAA,CAiBI,MAjBJpD,UAiBI,CAAA;AAjBAE,IAAAA,IAAI,EAAC;KAAcM,IAAG,CAAAhC,GAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EACtBsB,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAeUuF,QAfmB,EAAA,IAAA,EAAAyG,UAAA,CAAA9L,QAAA,CAAA2G,OAAO,EAAlB,UAAAI,GAAG,EAAEnF,CAAC,EAAA;;AAAoBhE,MAAAA,GAAA,EAAAoC,QAAA,CAAA3C,UAAU,CAAC0J,GAAG,EAAA,WAAA,CAAA,IAAkB/G,mBAAU,CAAC+G,GAAG,cAAcnF;QAEzF,CAAA5B,QAAA,CAAA3C,UAAU,CAAC0J,GAAG,EAAA,QAAA,CAAA,iBADzB1G,WAae,CAAA+Y,uBAAA,EAAA;;AAXVpc,MAAAA,MAAM,EAAE+J,GAAG;MACX/K,gBAAgB,EAAEuE,IAAgB,CAAAvE,gBAAA;MAClCT,SAAS,EAAE4C,KAAW,CAAA6O,WAAA;MACtBxR,SAAS,EAAE2C,KAAW,CAAA8O,WAAA;MACtBvR,aAAa,EAAEyC,KAAe,CAAA+O,eAAA;MAC9BvR,QAAQ,EAAE4E,IAAQ,CAAA5E,QAAA;AAClB0d,MAAAA,aAAY,EAAApN,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,QAAA,OAAElM,QAAmB,CAAA+O,mBAAA,CAAC7C,MAAM,CAAA;AAAA,OAAA,CAAA;AACxCoN,MAAAA,mBAAkB,EAAArN,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,QAAA,OAAElM,QAAmB,CAAAqS,mBAAA,CAACnG,MAAM,CAAA;AAAA,OAAA,CAAA;AAC9CjP,MAAAA,KAAK,EAAE2E,CAAC;MACRsE,QAAQ,EAAE3F,IAAQ,CAAA2F,QAAA;MAClBvH,EAAE,EAAE4B,IAAE,CAAA5B;;mBAITqB,QAAe,CAAAwV,eAAA,EAAA,IAAzB3V,SAAA,EAAA,EAAAC,kBAAA,CAMI;;KANiCS,IAAG,CAAAhC,GAAA,CAAA,WAAA,CAAA,CAAA,CAAA,EAAA,EACpCsB,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAIUuF,QAJmB,EAAA,IAAA,EAAAyG,UAAA,CAAA9L,QAAA,CAAA2G,OAAO,EAAlB,UAAAI,GAAG,EAAEnF,CAAC,EAAA;;AAAoBhE,MAAAA,GAAA,EAAAoC,QAAA,CAAA3C,UAAU,CAAC0J,GAAG,EAAA,WAAA,CAAA,IAAkB/G,mBAAU,CAAC+G,GAAG,cAAcnF;QACzF,CAAA5B,QAAA,CAAA3C,UAAU,CAAC0J,GAAG,EAAA,QAAA,CAAA,IAAzBlH,SAAA,EAAA,EAAAC,kBAAA,CAEI,MAFJC,UAEI,CAAA;;AAFmC,MAAA,OAAA,EAAOC,QAA0B,CAAA8O,0BAAA,CAAC/H,GAAG,CAAA;AAAIrK,MAAAA,KAAK,GAAGsD,QAAU,CAAA3C,UAAA,CAAC0J,GAAG,EAAY,OAAA,CAAA,EAAA/G,QAAA,CAAA3C,UAAU,CAAC0J,GAAG,EAAA,mBAAA,CAAA;;;OAAiCxG,IAAG,CAAAhC,GAAA,CAAA,YAAA,EAAeyB,QAAmB,CAAA8N,mBAAA,CAAC/G,GAAG,CAAA,CAAA,CAAA,EAAA,CACrLA,GAAG,CAAC5G,YAAY4G,GAAG,CAAC5G,QAAQ,CAACkJ,MAAM,IAApDxJ,SAAA,EAAA,EAAAQ,WAAA,CAA0GC,uBAA/C,CAAAyG,GAAG,CAAC5G,QAAQ,CAACkJ,MAAM,CAAA,EAAA;;AAAGrM,MAAAA,MAAM,EAAE+J,GAAG;AAAG9J,MAAAA,KAAK,EAAE2E;;yDAKtHuB,kBAAA,CAiCO,SAjCPpD,UAiCO,CAAA;AAjCC,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,OAAA,CAAA;AAAWQ,IAAAA,IAAI,EAAC;KAAmBM,IAAG,CAAAhC,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,EAClCyB,QAAK,CAAA0W,KAAA,IAClB7W,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAwBQuF,QAvBoB,EAAA;AAAAzH,IAAAA,GAAA,EAAA;GAAA,EAAAkO,UAAA,CAAA9L,QAAA,CAAAwW,YAAY,EAA5B,UAAAjT,IAAI,EAAEtG,KAAK,EAAA;wBADvBoD,WAwBQ,CAAAkZ,gBAAA,EAAA;AAtBH3b,MAAAA,GAAG,EAAEoC,QAAO,CAAAsH,OAAA,CAAC/D,IAAI,CAAA;MACjB9J,OAAO,EAAE8G,IAAO,CAAA9G,OAAA;MAChBkN,OAAO,EAAE3G,QAAO,CAAA2G,OAAA;AAChBpD,MAAAA,IAAI,EAAEA,IAAI;AACVC,MAAAA,KAAK,EAAE,CAAC;MACR5J,YAAY,EAAEuE,KAAc,CAAA0O,cAAA;MAC5B3Q,WAAW,EAAEqE,IAAW,CAAArE,WAAA;MACxBpC,aAAa,EAAEyG,IAAa,CAAAzG,aAAA;MAC5BD,aAAa,EAAE0G,IAAa,CAAA1G,aAAA;AAC5B+M,MAAAA,WAAW,EAAE5G,QAAY,CAAAwW,YAAA,CAAC3U,MAAM;MAChCgF,YAAY,EAAE5J,KAAI,GAAA,CAAA;MAClBgG,QAAQ,EAAEjD,QAAA,CAAAqW,WAAW,CAAC9S,IAAI,EAAEtG,KAAK,CAAA;MACjC7B,WAAW,EAAEmF,IAAW,CAAAnF,WAAA;MACxBnB,WAAW,EAAEsG,IAAW,CAAAtG,WAAA;MACxBC,oBAAoB,EAAEqG,IAAoB,CAAArG,oBAAA;MAC1C2J,SAAS,EAAEtD,IAAM,CAAAsX,MAAA;MACjB7L,YAAW,EAAEhM,QAAY,CAAAgM,YAAA;MACzBO,WAAU,EAAEvM,QAAW,CAAAuM,WAAA;MACvBzB,gBAAe,EAAE9K,QAAgB,CAAA8K,gBAAA;AACjC0B,MAAAA,eAAc,EAAAP,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,QAAA,OAAElM,QAAe,CAAAoH,eAAA,CAAC8E,MAAM,CAAA;AAAA,OAAA,CAAA;MACtChG,QAAQ,EAAE3F,IAAQ,CAAA2F,QAAA;MAClBvH,EAAE,EAAE4B,IAAE,CAAA5B;;eAGfkB,SAAA,EAAA,EAAAC,kBAAA,CAII,MAJJC,UAII,CAAA;;AAJQ,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,cAAA;KAA0Bc,IAAG,CAAAhC,GAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAC9C4E,kBAAA,CAEI,MAFJpD,UAEI,CAAA;AAFCyZ,IAAAA,OAAO,EAAExZ,QAAO,CAAA2G,OAAA,CAAC9E;KAAgBtB,IAAG,CAAAhC,GAAA,CAAA,kBAAA,CAAA,CAAA,EAAA,CACrCqZ,UAAyB,CAAArX,IAAA,CAAAsX,MAAA,EAAA,OAAA,CAAA,iCAIxB7X,QAAS,CAAA2W,SAAA,IAAtB9W,SAAA,EAAA,EAAAC,kBAAA,CAMO,SANPC,UAMO,CAAA;;AANkB,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,OAAA,CAAA;AAAY/C,IAAAA,KAAK,EAAE6D,IAAE,CAAA2Y,EAAA,CAAA,OAAA,CAAA;AAAWjZ,IAAAA,IAAI,EAAC;KAAmBM,IAAG,CAAAhC,GAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CACzF4E,kBAAA,CAII,MAJJpD,UAII,CAAA;AAJAE,IAAAA,IAAI,EAAC;KAAcM,IAAG,CAAAhC,GAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EACtBsB,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAEUuF,QAFmB,EAAA,IAAA,EAAAyG,UAAA,CAAA9L,QAAA,CAAA2G,OAAO,EAAlB,UAAAI,GAAG,EAAEnF,CAAC,EAAA;;AAAoBhE,MAAAA,GAAA,EAAAoC,QAAA,CAAA3C,UAAU,CAAC0J,GAAG,EAAA,WAAA,CAAA,IAAkB/G,mBAAU,CAAC+G,GAAG,cAAcnF;QAC/E,CAAA5B,QAAA,CAAA3C,UAAU,CAAC0J,GAAG,EAAA,QAAA,CAAA,iBAAnC1G,WAAqH,CAAAoZ,uBAAA,EAAA;;AAApEzc,MAAAA,MAAM,EAAE+J,GAAG;AAAG9J,MAAAA,KAAK,EAAE2E,CAAC;MAAGsE,QAAQ,EAAE3F,IAAQ,CAAA2F,QAAA;MAAGvH,EAAE,EAAE4B,IAAE,CAAA5B;;qEAO/GqB,QAAe,CAAA+W,eAAA,iBADzB1W,WAuDa,CAAAyX,sBAAA,EAAA;;IArDR1d,IAAI,EAAE+D,KAAM,CAAA4O,MAAA;IACZzS,KAAK,EAAE6D,KAAO,CAAA2O,OAAA;IACdvS,YAAY,EAAEyF,QAAkB,CAAAkX,kBAAA;IAChCtc,YAAY,EAAE2F,IAAY,CAAA3F,YAAA;IAC1Bmd,QAAQ,EAAExX,IAAiB,CAAA5F,iBAAA;IAC3BE,kBAAkB,EAAE0F,IAAkB,CAAA1F,kBAAA;IACtCE,yBAAyB,EAAEwF,IAAyB,CAAAxF,yBAAA;AACpD,IAAA,OAAA,iBAAOwF,IAAE,CAAAd,EAAA,CAAA,aAAA,EAAA;AAAAuY,MAAAA,QAAA,EAAA;AAAA,KAAA,CAAA,CAAA;AACTxJ,IAAAA,MAAI,EAAAvC,MAAA,CAAA,CAAA,CAAA,KAAAA,MAAA,CAAA,CAAA,CAAA,GAAA,UAAAC,MAAA,EAAA;AAAA,MAAA,OAAElM,QAAM,CAAAwO,MAAA,CAACtC,MAAM,CAAA;AAAA,KAAA,CAAA;IACnB+L,UAAU,EAAE1X,IAAmB,CAAA7F,mBAAA;IAC/BwL,QAAQ,EAAE3F,IAAQ,CAAA2F,QAAA;AAClBvH,IAAAA,EAAE,EAAE4B,IAAG,CAAAhC,GAAA,CAAA,aAAA;;;MAEQgC,IAAA,CAAAsX,MAAM,CAACK,kBAAkB;UAAG,WAAS;AACjDC,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADmDC,SAAS,EAAA;MAAA,OAAA,CAC5DuR,UAaO,CAAArX,IAAA,CAAAsX,MAAA,EAAA,oBAAA,EAAA;QAXFvd,KAAK,EAAE+L,SAAS,CAAC/L,KAAK;QACtB8d,IAAI,EAAE/R,SAAS,CAAC+R,IAAI;QACpBhe,IAAI,EAAEiM,SAAS,CAACjM,IAAI;QACpBwU,IAAI,EAAEvI,SAAS,CAACuI,IAAI;QACpBD,SAAS,EAAEtI,SAAS,CAACsI,SAAS;QAC9BpU,YAAY,EAAE8L,SAAS,CAAC9L,YAAY;QACpC8d,iBAAiB,EAAEhS,SAAS,CAACgS,iBAAiB;QAC9CC,gBAAgB,EAAEjS,SAAS,CAACiS,gBAAgB;QAC5CC,gBAAgB,EAAElS,SAAS,CAACkS,gBAAgB;QAC5CC,gBAAgB,EAAEnS,SAAS,CAACmS,gBAAgB;QAC5CC,iBAAiB,EAAEpS,SAAS,CAACoS;;;;iBAGtBlY,IAAA,CAAAsX,MAAM,CAACa,cAAc;UAAG,OAAK;gBACzC,YAAA;MAAA,OAAkC,CAAlCd,UAAkC,CAAArX,IAAA,CAAAsX,MAAA,EAAA,gBAAA,CAAA;;;iBAEtBtX,IAAA,CAAAsX,MAAM,CAACc,YAAY;UAAG,KAAG;gBACrC,YAAA;MAAA,OAAgC,CAAhCf,UAAgC,CAAArX,IAAA,CAAAsX,MAAA,EAAA,cAAA,CAAA;;;iBAEpBtX,IAAA,CAAAsX,MAAM,CAACe,0BAA0B;UAAG,mBAAiB;AACjET,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADmEC,SAAS,EAAA;MAAA,OAAA,CAC5EuR,UAAuE,CAAArX,IAAA,CAAAsX,MAAA,EAAA,4BAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAEnD9F,IAAA,CAAAsX,MAAM,CAACgB,yBAAyB;UAAG,kBAAgB;AAC/DV,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADiEC,SAAS,EAAA;MAAA,OAAA,CAC1EuR,UAAsE,CAAArX,IAAA,CAAAsX,MAAA,EAAA,2BAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAElD9F,IAAA,CAAAsX,MAAM,CAACiB,yBAAyB;UAAG,kBAAgB;AAC/DX,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADiEC,SAAS,EAAA;MAAA,OAAA,CAC1EuR,UAAsE,CAAArX,IAAA,CAAAsX,MAAA,EAAA,2BAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAElD9F,IAAA,CAAAsX,MAAM,CAACkB,yBAAyB;UAAG,kBAAgB;AAC/DZ,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UADiEC,SAAS,EAAA;MAAA,OAAA,CAC1EuR,UAAsE,CAAArX,IAAA,CAAAsX,MAAA,EAAA,2BAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAElD9F,IAAA,CAAAsX,MAAM,CAACmB,+BAA+B;UAAG,wBAAsB;AAC3Eb,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UAD6EC,SAAS,EAAA;MAAA,OAAA,CACtFuR,UAA4E,CAAArX,IAAA,CAAAsX,MAAA,EAAA,iCAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;iBAExD9F,IAAA,CAAAsX,MAAM,CAACoB,gCAAgC;UAAG,yBAAuB;AAC7Ed,IAAAA,EAAA,EAAA/R,OAAA,CAAA,UAD+EC,SAAS,EAAA;MAAA,OAAA,CACxFuR,UAA6E,CAAArX,IAAA,CAAAsX,MAAA,EAAA,kCAAA,EAAA;QAA9B,OAAKvR,EAAAA,cAAA,CAAED,SAAS,CAAM,OAAA,CAAA;;;;qNAGlE9F,IAAA,CAAAsX,MAAM,CAACzX,MAAM,IAAxBP,SAAA,EAAA,EAAAC,kBAAA,CAEK,OAFLC,UAEK,CAAA;;AAFsB,IAAA,OAAA,EAAOQ,IAAE,CAAAd,EAAA,CAAA,QAAA;KAAoBc,IAAG,CAAAhC,GAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CACvDqZ,UAA0B,CAAArX,IAAA,CAAAsX,MAAA,EAAA,QAAA,CAAA,wCAE9B1U,kBAAA,CAA8H,OAA9HpD,UAA8H,CAAA;AAAzH4L,IAAAA,GAAG,EAAC,cAAe;AAAC,IAAA,OAAA,EAAOpL,IAAE,CAAAd,EAAA,CAAA,uBAAA,CAAA;AAA2B/C,IAAAA,KAAsB,EAAtB;AAAsB,MAAA,SAAA,EAAA;AAAA;KAAQ6D,IAAG,CAAAhC,GAAA,CAAA,uBAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA;;;;;;;"}