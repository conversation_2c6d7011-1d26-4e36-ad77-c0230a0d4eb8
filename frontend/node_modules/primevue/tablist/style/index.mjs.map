{"version": 3, "file": "index.mjs", "sources": ["../../../src/tablist/style/TabListStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-tablist',\n    content: ({ instance }) => [\n        'p-tablist-content',\n        {\n            'p-tablist-viewport': instance.$pcTabs.scrollable\n        }\n    ],\n    tabList: 'p-tablist-tab-list',\n    activeBar: 'p-tablist-active-bar',\n    prevButton: 'p-tablist-prev-button p-tablist-nav-button',\n    nextButton: 'p-tablist-next-button p-tablist-nav-button'\n};\n\nexport default BaseStyle.extend({\n    name: 'tablist',\n    classes\n});\n"], "names": ["classes", "root", "content", "_ref", "instance", "$pcTabs", "scrollable", "tabList", "activeBar", "prevButton", "nextButton", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,WAAW;AACjBC,EAAAA,OAAO,EAAE,SAATA,OAAOA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAA,OAAO,CACvB,mBAAmB,EACnB;AACI,MAAA,oBAAoB,EAAEA,QAAQ,CAACC,OAAO,CAACC;AAC3C,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,OAAO,EAAE,oBAAoB;AAC7BC,EAAAA,SAAS,EAAE,sBAAsB;AACjCC,EAAAA,UAAU,EAAE,4CAA4C;AACxDC,EAAAA,UAAU,EAAE;AAChB,CAAC;AAED,mBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,SAAS;AACfb,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}