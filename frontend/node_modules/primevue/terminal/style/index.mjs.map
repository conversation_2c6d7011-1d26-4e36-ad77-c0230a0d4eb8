{"version": 3, "file": "index.mjs", "sources": ["../../../src/terminal/style/TerminalStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/terminal';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-terminal p-component',\n    welcomeMessage: 'p-terminal-welcome-message',\n    commandList: 'p-terminal-command-list',\n    command: 'p-terminal-command',\n    commandValue: 'p-terminal-command-value',\n    commandResponse: 'p-terminal-command-response',\n    prompt: 'p-terminal-prompt',\n    promptLabel: 'p-terminal-prompt-label',\n    promptValue: 'p-terminal-prompt-value'\n};\n\nexport default BaseStyle.extend({\n    name: 'terminal',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "welcomeMessage", "commandList", "command", "commandValue", "commandResponse", "prompt", "prompt<PERSON><PERSON><PERSON>", "promptValue", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,wBAAwB;AAC9BC,EAAAA,cAAc,EAAE,4BAA4B;AAC5CC,EAAAA,WAAW,EAAE,yBAAyB;AACtCC,EAAAA,OAAO,EAAE,oBAAoB;AAC7BC,EAAAA,YAAY,EAAE,0BAA0B;AACxCC,EAAAA,eAAe,EAAE,6BAA6B;AAC9CC,EAAAA,MAAM,EAAE,mBAAmB;AAC3BC,EAAAA,WAAW,EAAE,yBAAyB;AACtCC,EAAAA,WAAW,EAAE;AACjB,CAAC;AAED,oBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,UAAU;AAChBC,EAAAA,KAAK,EAALA,KAAK;AACLb,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}