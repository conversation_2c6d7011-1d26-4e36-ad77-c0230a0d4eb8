{"version": 3, "file": "index.mjs", "sources": ["../../src/terminal/BaseTerminal.vue", "../../src/terminal/Terminal.vue", "../../src/terminal/Terminal.vue?vue&type=template&id=df319ce0&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TerminalStyle from 'primevue/terminal/style';\n\nexport default {\n    name: 'BaseTerminal',\n    extends: BaseComponent,\n    props: {\n        welcomeMessage: {\n            type: String,\n            default: null\n        },\n        prompt: {\n            type: String,\n            default: null\n        }\n    },\n    style: TerminalStyle,\n    provide() {\n        return {\n            $pcTerminal: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" @click=\"onClick\" v-bind=\"ptmi('root')\">\n        <div v-if=\"welcomeMessage\" :class=\"cx('welcomeMessage')\" v-bind=\"ptm('welcomeMessage')\">{{ welcomeMessage }}</div>\n        <div :class=\"cx('commandList')\" v-bind=\"ptm('content')\">\n            <div v-for=\"(command, i) of commands\" :key=\"command.text + i.toString()\" :class=\"cx('command')\" v-bind=\"ptm('commands')\">\n                <span :class=\"cx('promptLabel')\" v-bind=\"ptm('prompt')\">{{ prompt }}</span>\n                <span :class=\"cx('commandValue')\" v-bind=\"ptm('command')\">{{ command.text }}</span>\n                <div :class=\"cx('commandResponse')\" aria-live=\"polite\" v-bind=\"ptm('response')\">{{ command.response }}</div>\n            </div>\n        </div>\n        <div :class=\"cx('prompt')\" v-bind=\"ptm('container')\">\n            <span :class=\"cx('promptLabel')\" v-bind=\"ptm('prompt')\">{{ prompt }}</span>\n            <input ref=\"input\" v-model=\"commandText\" :class=\"cx('promptValue')\" type=\"text\" autocomplete=\"off\" @keydown=\"onKeydown\" v-bind=\"ptm('commandText')\" />\n        </div>\n    </div>\n</template>\n\n<script>\nimport TerminalService from 'primevue/terminalservice';\nimport BaseTerminal from './BaseTerminal.vue';\n\nexport default {\n    name: 'Terminal',\n    extends: BaseTerminal,\n    inheritAttrs: false,\n    data() {\n        return {\n            commandText: null,\n            commands: []\n        };\n    },\n    mounted() {\n        TerminalService.on('response', this.responseListener);\n        this.$refs.input.focus();\n    },\n    updated() {\n        this.$el.scrollTop = this.$el.scrollHeight;\n    },\n    beforeUnmount() {\n        TerminalService.off('response', this.responseListener);\n    },\n    methods: {\n        onClick() {\n            this.$refs.input.focus();\n        },\n        onKeydown(event) {\n            if (event.key === 'Enter' && this.commandText) {\n                this.commands.push({ text: this.commandText });\n                TerminalService.emit('command', this.commandText);\n                this.commandText = '';\n            }\n        },\n        responseListener(response) {\n            this.commands[this.commands.length - 1].response = response;\n        }\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" @click=\"onClick\" v-bind=\"ptmi('root')\">\n        <div v-if=\"welcomeMessage\" :class=\"cx('welcomeMessage')\" v-bind=\"ptm('welcomeMessage')\">{{ welcomeMessage }}</div>\n        <div :class=\"cx('commandList')\" v-bind=\"ptm('content')\">\n            <div v-for=\"(command, i) of commands\" :key=\"command.text + i.toString()\" :class=\"cx('command')\" v-bind=\"ptm('commands')\">\n                <span :class=\"cx('promptLabel')\" v-bind=\"ptm('prompt')\">{{ prompt }}</span>\n                <span :class=\"cx('commandValue')\" v-bind=\"ptm('command')\">{{ command.text }}</span>\n                <div :class=\"cx('commandResponse')\" aria-live=\"polite\" v-bind=\"ptm('response')\">{{ command.response }}</div>\n            </div>\n        </div>\n        <div :class=\"cx('prompt')\" v-bind=\"ptm('container')\">\n            <span :class=\"cx('promptLabel')\" v-bind=\"ptm('prompt')\">{{ prompt }}</span>\n            <input ref=\"input\" v-model=\"commandText\" :class=\"cx('promptValue')\" type=\"text\" autocomplete=\"off\" @keydown=\"onKeydown\" v-bind=\"ptm('commandText')\" />\n        </div>\n    </div>\n</template>\n\n<script>\nimport TerminalService from 'primevue/terminalservice';\nimport BaseTerminal from './BaseTerminal.vue';\n\nexport default {\n    name: 'Terminal',\n    extends: BaseTerminal,\n    inheritAttrs: false,\n    data() {\n        return {\n            commandText: null,\n            commands: []\n        };\n    },\n    mounted() {\n        TerminalService.on('response', this.responseListener);\n        this.$refs.input.focus();\n    },\n    updated() {\n        this.$el.scrollTop = this.$el.scrollHeight;\n    },\n    beforeUnmount() {\n        TerminalService.off('response', this.responseListener);\n    },\n    methods: {\n        onClick() {\n            this.$refs.input.focus();\n        },\n        onKeydown(event) {\n            if (event.key === 'Enter' && this.commandText) {\n                this.commands.push({ text: this.commandText });\n                TerminalService.emit('command', this.commandText);\n                this.commandText = '';\n            }\n        },\n        responseListener(response) {\n            this.commands[this.commands.length - 1].response = response;\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "welcomeMessage", "type", "String", "prompt", "style", "TerminalStyle", "provide", "$pcTerminal", "$parentInstance", "BaseTerminal", "inheritAttrs", "data", "commandText", "commands", "mounted", "TerminalService", "on", "responseListener", "$refs", "input", "focus", "updated", "$el", "scrollTop", "scrollHeight", "beforeUnmount", "off", "methods", "onClick", "onKeydown", "event", "key", "push", "text", "emit", "response", "length", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "$options", "apply", "arguments", "ptmi", "ptm", "_createElementVNode", "_Fragment", "_renderList", "$data", "command", "i", "toString", "ref_for", "_toDisplayString", "_withDirectives", "ref", "$event", "autocomplete"], "mappings": ";;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,cAAc;AACpB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,cAAc,EAAE;AACZC,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,MAAM,EAAE;AACJF,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDE,EAAAA,KAAK,EAAEC,aAAa;EACpBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,WAAW,EAAE,IAAI;AACjBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACHD,aAAe;AACXX,EAAAA,IAAI,EAAE,UAAU;AAChB,EAAA,SAAA,EAASY,QAAY;AACrBC,EAAAA,YAAY,EAAE,KAAK;EACnBC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;AACHC,MAAAA,WAAW,EAAE,IAAI;AACjBC,MAAAA,QAAQ,EAAE;KACb;GACJ;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACNC,eAAe,CAACC,EAAE,CAAC,UAAU,EAAE,IAAI,CAACC,gBAAgB,CAAC;AACrD,IAAA,IAAI,CAACC,KAAK,CAACC,KAAK,CAACC,KAAK,EAAE;GAC3B;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACC,GAAG,CAACC,YAAY,IAAI,CAACD,GAAG,CAACE,YAAY;GAC7C;EACDC,aAAa,EAAA,SAAbA,aAAaA,GAAG;IACZV,eAAe,CAACW,GAAG,CAAC,UAAU,EAAE,IAAI,CAACT,gBAAgB,CAAC;GACzD;AACDU,EAAAA,OAAO,EAAE;IACLC,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,MAAA,IAAI,CAACV,KAAK,CAACC,KAAK,CAACC,KAAK,EAAE;KAC3B;AACDS,IAAAA,SAAS,EAATA,SAAAA,SAASA,CAACC,KAAK,EAAE;MACb,IAAIA,KAAK,CAACC,QAAQ,OAAM,IAAK,IAAI,CAACnB,WAAW,EAAE;AAC3C,QAAA,IAAI,CAACC,QAAQ,CAACmB,IAAI,CAAC;UAAEC,IAAI,EAAE,IAAI,CAACrB;AAAY,SAAC,CAAC;QAC9CG,eAAe,CAACmB,IAAI,CAAC,SAAS,EAAE,IAAI,CAACtB,WAAW,CAAC;QACjD,IAAI,CAACA,WAAY,GAAE,EAAE;AACzB;KACH;AACDK,IAAAA,gBAAgB,EAAhBA,SAAAA,gBAAgBA,CAACkB,QAAQ,EAAE;AACvB,MAAA,IAAI,CAACtB,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACuB,MAAK,GAAI,CAAC,CAAC,CAACD,QAAO,GAAIA,QAAQ;AAC/D;AACJ;AACJ,CAAC;;;ECvDG,OAAAE,SAAA,EAAA,EAAAC,kBAAA,CAaK,OAbLC,UAaK,CAAA;AAbC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;IAAWb,OAAK;aAAEc,QAAO,CAAAd,OAAA,IAAAc,QAAA,CAAAd,OAAA,CAAAe,KAAA,CAAAD,QAAA,EAAAE,SAAA,CAAA;KAAA;KAAUJ,IAAI,CAAAK,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACvCL,IAAc,CAAAxC,cAAA,IAAzBqC,SAAA,EAAA,EAAAC,kBAAA,CAAiH,OAAjHC,UAAiH,CAAA;;AAArF,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,gBAAA;AAA4B,GAAA,EAAAD,IAAA,CAAAM,GAAG,qCAAuBN,IAAe,CAAAxC,cAAA,CAAA,EAAA,EAAA,CAAA,kCAC1G+C,kBAAA,CAMK,OANLR,UAMK,CAAA;AANC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,aAAA;KAAyBD,IAAG,CAAAM,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EACvCT,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAIKU,QAJuB,EAAA,IAAA,EAAAC,UAAA,CAAAC,KAAA,CAAArC,QAAQ,EAAvB,UAAAsC,OAAO,EAAEC,CAAC,EAAA;IAAvB,OAAAf,SAAA,EAAA,EAAAC,kBAAA,CAIK,OAJLC,UAIK,CAAA;MAJkCR,GAAG,EAAEoB,OAAO,CAAClB,IAAG,GAAImB,CAAC,CAACC,QAAQ,EAAA;AAAK,MAAA,OAAA,EAAOb,IAAE,CAAAC,EAAA,CAAA,SAAA;;;OAAqBD,IAAG,CAAAM,GAAA,CAAA,UAAA,CAAA,CAAA,EAAA,CACvGC,kBAAA,CAA0E,QAA1ER,UAA0E,CAAA;AAAnE,MAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,aAAA;AAAyB,KAAA,EAAA;AAAAa,MAAAA,OAAA,EAAA;KAAA,EAAAd,IAAA,CAAAM,GAAG,6BAAeN,IAAK,CAAArC,MAAA,CAAA,EAAA,EAAA,CAAA,EAChE4C,kBAAA,CAAkF,QAAlFR,UAAkF,CAAA;AAA3E,MAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,cAAA;;;OAA0BD,IAAG,CAAAM,GAAA,CAAA,SAAA,CAAA,CAAA,EAAAS,eAAA,CAAgBJ,OAAO,CAAClB,IAAK,CAAA,EAAA,EAAA,CAAA,EAC1Ec,kBAAA,CAA2G,OAA3GR,UAA2G,CAAA;AAArG,MAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,iBAAA,CAAA;AAAqB,MAAA,WAAS,EAAC;;;OAAiBD,IAAG,CAAAM,GAAA,CAAA,UAAA,CAAA,CAAA,EAAAS,eAAA,CAAiBJ,OAAO,CAAChB,QAAS,CAAA,EAAA,EAAA,CAAA;mBAG5GY,kBAAA,CAGK,OAHLR,UAGK,CAAA;AAHC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,QAAA;KAAoBD,IAAG,CAAAM,GAAA,CAAA,WAAA,CAAA,CAAA,EAAA,CAClCC,kBAAA,CAA0E,QAA1ER,UAA0E,CAAA;AAAnE,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,aAAA;GAAyB,EAAAD,IAAA,CAAAM,GAAG,6BAAeN,IAAK,CAAArC,MAAA,CAAA,EAAA,EAAA,CAAA,EAChEqD,cAAA,CAAAT,kBAAA,CAAqJ,SAArJR,UAAqJ,CAAA;AAA9IkB,IAAAA,GAAG,EAAC,OAAQ;;aAASP,KAAW,CAAAtC,WAAA,GAAA8C,MAAA;AAAA,KAAA,CAAA;AAAG,IAAA,OAAA,EAAOlB,IAAE,CAAAC,EAAA,CAAA,aAAA,CAAA;AAAiBxC,IAAAA,IAAI,EAAC,MAAO;AAAA0D,IAAAA,YAAY,EAAC,KAAM;IAAC9B,SAAO;aAAEa,QAAS,CAAAb,SAAA,IAAAa,QAAA,CAAAb,SAAA,CAAAc,KAAA,CAAAD,QAAA,EAAAE,SAAA,CAAA;KAAA;KAAUJ,IAAG,CAAAM,GAAA,CAAA,aAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,EAAA,cAAvGI,KAAW,CAAAtC,WAAA,CAAA;;;;;;;"}