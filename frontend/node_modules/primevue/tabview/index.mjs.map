{"version": 3, "file": "index.mjs", "sources": ["../../src/tabview/BaseTabView.vue", "../../src/tabview/TabView.vue", "../../src/tabview/TabView.vue?vue&type=template&id=15a9c0e0&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport TabViewStyle from 'primevue/tabview/style';\n\nexport default {\n    name: 'BaseTabView',\n    extends: BaseComponent,\n    props: {\n        activeIndex: {\n            type: Number,\n            default: 0\n        },\n        lazy: {\n            type: Boolean,\n            default: false\n        },\n        scrollable: {\n            type: Boolean,\n            default: false\n        },\n        tabindex: {\n            type: Number,\n            default: 0\n        },\n        selectOnFocus: {\n            type: Boolean,\n            default: false\n        },\n        prevButtonProps: {\n            type: null,\n            default: null\n        },\n        nextButtonProps: {\n            type: null,\n            default: null\n        },\n        prevIcon: {\n            type: String,\n            default: undefined\n        },\n        nextIcon: {\n            type: String,\n            default: undefined\n        }\n    },\n    style: TabViewStyle,\n    provide() {\n        return {\n            $pcTabs: undefined, // Backwards compatible to prevent <TabPanel> component from breaking\n            $pcTabView: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" role=\"tablist\" v-bind=\"ptmi('root')\">\n        <div :class=\"cx('navContainer')\" v-bind=\"ptm('navContainer')\">\n            <button\n                v-if=\"scrollable && !isPrevButtonDisabled\"\n                ref=\"prevBtn\"\n                v-ripple\n                type=\"button\"\n                :class=\"cx('prevButton')\"\n                :tabindex=\"tabindex\"\n                :aria-label=\"prevButtonAriaLabel\"\n                @click=\"onPrevButtonClick\"\n                v-bind=\"{ ...prevButtonProps, ...ptm('prevButton') }\"\n                data-pc-group-section=\"navbutton\"\n            >\n                <slot name=\"previcon\">\n                    <component :is=\"prevIcon ? 'span' : 'ChevronLeftIcon'\" aria-hidden=\"true\" :class=\"prevIcon\" v-bind=\"ptm('prevIcon')\" />\n                </slot>\n            </button>\n            <div ref=\"content\" :class=\"cx('navContent')\" @scroll=\"onScroll\" v-bind=\"ptm('navContent')\">\n                <ul ref=\"nav\" :class=\"cx('nav')\" v-bind=\"ptm('nav')\">\n                    <li\n                        v-for=\"(tab, index) of tabs\"\n                        :key=\"getKey(tab, index)\"\n                        :style=\"getTabProp(tab, 'headerStyle')\"\n                        :class=\"cx('tab.header', { tab, index })\"\n                        role=\"presentation\"\n                        v-bind=\"{ ...getTabProp(tab, 'headerProps'), ...getTabPT(tab, 'root', index), ...getTabPT(tab, 'header', index) }\"\n                        data-pc-name=\"tabpanel\"\n                        :data-p-active=\"d_activeIndex === index\"\n                        :data-p-disabled=\"getTabProp(tab, 'disabled')\"\n                        :data-pc-index=\"index\"\n                    >\n                        <a\n                            :id=\"getTabHeaderActionId(index)\"\n                            v-ripple\n                            :class=\"cx('tab.headerAction')\"\n                            :tabindex=\"getTabProp(tab, 'disabled') || !isTabActive(index) ? -1 : tabindex\"\n                            role=\"tab\"\n                            :aria-disabled=\"getTabProp(tab, 'disabled')\"\n                            :aria-selected=\"isTabActive(index)\"\n                            :aria-controls=\"getTabContentId(index)\"\n                            @click=\"onTabClick($event, tab, index)\"\n                            @keydown=\"onTabKeyDown($event, tab, index)\"\n                            v-bind=\"{ ...getTabProp(tab, 'headerActionProps'), ...getTabPT(tab, 'headerAction', index) }\"\n                        >\n                            <span v-if=\"tab.props && tab.props.header\" :class=\"cx('tab.headerTitle')\" v-bind=\"getTabPT(tab, 'headerTitle', index)\">{{ tab.props.header }}</span>\n                            <component v-if=\"tab.children && tab.children.header\" :is=\"tab.children.header\"></component>\n                        </a>\n                    </li>\n                    <li ref=\"inkbar\" :class=\"cx('inkbar')\" role=\"presentation\" aria-hidden=\"true\" v-bind=\"ptm('inkbar')\"></li>\n                </ul>\n            </div>\n            <button\n                v-if=\"scrollable && !isNextButtonDisabled\"\n                ref=\"nextBtn\"\n                v-ripple\n                type=\"button\"\n                :class=\"cx('nextButton')\"\n                :tabindex=\"tabindex\"\n                :aria-label=\"nextButtonAriaLabel\"\n                @click=\"onNextButtonClick\"\n                v-bind=\"{ ...nextButtonProps, ...ptm('nextButton') }\"\n                data-pc-group-section=\"navbutton\"\n            >\n                <slot name=\"nexticon\">\n                    <component :is=\"nextIcon ? 'span' : 'ChevronRightIcon'\" aria-hidden=\"true\" :class=\"nextIcon\" v-bind=\"ptm('nextIcon')\" />\n                </slot>\n            </button>\n        </div>\n        <div :class=\"cx('panelContainer')\" v-bind=\"ptm('panelContainer')\">\n            <template v-for=\"(tab, index) of tabs\" :key=\"getKey(tab, index)\">\n                <div\n                    v-if=\"lazy ? isTabActive(index) : true\"\n                    v-show=\"lazy ? true : isTabActive(index)\"\n                    :id=\"getTabContentId(index)\"\n                    :style=\"getTabProp(tab, 'contentStyle')\"\n                    :class=\"cx('tab.content', { tab })\"\n                    role=\"tabpanel\"\n                    :aria-labelledby=\"getTabHeaderActionId(index)\"\n                    v-bind=\"{ ...getTabProp(tab, 'contentProps'), ...getTabPT(tab, 'root', index), ...getTabPT(tab, 'content', index) }\"\n                    data-pc-name=\"tabpanel\"\n                    :data-pc-index=\"index\"\n                    :data-p-active=\"d_activeIndex === index\"\n                >\n                    <component :is=\"tab\"></component>\n                </div>\n            </template>\n        </div>\n    </div>\n</template>\n\n<script>\nimport { findSingle, focus, getAttribute, getOffset, getWidth } from '@primeuix/utils/dom';\nimport ChevronLeftIcon from '@primevue/icons/chevronleft';\nimport ChevronRightIcon from '@primevue/icons/chevronright';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\nimport BaseTabView from './BaseTabView.vue';\n\nexport default {\n    name: 'TabView',\n    extends: BaseTabView,\n    inheritAttrs: false,\n    emits: ['update:activeIndex', 'tab-change', 'tab-click'],\n    data() {\n        return {\n            d_activeIndex: this.activeIndex,\n            isPrevButtonDisabled: true,\n            isNextButtonDisabled: false\n        };\n    },\n    watch: {\n        activeIndex(newValue) {\n            this.d_activeIndex = newValue;\n\n            this.scrollInView({ index: newValue });\n        }\n    },\n    mounted() {\n        console.warn('Deprecated since v4. Use Tabs component instead.');\n\n        this.updateInkBar();\n        this.scrollable && this.updateButtonState();\n    },\n    updated() {\n        this.updateInkBar();\n        this.scrollable && this.updateButtonState();\n    },\n    methods: {\n        isTabPanel(child) {\n            return child.type.name === 'TabPanel';\n        },\n        isTabActive(index) {\n            return this.d_activeIndex === index;\n        },\n        getTabProp(tab, name) {\n            return tab.props ? tab.props[name] : undefined;\n        },\n        getKey(tab, index) {\n            return this.getTabProp(tab, 'header') || index;\n        },\n        getTabHeaderActionId(index) {\n            return `${this.$id}_${index}_header_action`;\n        },\n        getTabContentId(index) {\n            return `${this.$id}_${index}_content`;\n        },\n        getTabPT(tab, key, index) {\n            const count = this.tabs.length;\n            const tabMetaData = {\n                props: tab.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    index,\n                    count,\n                    first: index === 0,\n                    last: index === count - 1,\n                    active: this.isTabActive(index)\n                }\n            };\n\n            return mergeProps(this.ptm(`tabpanel.${key}`, { tabpanel: tabMetaData }), this.ptm(`tabpanel.${key}`, tabMetaData), this.ptmo(this.getTabProp(tab, 'pt'), key, tabMetaData));\n        },\n        onScroll(event) {\n            this.scrollable && this.updateButtonState();\n\n            event.preventDefault();\n        },\n        onPrevButtonClick() {\n            const content = this.$refs.content;\n            const width = getWidth(content);\n            const pos = content.scrollLeft - width;\n\n            content.scrollLeft = pos <= 0 ? 0 : pos;\n        },\n        onNextButtonClick() {\n            const content = this.$refs.content;\n            const width = getWidth(content) - this.getVisibleButtonWidths();\n            const pos = content.scrollLeft + width;\n            const lastPos = content.scrollWidth - width;\n\n            content.scrollLeft = pos >= lastPos ? lastPos : pos;\n        },\n        onTabClick(event, tab, index) {\n            this.changeActiveIndex(event, tab, index);\n            this.$emit('tab-click', { originalEvent: event, index });\n        },\n        onTabKeyDown(event, tab, index) {\n            switch (event.code) {\n                case 'ArrowLeft':\n                    this.onTabArrowLeftKey(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onTabArrowRightKey(event);\n                    break;\n\n                case 'Home':\n                    this.onTabHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onTabEndKey(event);\n                    break;\n\n                case 'PageDown':\n                    this.onPageDownKey(event);\n                    break;\n\n                case 'PageUp':\n                    this.onPageUpKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                case 'Space':\n                    this.onTabEnterKey(event, tab, index);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onTabArrowRightKey(event) {\n            const nextHeaderAction = this.findNextHeaderAction(event.target.parentElement);\n\n            nextHeaderAction ? this.changeFocusedTab(event, nextHeaderAction) : this.onTabHomeKey(event);\n            event.preventDefault();\n        },\n        onTabArrowLeftKey(event) {\n            const prevHeaderAction = this.findPrevHeaderAction(event.target.parentElement);\n\n            prevHeaderAction ? this.changeFocusedTab(event, prevHeaderAction) : this.onTabEndKey(event);\n            event.preventDefault();\n        },\n        onTabHomeKey(event) {\n            const firstHeaderAction = this.findFirstHeaderAction();\n\n            this.changeFocusedTab(event, firstHeaderAction);\n            event.preventDefault();\n        },\n        onTabEndKey(event) {\n            const lastHeaderAction = this.findLastHeaderAction();\n\n            this.changeFocusedTab(event, lastHeaderAction);\n            event.preventDefault();\n        },\n        onPageDownKey(event) {\n            this.scrollInView({ index: this.$refs.nav.children.length - 2 });\n            event.preventDefault();\n        },\n        onPageUpKey(event) {\n            this.scrollInView({ index: 0 });\n            event.preventDefault();\n        },\n        onTabEnterKey(event, tab, index) {\n            this.changeActiveIndex(event, tab, index);\n\n            event.preventDefault();\n        },\n        findNextHeaderAction(tabElement, selfCheck = false) {\n            const headerElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n\n            return headerElement\n                ? getAttribute(headerElement, 'data-p-disabled') || getAttribute(headerElement, 'data-pc-section') === 'inkbar'\n                    ? this.findNextHeaderAction(headerElement)\n                    : findSingle(headerElement, '[data-pc-section=\"headeraction\"]')\n                : null;\n        },\n        findPrevHeaderAction(tabElement, selfCheck = false) {\n            const headerElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n\n            return headerElement\n                ? getAttribute(headerElement, 'data-p-disabled') || getAttribute(headerElement, 'data-pc-section') === 'inkbar'\n                    ? this.findPrevHeaderAction(headerElement)\n                    : findSingle(headerElement, '[data-pc-section=\"headeraction\"]')\n                : null;\n        },\n        findFirstHeaderAction() {\n            return this.findNextHeaderAction(this.$refs.nav.firstElementChild, true);\n        },\n        findLastHeaderAction() {\n            return this.findPrevHeaderAction(this.$refs.nav.lastElementChild, true);\n        },\n        changeActiveIndex(event, tab, index) {\n            if (!this.getTabProp(tab, 'disabled') && this.d_activeIndex !== index) {\n                this.d_activeIndex = index;\n\n                this.$emit('update:activeIndex', index);\n                this.$emit('tab-change', { originalEvent: event, index });\n\n                this.scrollInView({ index });\n            }\n        },\n        changeFocusedTab(event, element) {\n            if (element) {\n                focus(element);\n                this.scrollInView({ element });\n\n                if (this.selectOnFocus) {\n                    const index = parseInt(element.parentElement.dataset.pcIndex, 10);\n                    const tab = this.tabs[index];\n\n                    this.changeActiveIndex(event, tab, index);\n                }\n            }\n        },\n        scrollInView({ element, index = -1 }) {\n            const currentElement = element || this.$refs.nav.children[index];\n\n            if (currentElement) {\n                currentElement.scrollIntoView && currentElement.scrollIntoView({ block: 'nearest' });\n            }\n        },\n        updateInkBar() {\n            let tabHeader = this.$refs.nav.children[this.d_activeIndex];\n\n            this.$refs.inkbar.style.width = getWidth(tabHeader) + 'px';\n            this.$refs.inkbar.style.left = getOffset(tabHeader).left - getOffset(this.$refs.nav).left + 'px';\n        },\n        updateButtonState() {\n            const content = this.$refs.content;\n            const { scrollLeft, scrollWidth } = content;\n            const width = getWidth(content);\n\n            this.isPrevButtonDisabled = scrollLeft === 0;\n            this.isNextButtonDisabled = parseInt(scrollLeft) === scrollWidth - width;\n        },\n        getVisibleButtonWidths() {\n            const { prevBtn, nextBtn } = this.$refs;\n\n            return [prevBtn, nextBtn].reduce((acc, el) => (el ? acc + getWidth(el) : acc), 0);\n        }\n    },\n    computed: {\n        tabs() {\n            return this.$slots.default().reduce((tabs, child) => {\n                if (this.isTabPanel(child)) {\n                    tabs.push(child);\n                } else if (child.children && child.children instanceof Array) {\n                    child.children.forEach((nestedChild) => {\n                        if (this.isTabPanel(nestedChild)) {\n                            tabs.push(nestedChild);\n                        }\n                    });\n                }\n\n                return tabs;\n            }, []);\n        },\n        prevButtonAriaLabel() {\n            return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.previous : undefined;\n        },\n        nextButtonAriaLabel() {\n            return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.next : undefined;\n        }\n    },\n    directives: {\n        ripple: Ripple\n    },\n    components: {\n        ChevronLeftIcon,\n        ChevronRightIcon\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" role=\"tablist\" v-bind=\"ptmi('root')\">\n        <div :class=\"cx('navContainer')\" v-bind=\"ptm('navContainer')\">\n            <button\n                v-if=\"scrollable && !isPrevButtonDisabled\"\n                ref=\"prevBtn\"\n                v-ripple\n                type=\"button\"\n                :class=\"cx('prevButton')\"\n                :tabindex=\"tabindex\"\n                :aria-label=\"prevButtonAriaLabel\"\n                @click=\"onPrevButtonClick\"\n                v-bind=\"{ ...prevButtonProps, ...ptm('prevButton') }\"\n                data-pc-group-section=\"navbutton\"\n            >\n                <slot name=\"previcon\">\n                    <component :is=\"prevIcon ? 'span' : 'ChevronLeftIcon'\" aria-hidden=\"true\" :class=\"prevIcon\" v-bind=\"ptm('prevIcon')\" />\n                </slot>\n            </button>\n            <div ref=\"content\" :class=\"cx('navContent')\" @scroll=\"onScroll\" v-bind=\"ptm('navContent')\">\n                <ul ref=\"nav\" :class=\"cx('nav')\" v-bind=\"ptm('nav')\">\n                    <li\n                        v-for=\"(tab, index) of tabs\"\n                        :key=\"getKey(tab, index)\"\n                        :style=\"getTabProp(tab, 'headerStyle')\"\n                        :class=\"cx('tab.header', { tab, index })\"\n                        role=\"presentation\"\n                        v-bind=\"{ ...getTabProp(tab, 'headerProps'), ...getTabPT(tab, 'root', index), ...getTabPT(tab, 'header', index) }\"\n                        data-pc-name=\"tabpanel\"\n                        :data-p-active=\"d_activeIndex === index\"\n                        :data-p-disabled=\"getTabProp(tab, 'disabled')\"\n                        :data-pc-index=\"index\"\n                    >\n                        <a\n                            :id=\"getTabHeaderActionId(index)\"\n                            v-ripple\n                            :class=\"cx('tab.headerAction')\"\n                            :tabindex=\"getTabProp(tab, 'disabled') || !isTabActive(index) ? -1 : tabindex\"\n                            role=\"tab\"\n                            :aria-disabled=\"getTabProp(tab, 'disabled')\"\n                            :aria-selected=\"isTabActive(index)\"\n                            :aria-controls=\"getTabContentId(index)\"\n                            @click=\"onTabClick($event, tab, index)\"\n                            @keydown=\"onTabKeyDown($event, tab, index)\"\n                            v-bind=\"{ ...getTabProp(tab, 'headerActionProps'), ...getTabPT(tab, 'headerAction', index) }\"\n                        >\n                            <span v-if=\"tab.props && tab.props.header\" :class=\"cx('tab.headerTitle')\" v-bind=\"getTabPT(tab, 'headerTitle', index)\">{{ tab.props.header }}</span>\n                            <component v-if=\"tab.children && tab.children.header\" :is=\"tab.children.header\"></component>\n                        </a>\n                    </li>\n                    <li ref=\"inkbar\" :class=\"cx('inkbar')\" role=\"presentation\" aria-hidden=\"true\" v-bind=\"ptm('inkbar')\"></li>\n                </ul>\n            </div>\n            <button\n                v-if=\"scrollable && !isNextButtonDisabled\"\n                ref=\"nextBtn\"\n                v-ripple\n                type=\"button\"\n                :class=\"cx('nextButton')\"\n                :tabindex=\"tabindex\"\n                :aria-label=\"nextButtonAriaLabel\"\n                @click=\"onNextButtonClick\"\n                v-bind=\"{ ...nextButtonProps, ...ptm('nextButton') }\"\n                data-pc-group-section=\"navbutton\"\n            >\n                <slot name=\"nexticon\">\n                    <component :is=\"nextIcon ? 'span' : 'ChevronRightIcon'\" aria-hidden=\"true\" :class=\"nextIcon\" v-bind=\"ptm('nextIcon')\" />\n                </slot>\n            </button>\n        </div>\n        <div :class=\"cx('panelContainer')\" v-bind=\"ptm('panelContainer')\">\n            <template v-for=\"(tab, index) of tabs\" :key=\"getKey(tab, index)\">\n                <div\n                    v-if=\"lazy ? isTabActive(index) : true\"\n                    v-show=\"lazy ? true : isTabActive(index)\"\n                    :id=\"getTabContentId(index)\"\n                    :style=\"getTabProp(tab, 'contentStyle')\"\n                    :class=\"cx('tab.content', { tab })\"\n                    role=\"tabpanel\"\n                    :aria-labelledby=\"getTabHeaderActionId(index)\"\n                    v-bind=\"{ ...getTabProp(tab, 'contentProps'), ...getTabPT(tab, 'root', index), ...getTabPT(tab, 'content', index) }\"\n                    data-pc-name=\"tabpanel\"\n                    :data-pc-index=\"index\"\n                    :data-p-active=\"d_activeIndex === index\"\n                >\n                    <component :is=\"tab\"></component>\n                </div>\n            </template>\n        </div>\n    </div>\n</template>\n\n<script>\nimport { findSingle, focus, getAttribute, getOffset, getWidth } from '@primeuix/utils/dom';\nimport ChevronLeftIcon from '@primevue/icons/chevronleft';\nimport ChevronRightIcon from '@primevue/icons/chevronright';\nimport Ripple from 'primevue/ripple';\nimport { mergeProps } from 'vue';\nimport BaseTabView from './BaseTabView.vue';\n\nexport default {\n    name: 'TabView',\n    extends: BaseTabView,\n    inheritAttrs: false,\n    emits: ['update:activeIndex', 'tab-change', 'tab-click'],\n    data() {\n        return {\n            d_activeIndex: this.activeIndex,\n            isPrevButtonDisabled: true,\n            isNextButtonDisabled: false\n        };\n    },\n    watch: {\n        activeIndex(newValue) {\n            this.d_activeIndex = newValue;\n\n            this.scrollInView({ index: newValue });\n        }\n    },\n    mounted() {\n        console.warn('Deprecated since v4. Use Tabs component instead.');\n\n        this.updateInkBar();\n        this.scrollable && this.updateButtonState();\n    },\n    updated() {\n        this.updateInkBar();\n        this.scrollable && this.updateButtonState();\n    },\n    methods: {\n        isTabPanel(child) {\n            return child.type.name === 'TabPanel';\n        },\n        isTabActive(index) {\n            return this.d_activeIndex === index;\n        },\n        getTabProp(tab, name) {\n            return tab.props ? tab.props[name] : undefined;\n        },\n        getKey(tab, index) {\n            return this.getTabProp(tab, 'header') || index;\n        },\n        getTabHeaderActionId(index) {\n            return `${this.$id}_${index}_header_action`;\n        },\n        getTabContentId(index) {\n            return `${this.$id}_${index}_content`;\n        },\n        getTabPT(tab, key, index) {\n            const count = this.tabs.length;\n            const tabMetaData = {\n                props: tab.props,\n                parent: {\n                    instance: this,\n                    props: this.$props,\n                    state: this.$data\n                },\n                context: {\n                    index,\n                    count,\n                    first: index === 0,\n                    last: index === count - 1,\n                    active: this.isTabActive(index)\n                }\n            };\n\n            return mergeProps(this.ptm(`tabpanel.${key}`, { tabpanel: tabMetaData }), this.ptm(`tabpanel.${key}`, tabMetaData), this.ptmo(this.getTabProp(tab, 'pt'), key, tabMetaData));\n        },\n        onScroll(event) {\n            this.scrollable && this.updateButtonState();\n\n            event.preventDefault();\n        },\n        onPrevButtonClick() {\n            const content = this.$refs.content;\n            const width = getWidth(content);\n            const pos = content.scrollLeft - width;\n\n            content.scrollLeft = pos <= 0 ? 0 : pos;\n        },\n        onNextButtonClick() {\n            const content = this.$refs.content;\n            const width = getWidth(content) - this.getVisibleButtonWidths();\n            const pos = content.scrollLeft + width;\n            const lastPos = content.scrollWidth - width;\n\n            content.scrollLeft = pos >= lastPos ? lastPos : pos;\n        },\n        onTabClick(event, tab, index) {\n            this.changeActiveIndex(event, tab, index);\n            this.$emit('tab-click', { originalEvent: event, index });\n        },\n        onTabKeyDown(event, tab, index) {\n            switch (event.code) {\n                case 'ArrowLeft':\n                    this.onTabArrowLeftKey(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onTabArrowRightKey(event);\n                    break;\n\n                case 'Home':\n                    this.onTabHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onTabEndKey(event);\n                    break;\n\n                case 'PageDown':\n                    this.onPageDownKey(event);\n                    break;\n\n                case 'PageUp':\n                    this.onPageUpKey(event);\n                    break;\n\n                case 'Enter':\n                case 'NumpadEnter':\n                case 'Space':\n                    this.onTabEnterKey(event, tab, index);\n                    break;\n\n                default:\n                    break;\n            }\n        },\n        onTabArrowRightKey(event) {\n            const nextHeaderAction = this.findNextHeaderAction(event.target.parentElement);\n\n            nextHeaderAction ? this.changeFocusedTab(event, nextHeaderAction) : this.onTabHomeKey(event);\n            event.preventDefault();\n        },\n        onTabArrowLeftKey(event) {\n            const prevHeaderAction = this.findPrevHeaderAction(event.target.parentElement);\n\n            prevHeaderAction ? this.changeFocusedTab(event, prevHeaderAction) : this.onTabEndKey(event);\n            event.preventDefault();\n        },\n        onTabHomeKey(event) {\n            const firstHeaderAction = this.findFirstHeaderAction();\n\n            this.changeFocusedTab(event, firstHeaderAction);\n            event.preventDefault();\n        },\n        onTabEndKey(event) {\n            const lastHeaderAction = this.findLastHeaderAction();\n\n            this.changeFocusedTab(event, lastHeaderAction);\n            event.preventDefault();\n        },\n        onPageDownKey(event) {\n            this.scrollInView({ index: this.$refs.nav.children.length - 2 });\n            event.preventDefault();\n        },\n        onPageUpKey(event) {\n            this.scrollInView({ index: 0 });\n            event.preventDefault();\n        },\n        onTabEnterKey(event, tab, index) {\n            this.changeActiveIndex(event, tab, index);\n\n            event.preventDefault();\n        },\n        findNextHeaderAction(tabElement, selfCheck = false) {\n            const headerElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n\n            return headerElement\n                ? getAttribute(headerElement, 'data-p-disabled') || getAttribute(headerElement, 'data-pc-section') === 'inkbar'\n                    ? this.findNextHeaderAction(headerElement)\n                    : findSingle(headerElement, '[data-pc-section=\"headeraction\"]')\n                : null;\n        },\n        findPrevHeaderAction(tabElement, selfCheck = false) {\n            const headerElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n\n            return headerElement\n                ? getAttribute(headerElement, 'data-p-disabled') || getAttribute(headerElement, 'data-pc-section') === 'inkbar'\n                    ? this.findPrevHeaderAction(headerElement)\n                    : findSingle(headerElement, '[data-pc-section=\"headeraction\"]')\n                : null;\n        },\n        findFirstHeaderAction() {\n            return this.findNextHeaderAction(this.$refs.nav.firstElementChild, true);\n        },\n        findLastHeaderAction() {\n            return this.findPrevHeaderAction(this.$refs.nav.lastElementChild, true);\n        },\n        changeActiveIndex(event, tab, index) {\n            if (!this.getTabProp(tab, 'disabled') && this.d_activeIndex !== index) {\n                this.d_activeIndex = index;\n\n                this.$emit('update:activeIndex', index);\n                this.$emit('tab-change', { originalEvent: event, index });\n\n                this.scrollInView({ index });\n            }\n        },\n        changeFocusedTab(event, element) {\n            if (element) {\n                focus(element);\n                this.scrollInView({ element });\n\n                if (this.selectOnFocus) {\n                    const index = parseInt(element.parentElement.dataset.pcIndex, 10);\n                    const tab = this.tabs[index];\n\n                    this.changeActiveIndex(event, tab, index);\n                }\n            }\n        },\n        scrollInView({ element, index = -1 }) {\n            const currentElement = element || this.$refs.nav.children[index];\n\n            if (currentElement) {\n                currentElement.scrollIntoView && currentElement.scrollIntoView({ block: 'nearest' });\n            }\n        },\n        updateInkBar() {\n            let tabHeader = this.$refs.nav.children[this.d_activeIndex];\n\n            this.$refs.inkbar.style.width = getWidth(tabHeader) + 'px';\n            this.$refs.inkbar.style.left = getOffset(tabHeader).left - getOffset(this.$refs.nav).left + 'px';\n        },\n        updateButtonState() {\n            const content = this.$refs.content;\n            const { scrollLeft, scrollWidth } = content;\n            const width = getWidth(content);\n\n            this.isPrevButtonDisabled = scrollLeft === 0;\n            this.isNextButtonDisabled = parseInt(scrollLeft) === scrollWidth - width;\n        },\n        getVisibleButtonWidths() {\n            const { prevBtn, nextBtn } = this.$refs;\n\n            return [prevBtn, nextBtn].reduce((acc, el) => (el ? acc + getWidth(el) : acc), 0);\n        }\n    },\n    computed: {\n        tabs() {\n            return this.$slots.default().reduce((tabs, child) => {\n                if (this.isTabPanel(child)) {\n                    tabs.push(child);\n                } else if (child.children && child.children instanceof Array) {\n                    child.children.forEach((nestedChild) => {\n                        if (this.isTabPanel(nestedChild)) {\n                            tabs.push(nestedChild);\n                        }\n                    });\n                }\n\n                return tabs;\n            }, []);\n        },\n        prevButtonAriaLabel() {\n            return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.previous : undefined;\n        },\n        nextButtonAriaLabel() {\n            return this.$primevue.config.locale.aria ? this.$primevue.config.locale.aria.next : undefined;\n        }\n    },\n    directives: {\n        ripple: Ripple\n    },\n    components: {\n        ChevronLeftIcon,\n        ChevronRightIcon\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "props", "activeIndex", "type", "Number", "lazy", "Boolean", "scrollable", "tabindex", "selectOnFocus", "prevButtonProps", "nextButtonProps", "prevIcon", "String", "undefined", "nextIcon", "style", "TabViewStyle", "provide", "$pcTabs", "$pcTabView", "$parentInstance", "BaseTabView", "inheritAttrs", "emits", "data", "d_activeIndex", "isPrevButtonDisabled", "isNextButtonDisabled", "watch", "newValue", "scrollInView", "index", "mounted", "console", "warn", "updateInkBar", "updateButtonState", "updated", "methods", "isTabPanel", "child", "isTabActive", "getTabProp", "tab", "<PERSON><PERSON><PERSON>", "getTabHeaderActionId", "concat", "$id", "getTabContentId", "getTabPT", "key", "count", "tabs", "length", "tabMetaData", "parent", "instance", "$props", "state", "$data", "context", "first", "last", "active", "mergeProps", "ptm", "tabpanel", "ptmo", "onScroll", "event", "preventDefault", "onPrevButtonClick", "content", "$refs", "width", "getWidth", "pos", "scrollLeft", "onNextButtonClick", "getVisibleButtonWidths", "lastPos", "scrollWidth", "onTabClick", "changeActiveIndex", "$emit", "originalEvent", "onTabKeyDown", "code", "onTabArrowLeftKey", "onTabArrowRightKey", "onTabHomeKey", "onTabEndKey", "onPageDownKey", "onPageUpKey", "onTabEnterKey", "nextHeaderAction", "findNextHeaderAction", "target", "parentElement", "changeFocusedTab", "prevHeaderAction", "findPrevHeaderAction", "firstHeaderAction", "findFirstHeaderAction", "lastHeaderAction", "findLastHeaderAction", "nav", "children", "tabElement", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "headerElement", "nextElement<PERSON><PERSON>ling", "getAttribute", "findSingle", "previousElementSibling", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "focus", "parseInt", "dataset", "pcIndex", "_ref", "_ref$index", "currentElement", "scrollIntoView", "block", "tabHeader", "inkbar", "left", "getOffset", "_this$$refs", "prevBtn", "nextBtn", "reduce", "acc", "el", "computed", "_this", "$slots", "push", "Array", "for<PERSON>ach", "nested<PERSON><PERSON><PERSON>", "prevButtonAriaLabel", "$primevue", "config", "locale", "aria", "previous", "nextButtonAriaLabel", "next", "directives", "ripple", "<PERSON><PERSON><PERSON>", "components", "ChevronLeftIcon", "ChevronRightIcon", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "role", "ptmi", "_createElementVNode", "_withDirectives", "ref", "$options", "onClick", "apply", "_objectSpread", "_renderSlot", "_createBlock", "_resolveDynamicComponent", "_Fragment", "_renderList", "ref_for", "id", "$event", "onKeydown", "header"], "mappings": ";;;;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,aAAa;AACnB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,WAAW,EAAE;AACTC,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,IAAI,EAAE;AACFF,MAAAA,IAAI,EAAEG,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRJ,MAAAA,IAAI,EAAEG,OAAO;MACb,SAAS,EAAA;KACZ;AACDE,IAAAA,QAAQ,EAAE;AACNL,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAAS,EAAA;KACZ;AACDK,IAAAA,aAAa,EAAE;AACXN,MAAAA,IAAI,EAAEG,OAAO;MACb,SAAS,EAAA;KACZ;AACDI,IAAAA,eAAe,EAAE;AACbP,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDQ,IAAAA,eAAe,EAAE;AACbR,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDS,IAAAA,QAAQ,EAAE;AACNT,MAAAA,IAAI,EAAEU,MAAM;MACZ,SAASC,EAAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNZ,MAAAA,IAAI,EAAEU,MAAM;MACZ,SAASC,EAAAA;AACb;GACH;AACDE,EAAAA,KAAK,EAAEC,YAAY;EACnBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,OAAO,EAAEL,SAAS;AAAE;AACpBM,MAAAA,UAAU,EAAE,IAAI;AAChBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;AC+CD,aAAe;AACXtB,EAAAA,IAAI,EAAE,SAAS;AACf,EAAA,SAAA,EAASuB,QAAW;AACpBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,KAAK,EAAE,CAAC,oBAAoB,EAAE,YAAY,EAAE,WAAW,CAAC;EACxDC,IAAI,EAAA,SAAJA,IAAIA,GAAG;IACH,OAAO;MACHC,aAAa,EAAE,IAAI,CAACxB,WAAW;AAC/ByB,MAAAA,oBAAoB,EAAE,IAAI;AAC1BC,MAAAA,oBAAoB,EAAE;KACzB;GACJ;AACDC,EAAAA,KAAK,EAAE;AACH3B,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAAC4B,QAAQ,EAAE;MAClB,IAAI,CAACJ,aAAY,GAAII,QAAQ;MAE7B,IAAI,CAACC,YAAY,CAAC;AAAEC,QAAAA,KAAK,EAAEF;AAAS,OAAC,CAAC;AAC1C;GACH;EACDG,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACNC,IAAAA,OAAO,CAACC,IAAI,CAAC,kDAAkD,CAAC;IAEhE,IAAI,CAACC,YAAY,EAAE;AACnB,IAAA,IAAI,CAAC7B,cAAc,IAAI,CAAC8B,iBAAiB,EAAE;GAC9C;EACDC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,IAAI,CAACF,YAAY,EAAE;AACnB,IAAA,IAAI,CAAC7B,cAAc,IAAI,CAAC8B,iBAAiB,EAAE;GAC9C;AACDE,EAAAA,OAAO,EAAE;AACLC,IAAAA,UAAU,EAAVA,SAAAA,UAAUA,CAACC,KAAK,EAAE;AACd,MAAA,OAAOA,KAAK,CAACtC,IAAI,CAACJ,IAAG,KAAM,UAAU;KACxC;AACD2C,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACV,KAAK,EAAE;AACf,MAAA,OAAO,IAAI,CAACN,aAAY,KAAMM,KAAK;KACtC;AACDW,IAAAA,UAAU,WAAVA,UAAUA,CAACC,GAAG,EAAE7C,IAAI,EAAE;MAClB,OAAO6C,GAAG,CAAC3C,QAAQ2C,GAAG,CAAC3C,KAAK,CAACF,IAAI,CAAA,GAAIe,SAAS;KACjD;AACD+B,IAAAA,MAAM,WAANA,MAAMA,CAACD,GAAG,EAAEZ,KAAK,EAAE;MACf,OAAO,IAAI,CAACW,UAAU,CAACC,GAAG,EAAE,QAAQ,KAAKZ,KAAK;KACjD;AACDc,IAAAA,oBAAoB,EAApBA,SAAAA,oBAAoBA,CAACd,KAAK,EAAE;MACxB,OAAAe,EAAAA,CAAAA,MAAA,CAAU,IAAI,CAACC,GAAG,EAAAD,GAAAA,CAAAA,CAAAA,MAAA,CAAIf,KAAK,EAAA,gBAAA,CAAA;KAC9B;AACDiB,IAAAA,eAAe,EAAfA,SAAAA,eAAeA,CAACjB,KAAK,EAAE;MACnB,OAAAe,EAAAA,CAAAA,MAAA,CAAU,IAAI,CAACC,GAAG,EAAAD,GAAAA,CAAAA,CAAAA,MAAA,CAAIf,KAAK,EAAA,UAAA,CAAA;KAC9B;IACDkB,QAAQ,EAAA,SAARA,QAAQA,CAACN,GAAG,EAAEO,GAAG,EAAEnB,KAAK,EAAE;AACtB,MAAA,IAAMoB,KAAM,GAAE,IAAI,CAACC,IAAI,CAACC,MAAM;AAC9B,MAAA,IAAMC,cAAc;QAChBtD,KAAK,EAAE2C,GAAG,CAAC3C,KAAK;AAChBuD,QAAAA,MAAM,EAAE;AACJC,UAAAA,QAAQ,EAAE,IAAI;UACdxD,KAAK,EAAE,IAAI,CAACyD,MAAM;UAClBC,KAAK,EAAE,IAAI,CAACC;SACf;AACDC,QAAAA,OAAO,EAAE;AACL7B,UAAAA,KAAK,EAALA,KAAK;AACLoB,UAAAA,KAAK,EAALA,KAAK;UACLU,KAAK,EAAE9B,KAAI,KAAM,CAAC;AAClB+B,UAAAA,IAAI,EAAE/B,KAAI,KAAMoB,KAAI,GAAI,CAAC;AACzBY,UAAAA,MAAM,EAAE,IAAI,CAACtB,WAAW,CAACV,KAAK;AAClC;OACH;MAED,OAAOiC,UAAU,CAAC,IAAI,CAACC,GAAG,CAAAnB,WAAAA,CAAAA,MAAA,CAAaI,GAAG,CAAI,EAAA;AAAEgB,QAAAA,QAAQ,EAAEZ;AAAY,OAAC,CAAC,EAAE,IAAI,CAACW,GAAG,CAAA,WAAA,CAAAnB,MAAA,CAAaI,GAAG,CAAA,EAAII,WAAW,CAAC,EAAE,IAAI,CAACa,IAAI,CAAC,IAAI,CAACzB,UAAU,CAACC,GAAG,EAAE,IAAI,CAAC,EAAEO,GAAG,EAAEI,WAAW,CAAC,CAAC;KAC/K;AACDc,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAACC,KAAK,EAAE;AACZ,MAAA,IAAI,CAAC/D,cAAc,IAAI,CAAC8B,iBAAiB,EAAE;MAE3CiC,KAAK,CAACC,cAAc,EAAE;KACzB;IACDC,iBAAiB,EAAA,SAAjBA,iBAAiBA,GAAG;AAChB,MAAA,IAAMC,OAAQ,GAAE,IAAI,CAACC,KAAK,CAACD,OAAO;AAClC,MAAA,IAAME,KAAI,GAAIC,QAAQ,CAACH,OAAO,CAAC;AAC/B,MAAA,IAAMI,GAAI,GAAEJ,OAAO,CAACK,UAAS,GAAIH,KAAK;MAEtCF,OAAO,CAACK,UAAW,GAAED,GAAI,IAAG,CAAA,GAAI,CAAA,GAAIA,GAAG;KAC1C;IACDE,iBAAiB,EAAA,SAAjBA,iBAAiBA,GAAG;AAChB,MAAA,IAAMN,OAAQ,GAAE,IAAI,CAACC,KAAK,CAACD,OAAO;MAClC,IAAME,KAAI,GAAIC,QAAQ,CAACH,OAAO,CAAE,GAAE,IAAI,CAACO,sBAAsB,EAAE;AAC/D,MAAA,IAAMH,GAAI,GAAEJ,OAAO,CAACK,UAAS,GAAIH,KAAK;AACtC,MAAA,IAAMM,UAAUR,OAAO,CAACS,cAAcP,KAAK;MAE3CF,OAAO,CAACK,UAAS,GAAID,GAAI,IAAGI,OAAQ,GAAEA,UAAUJ,GAAG;KACtD;IACDM,UAAU,EAAA,SAAVA,UAAUA,CAACb,KAAK,EAAE1B,GAAG,EAAEZ,KAAK,EAAE;MAC1B,IAAI,CAACoD,iBAAiB,CAACd,KAAK,EAAE1B,GAAG,EAAEZ,KAAK,CAAC;AACzC,MAAA,IAAI,CAACqD,KAAK,CAAC,WAAW,EAAE;AAAEC,QAAAA,aAAa,EAAEhB,KAAK;AAAEtC,QAAAA,KAAM,EAANA;AAAM,OAAC,CAAC;KAC3D;IACDuD,YAAY,EAAA,SAAZA,YAAYA,CAACjB,KAAK,EAAE1B,GAAG,EAAEZ,KAAK,EAAE;MAC5B,QAAQsC,KAAK,CAACkB,IAAI;AACd,QAAA,KAAK,WAAW;AACZ,UAAA,IAAI,CAACC,iBAAiB,CAACnB,KAAK,CAAC;AAC7B,UAAA;AAEJ,QAAA,KAAK,YAAY;AACb,UAAA,IAAI,CAACoB,kBAAkB,CAACpB,KAAK,CAAC;AAC9B,UAAA;AAEJ,QAAA,KAAK,MAAM;AACP,UAAA,IAAI,CAACqB,YAAY,CAACrB,KAAK,CAAC;AACxB,UAAA;AAEJ,QAAA,KAAK,KAAK;AACN,UAAA,IAAI,CAACsB,WAAW,CAACtB,KAAK,CAAC;AACvB,UAAA;AAEJ,QAAA,KAAK,UAAU;AACX,UAAA,IAAI,CAACuB,aAAa,CAACvB,KAAK,CAAC;AACzB,UAAA;AAEJ,QAAA,KAAK,QAAQ;AACT,UAAA,IAAI,CAACwB,WAAW,CAACxB,KAAK,CAAC;AACvB,UAAA;AAEJ,QAAA,KAAK,OAAO;AACZ,QAAA,KAAK,aAAa;AAClB,QAAA,KAAK,OAAO;UACR,IAAI,CAACyB,aAAa,CAACzB,KAAK,EAAE1B,GAAG,EAAEZ,KAAK,CAAC;AACrC,UAAA;AAIR;KACH;AACD0D,IAAAA,kBAAkB,EAAlBA,SAAAA,kBAAkBA,CAACpB,KAAK,EAAE;MACtB,IAAM0B,gBAAiB,GAAE,IAAI,CAACC,oBAAoB,CAAC3B,KAAK,CAAC4B,MAAM,CAACC,aAAa,CAAC;AAE9EH,MAAAA,mBAAmB,IAAI,CAACI,gBAAgB,CAAC9B,KAAK,EAAE0B,gBAAgB,CAAA,GAAI,IAAI,CAACL,YAAY,CAACrB,KAAK,CAAC;MAC5FA,KAAK,CAACC,cAAc,EAAE;KACzB;AACDkB,IAAAA,iBAAiB,EAAjBA,SAAAA,iBAAiBA,CAACnB,KAAK,EAAE;MACrB,IAAM+B,gBAAiB,GAAE,IAAI,CAACC,oBAAoB,CAAChC,KAAK,CAAC4B,MAAM,CAACC,aAAa,CAAC;AAE9EE,MAAAA,mBAAmB,IAAI,CAACD,gBAAgB,CAAC9B,KAAK,EAAE+B,gBAAgB,CAAA,GAAI,IAAI,CAACT,WAAW,CAACtB,KAAK,CAAC;MAC3FA,KAAK,CAACC,cAAc,EAAE;KACzB;AACDoB,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACrB,KAAK,EAAE;AAChB,MAAA,IAAMiC,iBAAkB,GAAE,IAAI,CAACC,qBAAqB,EAAE;AAEtD,MAAA,IAAI,CAACJ,gBAAgB,CAAC9B,KAAK,EAAEiC,iBAAiB,CAAC;MAC/CjC,KAAK,CAACC,cAAc,EAAE;KACzB;AACDqB,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACtB,KAAK,EAAE;AACf,MAAA,IAAMmC,gBAAiB,GAAE,IAAI,CAACC,oBAAoB,EAAE;AAEpD,MAAA,IAAI,CAACN,gBAAgB,CAAC9B,KAAK,EAAEmC,gBAAgB,CAAC;MAC9CnC,KAAK,CAACC,cAAc,EAAE;KACzB;AACDsB,IAAAA,aAAa,EAAbA,SAAAA,aAAaA,CAACvB,KAAK,EAAE;MACjB,IAAI,CAACvC,YAAY,CAAC;QAAEC,KAAK,EAAE,IAAI,CAAC0C,KAAK,CAACiC,GAAG,CAACC,QAAQ,CAACtD,MAAO,GAAE;AAAE,OAAC,CAAC;MAChEgB,KAAK,CAACC,cAAc,EAAE;KACzB;AACDuB,IAAAA,WAAW,EAAXA,SAAAA,WAAWA,CAACxB,KAAK,EAAE;MACf,IAAI,CAACvC,YAAY,CAAC;AAAEC,QAAAA,KAAK,EAAE;AAAE,OAAC,CAAC;MAC/BsC,KAAK,CAACC,cAAc,EAAE;KACzB;IACDwB,aAAa,EAAA,SAAbA,aAAaA,CAACzB,KAAK,EAAE1B,GAAG,EAAEZ,KAAK,EAAE;MAC7B,IAAI,CAACoD,iBAAiB,CAACd,KAAK,EAAE1B,GAAG,EAAEZ,KAAK,CAAC;MAEzCsC,KAAK,CAACC,cAAc,EAAE;KACzB;AACD0B,IAAAA,oBAAoB,EAApBA,SAAAA,oBAAoBA,CAACY,UAAU,EAAqB;AAAA,MAAA,IAAnBC,SAAU,GAAAC,SAAA,CAAAzD,MAAA,GAAA,CAAA,IAAAyD,SAAA,CAAA,CAAA,CAAA,KAAAjG,SAAA,GAAAiG,SAAA,CAAA,CAAA,CAAA,GAAE,KAAK;MAC9C,IAAMC,aAAY,GAAIF,SAAU,GAAED,UAAW,GAAEA,UAAU,CAACI,kBAAkB;AAE5E,MAAA,OAAOD,aAAY,GACbE,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAE,IAAGE,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAA,KAAM,QAAO,GACxG,IAAI,CAACf,oBAAoB,CAACe,aAAa,CAAA,GACvCG,UAAU,CAACH,aAAa,EAAE,kCAAkC,CAAA,GAChE,IAAI;KACb;AACDV,IAAAA,oBAAoB,EAApBA,SAAAA,oBAAoBA,CAACO,UAAU,EAAqB;AAAA,MAAA,IAAnBC,SAAU,GAAAC,SAAA,CAAAzD,MAAA,GAAA,CAAA,IAAAyD,SAAA,CAAA,CAAA,CAAA,KAAAjG,SAAA,GAAAiG,SAAA,CAAA,CAAA,CAAA,GAAE,KAAK;MAC9C,IAAMC,aAAc,GAAEF,SAAQ,GAAID,UAAW,GAAEA,UAAU,CAACO,sBAAsB;AAEhF,MAAA,OAAOJ,aAAY,GACbE,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAE,IAAGE,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAA,KAAM,QAAO,GACxG,IAAI,CAACV,oBAAoB,CAACU,aAAa,CAAA,GACvCG,UAAU,CAACH,aAAa,EAAE,kCAAkC,CAAA,GAChE,IAAI;KACb;IACDR,qBAAqB,EAAA,SAArBA,qBAAqBA,GAAG;AACpB,MAAA,OAAO,IAAI,CAACP,oBAAoB,CAAC,IAAI,CAACvB,KAAK,CAACiC,GAAG,CAACU,iBAAiB,EAAE,IAAI,CAAC;KAC3E;IACDX,oBAAoB,EAAA,SAApBA,oBAAoBA,GAAG;AACnB,MAAA,OAAO,IAAI,CAACJ,oBAAoB,CAAC,IAAI,CAAC5B,KAAK,CAACiC,GAAG,CAACW,gBAAgB,EAAE,IAAI,CAAC;KAC1E;IACDlC,iBAAiB,EAAA,SAAjBA,iBAAiBA,CAACd,KAAK,EAAE1B,GAAG,EAAEZ,KAAK,EAAE;AACjC,MAAA,IAAI,CAAC,IAAI,CAACW,UAAU,CAACC,GAAG,EAAE,UAAU,CAAE,IAAG,IAAI,CAAClB,aAAc,KAAIM,KAAK,EAAE;QACnE,IAAI,CAACN,aAAY,GAAIM,KAAK;AAE1B,QAAA,IAAI,CAACqD,KAAK,CAAC,oBAAoB,EAAErD,KAAK,CAAC;AACvC,QAAA,IAAI,CAACqD,KAAK,CAAC,YAAY,EAAE;AAAEC,UAAAA,aAAa,EAAEhB,KAAK;AAAEtC,UAAAA,KAAM,EAANA;AAAM,SAAC,CAAC;QAEzD,IAAI,CAACD,YAAY,CAAC;AAAEC,UAAAA,KAAM,EAANA;AAAM,SAAC,CAAC;AAChC;KACH;AACDoE,IAAAA,gBAAgB,WAAhBA,gBAAgBA,CAAC9B,KAAK,EAAEiD,OAAO,EAAE;AAC7B,MAAA,IAAIA,OAAO,EAAE;QACTC,KAAK,CAACD,OAAO,CAAC;QACd,IAAI,CAACxF,YAAY,CAAC;AAAEwF,UAAAA,OAAQ,EAARA;AAAQ,SAAC,CAAC;QAE9B,IAAI,IAAI,CAAC9G,aAAa,EAAE;AACpB,UAAA,IAAMuB,KAAI,GAAIyF,QAAQ,CAACF,OAAO,CAACpB,aAAa,CAACuB,OAAO,CAACC,OAAO,EAAE,EAAE,CAAC;AACjE,UAAA,IAAM/E,GAAE,GAAI,IAAI,CAACS,IAAI,CAACrB,KAAK,CAAC;UAE5B,IAAI,CAACoD,iBAAiB,CAACd,KAAK,EAAE1B,GAAG,EAAEZ,KAAK,CAAC;AAC7C;AACJ;KACH;AACDD,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAAA6F,IAAA,EAA0B;AAAA,MAAA,IAAvBL,OAAO,GAAAK,IAAA,CAAPL,OAAO;QAAAM,UAAA,GAAAD,IAAA,CAAE5F,KAAM;AAANA,QAAAA,KAAM,GAAA6F,UAAA,KAAA,MAAA,GAAE,EAAC,GAAAA,UAAA;AAC7B,MAAA,IAAMC,cAAa,GAAIP,OAAM,IAAK,IAAI,CAAC7C,KAAK,CAACiC,GAAG,CAACC,QAAQ,CAAC5E,KAAK,CAAC;AAEhE,MAAA,IAAI8F,cAAc,EAAE;AAChBA,QAAAA,cAAc,CAACC,cAAa,IAAKD,cAAc,CAACC,cAAc,CAAC;AAAEC,UAAAA,KAAK,EAAE;AAAU,SAAC,CAAC;AACxF;KACH;IACD5F,YAAY,EAAA,SAAZA,YAAYA,GAAG;AACX,MAAA,IAAI6F,SAAU,GAAE,IAAI,CAACvD,KAAK,CAACiC,GAAG,CAACC,QAAQ,CAAC,IAAI,CAAClF,aAAa,CAAC;AAE3D,MAAA,IAAI,CAACgD,KAAK,CAACwD,MAAM,CAAClH,KAAK,CAAC2D,KAAM,GAAEC,QAAQ,CAACqD,SAAS,IAAI,IAAI;MAC1D,IAAI,CAACvD,KAAK,CAACwD,MAAM,CAAClH,KAAK,CAACmH,OAAOC,SAAS,CAACH,SAAS,CAAC,CAACE,OAAOC,SAAS,CAAC,IAAI,CAAC1D,KAAK,CAACiC,GAAG,CAAC,CAACwB,IAAG,GAAI,IAAI;KACnG;IACD9F,iBAAiB,EAAA,SAAjBA,iBAAiBA,GAAG;AAChB,MAAA,IAAMoC,OAAQ,GAAE,IAAI,CAACC,KAAK,CAACD,OAAO;AAClC,MAAA,IAAQK,UAAU,GAAkBL,OAAO,CAAnCK,UAAU;QAAEI,WAAU,GAAMT,OAAO,CAAvBS,WAAU;AAC9B,MAAA,IAAMP,KAAI,GAAIC,QAAQ,CAACH,OAAO,CAAC;AAE/B,MAAA,IAAI,CAAC9C,oBAAmB,GAAImD,eAAe,CAAC;MAC5C,IAAI,CAAClD,oBAAqB,GAAE6F,QAAQ,CAAC3C,UAAU,CAAE,KAAII,WAAU,GAAIP,KAAK;KAC3E;IACDK,sBAAsB,EAAA,SAAtBA,sBAAsBA,GAAG;AACrB,MAAA,IAAAqD,WAAA,GAA6B,IAAI,CAAC3D,KAAK;QAA/B4D,OAAO,GAAAD,WAAA,CAAPC,OAAO;QAAEC,OAAQ,GAAAF,WAAA,CAARE,OAAQ;MAEzB,OAAO,CAACD,OAAO,EAAEC,OAAO,CAAC,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,EAAE,EAAA;QAAA,OAAMA,EAAG,GAAED,GAAI,GAAE7D,QAAQ,CAAC8D,EAAE,CAAA,GAAID,GAAG;OAAC,EAAE,CAAC,CAAC;AACrF;GACH;AACDE,EAAAA,QAAQ,EAAE;IACNtF,IAAI,EAAA,SAAJA,IAAIA,GAAG;AAAA,MAAA,IAAAuF,KAAA,GAAA,IAAA;AACH,MAAA,OAAO,IAAI,CAACC,MAAM,CAAA,SAAA,CAAQ,EAAE,CAACL,MAAM,CAAC,UAACnF,IAAI,EAAEZ,KAAK,EAAK;AACjD,QAAA,IAAImG,KAAI,CAACpG,UAAU,CAACC,KAAK,CAAC,EAAE;AACxBY,UAAAA,IAAI,CAACyF,IAAI,CAACrG,KAAK,CAAC;SAClB,MAAK,IAAIA,KAAK,CAACmE,QAAS,IAAGnE,KAAK,CAACmE,QAAS,YAAWmC,KAAK,EAAE;AAC1DtG,UAAAA,KAAK,CAACmE,QAAQ,CAACoC,OAAO,CAAC,UAACC,WAAW,EAAK;AACpC,YAAA,IAAIL,KAAI,CAACpG,UAAU,CAACyG,WAAW,CAAC,EAAE;AAC9B5F,cAAAA,IAAI,CAACyF,IAAI,CAACG,WAAW,CAAC;AAC1B;AACJ,WAAC,CAAC;AACN;AAEA,QAAA,OAAO5F,IAAI;OACd,EAAE,EAAE,CAAC;KACT;IACD6F,mBAAmB,EAAA,SAAnBA,mBAAmBA,GAAG;MAClB,OAAO,IAAI,CAACC,SAAS,CAACC,MAAM,CAACC,MAAM,CAACC,IAAK,GAAE,IAAI,CAACH,SAAS,CAACC,MAAM,CAACC,MAAM,CAACC,IAAI,CAACC,WAAWzI,SAAS;KACpG;IACD0I,mBAAmB,EAAA,SAAnBA,mBAAmBA,GAAG;MAClB,OAAO,IAAI,CAACL,SAAS,CAACC,MAAM,CAACC,MAAM,CAACC,OAAO,IAAI,CAACH,SAAS,CAACC,MAAM,CAACC,MAAM,CAACC,IAAI,CAACG,OAAO3I,SAAS;AACjG;GACH;AACD4I,EAAAA,UAAU,EAAE;AACRC,IAAAA,MAAM,EAAEC;GACX;AACDC,EAAAA,UAAU,EAAE;AACRC,IAAAA,eAAe,EAAfA,eAAe;AACfC,IAAAA,gBAAe,EAAfA;AACJ;AACJ,CAAC;;;;;;;;;;;;;;;EChXG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAwFK,OAxFLC,UAwFK,CAAA;AAxFC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;AAAUC,IAAAA,IAAI,EAAC;KAAkBF,IAAI,CAAAG,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAChDC,kBAAA,CAmEK,OAnELL,UAmEK,CAAA;AAnEC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,cAAA;KAA0BD,IAAG,CAAAjG,GAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAE9BiG,IAAA,CAAA5J,UAAS,KAAMqD,KAAoB,CAAAjC,oBAAA,GAD7C6I,cAAA,EAAAR,SAAA,EAAA,EAAAC,kBAAA,CAeQ,UAfRC,UAeQ,CAAA;;AAbJO,IAAAA,GAAG,EAAC,SAAQ;AAEZtK,IAAAA,IAAI,EAAC,QAAO;AACX,IAAA,OAAA,EAAOgK,IAAE,CAAAC,EAAA,CAAA,YAAA,CAAA;IACT5J,QAAQ,EAAE2J,IAAQ,CAAA3J,QAAA;IAClB,YAAU,EAAEkK,QAAmB,CAAAxB,mBAAA;IAC/ByB,OAAK;aAAED,QAAiB,CAAAlG,iBAAA,IAAAkG,QAAA,CAAAlG,iBAAA,CAAAoG,KAAA,CAAAF,QAAA,EAAA3D,SAAA,CAAA;KAAA;AACZ,GAAA,EAAA8D,aAAA,CAAAA,aAAA,KAAAV,IAAA,CAAAzJ,eAAe,CAAA,EAAKyJ,IAAA,CAAAjG,GAAG,CACpC,YAAA,CAAA,CAAA,EAAA;AAAA,IAAA,uBAAqB,EAAC;AAAU,GAAA,CAAA,EAAA,CAEhC4G,UAAA,CAEMX,6BAFN,YAAA;AAAA,IAAA,OAEM,eADFY,WAAsH,CAAAC,uBAAA,CAAtGb,IAAS,CAAAvJ,QAAA,GAAA,MAAA,GAAA,iBAAA,CAAA,EAAzBsJ,UAAsH,CAAA;AAA/D,MAAA,aAAW,EAAC,MAAO;AAAC,MAAA,OAAA,EAAOC,IAAQ,CAAAvJ;OAAUuJ,IAAG,CAAAjG,GAAA,CAAA,UAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA;gFAG/GqG,kBAAA,CAiCK,OAjCLL,UAiCK,CAAA;AAjCAO,IAAAA,GAAG,EAAC,SAAU;AAAC,IAAA,OAAA,EAAON,IAAE,CAAAC,EAAA,CAAA,YAAA,CAAA;IAAiB/F,QAAM;aAAEqG,QAAQ,CAAArG,QAAA,IAAAqG,QAAA,CAAArG,QAAA,CAAAuG,KAAA,CAAAF,QAAA,EAAA3D,SAAA,CAAA;KAAA;KAAUoD,IAAG,CAAAjG,GAAA,CAAA,YAAA,CAAA,CAAA,EAAA,CACvEqG,kBAAA,CA+BI,MA/BJL,UA+BI,CAAA;AA/BAO,IAAAA,GAAG,EAAC;AAAO,IAAA,OAAA,EAAON,IAAE,CAAAC,EAAA,CAAA,KAAA;KAAiBD,IAAG,CAAAjG,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA,EACxC8F,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CA4BIgB,QA3BuB,EAAA,IAAA,EAAAC,UAAA,CAAAR,QAAA,CAAArH,IAAI,EAAnB,UAAAT,GAAG,EAAEZ,KAAK,EAAA;IADtB,OAAAgI,SAAA,EAAA,EAAAC,kBAAA,CA4BI,MA5BJC,UA4BI,CAAA;MA1BC/G,GAAG,EAAEuH,QAAA,CAAA7H,MAAM,CAACD,GAAG,EAAEZ,KAAK,CAAA;MACtBhB,KAAK,EAAE0J,QAAU,CAAA/H,UAAA,CAACC,GAAG,EAAA,aAAA,CAAA;AACrB,MAAA,OAAA,EAAOuH,IAAA,CAAAC,EAAE,CAAiB,YAAA,EAAA;AAAAxH,QAAAA,GAAG,EAAHA,GAAG;AAAEZ,QAAAA,KAAI,EAAJA;AAAI,OAAA,CAAA;AACpCqI,MAAAA,IAAI,EAAC;AACQ,KAAA,EAAA;AAAAc,MAAAA,OAAA,EAAA;AAAA,KAAA,EAAAN,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAH,EAAAA,EAAAA,QAAA,CAAA/H,UAAU,CAACC,GAAG,EAAqB,aAAA,CAAA,GAAA8H,QAAA,CAAAxH,QAAQ,CAACN,GAAG,EAAU,MAAA,EAAAZ,KAAK,CAAM,CAAA0I,EAAAA,QAAA,CAAAxH,QAAQ,CAACN,GAAG,YAAYZ,KAAK,CAAA,CAAA,EAAA;AAC9G,MAAA,cAAY,EAAC,UAAS;AACrB,MAAA,eAAa,EAAE4B,KAAc,CAAAlC,aAAA,KAAIM,KAAK;MACtC,iBAAe,EAAE0I,QAAU,CAAA/H,UAAA,CAACC,GAAG,EAAA,UAAA,CAAA;AAC/B,MAAA,eAAa,EAAEZ;SAEhBwI,cAAA,EAAAR,SAAA,EAAA,EAAAC,kBAAA,CAeG,KAfHC,UAeG,CAAA;AAdEkB,MAAAA,EAAE,EAAEV,QAAoB,CAAA5H,oBAAA,CAACd,KAAK,CAAA;AAE9B,MAAA,OAAA,EAAOmI,IAAE,CAAAC,EAAA,CAAA,kBAAA,CAAA;MACT5J,QAAQ,EAAEkK,mBAAU,CAAC9H,GAAG,kBAAkB8H,QAAW,CAAAhI,WAAA,CAACV,KAAK,CAAA,GAAA,EAAA,GAASmI,IAAQ,CAAA3J,QAAA;AAC7E6J,MAAAA,IAAI,EAAC,KAAI;MACR,eAAa,EAAEK,QAAU,CAAA/H,UAAA,CAACC,GAAG,EAAA,UAAA,CAAA;AAC7B,MAAA,eAAa,EAAE8H,QAAW,CAAAhI,WAAA,CAACV,KAAK,CAAA;AAChC,MAAA,eAAa,EAAE0I,QAAe,CAAAzH,eAAA,CAACjB,KAAK,CAAA;AACpC2I,MAAAA,OAAK,WAALA,OAAKA;eAAED,QAAU,CAAAvF,UAAA,CAACkG,MAAM,EAAEzI,GAAG,EAAEZ,KAAK,CAAA;OAAA;AACpCsJ,MAAAA,SAAO,WAAPA,SAAOA;eAAEZ,QAAY,CAAAnF,YAAA,CAAC8F,MAAM,EAAEzI,GAAG,EAAEZ,KAAK,CAAA;AAAA;AAC5B,KAAA,EAAA;AAAAmJ,MAAAA,OAAA,EAAA;KAAA,EAAAN,aAAA,CAAAA,aAAA,KAAAH,QAAA,CAAA/H,UAAU,CAACC,GAAG,EAAA,mBAAA,CAAA,GAA2B8H,iBAAQ,CAAC9H,GAAG,kBAAkBZ,KAAK,CAAA,CAAA,CAAA,EAAA,CAE7EY,GAAG,CAAC3C,KAAI,IAAK2C,GAAG,CAAC3C,KAAK,CAACsL,MAAM,IAAzCvB,SAAA,EAAA,EAAAC,kBAAA,CAAmJ,QAAnJC,UAAmJ,CAAA;;AAAvG,MAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,iBAAA;;;OAA6BM,QAAQ,CAAAxH,QAAA,CAACN,GAAG,EAAiB,aAAA,EAAAZ,KAAK,oBAAMY,GAAG,CAAC3C,KAAK,CAACsL,MAAK,CAAA,EAAA,EAAA,CAAA,kCACxH3I,GAAG,CAACgE,QAAS,IAAGhE,GAAG,CAACgE,QAAQ,CAAC2E,MAAM,IAApDvB,SAAA,EAAA,EAAAe,WAAA,CAA2FC,uBAAhC,CAAApI,GAAG,CAACgE,QAAQ,CAAC2E,MAAM,CAAA,EAAA;AAAApI,MAAAA,GAAA,EAAA;KAAA,CAAA;aAGtFoH,kBAAA,CAAyG,MAAzGL,UAAyG,CAAA;AAArGO,IAAAA,GAAG,EAAC;AAAU,IAAA,OAAA,EAAON,IAAE,CAAAC,EAAA,CAAA,QAAA,CAAA;AAAYC,IAAAA,IAAI,EAAC,cAAe;AAAA,IAAA,aAAW,EAAC;KAAeF,IAAG,CAAAjG,GAAA,CAAA,QAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,cAIvFiG,IAAA,CAAA5J,UAAS,KAAMqD,KAAoB,CAAAhC,oBAAA,GAD7C4I,cAAA,EAAAR,SAAA,EAAA,EAAAC,kBAAA,CAeQ,UAfRC,UAeQ,CAAA;;AAbJO,IAAAA,GAAG,EAAC,SAAQ;AAEZtK,IAAAA,IAAI,EAAC,QAAO;AACX,IAAA,OAAA,EAAOgK,IAAE,CAAAC,EAAA,CAAA,YAAA,CAAA;IACT5J,QAAQ,EAAE2J,IAAQ,CAAA3J,QAAA;IAClB,YAAU,EAAEkK,QAAmB,CAAAlB,mBAAA;IAC/BmB,OAAK;aAAED,QAAiB,CAAA3F,iBAAA,IAAA2F,QAAA,CAAA3F,iBAAA,CAAA6F,KAAA,CAAAF,QAAA,EAAA3D,SAAA,CAAA;KAAA;AACZ,GAAA,EAAA8D,aAAA,CAAAA,aAAA,KAAAV,IAAA,CAAAxJ,eAAe,CAAA,EAAKwJ,IAAA,CAAAjG,GAAG,CACpC,YAAA,CAAA,CAAA,EAAA;AAAA,IAAA,uBAAqB,EAAC;AAAU,GAAA,CAAA,EAAA,CAEhC4G,UAAA,CAEMX,6BAFN,YAAA;AAAA,IAAA,OAEM,eADFY,WAAuH,CAAAC,uBAAA,CAAvGb,IAAO,CAAApJ,QAAA,GAAA,MAAA,GAAA,kBAAA,CAAA,EAAvBmJ,UAAuH,CAAA;AAA/D,MAAA,aAAW,EAAC,MAAO;AAAC,MAAA,OAAA,EAAOC,IAAQ,CAAApJ;OAAUoJ,IAAG,CAAAjG,GAAA,CAAA,UAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA;sFAIpHqG,kBAAA,CAkBK,OAlBLL,UAkBK,CAAA;AAlBC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,gBAAA;KAA4BD,IAAG,CAAAjG,GAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,EAC1C8F,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAgBUgB,QAhBuB,EAAA,IAAA,EAAAC,UAAA,CAAAR,QAAA,CAAArH,IAAI,EAAnB,UAAAT,GAAG,EAAEZ,KAAK,EAAA;;WAAiB0I,QAAM,CAAA7H,MAAA,CAACD,GAAG,EAAEZ,KAAK;SAEhDmI,IAAG,CAAA9J,IAAA,GAAIqK,QAAW,CAAAhI,WAAA,CAACV,KAAK,CAAA,GAAA,IAAA,IADlCwI,cAAA,EAAAR,SAAA,EAAA,EAAAC,kBAAA,CAcK,OAdLC,UAcK,CAAA;;AAXAkB,MAAAA,EAAE,EAAEV,QAAe,CAAAzH,eAAA,CAACjB,KAAK,CAAA;MACzBhB,KAAK,EAAE0J,QAAU,CAAA/H,UAAA,CAACC,GAAG,EAAA,cAAA,CAAA;AACrB,MAAA,OAAA,EAAOuH,IAAE,CAAAC,EAAA,CAAA,aAAA,EAAA;AAAkBxH,QAAAA,GAAE,EAAFA;AAAE,OAAA,CAAA;AAC9ByH,MAAAA,IAAI,EAAC,UAAS;AACb,MAAA,iBAAe,EAAEK,QAAoB,CAAA5H,oBAAA,CAACd,KAAK;AAC/B,KAAA,EAAA;AAAAmJ,MAAAA,OAAA,EAAA;AAAA,KAAA,EAAAN,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAH,EAAAA,EAAAA,QAAA,CAAA/H,UAAU,CAACC,GAAG,EAAsB,cAAA,CAAA,GAAA8H,QAAA,CAAAxH,QAAQ,CAACN,GAAG,EAAU,MAAA,EAAAZ,KAAK,CAAM,CAAA0I,EAAAA,QAAA,CAAAxH,QAAQ,CAACN,GAAG,aAAaZ,KAAK,CAAA,CAAA,EAAA;AAChH,MAAA,cAAY,EAAC,UAAS;AACrB,MAAA,eAAa,EAAEA,KAAK;AACpB,MAAA,eAAa,EAAE4B,KAAc,CAAAlC,aAAA,KAAIM;UAElCgI,SAAA,EAAA,EAAAe,WAAA,CAAgCC,wBAAhBpI,GAAG,CAAA,CAAA,+BAXXuH,IAAK,CAAA9J,IAAA,GAAA,IAAA,GAASqK,QAAW,CAAAhI,WAAA,CAACV,KAAK,CAAA,CAAA;;;;;;;;"}