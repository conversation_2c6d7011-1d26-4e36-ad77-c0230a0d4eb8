{"version": 3, "file": "index.mjs", "sources": ["../../../src/tabview/style/TabViewStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/tabview';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: ({ props }) => [\n        'p-tabview p-component',\n        {\n            'p-tabview-scrollable': props.scrollable\n        }\n    ],\n    navContainer: 'p-tabview-tablist-container',\n    prevButton: 'p-tabview-prev-button',\n    navContent: 'p-tabview-tablist-scroll-container',\n    nav: 'p-tabview-tablist',\n    tab: {\n        header: ({ instance, tab, index }) => [\n            'p-tabview-tablist-item',\n            instance.getTabProp(tab, 'headerClass'),\n            {\n                'p-tabview-tablist-item-active': instance.d_activeIndex === index,\n                'p-disabled': instance.getTabProp(tab, 'disabled')\n            }\n        ],\n        headerAction: 'p-tabview-tab-header',\n        headerTitle: 'p-tabview-tab-title',\n        content: ({ instance, tab }) => ['p-tabview-panel', instance.getTabProp(tab, 'contentClass')]\n    },\n    inkbar: 'p-tabview-ink-bar',\n    nextButton: 'p-tabview-next-button',\n    panelContainer: 'p-tabview-panels'\n};\n\nexport default BaseStyle.extend({\n    name: 'tabview',\n    style,\n    classes\n});\n"], "names": ["classes", "root", "_ref", "props", "scrollable", "navContainer", "prevButton", "navContent", "nav", "tab", "header", "_ref2", "instance", "index", "getTabProp", "d_activeIndex", "headerAction", "headerTitle", "content", "_ref3", "inkbar", "nextButton", "panelContainer", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAC,IAAA,EAAA;AAAA,IAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAAA,OAAO,CACjB,uBAAuB,EACvB;MACI,sBAAsB,EAAEA,KAAK,CAACC;AAClC,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,YAAY,EAAE,6BAA6B;AAC3CC,EAAAA,UAAU,EAAE,uBAAuB;AACnCC,EAAAA,UAAU,EAAE,oCAAoC;AAChDC,EAAAA,GAAG,EAAE,mBAAmB;AACxBC,EAAAA,GAAG,EAAE;AACDC,IAAAA,MAAM,EAAE,SAARA,MAAMA,CAAAC,KAAA,EAAA;AAAA,MAAA,IAAKC,QAAQ,GAAAD,KAAA,CAARC,QAAQ;QAAEH,GAAG,GAAAE,KAAA,CAAHF,GAAG;QAAEI,KAAK,GAAAF,KAAA,CAALE,KAAK;MAAA,OAAO,CAClC,wBAAwB,EACxBD,QAAQ,CAACE,UAAU,CAACL,GAAG,EAAE,aAAa,CAAC,EACvC;AACI,QAAA,+BAA+B,EAAEG,QAAQ,CAACG,aAAa,KAAKF,KAAK;AACjE,QAAA,YAAY,EAAED,QAAQ,CAACE,UAAU,CAACL,GAAG,EAAE,UAAU;AACrD,OAAC,CACJ;AAAA,KAAA;AACDO,IAAAA,YAAY,EAAE,sBAAsB;AACpCC,IAAAA,WAAW,EAAE,qBAAqB;AAClCC,IAAAA,OAAO,EAAE,SAATA,OAAOA,CAAAC,KAAA,EAAA;AAAA,MAAA,IAAKP,QAAQ,GAAAO,KAAA,CAARP,QAAQ;QAAEH,GAAG,GAAAU,KAAA,CAAHV,GAAG;MAAA,OAAO,CAAC,iBAAiB,EAAEG,QAAQ,CAACE,UAAU,CAACL,GAAG,EAAE,cAAc,CAAC,CAAC;AAAA;GAChG;AACDW,EAAAA,MAAM,EAAE,mBAAmB;AAC3BC,EAAAA,UAAU,EAAE,uBAAuB;AACnCC,EAAAA,cAAc,EAAE;AACpB,CAAC;AAED,mBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,SAAS;AACfC,EAAAA,KAAK,EAALA,KAAK;AACL1B,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}