{"version": 3, "file": "index.mjs", "sources": ["../../../src/toggleswitch/style/ToggleSwitchStyle.js"], "sourcesContent": ["import { style } from '@primeuix/styles/toggleswitch';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst inlineStyles = {\n    root: { position: 'relative' }\n};\n\nconst classes = {\n    root: ({ instance, props }) => [\n        'p-toggleswitch p-component',\n        {\n            'p-toggleswitch-checked': instance.checked,\n            'p-disabled': props.disabled,\n            'p-invalid': instance.$invalid\n        }\n    ],\n    input: 'p-toggleswitch-input',\n    slider: 'p-toggleswitch-slider',\n    handle: 'p-toggleswitch-handle'\n};\n\nexport default BaseStyle.extend({\n    name: 'toggleswitch',\n    style,\n    classes,\n    inlineStyles\n});\n"], "names": ["inlineStyles", "root", "position", "classes", "_ref", "instance", "props", "checked", "disabled", "$invalid", "input", "slider", "handle", "BaseStyle", "extend", "name", "style"], "mappings": ";;;AAGA,IAAMA,YAAY,GAAG;AACjBC,EAAAA,IAAI,EAAE;AAAEC,IAAAA,QAAQ,EAAE;AAAW;AACjC,CAAC;AAED,IAAMC,OAAO,GAAG;AACZF,EAAAA,IAAI,EAAE,SAANA,IAAIA,CAAAG,IAAA,EAAA;AAAA,IAAA,IAAKC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAA,OAAO,CAC3B,4BAA4B,EAC5B;MACI,wBAAwB,EAAED,QAAQ,CAACE,OAAO;MAC1C,YAAY,EAAED,KAAK,CAACE,QAAQ;MAC5B,WAAW,EAAEH,QAAQ,CAACI;AAC1B,KAAC,CACJ;AAAA,GAAA;AACDC,EAAAA,KAAK,EAAE,sBAAsB;AAC7BC,EAAAA,MAAM,EAAE,uBAAuB;AAC/BC,EAAAA,MAAM,EAAE;AACZ,CAAC;AAED,wBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,cAAc;AACpBC,EAAAA,KAAK,EAALA,KAAK;AACLb,EAAAA,OAAO,EAAPA,OAAO;AACPH,EAAAA,YAAY,EAAZA;AACJ,CAAC,CAAC;;;;"}