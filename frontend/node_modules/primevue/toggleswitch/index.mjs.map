{"version": 3, "file": "index.mjs", "sources": ["../../src/toggleswitch/BaseToggleSwitch.vue", "../../src/toggleswitch/ToggleSwitch.vue", "../../src/toggleswitch/ToggleSwitch.vue?vue&type=template&id=b155e212&lang.js"], "sourcesContent": ["<script>\nimport BaseEditableHolder from '@primevue/core/baseeditableholder';\nimport ToggleSwitchStyle from 'primevue/toggleswitch/style';\n\nexport default {\n    name: 'BaseToggleSwitch',\n    extends: BaseEditableHolder,\n    props: {\n        trueValue: {\n            type: null,\n            default: true\n        },\n        falseValue: {\n            type: null,\n            default: false\n        },\n        readonly: {\n            type: Boolean,\n            default: false\n        },\n        tabindex: {\n            type: Number,\n            default: null\n        },\n        inputId: {\n            type: String,\n            default: null\n        },\n        inputClass: {\n            type: [String, Object],\n            default: null\n        },\n        inputStyle: {\n            type: Object,\n            default: null\n        },\n        ariaLabelledby: {\n            type: String,\n            default: null\n        },\n        ariaLabel: {\n            type: String,\n            default: null\n        }\n    },\n    style: ToggleSwitchStyle,\n    provide() {\n        return {\n            $pcToggleSwitch: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :style=\"sx('root')\" v-bind=\"getPTOptions('root')\" :data-p-checked=\"checked\" :data-p-disabled=\"disabled\" :data-p=\"dataP\">\n        <input\n            :id=\"inputId\"\n            type=\"checkbox\"\n            role=\"switch\"\n            :class=\"[cx('input'), inputClass]\"\n            :style=\"inputStyle\"\n            :checked=\"checked\"\n            :tabindex=\"tabindex\"\n            :disabled=\"disabled\"\n            :readonly=\"readonly\"\n            :aria-checked=\"checked\"\n            :aria-labelledby=\"ariaLabelledby\"\n            :aria-label=\"ariaLabel\"\n            :aria-invalid=\"invalid || undefined\"\n            @focus=\"onFocus\"\n            @blur=\"onBlur\"\n            @change=\"onChange\"\n            v-bind=\"getPTOptions('input')\"\n        />\n        <div :class=\"cx('slider')\" v-bind=\"getPTOptions('slider')\" :data-p=\"dataP\">\n            <div :class=\"cx('handle')\" v-bind=\"getPTOptions('handle')\" :data-p=\"dataP\">\n                <slot name=\"handle\" :checked=\"checked\" />\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseToggleSwitch from './BaseToggleSwitch.vue';\n\nexport default {\n    name: 'ToggleSwitch',\n    extends: BaseToggleSwitch,\n    inheritAttrs: false,\n    emits: ['change', 'focus', 'blur'],\n    methods: {\n        getPTOptions(key) {\n            const _ptm = key === 'root' ? this.ptmi : this.ptm;\n\n            return _ptm(key, {\n                context: {\n                    checked: this.checked,\n                    disabled: this.disabled\n                }\n            });\n        },\n        onChange(event) {\n            if (!this.disabled && !this.readonly) {\n                const newValue = this.checked ? this.falseValue : this.trueValue;\n\n                this.writeValue(newValue, event);\n                this.$emit('change', event);\n            }\n        },\n        onFocus(event) {\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.$emit('blur', event);\n            this.formField.onBlur?.(event);\n        }\n    },\n    computed: {\n        checked() {\n            return this.d_value === this.trueValue;\n        },\n        dataP() {\n            return cn({\n                checked: this.checked,\n                disabled: this.disabled,\n                invalid: this.$invalid\n            });\n        }\n    }\n};\n</script>\n", "<template>\n    <div :class=\"cx('root')\" :style=\"sx('root')\" v-bind=\"getPTOptions('root')\" :data-p-checked=\"checked\" :data-p-disabled=\"disabled\" :data-p=\"dataP\">\n        <input\n            :id=\"inputId\"\n            type=\"checkbox\"\n            role=\"switch\"\n            :class=\"[cx('input'), inputClass]\"\n            :style=\"inputStyle\"\n            :checked=\"checked\"\n            :tabindex=\"tabindex\"\n            :disabled=\"disabled\"\n            :readonly=\"readonly\"\n            :aria-checked=\"checked\"\n            :aria-labelledby=\"ariaLabelledby\"\n            :aria-label=\"ariaLabel\"\n            :aria-invalid=\"invalid || undefined\"\n            @focus=\"onFocus\"\n            @blur=\"onBlur\"\n            @change=\"onChange\"\n            v-bind=\"getPTOptions('input')\"\n        />\n        <div :class=\"cx('slider')\" v-bind=\"getPTOptions('slider')\" :data-p=\"dataP\">\n            <div :class=\"cx('handle')\" v-bind=\"getPTOptions('handle')\" :data-p=\"dataP\">\n                <slot name=\"handle\" :checked=\"checked\" />\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport { cn } from '@primeuix/utils';\nimport BaseToggleSwitch from './BaseToggleSwitch.vue';\n\nexport default {\n    name: 'ToggleSwitch',\n    extends: BaseToggleSwitch,\n    inheritAttrs: false,\n    emits: ['change', 'focus', 'blur'],\n    methods: {\n        getPTOptions(key) {\n            const _ptm = key === 'root' ? this.ptmi : this.ptm;\n\n            return _ptm(key, {\n                context: {\n                    checked: this.checked,\n                    disabled: this.disabled\n                }\n            });\n        },\n        onChange(event) {\n            if (!this.disabled && !this.readonly) {\n                const newValue = this.checked ? this.falseValue : this.trueValue;\n\n                this.writeValue(newValue, event);\n                this.$emit('change', event);\n            }\n        },\n        onFocus(event) {\n            this.$emit('focus', event);\n        },\n        onBlur(event) {\n            this.$emit('blur', event);\n            this.formField.onBlur?.(event);\n        }\n    },\n    computed: {\n        checked() {\n            return this.d_value === this.trueValue;\n        },\n        dataP() {\n            return cn({\n                checked: this.checked,\n                disabled: this.disabled,\n                invalid: this.$invalid\n            });\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseEditableHolder", "props", "trueValue", "type", "falseValue", "readonly", "Boolean", "tabindex", "Number", "inputId", "String", "inputClass", "Object", "inputStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "style", "ToggleSwitchStyle", "provide", "$pcToggleSwitch", "$parentInstance", "BaseToggleSwitch", "inheritAttrs", "emits", "methods", "getPTOptions", "key", "_ptm", "ptmi", "ptm", "context", "checked", "disabled", "onChange", "event", "newValue", "writeValue", "$emit", "onFocus", "onBlur", "_this$formField$onBlu", "_this$formField", "formField", "call", "computed", "d_value", "dataP", "cn", "invalid", "$invalid", "_openBlock", "_createElementBlock", "_mergeProps", "_ctx", "cx", "sx", "$options", "_createElementVNode", "id", "role", "undefined", "apply", "arguments", "_hoisted_2", "_renderSlot", "$slots"], "mappings": ";;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,kBAAkB;AACxB,EAAA,SAAA,EAASC,kBAAkB;AAC3BC,EAAAA,KAAK,EAAE;AACHC,IAAAA,SAAS,EAAE;AACPC,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRD,MAAAA,IAAI,EAAE,IAAI;MACV,SAAS,EAAA;KACZ;AACDE,IAAAA,QAAQ,EAAE;AACNF,MAAAA,IAAI,EAAEG,OAAO;MACb,SAAS,EAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNJ,MAAAA,IAAI,EAAEK,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,OAAO,EAAE;AACLN,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRR,MAAAA,IAAI,EAAE,CAACO,MAAM,EAAEE,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDC,IAAAA,UAAU,EAAE;AACRV,MAAAA,IAAI,EAAES,MAAM;MACZ,SAAS,EAAA;KACZ;AACDE,IAAAA,cAAc,EAAE;AACZX,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;KACZ;AACDK,IAAAA,SAAS,EAAE;AACPZ,MAAAA,IAAI,EAAEO,MAAM;MACZ,SAAS,EAAA;AACb;GACH;AACDM,EAAAA,KAAK,EAAEC,iBAAiB;EACxBC,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,eAAe,EAAE,IAAI;AACrBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;ACnBD,aAAe;AACXrB,EAAAA,IAAI,EAAE,cAAc;AACpB,EAAA,SAAA,EAASsB,QAAgB;AACzBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;AAClCC,EAAAA,OAAO,EAAE;AACLC,IAAAA,YAAY,EAAZA,SAAAA,YAAYA,CAACC,GAAG,EAAE;AACd,MAAA,IAAMC,IAAG,GAAID,GAAI,KAAI,MAAK,GAAI,IAAI,CAACE,IAAK,GAAE,IAAI,CAACC,GAAG;MAElD,OAAOF,IAAI,CAACD,GAAG,EAAE;AACbI,QAAAA,OAAO,EAAE;UACLC,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBC,QAAQ,EAAE,IAAI,CAACA;AACnB;AACJ,OAAC,CAAC;KACL;AACDC,IAAAA,QAAQ,EAARA,SAAAA,QAAQA,CAACC,KAAK,EAAE;MACZ,IAAI,CAAC,IAAI,CAACF,QAAO,IAAK,CAAC,IAAI,CAAC3B,QAAQ,EAAE;AAClC,QAAA,IAAM8B,QAAO,GAAI,IAAI,CAACJ,OAAM,GAAI,IAAI,CAAC3B,UAAW,GAAE,IAAI,CAACF,SAAS;AAEhE,QAAA,IAAI,CAACkC,UAAU,CAACD,QAAQ,EAAED,KAAK,CAAC;AAChC,QAAA,IAAI,CAACG,KAAK,CAAC,QAAQ,EAAEH,KAAK,CAAC;AAC/B;KACH;AACDI,IAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACJ,KAAK,EAAE;AACX,MAAA,IAAI,CAACG,KAAK,CAAC,OAAO,EAAEH,KAAK,CAAC;KAC7B;AACDK,IAAAA,MAAM,EAANA,SAAAA,MAAMA,CAACL,KAAK,EAAE;MAAA,IAAAM,qBAAA,EAAAC,eAAA;AACV,MAAA,IAAI,CAACJ,KAAK,CAAC,MAAM,EAAEH,KAAK,CAAC;AACzB,MAAA,CAAAM,qBAAA,GAAAC,CAAAA,eAAA,OAAI,CAACC,SAAS,EAACH,MAAM,MAAA,IAAA,IAAAC,qBAAA,KAAA,MAAA,IAArBA,qBAAA,CAAAG,IAAA,CAAAF,eAAA,EAAwBP,KAAK,CAAC;AAClC;GACH;AACDU,EAAAA,QAAQ,EAAE;IACNb,OAAO,EAAA,SAAPA,OAAOA,GAAG;AACN,MAAA,OAAO,IAAI,CAACc,OAAQ,KAAI,IAAI,CAAC3C,SAAS;KACzC;IACD4C,KAAK,EAAA,SAALA,KAAKA,GAAG;AACJ,MAAA,OAAOC,EAAE,CAAC;QACNhB,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBgB,OAAO,EAAE,IAAI,CAACC;AAClB,OAAC,CAAC;AACN;AACJ;AACJ,CAAC;;;;;;;EC5EG,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAyBK,OAzBLC,UAyBK,CAAA;AAzBC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA,CAAA;AAAWtC,IAAAA,KAAK,EAAEqC,IAAE,CAAAE,EAAA,CAAA,MAAA;KAAkBC,QAAY,CAAA/B,YAAA,CAAA,MAAA,CAAA,EAAA;IAAW,gBAAc,EAAE+B,QAAO,CAAAzB,OAAA;IAAG,iBAAe,EAAEsB,IAAQ,CAAArB,QAAA;IAAG,QAAM,EAAEwB,QAAK,CAAAV;OAC3IW,kBAAA,CAkBC,SAlBDL,UAkBC,CAAA;IAjBIM,EAAE,EAAEL,IAAO,CAAA5C,OAAA;AACZN,IAAAA,IAAI,EAAC,UAAS;AACdwD,IAAAA,IAAI,EAAC,QAAO;IACX,OAAK,EAAA,CAAGN,IAAE,CAAAC,EAAA,CAAA,OAAA,CAAA,EAAWD,IAAU,CAAA1C,UAAA,CAAA;IAC/BK,KAAK,EAAEqC,IAAU,CAAAxC,UAAA;IACjBkB,OAAO,EAAEyB,QAAO,CAAAzB,OAAA;IAChBxB,QAAQ,EAAE8C,IAAQ,CAAA9C,QAAA;IAClByB,QAAQ,EAAEqB,IAAQ,CAAArB,QAAA;IAClB3B,QAAQ,EAAEgD,IAAQ,CAAAhD,QAAA;IAClB,cAAY,EAAEmD,QAAO,CAAAzB,OAAA;IACrB,iBAAe,EAAEsB,IAAc,CAAAvC,cAAA;IAC/B,YAAU,EAAEuC,IAAS,CAAAtC,SAAA;AACrB,IAAA,cAAY,EAAEsC,IAAM,CAAAL,OAAA,IAAKY,SAAS;IAClCtB,OAAK;aAAEkB,QAAO,CAAAlB,OAAA,IAAAkB,QAAA,CAAAlB,OAAA,CAAAuB,KAAA,CAAAL,QAAA,EAAAM,SAAA,CAAA;AAAA,KAAA,CAAA;IACdvB,MAAI;aAAEiB,QAAM,CAAAjB,MAAA,IAAAiB,QAAA,CAAAjB,MAAA,CAAAsB,KAAA,CAAAL,QAAA,EAAAM,SAAA,CAAA;AAAA,KAAA,CAAA;IACZ7B,QAAM;aAAEuB,QAAQ,CAAAvB,QAAA,IAAAuB,QAAA,CAAAvB,QAAA,CAAA4B,KAAA,CAAAL,QAAA,EAAAM,SAAA,CAAA;KAAA;KACTN,QAAY,CAAA/B,YAAA,CAAA,OAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAAsC,UAAA,CAAA,EAExBN,kBAAA,CAIK,OAJLL,UAIK,CAAA;AAJC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,QAAA;KAAoBE,QAAY,CAAA/B,YAAA,CAAA,QAAA,CAAA,EAAA;IAAa,QAAM,EAAE+B,QAAK,CAAAV;GAAA,CAAA,EAAA,CACrEW,kBAAA,CAEK,OAFLL,UAEK,CAAA;AAFC,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,QAAA;KAAoBE,QAAY,CAAA/B,YAAA,CAAA,QAAA,CAAA,EAAA;IAAa,QAAM,EAAE+B,QAAK,CAAAV;GAAA,CAAA,EAAA,CACrEkB,UAAwC,CAAAX,IAAA,CAAAY,MAAA,EAAA,QAAA,EAAA;IAAnBlC,OAAO,EAAEyB,QAAO,CAAAzB;AAAA,GAAA,CAAA;;;;;;;"}