@theme inline {
    --color-primary: var(--p-primary-color);
    --color-primary-emphasis: var(--p-primary-hover-color);
    --color-primary-emphasis-alt: var(--p-primary-active-color);
    --color-primary-contrast: var(--p-primary-contrast-color);
    --color-primary-50: var(--p-primary-50);
    --color-primary-100: var(--p-primary-100);
    --color-primary-200: var(--p-primary-200);
    --color-primary-300: var(--p-primary-300);
    --color-primary-400: var(--p-primary-400);
    --color-primary-500: var(--p-primary-500);
    --color-primary-600: var(--p-primary-600);
    --color-primary-700: var(--p-primary-700);
    --color-primary-800: var(--p-primary-800);
    --color-primary-900: var(--p-primary-900);
    --color-primary-950: var(--p-primary-950);
    --color-surface-0: var(--p-surface-0);
    --color-surface-50: var(--p-surface-50);
    --color-surface-100: var(--p-surface-100);
    --color-surface-200: var(--p-surface-200);
    --color-surface-300: var(--p-surface-300);
    --color-surface-400: var(--p-surface-400);
    --color-surface-500: var(--p-surface-500);
    --color-surface-600: var(--p-surface-600);
    --color-surface-700: var(--p-surface-700);
    --color-surface-800: var(--p-surface-800);
    --color-surface-900: var(--p-surface-900);
    --color-surface-950: var(--p-surface-950);
}
