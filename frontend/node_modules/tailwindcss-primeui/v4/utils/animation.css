@utility animate-fadein {
    animation: fadein 0.15s linear;
}
@utility animate-fadeout {
    animation: fadeout 0.15s linear;
}
@utility animate-slidedown {
    animation: slidedown 0.45s ease-in-out;
}
@utility animate-slideup {
    animation: slideup 0.45s cubic-bezier(0, 1, 0, 1);
}
@utility animate-scalein {
    animation: scalein 0.15s linear;
}
@utility animate-fadeinleft {
    animation: fadeinleft 0.15s linear;
}
@utility animate-fadeoutleft {
    animation: fadeoutleft 0.15s linear;
}
@utility animate-fadeinright {
    animation: fadeinright 0.15s linear;
}
@utility animate-fadeoutright {
    animation: fadeoutright 0.15s linear;
}
@utility animate-fadeinup {
    animation: fadeinup 0.15s linear;
}
@utility animate-fadeoutup {
    animation: fadeoutup 0.15s linear;
}
@utility animate-fadeindown {
    animation: fadeindown 0.15s linear;
}
@utility animate-fadeoutdown {
    animation: fadeoutdown 0.15s linear;
}
@utility animate-width {
    animation: width 0.45s linear;
}
@utility animate-flip {
    animation: flip 0.15s linear;
}
@utility animate-flipup {
    animation: flipup 0.15s linear;
}
@utility animate-flipleft {
    animation: flipleft 0.15s linear;
}
@utility animate-flipright {
    animation: flipright 0.15s linear;
}
@utility animate-zoomin {
    animation: zoomin 0.15s linear;
}
@utility animate-zoomindown {
    animation: zoomindown 0.15s linear;
}
@utility animate-zoominleft {
    animation: zoominleft 0.15s linear;
}
@utility animate-zoominright {
    animation: zoominright 0.15s linear;
}
@utility animate-zoominup {
    animation: zoominup 0.15s linear;
}
