@layer keyframes {
    @keyframes enter {
        from {
            opacity: var(--p-enter-opacity, 1);
            transform: translate3d(var(--p-enter-translate-x, 0), var(--p-enter-translate-y, 0), 0) scale3d(var(--p-enter-scale, 1), var(--p-enter-scale, 1), var(--p-enter-scale, 1)) rotate(var(--p-enter-rotate, 0));
        }
    }

    @keyframes leave {
        to {
            opacity: var(--p-leave-opacity, 1);
            transform: translate3d(var(--p-leave-translate-x, 0), var(--p-leave-translate-y, 0), 0) scale3d(var(--p-leave-scale, 1), var(--p-leave-scale, 1), var(--p-leave-scale, 1)) rotate(var(--p-leave-rotate, 0));
        }
    }

    @keyframes fadein {
        0% { opacity: 0; }
        100% { opacity: 1; }
    }

    @keyframes fadeout {
        0% { opacity: 1; }
        100% { opacity: 0; }
    }

    @keyframes infinite-scroll {
        from {
            transform: translateX(0);
        }
        to {
            transform: translateX(-100%);
        }
    }

    @keyframes scalein {
        0% {
            opacity: 0;
            transform: scaleY(0.8);
            transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
        }
        100% {
            opacity: 1;
            transform: scaleY(1);
        }
    }

    @keyframes slidedown {
        0% { max-height: 0; }
        100% { max-height: auto; }
    }

    @keyframes slideup {
        0% { max-height: 1000px; }
        100% { max-height: 0; }
    }

    @keyframes fadeinleft {
        0% {
            opacity: 0;
            transform: translateX(-100%);
            transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
        }
        100% {
            opacity: 1;
            transform: translateX(0%);
        }
    }

    @keyframes fadeoutleft {
        0% {
            opacity: 1;
            transform: translateX(0%);
            transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
        }
        100% {
            opacity: 0;
            transform: translateX(-100%);
        }
    }

    @keyframes fadeinright {
        0% {
            opacity: 0;
            transform: translateX(100%);
            transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
        }
        100% {
            opacity: 1;
            transform: translateX(0%);
        }
    }

    @keyframes fadeoutright {
        0% {
            opacity: 1;
            transform: translateX(0%);
            transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
        }
        100% {
            opacity: 0;
            transform: translateX(100%);
        }
    }

    @keyframes fadeinup {
        0% {
            opacity: 0;
            transform: translateY(-100%);
            transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
        }
        100% {
            opacity: 1;
            transform: translateY(0%);
        }
    }

    @keyframes fadeoutup {
        0% {
            opacity: 1;
            transform: translateY(0%);
            transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
        }
        100% {
            opacity: 0;
            transform: translateY(-100%);
        }
    }

    @keyframes fadeindown {
        0% {
            opacity: 0;
            transform: translateY(100%);
            transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
        }
        100% {
            opacity: 1;
            transform: translateY(0%);
        }
    }

    @keyframes fadeoutdown {
        0% {
            opacity: 1;
            transform: translateY(0%);
            transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
        }
        100% {
            opacity: 0;
            transform: translateY(100%);
        }
    }

    @keyframes width {
        0% { width: 0; }
        100% { width: 100%; }
    }

    @keyframes flip {
        from { transform: perspective(2000px) rotateX(-100deg); }
        to { transform: perspective(2000px) rotateX(0); }
    }

    @keyframes flipleft {
        from {
            transform: perspective(2000px) rotateY(-100deg);
            opacity: 0;
        }
        to {
            transform: perspective(2000px) rotateY(0);
            opacity: 1;
        }
    }

    @keyframes flipright {
        from {
            transform: perspective(2000px) rotateY(100deg);
            opacity: 0;
        }
        to {
            transform: perspective(2000px) rotateY(0);
            opacity: 1;
        }
    }

    @keyframes flipup {
        from {
            transform: perspective(2000px) rotateX(-100deg);
            opacity: 0;
        }
        to {
            transform: perspective(2000px) rotateX(0);
            opacity: 1;
        }
    }

    @keyframes zoomin {
        from {
            transform: scale3d(0.3, 0.3, 0.3);
            opacity: 0;
        }
        50% {
            opacity: 1;
        }
    }

    @keyframes zoomindown {
        from {
            transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
            opacity: 0;
        }
        60% {
            transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
            opacity: 1;
        }
    }

    @keyframes zoominleft {
        from {
            transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
            opacity: 0;
        }
        60% {
            transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
            opacity: 1;
        }
    }
} 