@custom-variant p-invalid (&[data-p~="invalid"]);
@custom-variant p-small (&[data-p~="small"]);
@custom-variant p-large (&[data-p~="large"]);
@custom-variant p-xlarge (&[data-p~="xlarge"]);
@custom-variant p-fluid (&[data-p~="fluid"]);
@custom-variant p-filled (&[data-p~="filled"]);
@custom-variant p-horizontal (&[data-p~="horizontal"]);
@custom-variant p-vertical (&[data-p~="vertical"]);
@custom-variant p-stacked (&[data-p~="stacked"]);
@custom-variant p-checked (&[data-p~="checked"]);
@custom-variant p-disabled (&[data-p~="disabled"],&[data-p-disabled="true"]);
@custom-variant p-enabled (&:not([data-p~="disabled"]));
@custom-variant p-selected (&[data-p~="selected"],&[data-p-selected="true"]);
@custom-variant p-selectable (&[data-p~="selectable"],&[data-p-selectable="true"],&[data-p-selectable-row="true"]);
@custom-variant p-left (&[data-p~="left"],&[data-p-left="true"]);
@custom-variant p-right (&[data-p~="right"],&[data-p-right="true"]);
@custom-variant p-top (&[data-p~="top"],&[data-p-top="true"]);
@custom-variant p-bottom (&[data-p~="bottom"],&[data-p-bottom="true"]);
@custom-variant p-alternate (&[data-p~="alternate"]);
@custom-variant p-center (&[data-p~="center"]);
@custom-variant p-top-center (&[data-p~="top-center"]);
@custom-variant p-bottom-center (&[data-p~="bottom-center"]);
@custom-variant p-active (&[data-p~="active"],&[data-p-active="true"]);
@custom-variant p-focus (&[data-p~="focus"],&[data-p-focused="true"]);
@custom-variant p-focus-visible (&[data-p~="focus-visible"]);
@custom-variant p-readonly (&[data-p~="readonly"]);
@custom-variant p-removable (&[data-p~="removable"]);
@custom-variant p-circle (&[data-p~="circle"]);
@custom-variant p-empty (&[data-p~="empty"]);
@custom-variant p-determinate (&[data-p~="determinate"]);
@custom-variant p-indeterminate (&[data-p~="indeterminate"]);
@custom-variant p-icon-only (&[data-p~="icon-only"]);
@custom-variant p-rounded (&[data-p~="rounded"]);
@custom-variant p-raised (&[data-p~="raised"]);
@custom-variant p-toggleable (&[data-p~="toggleable"]);
@custom-variant p-solid (&[data-p~="solid"]);
@custom-variant p-dashed (&[data-p~="dashed"]);
@custom-variant p-dotted (&[data-p~="dotted"]);
@custom-variant p-secondary (&[data-p~="secondary"]);
@custom-variant p-contrast (&[data-p~="contrast"]);
@custom-variant p-success (&[data-p~="success"]);
@custom-variant p-info (&[data-p~="info"]);
@custom-variant p-warn (&[data-p~="warn"]);
@custom-variant p-danger (&[data-p~="danger"]);
@custom-variant p-error (&[data-p~="error"]);
@custom-variant p-custom (&[data-p~="custom"]);
@custom-variant p-outlined (&[data-p~="outlined"]);
@custom-variant p-text (&[data-p~="text"]);
@custom-variant p-simple (&[data-p~="simple"]);
@custom-variant p-scrollable (&[data-p~="scrollable"]);
@custom-variant p-maximized (&[data-p~="maximized"]);
@custom-variant p-modal (&[data-p~="modal"]);
@custom-variant p-flipped (&[data-p-popover-flipped~="true"]);
@custom-variant p-nested (&[data-p~="nested"]);
@custom-variant p-weak (&[data-p~="weak"]);
@custom-variant p-medium (&[data-p~="medium"]);
@custom-variant p-strong (&[data-p~="strong"]);
@custom-variant p-portal-body (&[data-p~="portal-body"]);
@custom-variant p-portal-self (&[data-p~="portal-self"]);
@custom-variant p-has-s-icon (&[data-p-has-s-icon~="true"]);
@custom-variant p-has-e-icon (&[data-p-has-e-icon~="true"]);
@custom-variant p-full-screen (&[data-p~="full-screen"]);
@custom-variant p-open (&[data-p~="open"]);
@custom-variant p-popup (&[data-p~="popup"]);
@custom-variant p-placeholder (&::placeholder,&[data-p~="placeholder"]);
@custom-variant p-clearable (&[data-p~="clearable"]);
@custom-variant p-editable (&[data-p~="editable"]);
@custom-variant p-has-dropdown (&[data-p-has-dropdown="true"]);
@custom-variant p-has-chip (&[data-p~="has-chip"]);
@custom-variant p-inline (&[data-p~="inline"]);
@custom-variant p-today (&[data-p~="today"]);
@custom-variant p-other-month (&[data-p~="other-month"]);
@custom-variant p-time-only (&[data-p~="time-only"]);
@custom-variant p-completed (&[data-p~="completed"]);
@custom-variant p-loading (&[data-p~="loading"]);
@custom-variant p-scrollable (&[data-p~="scrollable"]);
@custom-variant p-leaf (&[data-p~="leaf"],&[data-p-leaf="true"]);
@custom-variant p-sortable (&[data-p~="sortable"],&[data-p-sortable-column="true"],&[data-p-sortable="true"]);
@custom-variant p-sorted (&[data-p~="sorted"],&[data-p-sorted="true"]);
@custom-variant p-resizable (&[data-p~="resizable"],&[data-p-resizable-column="true"],&[data-p-resizable="true"]);
@custom-variant p-hoverable (&[data-p~="hoverable"]);
@custom-variant p-scrollable (&[data-p~="scrollable"],&[data-p-scrollable="true"]);
@custom-variant p-flex-scrollable (&[data-p~="flex-scrollable"],&[data-p-flex-scrollable="true"]);
@custom-variant p-frozen (&[data-p~="frozen"],&[data-p-frozen="true"],&[data-p-frozen-column="true"]);