"use strict";var P=Object.create;var s=Object.defineProperty;var z=Object.getOwnPropertyDescriptor;var w=Object.getOwnPropertyNames;var k=Object.getPrototypeOf,A=Object.prototype.hasOwnProperty;var I=(a,i)=>{for(var e in i)s(a,e,{get:i[e],enumerable:!0})},l=(a,i,e,t)=>{if(i&&typeof i=="object"||typeof i=="function")for(let o of w(i))!A.call(a,o)&&o!==e&&s(a,o,{get:()=>i[o],enumerable:!(t=z(i,o))||t.enumerable});return a};var Y=(a,i,e)=>(e=a!=null?P(k(a)):{},l(i||!a||!a.__esModule?s(e,"default",{value:a,enumerable:!0}):e,a)),X=a=>l(s({},"__esModule",{value:!0}),a);var C={};I(C,{default:()=>$});module.exports=X(C);var x=Y(require("tailwindcss/plugin"),1);var r=a=>`color-mix(in srgb, ${a} calc(100% * <alpha-value>), transparent)`,p={extend:{colors:{primary:r("var(--p-primary-color)"),"primary-emphasis":r("var(--p-primary-hover-color)"),"primary-emphasis-alt":r("var(--p-primary-active-color)"),"primary-contrast":r("var(--p-primary-contrast-color)"),"primary-50":r("var(--p-primary-50)"),"primary-100":r("var(--p-primary-100)"),"primary-200":r("var(--p-primary-200)"),"primary-300":r("var(--p-primary-300)"),"primary-400":r("var(--p-primary-400)"),"primary-500":r("var(--p-primary-500)"),"primary-600":r("var(--p-primary-600)"),"primary-700":r("var(--p-primary-700)"),"primary-800":r("var(--p-primary-800)"),"primary-900":r("var(--p-primary-900)"),"primary-950":r("var(--p-primary-950)"),"surface-0":r("var(--p-surface-0)"),"surface-50":r("var(--p-surface-50)"),"surface-100":r("var(--p-surface-100)"),"surface-200":r("var(--p-surface-200)"),"surface-300":r("var(--p-surface-300)"),"surface-400":r("var(--p-surface-400)"),"surface-500":r("var(--p-surface-500)"),"surface-600":r("var(--p-surface-600)"),"surface-700":r("var(--p-surface-700)"),"surface-800":r("var(--p-surface-800)"),"surface-900":r("var(--p-surface-900)"),"surface-950":r("var(--p-surface-950)")},keyframes:{enter:{from:{opacity:"var(--p-enter-opacity, 1)",transform:"translate3d(var(--p-enter-translate-x, 0), var(--p-enter-translate-y, 0), 0) scale3d(var(--p-enter-scale, 1), var(--p-enter-scale, 1), var(--p-enter-scale, 1)) rotate(var(--p-enter-rotate, 0))"}},leave:{to:{opacity:"var(--p-leave-opacity, 1)",transform:"translate3d(var(--p-leave-translate-x, 0), var(--p-leave-translate-y, 0), 0) scale3d(var(--p-leave-scale, 1), var(--p-leave-scale, 1), var(--p-leave-scale, 1)) rotate(var(--p-leave-rotate, 0))"}},fadein:{"0%":{opacity:"0"},"100%":{opacity:"1"}},fadeout:{"0%":{opacity:"1"},"100%":{opacity:"0"}},scalein:{"0%":{opacity:"0",transform:"scaleY(0.8)",transition:"transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1)"},"100%":{opacity:"1",transform:"scaleY(1)"}},slidedown:{"0%":{maxHeight:"0"},"100%":{maxHeight:"auto"}},slideup:{"0%":{maxHeight:"1000px"},"100%":{maxHeight:"0"}},fadeinleft:{"0%":{opacity:"0",transform:"translateX(-100%)",transition:"transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1)"},"100%":{opacity:"1",transform:"translateX(0%)"}},fadeoutleft:{"0%":{opacity:"1",transform:"translateX(0%)",transition:"transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1)"},"100%":{opacity:"0",transform:"translateX(-100%)"}},fadeinright:{"0%":{opacity:"0",transform:"translateX(100%)",transition:"transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1)"},"100%":{opacity:"1",transform:"translateX(0%)"}},fadeoutright:{"0%":{opacity:"1",transform:"translateX(0%)",transition:"transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1)"},"100%":{opacity:"0",transform:"translateX(100%)"}},fadeinup:{"0%":{opacity:"0",transform:"translateY(-100%)",transition:"transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1)"},"100%":{opacity:"1",transform:"translateY(0%)"}},fadeoutup:{"0%":{opacity:"1",transform:"translateY(0%)",transition:"transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1)"},"100%":{opacity:"0",transform:"translateY(-100%)"}},fadeindown:{"0%":{opacity:"0",transform:"translateY(100%)",transition:"transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1)"},"100%":{opacity:"1",transform:"translateY(0%)"}},fadeoutdown:{"0%":{opacity:"1",transform:"translateY(0%)",transition:"transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1)"},"100%":{opacity:"0",transform:"translateY(100%)"}},width:{"0%":{width:"0"},"100%":{width:"100%"}},flip:{from:{transform:"perspective(2000px) rotateX(-100deg)"},to:{transform:"perspective(2000px) rotateX(0)"}},flipleft:{from:{transform:"perspective(2000px) rotateY(-100deg)",opacity:"0"},to:{transform:"perspective(2000px) rotateY(0)",opacity:"1"}},flipright:{from:{transform:"perspective(2000px) rotateY(100deg)",opacity:"0"},to:{transform:"perspective(2000px) rotateY(0)",opacity:"1"}},flipup:{from:{transform:"perspective(2000px) rotateX(-100deg)",opacity:"0"},to:{transform:"perspective(2000px) rotateX(0)",opacity:"1"}},zoomin:{from:{transform:"scale3d(0.3, 0.3, 0.3)",opacity:"0"},"50%":{opacity:"1"}},zoomindown:{from:{transform:"scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0)",opacity:"0"},"60%":{transform:"scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0)",opacity:"1"}},zoominleft:{from:{transform:"scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0)",opacity:"0"},"60%":{transform:"scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0)",opacity:"1"}},zoominright:{from:{transform:"scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0)",opacity:"0"},"60%":{transform:"scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0)",opacity:"1"}},zoominup:{from:{transform:"scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0)",opacity:"0"},"60%":{transform:"scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0)",opacity:"1"}}},animation:{fadein:"fadein 0.15s linear",fadeout:"fadeout 0.15s linear",slidedown:"slidedown 0.45s ease-in-out",slideup:"slideup 0.45s cubic-bezier(0, 1, 0, 1)",scalein:"scalein 0.15s linear",fadeinleft:"fadeinleft 0.15s linear",fadeoutleft:"fadeoutleft 0.15s linear",fadeinright:"fadeinright 0.15s linear",fadeoutright:"fadeoutright 0.15s linear",fadeinup:"fadeinup 0.15s linear",fadeoutup:"fadeoutup 0.15s linear",fadeindown:"fadeindown 0.15s linear",fadeoutdown:"fadeoutdown 0.15s linear",width:"width 0.45s linear",flip:"flip 0.15s linear",flipup:"flipup 0.15s linear",flipleft:"flipleft 0.15s linear",flipright:"flipright 0.15s linear",zoomin:"zoomin 0.15s linear",zoomindown:"zoomindown 0.15s linear",zoominleft:"zoominleft 0.15s linear",zoominright:"zoominright 0.15s linear",zoominup:"zoominup 0.15s linear"},animationDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",400:"400ms",500:"500ms",700:"700ms",1e3:"1000ms"},animationDuration:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",400:"400ms",500:"500ms",700:"700ms",1e3:"1000ms",2e3:"2000ms",3e3:"3000ms"},animationOpacity:({theme:a})=>a("opacity"),animationTranslate:({theme:a})=>a("translate"),animationScale:({theme:a})=>a("scale"),animationRotate:({theme:a})=>a("rotate")}};var m=({addUtilities:a})=>{a({".backface-visible":{"backface-visibility":"visible"},".backface-hidden":{"backface-visibility":"hidden"}})};var c=({matchUtilities:a,theme:i})=>{a({"animate-delay":e=>({"animation-delay":e})},{values:i("animationDelay")})};var f=({addUtilities:a})=>{a({".animate-normal":{"animation-direction":"normal"},".animate-reverse":{"animation-direction":"reverse"},".animate-alternate":{"animation-direction":"alternate"},".animate-alternate-reverse":{"animation-direction":"alternate-reverse"}})};var d=({matchUtilities:a,theme:i})=>{a({"animate-duration":e=>({"animation-duration":e})},{values:i("animationDuration")})};var u=({addUtilities:a,matchUtilities:i,theme:e})=>{a({"@keyframes enter":e("keyframes.enter"),"@keyframes leave":e("keyframes.leave"),".animate-enter":{"animation-name":"enter","--p-enter-opacity":"initial","--p-enter-scale":"initial","--p-enter-rotate":"initial","--p-enter-translate-x":"initial","--p-enter-translate-y":"initial"},".animate-leave":{"animation-name":"leave","--p-leave-opacity":"initial","--p-leave-scale":"initial","--p-leave-rotate":"initial","--p-leave-translate-x":"initial","--p-leave-translate-y":"initial"}}),i({"fade-in":t=>({"--p-enter-opacity":t}),"fade-out":t=>({"--p-leave-opacity":t})},{values:e("animationOpacity")}),i({"zoom-in":t=>({"--p-enter-scale":t}),"zoom-out":t=>({"--p-leave-scale":t})},{values:e("animationScale")}),i({"spin-in":t=>({"--p-enter-rotate":t}),"spin-out":t=>({"--p-leave-rotate":t})},{values:e("animationRotate")}),i({"slide-in-from-t":t=>({"--p-enter-translate-y":`-${t}`}),"slide-in-from-b":t=>({"--p-enter-translate-y":t}),"slide-in-from-l":t=>({"--p-enter-translate-x":`-${t}`}),"slide-in-from-r":t=>({"--p-enter-translate-x":t}),"slide-out-from-t":t=>({"--p-leave-translate-y":`-${t}`}),"slide-out-from-b":t=>({"--p-leave-translate-y":t}),"slide-out-from-l":t=>({"--p-leave-translate-x":`-${t}`}),"slide-out-from-r":t=>({"--p-leave-translate-x":t})},{values:e("translate")})};var y=({addUtilities:a})=>{a({".animate-fill-none":{"animation-fill-mode":"none"},".animate-fill-forwards":{"animation-fill-mode":"forwards"},".animate-fill-backwards":{"animation-fill-mode":"backwards"},".animate-fill-both":{"animation-fill-mode":"both"}})};var v=({addUtilities:a})=>{a({".animate-infinite":{"animation-iteration-count":"infinite"},".animate-once":{"animation-iteration-count":"1"},".animate-twice":{"animation-iteration-count":"2"}})};var g=({addUtilities:a})=>{a({".animate-running":{"animation-play-state":"running"},".animate-paused":{"animation-play-state":"paused"}})};var b=({addUtilities:a})=>{a({".border-surface":{"border-color":"var(--p-content-border-color)"},".bg-emphasis":{background:"var(--p-content-hover-background)",color:"var(--p-content-hover-color)"},".bg-highlight":{background:"var(--p-highlight-background)",color:"var(--p-highlight-color)"},".bg-highlight-emphasis":{background:"var(--p-highlight-focus-background)",color:"var(--p-highlight-focus-color)"},".rounded-border":{"border-radius":"var(--p-content-border-radius)"},".text-color":{color:"var(--p-text-color)"},".text-color-emphasis":{color:"var(--p-text-hover-color)"},".text-muted-color":{color:"var(--p-text-muted-color)"},".text-muted-color-emphasis":{color:"var(--p-text-hover-muted-color)"}})};var h=({addUtilities:a})=>{a({".animate-ease-linear":{"animation-timing-function":"linear"},".animate-ease-in":{"animation-timing-function":"cubic-bezier(0.4, 0, 1, 1)"},".animate-ease-out":{"animation-timing-function":"cubic-bezier(0, 0, 0.2, 1)"},".animate-ease-in-out":{"animation-timing-function":"cubic-bezier(0.4, 0, 0.2, 1)"}})};var n={backface:m,delay:c,direction:f,duration:d,enterleave:u,fillMode:y,iterationCount:v,playState:g,preset:b,timingFunction:h};var $=(0,x.default)(a=>{n.backface(a),n.delay(a),n.direction(a),n.duration(a),n.fillMode(a),n.iterationCount(a),n.playState(a),n.timingFunction(a),n.preset(a),n.enterleave(a)},{theme:p});
module.exports = module.exports.default || module.exports;
//# sourceMappingURL=index.cjs.map