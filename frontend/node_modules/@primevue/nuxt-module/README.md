![@primevue/nuxt-module](https://github.com/primefaces/primevue-nuxt-module/assets/11868120/c35e1180-573f-4650-bbe9-0c79bff71f05)

# PrimeVue Nuxt Module

[![npm version][npm-version-src]][npm-version-href]
[![npm downloads][npm-downloads-src]][npm-downloads-href]
[![Discord Chat][discord-src]](discord-href)
[![License][license-src]][license-href]

- [✨ Release Notes](https://github.com/primefaces/primevue/packages/nuxt-module/blob/main/CHANGELOG.md#changelog)
- [📖 Documentation](https://primevue.org/nuxt/)

## Quick Setup

1. Add `@primevue/nuxt-module` dependency to your project

```bash
npx nuxi@latest module add primevue
```

2. Add `@primevue/nuxt-module` to the `modules` section of `nuxt.config.{ts,js}`

```js
{
    modules: ['@primevue/nuxt-module'];
}
```

That's it! You can now use `@primevue/nuxt-module` in your Nuxt app ✨

[npm-version-src]: https://img.shields.io/npm/v/@primevue/nuxt-module/latest.svg?color
[npm-version-href]: https://npmjs.com/package/@primevue/nuxt-module
[npm-downloads-src]: https://img.shields.io/npm/dm/@primevue/nuxt-module
[npm-downloads-href]: https://npmjs.com/package/@primevue/nuxt-module
[discord-src]: https://img.shields.io/discord/557940238991753223.svg?colorB=7289da&label=chat&logo=discord
[license-src]: https://img.shields.io/npm/l/@primevue/nuxt-module.svg?style=flat&colorB=yellow
[license-href]: https://npmjs.com/package/@primevue/nuxt-module
