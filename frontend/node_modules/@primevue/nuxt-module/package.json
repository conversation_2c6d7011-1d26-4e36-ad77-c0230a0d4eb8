{"name": "@primevue/nuxt-module", "version": "4.3.5", "author": "PrimeTek Informatics", "description": "Nuxt module for PrimeVue", "homepage": "https://primevue.org/", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/primefaces/primevue.git", "directory": "packages/nuxt-module"}, "bugs": {"url": "https://github.com/primefaces/primevue/issues"}, "keywords": ["vue", "vue.js", "vue3", "nuxt", "nuxt3", "primevue", "primevue nuxt module", "primevue nuxt3", "primevue 4"], "type": "module", "sideEffects": false, "main": "./dist/module.cjs", "types": "./dist/types.d.ts", "files": ["dist"], "build": {"externals": ["primevue", "@primevue/metadata", "@primevue/auto-import-resolver", "unplugin-vue-components", "@primeuix/utils", "@primeuix/utils/object", "@primeuix/utils/dom"]}, "publishConfig": {"access": "public"}, "dependencies": {"@nuxt/kit": "^3", "@primeuix/styled": "^0.6.4", "@primeuix/utils": "^0.5.3", "unplugin-vue-components": "28.4.1", "pathe": "^1.1.2", "@primevue/auto-import-resolver": "4.3.5", "@primevue/metadata": "4.3.5", "primevue": "4.3.5"}, "devDependencies": {"@primeuix/themes": "^1.1.1", "@nuxt/devtools": "^2.1.0", "@nuxt/eslint-config": "^1.1.0", "@nuxt/module-builder": "^0.8.4", "@nuxt/schema": "^3", "@nuxt/test-utils": "^3", "@types/node": "latest", "changelogen": "^0.6.0", "nuxt": "3.15.4", "vitest": "^3.0.7", "vue-tsc": "~2.1.6"}, "engines": {"node": ">=12.11.0"}, "scripts": {"build": "NODE_ENV=production INPUT_DIR=src/ OUTPUT_DIR=dist/ pnpm run build:package", "build:package": "pnpm run build:prebuild && pnpm run prepack", "build:prebuild": "node ./scripts/prebuild.mjs", "dev": "nuxi dev playground", "dev:build": "nuxi build playground", "dev:prepare": "nuxt-module-build build --stub && nuxt-module-build prepare && nuxi prepare playground", "dev:link": "pnpm link --global && npm link", "test": "vitest run", "test:watch": "vitest watch", "test:types": "vue-tsc --noEmit && cd playground && vue-tsc --noEmit"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/module.mjs", "require": "./dist/module.cjs"}}}