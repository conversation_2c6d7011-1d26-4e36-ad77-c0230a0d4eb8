{"name": "@primevue/metadata", "version": "4.3.5", "author": "PrimeTek Informatics", "description": "", "homepage": "https://primevue.org/", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/primefaces/primevue.git", "directory": "packages/metadata"}, "bugs": {"url": "https://github.com/primefaces/primevue/issues"}, "main": "./index.mjs", "module": "./index.mjs", "types": "./index.d.mts", "publishConfig": {"access": "public"}, "engines": {"node": ">=12.11.0"}, "exports": {".": {"types": "./index.d.mts", "import": "./index.mjs", "default": "./index.mjs"}}}