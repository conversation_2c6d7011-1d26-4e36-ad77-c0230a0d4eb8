{"name": "@primevue/auto-import-resolver", "version": "4.3.5", "author": "PrimeTek Informatics", "description": "", "homepage": "https://primevue.org/", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/primefaces/primevue.git", "directory": "packages/auto-import-resolver"}, "bugs": {"url": "https://github.com/primefaces/primevue/issues"}, "main": "./index.mjs", "module": "./index.mjs", "types": "./index.d.mts", "publishConfig": {"access": "public"}, "dependencies": {"@primevue/metadata": "4.3.5"}, "engines": {"node": ">=12.11.0"}, "exports": {".": {"types": "./index.d.mts", "import": "./index.mjs", "default": "./index.mjs"}}}