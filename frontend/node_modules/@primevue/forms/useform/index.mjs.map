{"version": 3, "file": "index.mjs", "sources": ["../../src/useform/index.js"], "sourcesContent": ["import { isArray, isEmpty, isNotEmpty, mergeKeys, resolve } from '@primeuix/utils';\nimport { computed, getCurrentInstance, mergeProps, nextTick, onMounted, reactive, ref, toValue, watch } from 'vue';\n\nfunction tryOnMounted(fn, sync = true) {\n    if (getCurrentInstance()) onMounted(fn);\n    else if (sync) fn();\n    else nextTick(fn);\n}\n\nfunction watchPausable(source, callback, options) {\n    const isActive = ref(true);\n\n    const stop = watch(\n        source,\n        (newValue, oldValue) => {\n            if (!isActive.value) return;\n            callback(newValue, oldValue);\n        },\n        options\n    );\n\n    return {\n        stop,\n        pause: () => {\n            isActive.value = false;\n        },\n        resume: () => {\n            isActive.value = true;\n        }\n    };\n}\n\n// @todo: move to utils\nfunction groupKeys(obj) {\n    return Object.entries(obj).reduce((result, [key, value]) => {\n        /* eslint-disable-next-line no-useless-escape */\n        key.split(/[\\.\\[\\]]+/)\n            .filter(Boolean)\n            .reduce((acc, curr, idx, arr) => (acc[curr] ??= isNaN(arr[idx + 1]) ? (idx === arr.length - 1 ? value : {}) : []), result);\n\n        return result;\n    }, {});\n}\n\nfunction getValueByPath(obj, path) {\n    if (!obj || !path) {\n        // short circuit if there is nothing to resolve\n        return null;\n    }\n\n    try {\n        const value = obj[path];\n\n        if (isNotEmpty(value)) return value;\n    } catch {\n        // do nothing and continue to other methods to resolve path data\n    }\n\n    /* eslint-disable-next-line no-useless-escape */\n    const keys = path.split(/[\\.\\[\\]]+/).filter(Boolean);\n\n    return keys.reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj);\n}\n\nexport const useForm = (options = {}) => {\n    const _states = reactive({});\n    const fields = reactive({});\n    const valid = computed(() => Object.values(_states).every((field) => !field.invalid));\n    const states = computed(() => groupKeys(_states));\n\n    const getInitialState = (field, initialValue) => {\n        return {\n            value: initialValue ?? getValueByPath(options.initialValues, field),\n            touched: false,\n            dirty: false,\n            pristine: true,\n            valid: true,\n            invalid: false,\n            error: null,\n            errors: []\n        };\n    };\n\n    const isFieldValidate = (field, validateOn) => {\n        const value = resolve(validateOn, field);\n\n        return value === true || (isArray(value) && value.includes(field));\n    };\n\n    const validateOn = async (option, defaultValue) => {\n        let results = {};\n\n        isArray(options[option]) ? (results = await validate(options[option])) : (options[option] ?? defaultValue) && (results = await validate());\n        const fieldArr = Object.keys(fields).filter((field) => fields[field]?.options?.[option]) || [];\n\n        isNotEmpty(fieldArr) && (results = await validate(fieldArr));\n\n        return results;\n    };\n\n    const validateFieldOn = (field, fieldOptions, option, defaultValue) => {\n        (fieldOptions?.[option] ?? isFieldValidate(field, options[option] ?? defaultValue)) && validate(field);\n    };\n\n    const defineField = (field, fieldOptions) => {\n        if (!field) {\n            //console.warn('The `name` attribute is required for the field definition.');\n\n            return []; // prevent errors\n        }\n\n        fields[field]?._watcher.stop();\n\n        _states[field] ||= getInitialState(field, fieldOptions?.initialValue);\n\n        const props = mergeProps(resolve(fieldOptions, _states[field])?.props, resolve(fieldOptions?.props, _states[field]), {\n            name: field,\n            onBlur: () => {\n                _states[field].touched = true;\n                validateFieldOn(field, fieldOptions, 'validateOnBlur');\n            },\n            onInput: (event) => {\n                _states[field].value = event && Object.hasOwn(event, 'value') ? event.value : event.target.value;\n            },\n            onChange: (event) => {\n                _states[field].value = event && Object.hasOwn(event, 'value') ? event.value : event.target.type === 'checkbox' || event.target.type === 'radio' ? event.target.checked : event.target.value;\n            },\n            onInvalid: (errors) => {\n                _states[field].invalid = true;\n                _states[field].errors = errors;\n                _states[field].error = errors?.[0] ?? null;\n            }\n        });\n\n        const _watcher = watchPausable(\n            () => _states[field].value,\n            (newValue, oldValue) => {\n                if (_states[field].pristine) {\n                    _states[field].pristine = false;\n                }\n\n                if (newValue !== oldValue) {\n                    _states[field].dirty = true;\n                }\n\n                validateFieldOn(field, fieldOptions, 'validateOnValueUpdate', true);\n            }\n        );\n\n        fields[field] = { props, states: _states[field], options: fieldOptions, _watcher };\n\n        return [_states[field], props];\n    };\n\n    const handleSubmit = (callback) => {\n        return async (event) => {\n            const results = await validateOn('validateOnSubmit', true);\n\n            return callback({\n                originalEvent: event,\n                valid: toValue(valid),\n                states: toValue(states),\n                reset,\n                ...results\n            });\n        };\n    };\n\n    const handleReset = (callback) => {\n        return async (event) => {\n            reset();\n\n            return callback({\n                originalEvent: event\n            });\n        };\n    };\n\n    const validate = async (field) => {\n        const resolverOptions = Object.entries(_states).reduce(\n            (acc, [key, val]) => {\n                acc.names.push(key);\n                acc.values[key] = val.value;\n\n                return acc;\n            },\n            { names: [], values: {} }\n        );\n\n        const [names, values] = [resolverOptions.names, groupKeys(resolverOptions.values)];\n\n        let result = (await options.resolver?.({ names, values })) ?? { values };\n\n        result.errors ??= {};\n\n        const flattenFields = [field].flat();\n\n        for (const [fieldName, fieldInst] of Object.entries(fields)) {\n            if (flattenFields.includes(fieldName) || !field || isEmpty(result.errors)) {\n                const fieldResolver = fieldInst.options?.resolver;\n\n                if (fieldResolver) {\n                    const fieldValue = fieldInst.states.value;\n                    const fieldResult = (await fieldResolver({ values: fieldValue, value: fieldValue, name: fieldName })) ?? { values: fieldValue };\n\n                    isArray(fieldResult.errors) && (fieldResult.errors = { [fieldName]: fieldResult.errors });\n\n                    result = mergeKeys(result, fieldResult);\n                }\n\n                const errors = getValueByPath(result.errors, fieldName) ?? [];\n\n                //const value = result.values?.[fieldName] ?? _states[sField].value;\n                _states[fieldName].invalid = errors.length > 0;\n                _states[fieldName].valid = !_states[fieldName].invalid;\n                _states[fieldName].errors = errors;\n                _states[fieldName].error = errors?.[0] ?? null;\n                //states[fieldName].value = value;\n            }\n        }\n\n        return {\n            ...result,\n            errors: groupKeys(result.errors)\n        };\n    };\n\n    const reset = () => {\n        Object.keys(_states).forEach(async (field) => {\n            const watcher = fields[field]._watcher;\n\n            watcher.pause();\n            fields[field].states = _states[field] = getInitialState(field, fields[field]?.options?.initialValue);\n            await nextTick();\n            watcher.resume();\n        });\n    };\n\n    const setFieldValue = (field, value) => {\n        if (_states[field] !== undefined) _states[field].value = value;\n    };\n\n    const getFieldState = (field) => {\n        return fields[field]?.states;\n    };\n\n    const setValues = (values) => {\n        Object.keys(values).forEach((field) => setFieldValue(field, values[field]));\n    };\n\n    const validateOnMounted = () => {\n        validateOn('validateOnMount');\n    };\n\n    tryOnMounted(validateOnMounted);\n\n    return {\n        defineField,\n        setFieldValue,\n        getFieldState,\n        handleSubmit,\n        handleReset,\n        validate,\n        setValues,\n        reset,\n        valid,\n        states,\n        fields\n    };\n};\n"], "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "enumerable", "configurable", "writable", "_invoke", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "Array", "from", "test", "next", "push", "isArray", "tryOnMounted", "fn", "sync", "undefined", "getCurrentInstance", "onMounted", "nextTick", "watchPausable", "source", "callback", "options", "isActive", "ref", "stop", "watch", "newValue", "oldValue", "pause", "resume", "groupKeys", "obj", "entries", "reduce", "result", "_ref", "_ref2", "key", "split", "filter", "Boolean", "acc", "curr", "idx", "arr", "_acc$curr", "isNaN", "getValueByPath", "path", "isNotEmpty", "_unused", "keys", "useForm", "_states", "reactive", "fields", "valid", "computed", "values", "every", "field", "invalid", "states", "getInitialState", "initialValue", "initialValues", "touched", "dirty", "pristine", "error", "errors", "isFieldValidate", "validateOn", "includes", "_ref3", "_callee", "option", "defaultValue", "_options$option", "results", "fieldArr", "_t", "_t2", "_context", "validate", "_fields$field", "_x", "_x2", "validateFieldOn", "fieldOptions", "_fieldOptions$option", "_options$option2", "defineField", "_fields$field2", "_resolve", "_watcher", "props", "mergeProps", "onBlur", "onInput", "event", "hasOwn", "target", "onChange", "type", "checked", "onInvalid", "_errors$", "handleSubmit", "_ref4", "_callee2", "_context2", "_objectSpread", "originalEvent", "toValue", "reset", "_x3", "handleReset", "_ref5", "_callee3", "_context3", "_x4", "_ref6", "_callee4", "_yield$options$resolv", "_options$resolver", "_result", "_result$errors", "resolverOptions", "_ref9", "names", "flattenFields", "_i", "_Object$entries", "_Object$entries$_i", "fieldName", "fieldInst", "_fieldInst$options", "_getValueByPath", "_errors$2", "fieldResolver", "_yield$fieldResolver", "fieldValue", "fieldResult", "_t3", "_t4", "_t5", "_t6", "_t7", "_t8", "_context4", "_ref7", "_ref8", "val", "resolver", "flat", "isEmpty", "_defineProperty", "mergeKeys", "_x5", "for<PERSON>ach", "_ref0", "_callee5", "_fields$field3", "watcher", "_context5", "_x6", "setFieldValue", "getFieldState", "_fields$field4", "set<PERSON><PERSON><PERSON>", "validateOnMounted"], "mappings": ";;;;;;;;;AACA,SAAA,YAAA,GAAA,qKAAA,IAAAA,CAAA,EAAAC,CAAA,EAAAC,CAAA,GAAAC,UAAAA,IAAAA,OAAAA,MAAA,GAAAA,MAAA,GAAAC,EAAAA,EAAAA,CAAA,GAAAF,CAAA,CAAAG,QAAA,IAAA,YAAA,EAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,IAAA,eAAA,CAAA,CAAA,SAAAC,EAAAN,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAC,EAAAA,IAAAA,CAAA,GAAAL,CAAA,IAAAA,CAAA,CAAAM,SAAA,YAAAC,SAAA,GAAAP,CAAA,GAAAO,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,CAAAK,CAAAA,CAAAA,OAAAA,mBAAA,CAAAH,CAAA,EAAAV,SAAAA,EAAAA,UAAAA,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAA,EAAA,IAAAE,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAI,CAAA,GAAA,CAAA,EAAAC,CAAA,GAAAX,CAAA,IAAA,EAAA,EAAAY,CAAA,GAAA,KAAA,EAAAC,CAAA,GAAAF,EAAAA,CAAA,EAAAb,CAAAA,EAAAA,CAAA,KAAAgB,CAAA,EAAApB,CAAA,EAAAqB,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAC,IAAA,CAAAvB,CAAA,EAAA,CAAA,CAAA,EAAAsB,CAAA,EAAA,SAAAA,EAAArB,CAAA,EAAAC,CAAA,EAAA,EAAA,OAAAM,CAAA,GAAAP,CAAA,EAAAQ,CAAA,GAAAG,CAAAA,EAAAA,CAAA,GAAAZ,CAAA,EAAAmB,CAAA,CAAAf,CAAA,GAAAF,CAAA,EAAAmB,CAAA,CAAAC,EAAAA,EAAAA,CAAAA,CAAAA,SAAAA,CAAAA,CAAApB,CAAA,EAAAE,CAAA,EAAAK,EAAAA,KAAAA,CAAA,GAAAP,CAAA,EAAAU,CAAA,GAAAR,CAAA,EAAAH,CAAA,GAAA,CAAA,EAAA,CAAAiB,CAAA,IAAAF,CAAA,IAAAV,CAAAA,CAAA,IAAAL,CAAA,GAAAgB,CAAA,CAAAO,MAAA,EAAAvB,CAAA,EAAA,EAAA,EAAA,IAAAK,CAAA,EAAAE,CAAA,GAAAS,CAAA,CAAAhB,CAAA,CAAAqB,EAAAA,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAQ,CAAA,GAAAjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAAN,CAAA,GAAAI,CAAAA,GAAAA,CAAAA,CAAA,GAAAmB,CAAA,KAAArB,CAAA,MAAAK,CAAA,GAAAD,CAAA,CAAAI,CAAAA,CAAAA,IAAAA,CAAAA,EAAAA,CAAA,GAAAJ,CAAA,CAAAR,CAAAA,CAAAA,KAAAA,CAAA,GAAAQ,CAAA,MAAAA,CAAA,CAAA,CAAA,CAAA,EAAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAAA,CAAA,CAAAR,CAAAA,CAAAA,GAAAA,CAAA,CAAAQ,GAAAA,CAAA,OAAAc,CAAA,KAAA,CAAAhB,CAAA,GAAAJ,CAAA,GAAA,CAAA,IAAAoB,CAAA,GAAAd,CAAA,CAAAC,CAAAA,CAAAA,KAAAA,CAAA,GAAAU,CAAAA,EAAAA,CAAA,CAAAC,CAAA,GAAAhB,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAI,CAAA,CAAAc,CAAAA,CAAAA,IAAAA,CAAA,GAAAG,CAAA,KAAAnB,CAAA,GAAAJ,CAAA,GAAA,CAAA,IAAAM,CAAA,CAAAJ,CAAAA,CAAAA,GAAAA,CAAA,IAAAA,CAAA,GAAAqB,CAAA,CAAA,KAAAjB,CAAA,CAAA,CAAA,CAAA,GAAAN,CAAA,EAAAM,CAAA,CAAA,CAAA,CAAA,GAAAJ,CAAA,EAAAe,CAAA,CAAAf,CAAA,GAAAqB,CAAA,EAAAhB,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAAH,CAAA,IAAAJ,CAAA,GAAAmB,CAAAA,EAAAA,OAAAA,CAAA,CAAAH,CAAAA,MAAAA,CAAA,GAAAd,IAAAA,EAAAA,CAAA,qBAAAE,CAAA,EAAAW,CAAA,EAAAQ,CAAA,EAAAT,EAAAA,IAAAA,CAAA,GAAAU,CAAAA,EAAAA,MAAAA,SAAA,uCAAAR,CAAA,IAAA,CAAA,KAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAQ,CAAA,CAAAhB,EAAAA,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAa,CAAA,EAAA,CAAAxB,CAAA,GAAAQ,CAAA,GAAAT,CAAAA,GAAAA,CAAA,GAAAY,CAAA,KAAAM,CAAAA,CAAA,KAAAV,CAAA,KAAAC,CAAA,GAAAA,CAAA,GAAA,CAAA,IAAAA,CAAA,GAAA,CAAA,KAAAU,CAAA,CAAAf,CAAA,GAAAkB,EAAAA,CAAAA,EAAAA,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAf,CAAA,GAAAQ,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,CAAA,CAAA,CAAA,IAAA,EAAA,IAAAI,CAAA,GAAA,CAAA,EAAAR,CAAA,EAAA,EAAA,IAAAC,CAAA,KAAAH,CAAA,GAAAL,MAAAA,CAAAA,EAAAA,CAAA,GAAAO,CAAA,CAAAF,CAAA,CAAA,EAAA,EAAA,IAAA,EAAAL,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,EAAAI,CAAA,CAAAc,CAAAA,EAAAA,MAAAA,SAAA,2CAAAzB,CAAA,CAAA2B,IAAA,EAAA,OAAA3B,CAAA,CAAAW,CAAAA,CAAA,GAAAX,CAAA,CAAA4B,KAAA,EAAApB,CAAA,GAAA,CAAA,KAAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,KAAAA,CAAA,KAAAR,CAAA,GAAAO,CAAA,CAAAP,QAAAA,CAAAA,CAAAA,IAAAA,CAAA,CAAA0B,IAAA,CAAAnB,CAAA,CAAA,EAAAC,CAAA,GAAAG,CAAAA,KAAAA,CAAA,GAAAc,SAAA,CAAApB,mCAAAA,GAAAA,CAAA,GAAAG,UAAAA,CAAAA,EAAAA,CAAA,OAAAD,CAAA,GAAAR,CAAA,CAAA,EAAA,MAAA,IAAA,CAAAC,CAAA,GAAAiB,CAAAA,CAAA,GAAAC,CAAA,CAAAf,CAAA,GAAA,CAAA,IAAAQ,CAAA,GAAAV,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,EAAAe,CAAA,CAAAE,MAAAA,CAAA,kBAAApB,CAAA,EAAA,EAAAO,CAAA,GAAAR,CAAA,EAAAS,CAAA,GAAAG,CAAAA,EAAAA,CAAA,GAAAX,CAAA,CAAAe,EAAAA,SAAAA,EAAAA,CAAA,mBAAAa,KAAA,EAAA5B,CAAA,EAAA2B,IAAA,EAAAV,CAAA,EAAAhB,CAAAA,EAAAA,CAAAA,EAAAA,CAAAA,CAAA,EAAAI,CAAA,EAAAE,CAAA,CAAA,EAAA,IAAA,CAAA,EAAAI,CAAA,CAAA,EAAA,CAAA,IAAAS,CAAA,GAAA,EAAA,CAAA,CAAA,SAAAV,YAAAmB,EAAAA,CAAAA,SAAAA,iBAAAA,GAAAC,EAAAA,CAAAA,SAAAA,0BAAAA,GAAA9B,EAAAA,CAAAA,CAAA,GAAAY,MAAA,CAAAmB,cAAA,CAAA,CAAA,IAAAvB,CAAA,GAAA,EAAA,CAAAL,CAAA,CAAA,GAAAH,CAAA,CAAAA,CAAA,CAAAG,EAAAA,CAAAA,CAAA,CAAAW,EAAAA,CAAAA,CAAAA,IAAAA,mBAAA,CAAAd,CAAA,GAAA,EAAA,EAAAG,CAAA,EAAA,YAAA,EAAA,OAAA,IAAA,CAAA,EAAA,CAAA,EAAAH,CAAA,CAAAW,EAAAA,CAAA,GAAAmB,0BAAA,CAAArB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAO,CAAAA,CAAAA,SAAAA,CAAAA,CAAAhB,CAAA,EAAAa,EAAAA,OAAAA,MAAA,CAAAoB,cAAA,GAAApB,MAAA,CAAAoB,cAAA,CAAAjC,CAAA,EAAA+B,0BAAA,CAAA,IAAA/B,CAAA,CAAAkC,SAAA,GAAAH,0BAAA,EAAAhB,mBAAA,CAAAf,CAAA,EAAAM,CAAA,EAAA,mBAAA,CAAA,CAAA,EAAAN,CAAA,CAAAU,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,CAAA,EAAAZ,CAAA,CAAA,EAAA,CAAA,OAAA8B,iBAAA,CAAApB,SAAA,GAAAqB,0BAAA,EAAAhB,mBAAA,CAAAH,CAAA,iBAAAmB,0BAAA,CAAA,EAAAhB,mBAAA,CAAAgB,0BAAA,EAAAD,aAAAA,EAAAA,iBAAA,CAAAA,EAAAA,iBAAA,CAAAK,WAAA,GAAA,mBAAA,EAAApB,mBAAA,CAAAgB,0BAAA,EAAAzB,CAAA,EAAA,mBAAA,CAAA,EAAAS,mBAAA,CAAAH,CAAA,CAAAG,EAAAA,mBAAA,CAAAH,CAAA,EAAAN,CAAA,EAAA,WAAA,CAAA,EAAAS,mBAAA,CAAAH,CAAA,EAAAR,CAAA,EAAAW,YAAAA,EAAAA,OAAAA,IAAAA,CAAAA,EAAAA,CAAAA,EAAAA,mBAAA,CAAAH,CAAA,8DAAAwB,YAAA,GAAA,SAAAA,YAAA,GAAA,EAAA,OAAA,EAAAC,CAAA,EAAA7B,CAAA,EAAA8B,CAAA,EAAAtB,CAAA,EAAA,CAAA,EAAA,GAAA,CAAA;AAAA,SAAAD,mBAAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,EAAA,EAAA,IAAAO,CAAA,GAAAK,MAAA,CAAA0B,cAAA,QAAA/B,CAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,OAAAR,CAAA,EAAAQ,EAAAA,CAAA,GAAAO,CAAAA,CAAAA,EAAAA,CAAAA,mBAAA,GAAAyB,SAAAA,kBAAAA,CAAAxC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,EAAAC,EAAAA,IAAAA,CAAA,EAAAM,CAAA,GAAAA,CAAA,CAAAR,CAAA,EAAAE,CAAA,EAAA2B,EAAAA,KAAA,EAAAzB,CAAA,EAAAqC,UAAA,EAAA,CAAAxC,CAAA,EAAAyC,YAAA,EAAAzC,CAAAA,CAAA,EAAA0C,QAAA,EAAA,CAAA1C,CAAA,EAAAD,CAAAA,GAAAA,CAAA,CAAAE,CAAA,CAAAE,GAAAA,CAAA,YAAAE,CAAA,GAAA,SAAAA,EAAAJ,CAAA,EAAAE,CAAA,EAAAW,EAAAA,mBAAA,CAAAf,CAAA,EAAAE,CAAA,YAAAF,CAAA,EAAA,EAAA,OAAA,IAAA,CAAA4C,OAAA,CAAA1C,CAAA,EAAAE,CAAA,EAAAJ,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAAM,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,EAAAA,CAAA,CAAAA,OAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAAA,CAAAS,QAAAA,EAAAA,CAAAA,CAAAA,CAAAA,EAAAA,EAAAA,EAAAA,mBAAA,CAAAf,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAH,CAAA,CAAA,CAAA;AAAA,SAAA4C,kBAAAA,CAAAzC,CAAA,EAAAH,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAI,CAAA,EAAAe,CAAA,EAAAZ,CAAA,EAAA,EAAA,IAAA,EAAA,IAAAD,CAAA,GAAAJ,CAAA,CAAAiB,CAAA,CAAAZ,CAAAA,CAAA,CAAAG,EAAAA,CAAA,GAAAJ,CAAA,CAAAqB,KAAA,CAAA,EAAA,CAAA,OAAAzB,CAAA,EAAA,EAAA,OAAA,KAAAJ,CAAA,CAAAI,CAAA,CAAAI,CAAAA,EAAAA,CAAAA,CAAA,CAAAoB,IAAA,GAAA3B,CAAA,CAAAW,CAAA,CAAA,GAAAkC,OAAA,CAAAC,OAAA,CAAAnC,CAAA,CAAA,CAAAoC,IAAA,CAAA9C,CAAA,EAAAI,CAAA,CAAA,CAAA;AAAA,SAAA2C,iBAAAA,CAAA7C,CAAA,EAAA,EAAA,OAAA,YAAA,EAAA,IAAAH,CAAA,GAAA,IAAA,EAAAD,CAAA,GAAAkD,SAAA,CAAA,CAAA,OAAA,IAAAJ,OAAA,CAAA,UAAA5C,CAAA,EAAAI,CAAA,EAAA,EAAA,IAAAe,CAAA,GAAAjB,CAAA,CAAA+C,KAAA,CAAAlD,CAAA,EAAAD,CAAA,CAAA,CAAA,CAAA,SAAAoD,KAAAhD,CAAAA,CAAA,EAAAyC,EAAAA,kBAAA,CAAAxB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA8C,KAAA,EAAAC,MAAA,EAAAjD,MAAAA,EAAAA,CAAA,CAAAiD,CAAAA,EAAAA,CAAAA,SAAAA,MAAAA,CAAAjD,CAAA,EAAA,EAAAyC,kBAAA,CAAAxB,CAAA,EAAAnB,CAAA,EAAAI,CAAA,EAAA8C,KAAA,EAAAC,MAAA,EAAA,OAAA,EAAAjD,CAAA,CAAA,CAAA,EAAA,CAAAgD,KAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA;AAAA,SAAAE,cAAAA,CAAApD,CAAA,EAAAF,CAAA,WAAAuD,eAAA,CAAArD,CAAA,CAAAsD,IAAAA,qBAAA,CAAAtD,CAAA,EAAAF,CAAA,CAAAyD,IAAAA,2BAAA,CAAAvD,CAAA,EAAAF,CAAA,CAAA,IAAA0D,gBAAA,EAAA,CAAA;AAAA,SAAAA,gBAAAA,eAAAhC,SAAA,CAAA,2IAAA,CAAA,CAAA;AAAA,SAAA+B,2BAAAA,CAAAvD,CAAA,EAAAmB,CAAA,EAAA,EAAA,IAAAnB,CAAA,EAAA,EAAA,IAAA,QAAA,IAAA,OAAAA,CAAA,EAAA,OAAAyD,iBAAA,CAAAzD,CAAA,EAAAmB,CAAA,CAAA,CAAA,CAAA,IAAApB,CAAA,GAAA,EAAA,CAAA2D,QAAA,CAAAjC,IAAA,CAAAzB,CAAA,CAAA,CAAA2D,KAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,QAAA,KAAA5D,CAAA,IAAAC,CAAA,CAAA4D,WAAA,KAAA7D,CAAA,GAAAC,CAAA,CAAA4D,WAAA,CAAAC,IAAA,CAAA,EAAA,KAAA,KAAA9D,CAAA,IAAA,KAAA,KAAAA,CAAA,GAAA+D,KAAA,CAAAC,IAAA,CAAA/D,CAAA,CAAA,GAAA,WAAA,KAAAD,CAAA,IAAA,0CAAA,CAAAiE,IAAA,CAAAjE,CAAA,CAAA,GAAA0D,iBAAA,CAAAzD,CAAA,EAAAmB,CAAA,CAAA,GAAA,MAAA,CAAA,EAAA;AAAA,SAAAsC,kBAAAzD,CAAA,EAAAmB,CAAA,EAAAA,EAAAA,CAAAA,IAAAA,IAAAA,CAAA,IAAAA,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,MAAAH,CAAA,GAAAnB,CAAA,CAAAsB,MAAA,CAAAxB,CAAAA,CAAAA,KAAAA,IAAAA,CAAA,GAAAI,CAAAA,EAAAA,CAAA,GAAA4D,KAAA,CAAA3C,CAAA,CAAArB,EAAAA,CAAA,GAAAqB,CAAA,EAAArB,CAAA,EAAAI,EAAAA,CAAA,CAAAJ,CAAA,CAAA,GAAAE,CAAA,CAAAF,CAAA,UAAAI,CAAA,CAAA;AAAA,SAAAoD,qBAAAA,CAAAtD,CAAA,EAAAuB,CAAA,EAAA,EAAA,IAAAxB,CAAA,GAAAC,IAAAA,IAAAA,CAAA,GAAAC,IAAAA,GAAAA,WAAAA,IAAAA,OAAAA,MAAA,IAAAD,CAAA,CAAAC,MAAA,CAAAE,QAAA,CAAA,IAAAH,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,IAAA,IAAA,IAAAD,CAAA,EAAAD,EAAAA,IAAAA,CAAA,EAAAI,CAAA,EAAAI,CAAA,EAAAI,CAAA,EAAAS,CAAA,GAAA,EAAA,EAAAL,CAAA,GAAA,IAAA,EAAAV,CAAA,GAAAE,KAAAA,CAAAA,CAAAA,IAAAA,EAAAA,IAAAA,CAAA,GAAAP,CAAAA,CAAA,GAAAA,CAAA,CAAA0B,IAAA,CAAAzB,CAAA,CAAA,EAAAiE,IAAA,EAAA,CAAA,KAAA1C,CAAA,EAAA,CAAAT,MAAAA,OAAAA,EAAAA,CAAA,GAAAhB,CAAAA,CAAA,GAAAQ,CAAA,CAAAmB,IAAA,CAAA1B,CAAA,CAAA,EAAA2B,IAAA,CAAA,KAAAP,CAAA,CAAA+C,IAAA,CAAApE,CAAA,CAAA6B,KAAA,CAAAR,EAAAA,CAAA,CAAAG,MAAA,KAAAC,CAAA,CAAAT,EAAAA,CAAA,iBAAAd,CAAA,EAAA,EAAAI,CAAA,GAAA,IAAA,EAAAF,CAAA,GAAAF,CAAA,CAAAc,EAAAA,SAAAA,EAAAA,IAAAA,EAAAA,IAAAA,CAAAA,CAAA,IAAAf,IAAAA,IAAAA,CAAA,CAAAW,QAAAA,CAAAA,KAAAA,CAAA,GAAAX,CAAA,CAAA,QAAA,CAAA,EAAA,EAAAY,MAAA,CAAAD,CAAA,CAAA,KAAAA,CAAA,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAAN,CAAA,EAAA,MAAAF,CAAA,CAAA,EAAA,EAAA,CAAA,OAAAiB,CAAA,CAAA,EAAA;AAAA,SAAAkC,eAAAA,CAAArD,CAAA,EAAA8D,EAAAA,IAAAA,KAAA,CAAAK,OAAA,CAAAnE,CAAA,CAAA,EAAA,OAAAA,CAAA,CAAA;AAEA,SAASoE,YAAYA,CAACC,EAAE,EAAe;AAAA,EAAA,IAAbC,IAAI,GAAAtB,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAuB,SAAA,GAAAvB,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI;EACjC,IAAIwB,kBAAkB,EAAE,EAAEC,SAAS,CAACJ,EAAE,CAAC,CAAC,KACnC,IAAIC,IAAI,EAAED,EAAE,EAAE,CAAC,KACfK,QAAQ,CAACL,EAAE,CAAC;AACrB;AAEA,SAASM,aAAaA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE;AAC9C,EAAA,IAAMC,QAAQ,GAAGC,GAAG,CAAC,IAAI,CAAC;EAE1B,IAAMC,IAAI,GAAGC,KAAK,CACdN,MAAM,EACN,UAACO,QAAQ,EAAEC,QAAQ,EAAK;AACpB,IAAA,IAAI,CAACL,QAAQ,CAACpD,KAAK,EAAE;AACrBkD,IAAAA,QAAQ,CAACM,QAAQ,EAAEC,QAAQ,CAAC;GAC/B,EACDN,OACJ,CAAC;EAED,OAAO;AACHG,IAAAA,IAAI,EAAJA,IAAI;AACJI,IAAAA,KAAK,EAAE,SAAPA,KAAKA,GAAQ;MACTN,QAAQ,CAACpD,KAAK,GAAG,KAAK;KACzB;AACD2D,IAAAA,MAAM,EAAE,SAARA,MAAMA,GAAQ;MACVP,QAAQ,CAACpD,KAAK,GAAG,IAAI;AACzB;GACH;AACL;;AAEA;AACA,SAAS4D,SAASA,CAACC,GAAG,EAAE;AACpB,EAAA,OAAO7E,MAAM,CAAC8E,OAAO,CAACD,GAAG,CAAC,CAACE,MAAM,CAAC,UAACC,MAAM,EAAAC,IAAA,EAAmB;AAAA,IAAA,IAAAC,KAAA,GAAAzC,cAAA,CAAAwC,IAAA,EAAA,CAAA,CAAA;AAAhBE,MAAAA,GAAG,GAAAD,KAAA,CAAA,CAAA,CAAA;AAAElE,MAAAA,KAAK,GAAAkE,KAAA,CAAA,CAAA,CAAA;AAClD;IACAC,GAAG,CAACC,KAAK,CAAC,WAAW,CAAC,CACjBC,MAAM,CAACC,OAAO,CAAC,CACfP,MAAM,CAAC,UAACQ,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAA;AAAA,MAAA,IAAAC,SAAA;AAAA,MAAA,OAAA,CAAAA,SAAA,GAAMJ,GAAG,CAACC,IAAI,CAAC,MAAAG,IAAAA,IAAAA,SAAA,KAAAA,MAAAA,GAAAA,SAAA,GAATJ,GAAG,CAACC,IAAI,CAAC,GAAKI,KAAK,CAACF,GAAG,CAACD,GAAG,GAAG,CAAC,CAAC,CAAC,GAAIA,GAAG,KAAKC,GAAG,CAAC/E,MAAM,GAAG,CAAC,GAAGK,KAAK,GAAG,EAAE,GAAI,EAAE;KAAC,EAAEgE,MAAM,CAAC;AAE9H,IAAA,OAAOA,MAAM;GAChB,EAAE,EAAE,CAAC;AACV;AAEA,SAASa,cAAcA,CAAChB,GAAG,EAAEiB,IAAI,EAAE;AAC/B,EAAA,IAAI,CAACjB,GAAG,IAAI,CAACiB,IAAI,EAAE;AACf;AACA,IAAA,OAAO,IAAI;AACf;EAEA,IAAI;AACA,IAAA,IAAM9E,KAAK,GAAG6D,GAAG,CAACiB,IAAI,CAAC;AAEvB,IAAA,IAAIC,UAAU,CAAC/E,KAAK,CAAC,EAAE,OAAOA,KAAK;GACtC,CAAC,OAAAgF,OAAA,EAAM;AACJ;AAAA;;AAGJ;AACA,EAAA,IAAMC,IAAI,GAAGH,IAAI,CAACV,KAAK,CAAC,WAAW,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;AAEpD,EAAA,OAAOW,IAAI,CAAClB,MAAM,CAAC,UAACQ,GAAG,EAAEJ,GAAG,EAAA;AAAA,IAAA,OAAMI,GAAG,IAAIA,GAAG,CAACJ,GAAG,CAAC,KAAKvB,SAAS,GAAG2B,GAAG,CAACJ,GAAG,CAAC,GAAGvB,SAAS;GAAC,EAAEiB,GAAG,CAAC;AACjG;IAEaqB,OAAO,GAAG,SAAVA,OAAOA,GAAqB;AAAA,EAAA,IAAjB/B,OAAO,GAAA9B,SAAA,CAAA1B,MAAA,GAAA,CAAA,IAAA0B,SAAA,CAAA,CAAA,CAAA,KAAAuB,SAAA,GAAAvB,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE;AAChC,EAAA,IAAM8D,OAAO,GAAGC,QAAQ,CAAC,EAAE,CAAC;AAC5B,EAAA,IAAMC,MAAM,GAAGD,QAAQ,CAAC,EAAE,CAAC;EAC3B,IAAME,KAAK,GAAGC,QAAQ,CAAC,YAAA;IAAA,OAAMvG,MAAM,CAACwG,MAAM,CAACL,OAAO,CAAC,CAACM,KAAK,CAAC,UAACC,KAAK,EAAA;MAAA,OAAK,CAACA,KAAK,CAACC,OAAO;KAAC,CAAA;GAAC,CAAA;EACrF,IAAMC,MAAM,GAAGL,QAAQ,CAAC,YAAA;IAAA,OAAM3B,SAAS,CAACuB,OAAO,CAAC;GAAC,CAAA;EAEjD,IAAMU,eAAe,GAAG,SAAlBA,eAAeA,CAAIH,KAAK,EAAEI,YAAY,EAAK;IAC7C,OAAO;AACH9F,MAAAA,KAAK,EAAE8F,YAAY,KAAZA,IAAAA,IAAAA,YAAY,cAAZA,YAAY,GAAIjB,cAAc,CAAC1B,OAAO,CAAC4C,aAAa,EAAEL,KAAK,CAAC;AACnEM,MAAAA,OAAO,EAAE,KAAK;AACdC,MAAAA,KAAK,EAAE,KAAK;AACZC,MAAAA,QAAQ,EAAE,IAAI;AACdZ,MAAAA,KAAK,EAAE,IAAI;AACXK,MAAAA,OAAO,EAAE,KAAK;AACdQ,MAAAA,KAAK,EAAE,IAAI;AACXC,MAAAA,MAAM,EAAE;KACX;GACJ;EAED,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIX,KAAK,EAAEY,UAAU,EAAK;AAC3C,IAAA,IAAMtG,KAAK,GAAGkB,OAAO,CAACoF,UAAU,EAAEZ,KAAK,CAAC;AAExC,IAAA,OAAO1F,KAAK,KAAK,IAAI,IAAKwC,OAAO,CAACxC,KAAK,CAAC,IAAIA,KAAK,CAACuG,QAAQ,CAACb,KAAK,CAAE;GACrE;AAED,EAAA,IAAMY,UAAU,gBAAA,YAAA;AAAA,IAAA,IAAAE,KAAA,GAAApF,iBAAA,cAAAb,YAAA,EAAA,CAAAE,CAAA,CAAG,SAAAgG,OAAAA,CAAOC,MAAM,EAAEC,YAAY,EAAA;AAAA,MAAA,IAAAC,eAAA;AAAA,MAAA,IAAAC,OAAA,EAAAC,QAAA,EAAAC,EAAA,EAAAC,GAAA;AAAA,MAAA,OAAAzG,YAAA,EAAA,CAAAC,CAAA,CAAA,UAAAyG,QAAA,EAAA;QAAA,OAAAA,CAAAA,EAAAA,QAAAA,QAAA,CAAA1I,CAAA;AAAA,UAAA,KAAA,CAAA;YACtCsI,OAAO,GAAG,EAAE;AAAA,YAAA,IAAA,CAEhBrE,OAAO,CAACW,OAAO,CAACuD,MAAM,CAAC,CAAC,EAAA;AAAAO,cAAAA,QAAA,CAAA1I,CAAA,GAAA,CAAA;AAAA,cAAA;AAAA;AAAA0I,YAAAA,QAAA,CAAA1I,CAAA,GAAA,CAAA;AAAA,YAAA,OAAoB2I,QAAQ,CAAC/D,OAAO,CAACuD,MAAM,CAAC,CAAC;AAAA,UAAA,KAAA,CAAA;YAAzCG,OAAO,GAAAI,QAAA,CAAA1H,CAAA;AAAA0H,YAAAA,QAAA,CAAA1I,CAAA,GAAA,CAAA;AAAA,YAAA;AAAA,UAAA,KAAA,CAAA;AAAAwI,YAAAA,EAAA,GAAAH,CAAAA,eAAA,GAAuCzD,OAAO,CAACuD,MAAM,CAAC,MAAA,IAAA,IAAAE,eAAA,KAAA,MAAA,GAAAA,eAAA,GAAID,YAAY;AAAA,YAAA,IAAA,CAAAI,EAAA,EAAA;AAAAE,cAAAA,QAAA,CAAA1I,CAAA,GAAA,CAAA;AAAA,cAAA;AAAA;AAAA0I,YAAAA,QAAA,CAAA1I,CAAA,GAAA,CAAA;YAAA,OAAsB2I,QAAQ,EAAE;AAAA,UAAA,KAAA,CAAA;YAA1BL,OAAO,GAAAI,QAAA,CAAA1H,CAAA;AAAA,UAAA,KAAA,CAAA;YAChHuH,QAAQ,GAAG9H,MAAM,CAACiG,IAAI,CAACI,MAAM,CAAC,CAAChB,MAAM,CAAC,UAACqB,KAAK,EAAA;AAAA,cAAA,IAAAyB,aAAA;cAAA,OAAAA,CAAAA,aAAA,GAAK9B,MAAM,CAACK,KAAK,CAAC,MAAA,IAAA,IAAAyB,aAAA,KAAAA,MAAAA,IAAAA,CAAAA,aAAA,GAAbA,aAAA,CAAehE,OAAO,MAAAgE,IAAAA,IAAAA,aAAA,uBAAtBA,aAAA,CAAyBT,MAAM,CAAC;AAAA,aAAA,CAAC,IAAI,EAAE;AAAAM,YAAAA,GAAA,GAE9FjC,UAAU,CAAC+B,QAAQ,CAAC;AAAA,YAAA,IAAA,CAAAE,GAAA,EAAA;AAAAC,cAAAA,QAAA,CAAA1I,CAAA,GAAA,CAAA;AAAA,cAAA;AAAA;AAAA0I,YAAAA,QAAA,CAAA1I,CAAA,GAAA,CAAA;YAAA,OAAqB2I,QAAQ,CAACJ,QAAQ,CAAC;AAAA,UAAA,KAAA,CAAA;YAAlCD,OAAO,GAAAI,QAAA,CAAA1H,CAAA;AAAA,UAAA,KAAA,CAAA;AAAA,YAAA,OAAA0H,QAAA,CAAAzH,CAAA,CAAA,CAAA,EAEzBqH,OAAO,CAAA;AAAA;AAAA,OAAA,EAAAJ,OAAA,CAAA;KACjB,CAAA,CAAA;AAAA,IAAA,OAAA,SATKH,UAAUA,CAAAc,EAAA,EAAAC,GAAA,EAAA;AAAA,MAAA,OAAAb,KAAA,CAAAlF,KAAA,CAAA,IAAA,EAAAD,SAAA,CAAA;AAAA,KAAA;GASf,EAAA;AAED,EAAA,IAAMiG,eAAe,GAAG,SAAlBA,eAAeA,CAAI5B,KAAK,EAAE6B,YAAY,EAAEb,MAAM,EAAEC,YAAY,EAAK;IAAA,IAAAa,oBAAA,EAAAC,gBAAA;AACnE,IAAA,CAAA,CAAAD,oBAAA,GAACD,YAAY,KAAZA,IAAAA,IAAAA,YAAY,uBAAZA,YAAY,CAAGb,MAAM,CAAC,cAAAc,oBAAA,KAAA,MAAA,GAAAA,oBAAA,GAAInB,eAAe,CAACX,KAAK,EAAA+B,CAAAA,gBAAA,GAAEtE,OAAO,CAACuD,MAAM,CAAC,cAAAe,gBAAA,KAAA,MAAA,GAAAA,gBAAA,GAAId,YAAY,CAAC,KAAKO,QAAQ,CAACxB,KAAK,CAAC;GACzG;EAED,IAAMgC,WAAW,GAAG,SAAdA,WAAWA,CAAIhC,KAAK,EAAE6B,YAAY,EAAK;IAAA,IAAAI,cAAA,EAAAC,QAAA;IACzC,IAAI,CAAClC,KAAK,EAAE;AACR;;MAEA,OAAO,EAAE,CAAC;AACd;AAEA,IAAA,CAAAiC,cAAA,GAAAtC,MAAM,CAACK,KAAK,CAAC,MAAA,IAAA,IAAAiC,cAAA,KAAA,MAAA,IAAbA,cAAA,CAAeE,QAAQ,CAACvE,IAAI,EAAE;IAE9B6B,OAAO,CAACO,KAAK,CAAC,KAAdP,OAAO,CAACO,KAAK,CAAC,GAAKG,eAAe,CAACH,KAAK,EAAE6B,YAAY,KAAZA,IAAAA,IAAAA,YAAY,uBAAZA,YAAY,CAAEzB,YAAY,CAAC,CAAA;AAErE,IAAA,IAAMgC,KAAK,GAAGC,UAAU,CAAAH,CAAAA,QAAA,GAAC1G,OAAO,CAACqG,YAAY,EAAEpC,OAAO,CAACO,KAAK,CAAC,CAAC,cAAAkC,QAAA,KAAA,MAAA,GAAA,MAAA,GAArCA,QAAA,CAAuCE,KAAK,EAAE5G,OAAO,CAACqG,YAAY,aAAZA,YAAY,KAAA,MAAA,GAAA,MAAA,GAAZA,YAAY,CAAEO,KAAK,EAAE3C,OAAO,CAACO,KAAK,CAAC,CAAC,EAAE;AACjHxD,MAAAA,IAAI,EAAEwD,KAAK;AACXsC,MAAAA,MAAM,EAAE,SAARA,MAAMA,GAAQ;AACV7C,QAAAA,OAAO,CAACO,KAAK,CAAC,CAACM,OAAO,GAAG,IAAI;AAC7BsB,QAAAA,eAAe,CAAC5B,KAAK,EAAE6B,YAAY,EAAE,gBAAgB,CAAC;OACzD;AACDU,MAAAA,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAK;QAChB/C,OAAO,CAACO,KAAK,CAAC,CAAC1F,KAAK,GAAGkI,KAAK,IAAIlJ,MAAM,CAACmJ,MAAM,CAACD,KAAK,EAAE,OAAO,CAAC,GAAGA,KAAK,CAAClI,KAAK,GAAGkI,KAAK,CAACE,MAAM,CAACpI,KAAK;OACnG;AACDqI,MAAAA,QAAQ,EAAE,SAAVA,QAAQA,CAAGH,KAAK,EAAK;QACjB/C,OAAO,CAACO,KAAK,CAAC,CAAC1F,KAAK,GAAGkI,KAAK,IAAIlJ,MAAM,CAACmJ,MAAM,CAACD,KAAK,EAAE,OAAO,CAAC,GAAGA,KAAK,CAAClI,KAAK,GAAGkI,KAAK,CAACE,MAAM,CAACE,IAAI,KAAK,UAAU,IAAIJ,KAAK,CAACE,MAAM,CAACE,IAAI,KAAK,OAAO,GAAGJ,KAAK,CAACE,MAAM,CAACG,OAAO,GAAGL,KAAK,CAACE,MAAM,CAACpI,KAAK;OAC9L;AACDwI,MAAAA,SAAS,EAAE,SAAXA,SAASA,CAAGpC,MAAM,EAAK;AAAA,QAAA,IAAAqC,QAAA;AACnBtD,QAAAA,OAAO,CAACO,KAAK,CAAC,CAACC,OAAO,GAAG,IAAI;AAC7BR,QAAAA,OAAO,CAACO,KAAK,CAAC,CAACU,MAAM,GAAGA,MAAM;QAC9BjB,OAAO,CAACO,KAAK,CAAC,CAACS,KAAK,GAAAsC,CAAAA,QAAA,GAAGrC,MAAM,KAAA,IAAA,IAANA,MAAM,KAANA,MAAAA,GAAAA,MAAAA,GAAAA,MAAM,CAAG,CAAC,CAAC,cAAAqC,QAAA,KAAA,MAAA,GAAAA,QAAA,GAAI,IAAI;AAC9C;AACJ,KAAC,CAAC;IAEF,IAAMZ,QAAQ,GAAG7E,aAAa,CAC1B,YAAA;AAAA,MAAA,OAAMmC,OAAO,CAACO,KAAK,CAAC,CAAC1F,KAAK;AAAA,KAAA,EAC1B,UAACwD,QAAQ,EAAEC,QAAQ,EAAK;AACpB,MAAA,IAAI0B,OAAO,CAACO,KAAK,CAAC,CAACQ,QAAQ,EAAE;AACzBf,QAAAA,OAAO,CAACO,KAAK,CAAC,CAACQ,QAAQ,GAAG,KAAK;AACnC;MAEA,IAAI1C,QAAQ,KAAKC,QAAQ,EAAE;AACvB0B,QAAAA,OAAO,CAACO,KAAK,CAAC,CAACO,KAAK,GAAG,IAAI;AAC/B;MAEAqB,eAAe,CAAC5B,KAAK,EAAE6B,YAAY,EAAE,uBAAuB,EAAE,IAAI,CAAC;AACvE,KACJ,CAAC;IAEDlC,MAAM,CAACK,KAAK,CAAC,GAAG;AAAEoC,MAAAA,KAAK,EAALA,KAAK;AAAElC,MAAAA,MAAM,EAAET,OAAO,CAACO,KAAK,CAAC;AAAEvC,MAAAA,OAAO,EAAEoE,YAAY;AAAEM,MAAAA,QAAQ,EAARA;KAAU;AAElF,IAAA,OAAO,CAAC1C,OAAO,CAACO,KAAK,CAAC,EAAEoC,KAAK,CAAC;GACjC;AAED,EAAA,IAAMY,YAAY,GAAG,SAAfA,YAAYA,CAAIxF,QAAQ,EAAK;AAC/B,IAAA,oBAAA,YAAA;MAAA,IAAAyF,KAAA,GAAAvH,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAO,SAAAmI,QAAAA,CAAOV,KAAK,EAAA;AAAA,QAAA,IAAArB,OAAA;AAAA,QAAA,OAAAtG,YAAA,EAAA,CAAAC,CAAA,CAAA,UAAAqI,SAAA,EAAA;UAAA,OAAAA,CAAAA,EAAAA,QAAAA,SAAA,CAAAtK,CAAA;AAAA,YAAA,KAAA,CAAA;AAAAsK,cAAAA,SAAA,CAAAtK,CAAA,GAAA,CAAA;AAAA,cAAA,OACO+H,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC;AAAA,YAAA,KAAA,CAAA;cAApDO,OAAO,GAAAgC,SAAA,CAAAtJ,CAAA;AAAA,cAAA,OAAAsJ,SAAA,CAAArJ,CAAA,CAEN0D,CAAAA,EAAAA,QAAQ,CAAA4F,aAAA,CAAA;AACXC,gBAAAA,aAAa,EAAEb,KAAK;AACpB5C,gBAAAA,KAAK,EAAE0D,OAAO,CAAC1D,KAAK,CAAC;AACrBM,gBAAAA,MAAM,EAAEoD,OAAO,CAACpD,MAAM,CAAC;AACvBqD,gBAAAA,KAAK,EAALA;eACGpC,EAAAA,OAAO,CACb,CAAC,CAAA;AAAA;AAAA,SAAA,EAAA+B,QAAA,CAAA;OACL,CAAA,CAAA;AAAA,MAAA,OAAA,UAAAM,GAAA,EAAA;AAAA,QAAA,OAAAP,KAAA,CAAArH,KAAA,CAAA,IAAA,EAAAD,SAAA,CAAA;AAAA,OAAA;AAAA,KAAA,EAAA;GACJ;AAED,EAAA,IAAM8H,WAAW,GAAG,SAAdA,WAAWA,CAAIjG,QAAQ,EAAK;AAC9B,IAAA,oBAAA,YAAA;MAAA,IAAAkG,KAAA,GAAAhI,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAO,SAAA4I,QAAAA,CAAOnB,KAAK,EAAA;AAAA,QAAA,OAAA3H,YAAA,EAAA,CAAAC,CAAA,CAAA,UAAA8I,SAAA,EAAA;UAAA,OAAAA,CAAAA,EAAAA,QAAAA,SAAA,CAAA/K,CAAA;AAAA,YAAA,KAAA,CAAA;AACf0K,cAAAA,KAAK,EAAE;AAAC,cAAA,OAAAK,SAAA,CAAA9J,CAAA,CAAA,CAAA,EAED0D,QAAQ,CAAC;AACZ6F,gBAAAA,aAAa,EAAEb;AACnB,eAAC,CAAC,CAAA;AAAA;AAAA,SAAA,EAAAmB,QAAA,CAAA;OACL,CAAA,CAAA;AAAA,MAAA,OAAA,UAAAE,GAAA,EAAA;AAAA,QAAA,OAAAH,KAAA,CAAA9H,KAAA,CAAA,IAAA,EAAAD,SAAA,CAAA;AAAA,OAAA;AAAA,KAAA,EAAA;GACJ;AAED,EAAA,IAAM6F,QAAQ,gBAAA,YAAA;IAAA,IAAAsC,KAAA,GAAApI,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAG,SAAAgJ,QAAAA,CAAO/D,KAAK,EAAA;AAAA,MAAA,IAAAgE,qBAAA,EAAAC,iBAAA,EAAAC,OAAA,EAAAC,cAAA;MAAA,IAAAC,eAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAxE,MAAA,EAAAxB,MAAA,EAAAiG,aAAA,EAAAC,EAAA,EAAAC,eAAA,EAAAC,kBAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,kBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAzE,MAAA,EAAA0E,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;AAAA,MAAA,OAAA5K,YAAA,EAAA,CAAAC,CAAA,CAAA,UAAA4K,SAAA,EAAA;QAAA,OAAAA,CAAAA,EAAAA,QAAAA,SAAA,CAAA7M,CAAA;AAAA,UAAA,KAAA,CAAA;AACnBuL,YAAAA,eAAe,GAAG9K,MAAM,CAAC8E,OAAO,CAACqB,OAAO,CAAC,CAACpB,MAAM,CAClD,UAACQ,GAAG,EAAA8G,KAAA,EAAiB;AAAA,cAAA,IAAAC,KAAA,GAAA7J,cAAA,CAAA4J,KAAA,EAAA,CAAA,CAAA;AAAdlH,gBAAAA,GAAG,GAAAmH,KAAA,CAAA,CAAA,CAAA;AAAEC,gBAAAA,GAAG,GAAAD,KAAA,CAAA,CAAA,CAAA;AACX/G,cAAAA,GAAG,CAACyF,KAAK,CAACzH,IAAI,CAAC4B,GAAG,CAAC;cACnBI,GAAG,CAACiB,MAAM,CAACrB,GAAG,CAAC,GAAGoH,GAAG,CAACvL,KAAK;AAE3B,cAAA,OAAOuE,GAAG;AACd,aAAC,EACD;AAAEyF,cAAAA,KAAK,EAAE,EAAE;AAAExE,cAAAA,MAAM,EAAE;AAAG,aAC5B,CAAC;YAAAuE,KAAA,GAEuB,CAACD,eAAe,CAACE,KAAK,EAAEpG,SAAS,CAACkG,eAAe,CAACtE,MAAM,CAAC,CAAC,EAA3EwE,KAAK,GAAAD,KAAA,CAAEvE,CAAAA,CAAAA,EAAAA,MAAM,GAAAuE,KAAA,CAAA,CAAA,CAAA;AAAAqB,YAAAA,SAAA,CAAA7M,CAAA,GAAA,CAAA;AAAA,YAAA,OAAA,CAAAoL,iBAAA,GAEAxG,OAAO,CAACqI,QAAQ,MAAA,IAAA,IAAA7B,iBAAA,KAAA,MAAA,GAAA,MAAA,GAAhBA,iBAAA,CAAA7J,IAAA,CAAAqD,OAAO,EAAY;AAAE6G,cAAAA,KAAK,EAALA,KAAK;AAAExE,cAAAA,MAAM,EAANA;AAAO,aAAC,CAAC;AAAA,UAAA,KAAA,CAAA;AAAAuF,YAAAA,GAAA,GAAArB,qBAAA,GAAA0B,SAAA,CAAA7L,CAAA;AAAAuL,YAAAA,GAAA,GAAAC,GAAA,KAAA,IAAA;AAAA,YAAA,IAAA,CAAAD,GAAA,EAAA;AAAAM,cAAAA,SAAA,CAAA7M,CAAA,GAAA,CAAA;AAAA,cAAA;AAAA;AAAAuM,YAAAA,GAAA,GAAApB,qBAAA,KAAA,MAAA;AAAA,UAAA,KAAA,CAAA;AAAA,YAAA,IAAA,CAAAoB,GAAA,EAAA;AAAAM,cAAAA,SAAA,CAAA7M,CAAA,GAAA,CAAA;AAAA,cAAA;AAAA;AAAAyM,YAAAA,GAAA,GAAAtB,qBAAA;AAAA0B,YAAAA,SAAA,CAAA7M,CAAA,GAAA,CAAA;AAAA,YAAA;AAAA,UAAA,KAAA,CAAA;AAAAyM,YAAAA,GAAA,GAAK;AAAExF,cAAAA,MAAM,EAANA;aAAQ;AAAA,UAAA,KAAA,CAAA;AAApExB,YAAAA,MAAM,GAAAgH,GAAA;AAEV,YAAA,CAAAnB,cAAA,GAAAD,CAAAA,OAAA,GAAA5F,MAAM,EAACoC,MAAM,MAAA,IAAA,IAAAyD,cAAA,KAAA,MAAA,GAAAA,cAAA,GAAbD,OAAA,CAAOxD,MAAM,GAAK,EAAE;AAEd6D,YAAAA,aAAa,GAAG,CAACvE,KAAK,CAAC,CAAC+F,IAAI,EAAE;YAAAvB,EAAA,GAAA,CAAA,EAAAC,eAAA,GAECnL,MAAM,CAAC8E,OAAO,CAACuB,MAAM,CAAC;AAAA,UAAA,KAAA,CAAA;AAAA,YAAA,IAAA,EAAA6E,EAAA,GAAAC,eAAA,CAAAxK,MAAA,CAAA,EAAA;AAAAyL,cAAAA,SAAA,CAAA7M,CAAA,GAAA,EAAA;AAAA,cAAA;AAAA;AAAA6L,YAAAA,kBAAA,GAAA3I,cAAA,CAAA0I,eAAA,CAAAD,EAAA,CAA/CG,EAAAA,CAAAA,CAAAA,EAAAA,SAAS,GAAAD,kBAAA,CAAEE,CAAAA,CAAAA,EAAAA,SAAS,GAAAF,kBAAA,CAAA,CAAA,CAAA;AAAA,YAAA,IAAA,EACxBH,aAAa,CAAC1D,QAAQ,CAAC8D,SAAS,CAAC,IAAI,CAAC3E,KAAK,IAAIgG,OAAO,CAAC1H,MAAM,CAACoC,MAAM,CAAC,CAAA,EAAA;AAAAgF,cAAAA,SAAA,CAAA7M,CAAA,GAAA,EAAA;AAAA,cAAA;AAAA;YAC/DmM,aAAa,GAAA,CAAAH,kBAAA,GAAGD,SAAS,CAACnH,OAAO,MAAA,IAAA,IAAAoH,kBAAA,KAAA,MAAA,GAAA,MAAA,GAAjBA,kBAAA,CAAmBiB,QAAQ;AAAA,YAAA,IAAA,CAE7Cd,aAAa,EAAA;AAAAU,cAAAA,SAAA,CAAA7M,CAAA,GAAA,EAAA;AAAA,cAAA;AAAA;AACPqM,YAAAA,UAAU,GAAGN,SAAS,CAAC1E,MAAM,CAAC5F,KAAK;AAAAoL,YAAAA,SAAA,CAAA7M,CAAA,GAAA,CAAA;AAAA,YAAA,OACdmM,aAAa,CAAC;AAAElF,cAAAA,MAAM,EAAEoF,UAAU;AAAE5K,cAAAA,KAAK,EAAE4K,UAAU;AAAE1I,cAAAA,IAAI,EAAEmI;AAAU,aAAC,CAAC;AAAA,UAAA,KAAA,CAAA;AAAAa,YAAAA,GAAA,GAAAP,oBAAA,GAAAS,SAAA,CAAA7L,CAAA;AAAA0L,YAAAA,GAAA,GAAAC,GAAA,KAAA,IAAA;AAAA,YAAA,IAAA,CAAAD,GAAA,EAAA;AAAAG,cAAAA,SAAA,CAAA7M,CAAA,GAAA,CAAA;AAAA,cAAA;AAAA;AAAA0M,YAAAA,GAAA,GAAAN,oBAAA,KAAA,MAAA;AAAA,UAAA,KAAA,CAAA;AAAA,YAAA,IAAA,CAAAM,GAAA,EAAA;AAAAG,cAAAA,SAAA,CAAA7M,CAAA,GAAA,CAAA;AAAA,cAAA;AAAA;AAAA4M,YAAAA,GAAA,GAAAR,oBAAA;AAAAS,YAAAA,SAAA,CAAA7M,CAAA,GAAA,CAAA;AAAA,YAAA;AAAA,UAAA,KAAA,CAAA;AAAA4M,YAAAA,GAAA,GAAK;AAAE3F,cAAAA,MAAM,EAAEoF;aAAY;AAAA,UAAA,KAAA,CAAA;AAAzHC,YAAAA,WAAW,GAAAM,GAAA;AAEjB3I,YAAAA,OAAO,CAACqI,WAAW,CAACzE,MAAM,CAAC,KAAKyE,WAAW,CAACzE,MAAM,GAAAuF,eAAA,KAAMtB,SAAS,EAAGQ,WAAW,CAACzE,MAAM,CAAE,CAAC;AAEzFpC,YAAAA,MAAM,GAAG4H,SAAS,CAAC5H,MAAM,EAAE6G,WAAW,CAAC;AAAC,UAAA,KAAA,EAAA;AAGtCzE,YAAAA,MAAM,IAAAoE,eAAA,GAAG3F,cAAc,CAACb,MAAM,CAACoC,MAAM,EAAEiE,SAAS,CAAC,MAAAG,IAAAA,IAAAA,eAAA,cAAAA,eAAA,GAAI,EAAE,CAE7D;YACArF,OAAO,CAACkF,SAAS,CAAC,CAAC1E,OAAO,GAAGS,MAAM,CAACzG,MAAM,GAAG,CAAC;AAC9CwF,YAAAA,OAAO,CAACkF,SAAS,CAAC,CAAC/E,KAAK,GAAG,CAACH,OAAO,CAACkF,SAAS,CAAC,CAAC1E,OAAO;AACtDR,YAAAA,OAAO,CAACkF,SAAS,CAAC,CAACjE,MAAM,GAAGA,MAAM;YAClCjB,OAAO,CAACkF,SAAS,CAAC,CAAClE,KAAK,GAAAsE,CAAAA,SAAA,GAAGrE,MAAM,KAAA,IAAA,IAANA,MAAM,KAANA,MAAAA,GAAAA,MAAAA,GAAAA,MAAM,CAAG,CAAC,CAAC,cAAAqE,SAAA,KAAA,MAAA,GAAAA,SAAA,GAAI,IAAI;AAC9C;AAAA,UAAA,KAAA,EAAA;YAAAP,EAAA,EAAA;AAAAkB,YAAAA,SAAA,CAAA7M,CAAA,GAAA,CAAA;AAAA,YAAA;AAAA,UAAA,KAAA,EAAA;YAAA,OAAA6M,SAAA,CAAA5L,CAAA,CAAA,CAAA,EAAAsJ,aAAA,CAAAA,aAAA,KAKD9E,MAAM,CAAA,EAAA,EAAA,EAAA;AACToC,cAAAA,MAAM,EAAExC,SAAS,CAACI,MAAM,CAACoC,MAAM;AAAC,aAAA,CAAA,CAAA;AAAA;AAAA,OAAA,EAAAqD,QAAA,CAAA;KAEvC,CAAA,CAAA;IAAA,OA/CKvC,SAAAA,QAAQA,CAAA2E,GAAA,EAAA;AAAA,MAAA,OAAArC,KAAA,CAAAlI,KAAA,CAAA,IAAA,EAAAD,SAAA,CAAA;AAAA,KAAA;GA+Cb,EAAA;AAED,EAAA,IAAM4H,KAAK,GAAG,SAARA,KAAKA,GAAS;AAChBjK,IAAAA,MAAM,CAACiG,IAAI,CAACE,OAAO,CAAC,CAAC2G,OAAO,cAAA,YAAA;MAAA,IAAAC,KAAA,GAAA3K,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAAC,SAAAuL,QAAAA,CAAOtG,KAAK,EAAA;AAAA,QAAA,IAAAuG,cAAA;AAAA,QAAA,IAAAC,OAAA;AAAA,QAAA,OAAA3L,YAAA,EAAA,CAAAC,CAAA,CAAA,UAAA2L,SAAA,EAAA;UAAA,OAAAA,CAAAA,EAAAA,QAAAA,SAAA,CAAA5N,CAAA;AAAA,YAAA,KAAA,CAAA;AAC/B2N,cAAAA,OAAO,GAAG7G,MAAM,CAACK,KAAK,CAAC,CAACmC,QAAQ;cAEtCqE,OAAO,CAACxI,KAAK,EAAE;AACf2B,cAAAA,MAAM,CAACK,KAAK,CAAC,CAACE,MAAM,GAAGT,OAAO,CAACO,KAAK,CAAC,GAAGG,eAAe,CAACH,KAAK,GAAAuG,cAAA,GAAE5G,MAAM,CAACK,KAAK,CAAC,MAAA,IAAA,IAAAuG,cAAA,KAAA,MAAA,IAAA,CAAAA,cAAA,GAAbA,cAAA,CAAe9I,OAAO,cAAA8I,cAAA,KAAA,MAAA,GAAA,MAAA,GAAtBA,cAAA,CAAwBnG,YAAY,CAAC;AAACqG,cAAAA,SAAA,CAAA5N,CAAA,GAAA,CAAA;cAAA,OAC/FwE,QAAQ,EAAE;AAAA,YAAA,KAAA,CAAA;cAChBmJ,OAAO,CAACvI,MAAM,EAAE;AAAC,YAAA,KAAA,CAAA;cAAA,OAAAwI,SAAA,CAAA3M,CAAA,CAAA,CAAA,CAAA;AAAA;AAAA,SAAA,EAAAwM,QAAA,CAAA;OACpB,CAAA,CAAA;AAAA,MAAA,OAAA,UAAAI,GAAA,EAAA;AAAA,QAAA,OAAAL,KAAA,CAAAzK,KAAA,CAAA,IAAA,EAAAD,SAAA,CAAA;AAAA,OAAA;KAAC,EAAA,CAAA;GACL;EAED,IAAMgL,aAAa,GAAG,SAAhBA,aAAaA,CAAI3G,KAAK,EAAE1F,KAAK,EAAK;AACpC,IAAA,IAAImF,OAAO,CAACO,KAAK,CAAC,KAAK9C,SAAS,EAAEuC,OAAO,CAACO,KAAK,CAAC,CAAC1F,KAAK,GAAGA,KAAK;GACjE;AAED,EAAA,IAAMsM,aAAa,GAAG,SAAhBA,aAAaA,CAAI5G,KAAK,EAAK;AAAA,IAAA,IAAA6G,cAAA;IAC7B,OAAAA,CAAAA,cAAA,GAAOlH,MAAM,CAACK,KAAK,CAAC,MAAA,IAAA,IAAA6G,cAAA,KAAA,MAAA,GAAA,MAAA,GAAbA,cAAA,CAAe3G,MAAM;GAC/B;AAED,EAAA,IAAM4G,SAAS,GAAG,SAAZA,SAASA,CAAIhH,MAAM,EAAK;IAC1BxG,MAAM,CAACiG,IAAI,CAACO,MAAM,CAAC,CAACsG,OAAO,CAAC,UAACpG,KAAK,EAAA;MAAA,OAAK2G,aAAa,CAAC3G,KAAK,EAAEF,MAAM,CAACE,KAAK,CAAC,CAAC;KAAC,CAAA;GAC9E;AAED,EAAA,IAAM+G,iBAAiB,GAAG,SAApBA,iBAAiBA,GAAS;IAC5BnG,UAAU,CAAC,iBAAiB,CAAC;GAChC;EAED7D,YAAY,CAACgK,iBAAiB,CAAC;EAE/B,OAAO;AACH/E,IAAAA,WAAW,EAAXA,WAAW;AACX2E,IAAAA,aAAa,EAAbA,aAAa;AACbC,IAAAA,aAAa,EAAbA,aAAa;AACb5D,IAAAA,YAAY,EAAZA,YAAY;AACZS,IAAAA,WAAW,EAAXA,WAAW;AACXjC,IAAAA,QAAQ,EAARA,QAAQ;AACRsF,IAAAA,SAAS,EAATA,SAAS;AACTvD,IAAAA,KAAK,EAALA,KAAK;AACL3D,IAAAA,KAAK,EAALA,KAAK;AACLM,IAAAA,MAAM,EAANA,MAAM;AACNP,IAAAA,MAAM,EAANA;GACH;AACL;;;;"}