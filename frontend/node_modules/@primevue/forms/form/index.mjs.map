{"version": 3, "file": "index.mjs", "sources": ["../../src/form/BaseForm.vue", "../../src/form/Form.vue", "../../src/form/Form.vue?vue&type=template&id=26f63129&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport FormStyle from '@primevue/forms/form/style';\n\nexport default {\n    name: 'BaseForm',\n    extends: BaseComponent,\n    style: FormStyle,\n    props: {\n        resolver: {\n            type: Function,\n            default: null\n        },\n        initialValues: {\n            type: Object,\n            default: null\n        },\n        validateOnValueUpdate: {\n            type: [<PERSON>ole<PERSON>, Array],\n            default: true\n        },\n        validateOnBlur: {\n            type: [Boolean, Array],\n            default: false\n        },\n        validateOnMount: {\n            type: [Boolean, Array],\n            default: false\n        },\n        validateOnSubmit: {\n            type: [Boolean, Array],\n            default: true\n        }\n    },\n    provide() {\n        return {\n            $pcForm: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <form ref=\"formRef\" @submit.prevent=\"onSubmit\" @reset.prevent=\"onReset\" :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot :register :valid :reset v-bind=\"states\" />\n    </form>\n</template>\n\n<script>\nimport { omit } from '@primeuix/utils';\nimport { useForm } from '@primevue/forms/useform';\nimport { ref } from 'vue';\nimport BaseForm from './BaseForm.vue';\n\nexport default {\n    name: 'Form',\n    extends: BaseForm,\n    inheritAttrs: false,\n    emits: ['submit', 'reset'],\n    setup(props, { emit }) {\n        const formRef = ref(null);\n        const $form = useForm(props);\n\n        const submit = () => {\n            formRef.value?.requestSubmit();\n        };\n\n        const register = (field, options) => {\n            if (!options?.novalidate) {\n                const [, fieldProps] = $form.defineField(field, options);\n\n                return fieldProps;\n            }\n\n            return {};\n        };\n\n        const onSubmit = $form.handleSubmit((e) => {\n            emit('submit', e);\n        });\n\n        const onReset = $form.handleReset((e) => {\n            emit('reset', e);\n        });\n\n        return {\n            formRef,\n            submit,\n            register,\n            onSubmit,\n            onReset,\n            ...omit($form, ['handleSubmit', 'handleReset'])\n        };\n    }\n};\n</script>\n", "<template>\n    <form ref=\"formRef\" @submit.prevent=\"onSubmit\" @reset.prevent=\"onReset\" :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot :register :valid :reset v-bind=\"states\" />\n    </form>\n</template>\n\n<script>\nimport { omit } from '@primeuix/utils';\nimport { useForm } from '@primevue/forms/useform';\nimport { ref } from 'vue';\nimport BaseForm from './BaseForm.vue';\n\nexport default {\n    name: 'Form',\n    extends: BaseForm,\n    inheritAttrs: false,\n    emits: ['submit', 'reset'],\n    setup(props, { emit }) {\n        const formRef = ref(null);\n        const $form = useForm(props);\n\n        const submit = () => {\n            formRef.value?.requestSubmit();\n        };\n\n        const register = (field, options) => {\n            if (!options?.novalidate) {\n                const [, fieldProps] = $form.defineField(field, options);\n\n                return fieldProps;\n            }\n\n            return {};\n        };\n\n        const onSubmit = $form.handleSubmit((e) => {\n            emit('submit', e);\n        });\n\n        const onReset = $form.handleReset((e) => {\n            emit('reset', e);\n        });\n\n        return {\n            formRef,\n            submit,\n            register,\n            onSubmit,\n            onReset,\n            ...omit($form, ['handleSubmit', 'handleReset'])\n        };\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "style", "FormStyle", "props", "resolver", "type", "Function", "initialValues", "Object", "validateOnValueUpdate", "Boolean", "Array", "validateOnBlur", "validateOnMount", "validateOnSubmit", "provide", "$pcForm", "$parentInstance", "BaseForm", "inheritAttrs", "emits", "setup", "_ref", "emit", "formRef", "ref", "$form", "useForm", "submit", "_formRef$value", "value", "requestSubmit", "register", "field", "options", "novalidate", "_$form$defineField", "defineField", "_$form$defineField2", "_slicedToArray", "fieldProps", "onSubmit", "handleSubmit", "e", "onReset", "handleReset", "_objectSpread", "omit", "_openBlock", "_createElementBlock", "_mergeProps", "$setup", "apply", "arguments", "_ctx", "cx", "ptmi", "_renderSlot", "valid", "reset", "states"], "mappings": ";;;;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,UAAU;AAChB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAEC,SAAS;AAChBC,EAAAA,KAAK,EAAE;AACHC,IAAAA,QAAQ,EAAE;AACNC,MAAAA,IAAI,EAAEC,QAAQ;MACd,SAAS,EAAA;KACZ;AACDC,IAAAA,aAAa,EAAE;AACXF,MAAAA,IAAI,EAAEG,MAAM;MACZ,SAAS,EAAA;KACZ;AACDC,IAAAA,qBAAqB,EAAE;AACnBJ,MAAAA,IAAI,EAAE,CAACK,OAAO,EAAEC,KAAK,CAAC;MACtB,SAAS,EAAA;KACZ;AACDC,IAAAA,cAAc,EAAE;AACZP,MAAAA,IAAI,EAAE,CAACK,OAAO,EAAEC,KAAK,CAAC;MACtB,SAAS,EAAA;KACZ;AACDE,IAAAA,eAAe,EAAE;AACbR,MAAAA,IAAI,EAAE,CAACK,OAAO,EAAEC,KAAK,CAAC;MACtB,SAAS,EAAA;KACZ;AACDG,IAAAA,gBAAgB,EAAE;AACdT,MAAAA,IAAI,EAAE,CAACK,OAAO,EAAEC,KAAK,CAAC;MACtB,SAAS,EAAA;AACb;GACH;EACDI,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;;;;;;;;;AC5BD,aAAe;AACXlB,EAAAA,IAAI,EAAE,MAAM;AACZ,EAAA,SAAA,EAASmB,QAAQ;AACjBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;AAC1BC,EAAAA,KAAK,WAALA,KAAKA,CAAClB,KAAK,EAAAmB,IAAA,EAAY;AAAA,IAAA,IAARC,IAAG,GAAAD,IAAA,CAAHC,IAAG;AACd,IAAA,IAAMC,UAAUC,GAAG,CAAC,IAAI,CAAC;AACzB,IAAA,IAAMC,KAAI,GAAIC,OAAO,CAACxB,KAAK,CAAC;AAE5B,IAAA,IAAMyB,MAAO,GAAE,SAATA,MAAOA,GAAQ;AAAA,MAAA,IAAAC,cAAA;AACjB,MAAA,CAAAA,cAAA,GAAAL,OAAO,CAACM,KAAK,MAAA,IAAA,IAAAD,cAAA,KAAA,MAAA,IAAbA,cAAA,CAAeE,aAAa,EAAE;KACjC;IAED,IAAMC,QAAO,GAAI,SAAXA,QAAOA,CAAKC,KAAK,EAAEC,OAAO,EAAK;MACjC,IAAI,EAACA,OAAO,KAAPA,IAAAA,IAAAA,OAAO,eAAPA,OAAO,CAAEC,UAAU,CAAE,EAAA;QACtB,IAAAC,kBAAA,GAAuBV,KAAK,CAACW,WAAW,CAACJ,KAAK,EAAEC,OAAO,CAAC;UAAAI,mBAAA,GAAAC,cAAA,CAAAH,kBAAA,EAAA,CAAA,CAAA;AAA/CI,UAAAA,UAAU,GAAAF,mBAAA,CAAA,CAAA,CAAA;AAEnB,QAAA,OAAOE,UAAU;AACrB;AAEA,MAAA,OAAO,EAAE;KACZ;IAED,IAAMC,QAAS,GAAEf,KAAK,CAACgB,YAAY,CAAC,UAACC,CAAC,EAAK;AACvCpB,MAAAA,IAAI,CAAC,QAAQ,EAAEoB,CAAC,CAAC;AACrB,KAAC,CAAC;IAEF,IAAMC,OAAQ,GAAElB,KAAK,CAACmB,WAAW,CAAC,UAACF,CAAC,EAAK;AACrCpB,MAAAA,IAAI,CAAC,OAAO,EAAEoB,CAAC,CAAC;AACpB,KAAC,CAAC;AAEF,IAAA,OAAAG,aAAA,CAAA;AACItB,MAAAA,OAAO,EAAPA,OAAO;AACPI,MAAAA,MAAM,EAANA,MAAM;AACNI,MAAAA,QAAQ,EAARA,QAAQ;AACRS,MAAAA,QAAQ,EAARA,QAAQ;AACRG,MAAAA,OAAO,EAAPA;KACGG,EAAAA,IAAI,CAACrB,KAAK,EAAE,CAAC,cAAc,EAAE,aAAa,CAAC,CAAA,CAAA;AAEtD;AACJ,CAAC;;;ECnDG,OAAAsB,SAAA,EAAA,EAAAC,kBAAA,CAEM,QAFNC,UAEM,CAAA;AAFAzB,IAAAA,GAAG,EAAC,SAAU;AAACgB,IAAAA,QAAM;aAAUU,MAAQ,CAAAV,QAAA,IAAAU,MAAA,CAAAV,QAAA,CAAAW,KAAA,CAAAD,MAAA,EAAAE,SAAA,CAAA;AAAA,KAAA,EAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AAAGT,IAAAA,OAAK;aAAUO,MAAO,CAAAP,OAAA,IAAAO,MAAA,CAAAP,OAAA,CAAAQ,KAAA,CAAAD,MAAA,EAAAE,SAAA,CAAA;AAAA,KAAA,EAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AAAG,IAAA,OAAA,EAAOC,IAAE,CAAAC,EAAA,CAAA,MAAA;KAAkBD,IAAI,CAAAE,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CACpGC,UAAA,CAA+CH,wBAA/CJ,UAA+C,CAAA;IAAxClB,QAAS,EAATmB,MAAS,CAAAnB,QAAA;IAAC0B,KAAM,EAANJ,IAAM,CAAAI,KAAA;IAACC,KAAI,EAAJL,IAAI,CAAAK;KAAUL,IAAM,CAAAM,MAAA,CAAA,CAAA;;;;;;;"}