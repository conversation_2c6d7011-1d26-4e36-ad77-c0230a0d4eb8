{"version": 3, "file": "index.mjs", "sources": ["../../../src/form/style/FormStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-form p-component'\n};\n\nexport default BaseStyle.extend({\n    name: 'form',\n    classes\n});\n"], "names": ["classes", "root", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE;AACV,CAAC;AAED,gBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,MAAM;AACZJ,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}