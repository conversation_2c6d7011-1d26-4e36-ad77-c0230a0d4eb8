{"version": 3, "file": "index.mjs", "sources": ["../../src/formfield/BaseFormField.vue", "../../src/formfield/FormField.vue", "../../src/formfield/FormField.vue?vue&type=template&id=4107279f&lang.js"], "sourcesContent": ["<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport FormFieldStyle from '@primevue/forms/formfield/style';\n\nexport default {\n    name: 'BaseForm<PERSON>ield',\n    extends: BaseComponent,\n    style: FormFieldStyle,\n    props: {\n        name: {\n            type: String,\n            default: undefined\n        },\n        resolver: {\n            type: Function,\n            default: undefined\n        },\n        initialValue: {\n            type: null,\n            default: undefined\n        },\n        validateOnValueUpdate: {\n            type: Boolean,\n            default: undefined\n        },\n        validateOnBlur: {\n            type: Boolean,\n            default: undefined\n        },\n        validateOnMount: {\n            type: Boolean,\n            default: undefined\n        },\n        validateOnSubmit: {\n            type: Boolean,\n            default: undefined\n        },\n        as: {\n            type: [String, Object],\n            default: 'DIV'\n        },\n        asChild: {\n            type: Boolean,\n            default: false\n        }\n    },\n    provide() {\n        return {\n            $pcFormField: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <component v-if=\"!asChild\" :is=\"as\" :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot :props=\"field.props\" v-bind=\"fieldAttrs\"></slot>\n    </component>\n    <slot v-else :class=\"cx('root')\" :props=\"field.props\" v-bind=\"fieldAttrs\"></slot>\n</template>\n\n<script>\nimport BaseFormField from './BaseFormField.vue';\n\nexport default {\n    name: 'FormField',\n    extends: BaseFormField,\n    inheritAttrs: false,\n    inject: {\n        $pcForm: {\n            default: undefined\n        }\n    },\n    watch: {\n        formControl: {\n            immediate: true,\n            handler(newValue) {\n                this.$pcForm?.register?.(this.name, newValue);\n            }\n        }\n    },\n    computed: {\n        formControl() {\n            return {\n                name: this.name,\n                resolver: this.resolver,\n                initialValue: this.initialValue,\n                validateOnValueUpdate: this.validateOnValueUpdate,\n                validateOnBlur: this.validateOnBlur,\n                validateOnMount: this.validateOnMount,\n                validateOnSubmit: this.validateOnSubmit\n            };\n        },\n        field() {\n            return this.$pcForm?.fields?.[this.name] || {};\n        },\n        fieldAttrs() {\n            return {\n                ...this.field.props,\n                ...this.field.states\n            };\n        }\n    }\n};\n</script>\n", "<template>\n    <component v-if=\"!asChild\" :is=\"as\" :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n        <slot :props=\"field.props\" v-bind=\"fieldAttrs\"></slot>\n    </component>\n    <slot v-else :class=\"cx('root')\" :props=\"field.props\" v-bind=\"fieldAttrs\"></slot>\n</template>\n\n<script>\nimport BaseFormField from './BaseFormField.vue';\n\nexport default {\n    name: 'FormField',\n    extends: BaseFormField,\n    inheritAttrs: false,\n    inject: {\n        $pcForm: {\n            default: undefined\n        }\n    },\n    watch: {\n        formControl: {\n            immediate: true,\n            handler(newValue) {\n                this.$pcForm?.register?.(this.name, newValue);\n            }\n        }\n    },\n    computed: {\n        formControl() {\n            return {\n                name: this.name,\n                resolver: this.resolver,\n                initialValue: this.initialValue,\n                validateOnValueUpdate: this.validateOnValueUpdate,\n                validateOnBlur: this.validateOnBlur,\n                validateOnMount: this.validateOnMount,\n                validateOnSubmit: this.validateOnSubmit\n            };\n        },\n        field() {\n            return this.$pcForm?.fields?.[this.name] || {};\n        },\n        fieldAttrs() {\n            return {\n                ...this.field.props,\n                ...this.field.states\n            };\n        }\n    }\n};\n</script>\n"], "names": ["name", "BaseComponent", "style", "FormFieldStyle", "props", "type", "String", "undefined", "resolver", "Function", "initialValue", "validateOnValueUpdate", "Boolean", "validateOnBlur", "validateOnMount", "validateOnSubmit", "as", "Object", "<PERSON><PERSON><PERSON><PERSON>", "provide", "$pcFormField", "$parentInstance", "BaseFormField", "inheritAttrs", "inject", "$pcForm", "watch", "formControl", "immediate", "handler", "newValue", "_this$$pcForm", "_this$$pcForm$registe", "register", "call", "computed", "field", "_this$$pcForm2", "fields", "fieldAttrs", "_objectSpread", "states", "_ctx", "_createBlock", "_resolveDynamicComponent", "_mergeProps", "cx", "ptmi", "_renderSlot", "$options"], "mappings": ";;;;AAIA,eAAe;AACXA,EAAAA,IAAI,EAAE,eAAe;AACrB,EAAA,SAAA,EAASC,aAAa;AACtBC,EAAAA,KAAK,EAAEC,cAAc;AACrBC,EAAAA,KAAK,EAAE;AACHJ,IAAAA,IAAI,EAAE;AACFK,MAAAA,IAAI,EAAEC,MAAM;MACZ,SAASC,EAAAA;KACZ;AACDC,IAAAA,QAAQ,EAAE;AACNH,MAAAA,IAAI,EAAEI,QAAQ;MACd,SAASF,EAAAA;KACZ;AACDG,IAAAA,YAAY,EAAE;AACVL,MAAAA,IAAI,EAAE,IAAI;MACV,SAASE,EAAAA;KACZ;AACDI,IAAAA,qBAAqB,EAAE;AACnBN,MAAAA,IAAI,EAAEO,OAAO;MACb,SAASL,EAAAA;KACZ;AACDM,IAAAA,cAAc,EAAE;AACZR,MAAAA,IAAI,EAAEO,OAAO;MACb,SAASL,EAAAA;KACZ;AACDO,IAAAA,eAAe,EAAE;AACbT,MAAAA,IAAI,EAAEO,OAAO;MACb,SAASL,EAAAA;KACZ;AACDQ,IAAAA,gBAAgB,EAAE;AACdV,MAAAA,IAAI,EAAEO,OAAO;MACb,SAASL,EAAAA;KACZ;AACDS,IAAAA,EAAE,EAAE;AACAX,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEW,MAAM,CAAC;MACtB,SAAS,EAAA;KACZ;AACDC,IAAAA,OAAO,EAAE;AACLb,MAAAA,IAAI,EAAEO,OAAO;MACb,SAAS,EAAA;AACb;GACH;EACDO,OAAO,EAAA,SAAPA,OAAOA,GAAG;IACN,OAAO;AACHC,MAAAA,YAAY,EAAE,IAAI;AAClBC,MAAAA,eAAe,EAAE;KACpB;AACL;AACJ,CAAC;;;;;;;;AC1CD,aAAe;AACXrB,EAAAA,IAAI,EAAE,WAAW;AACjB,EAAA,SAAA,EAASsB,QAAa;AACtBC,EAAAA,YAAY,EAAE,KAAK;AACnBC,EAAAA,MAAM,EAAE;AACJC,IAAAA,OAAO,EAAE;MACL,SAASlB,EAAAA;AACb;GACH;AACDmB,EAAAA,KAAK,EAAE;AACHC,IAAAA,WAAW,EAAE;AACTC,MAAAA,SAAS,EAAE,IAAI;AACfC,MAAAA,OAAO,EAAPA,SAAAA,OAAOA,CAACC,QAAQ,EAAE;QAAA,IAAAC,aAAA,EAAAC,qBAAA;QACd,CAAAD,aAAA,GAAI,IAAA,CAACN,OAAO,MAAA,IAAA,IAAAM,aAAA,KAAA,MAAA,IAAA,CAAAC,qBAAA,GAAZD,aAAA,CAAcE,QAAQ,MAAA,IAAA,IAAAD,qBAAA,KAAtBA,MAAAA,IAAAA,qBAAA,CAAAE,IAAA,CAAAH,aAAA,EAAyB,IAAI,CAAC/B,IAAI,EAAE8B,QAAQ,CAAC;AACjD;AACJ;GACH;AACDK,EAAAA,QAAQ,EAAE;IACNR,WAAW,EAAA,SAAXA,WAAWA,GAAG;MACV,OAAO;QACH3B,IAAI,EAAE,IAAI,CAACA,IAAI;QACfQ,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBE,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BC,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;QACjDE,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCC,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,gBAAgB,EAAE,IAAI,CAACA;OAC1B;KACJ;IACDqB,KAAK,EAAA,SAALA,KAAKA,GAAG;AAAA,MAAA,IAAAC,cAAA;MACJ,OAAO,CAAA,CAAAA,cAAA,GAAA,IAAI,CAACZ,OAAO,cAAAY,cAAA,KAAA,MAAA,IAAA,CAAAA,cAAA,GAAZA,cAAA,CAAcC,MAAM,MAAAD,IAAAA,IAAAA,cAAA,KAApBA,MAAAA,GAAAA,MAAAA,GAAAA,cAAA,CAAuB,IAAI,CAACrC,IAAI,MAAK,EAAE;KACjD;IACDuC,UAAU,EAAA,SAAVA,UAAUA,GAAG;AACT,MAAA,OAAAC,aAAA,CAAAA,aAAA,CAAA,EAAA,EACO,IAAI,CAACJ,KAAK,CAAChC,KAAK,CAChB,EAAA,IAAI,CAACgC,KAAK,CAACK,MAAK,CAAA;AAE3B;AACJ;AACJ,CAAC;;;UChDqBC,IAAO,CAAAxB,OAAA,iBAAzByB,WAEW,CAAAC,uBAAA,CAFqBF,IAAE,CAAA1B,EAAA,CAAA,EAAlC6B,UAEW,CAAA;;AAF0B,IAAA,OAAA,EAAOH,IAAE,CAAAI,EAAA,CAAA,MAAA;KAAkBJ,IAAI,CAAAK,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA;uBAChE,YAAA;MAAA,OAAqD,CAArDC,UAAA,CAAqDN,wBAArDG,UAAqD,CAAA;AAA9CzC,QAAAA,KAAK,EAAE6C,QAAK,CAAAb,KAAA,CAAChC;SAAe6C,QAAU,CAAAV,UAAA,CAAA,CAAA;;;uBAEjDS,UAAA,CAAgFN,wBAAhFG,UAAgF,CAAA;;AAAlE,IAAA,OAAA,EAAOH,IAAE,CAAAI,EAAA,CAAA,MAAA,CAAA;AAAW1C,IAAAA,KAAK,EAAE6C,QAAK,CAAAb,KAAA,CAAChC;KAAe6C,QAAU,CAAAV,UAAA,CAAA,CAAA;;;;;;;"}