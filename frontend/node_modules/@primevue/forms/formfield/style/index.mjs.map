{"version": 3, "file": "index.mjs", "sources": ["../../../src/formfield/style/FormFieldStyle.js"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-formfield p-component'\n};\n\nexport default BaseStyle.extend({\n    name: 'formfield',\n    classes\n});\n"], "names": ["classes", "root", "BaseStyle", "extend", "name"], "mappings": ";;AAEA,IAAMA,OAAO,GAAG;AACZC,EAAAA,IAAI,EAAE;AACV,CAAC;AAED,qBAAeC,SAAS,CAACC,MAAM,CAAC;AAC5BC,EAAAA,IAAI,EAAE,WAAW;AACjBJ,EAAAA,OAAO,EAAPA;AACJ,CAAC,CAAC;;;;"}