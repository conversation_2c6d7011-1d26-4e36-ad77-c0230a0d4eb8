{"name": "@primevue/forms", "version": "4.3.5", "author": "PrimeTek Informatics", "description": "", "homepage": "https://primevue.org/", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/primefaces/primevue.git", "directory": "packages/forms"}, "bugs": {"url": "https://github.com/primefaces/primevue/issues"}, "main": "./index.mjs", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "default": "./index.mjs"}, "./*": {"types": "./*/index.d.ts", "import": "./*/index.mjs", "default": "./*/index.mjs"}}, "publishConfig": {"access": "public"}, "dependencies": {"@primeuix/utils": "^0.5.3", "@primeuix/forms": "^0.0.4", "@primevue/core": "4.3.5"}, "engines": {"node": ">=12.11.0"}}