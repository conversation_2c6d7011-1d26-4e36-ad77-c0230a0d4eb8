/* eslint-disable spaced-comment */
export var WasiErrno;
(function (WasiErrno) {
    WasiErrno[WasiErrno["ESUCCESS"] = 0] = "ESUCCESS";
    WasiErrno[WasiErrno["E2BIG"] = 1] = "E2BIG";
    WasiErrno[WasiErrno["EACCES"] = 2] = "EACCES";
    WasiErrno[WasiErrno["EADDRINUSE"] = 3] = "EADDRINUSE";
    WasiErrno[WasiErrno["EADDRNOTAVAIL"] = 4] = "EADDRNOTAVAIL";
    WasiErrno[WasiErrno["EAFNOSUPPORT"] = 5] = "EAFNOSUPPORT";
    WasiErrno[WasiErrno["EAGAIN"] = 6] = "EAGAIN";
    WasiErrno[WasiErrno["EALREADY"] = 7] = "EALREADY";
    WasiErrno[WasiErrno["EBADF"] = 8] = "EBADF";
    Wasi<PERSON>rrno[WasiErrno["EBADMSG"] = 9] = "EBADMSG";
    WasiErrno[WasiErrno["EBUSY"] = 10] = "EBUSY";
    WasiErrno[WasiErrno["ECANCELED"] = 11] = "ECANCELED";
    WasiErrno[WasiErrno["ECHILD"] = 12] = "ECHILD";
    WasiErrno[WasiErrno["ECONNABORTED"] = 13] = "ECONNABORTED";
    WasiErrno[WasiErrno["ECONNREFUSED"] = 14] = "ECONNREFUSED";
    WasiErrno[WasiErrno["ECONNRESET"] = 15] = "ECONNRESET";
    WasiErrno[WasiErrno["EDEADLK"] = 16] = "EDEADLK";
    WasiErrno[WasiErrno["EDESTADDRREQ"] = 17] = "EDESTADDRREQ";
    WasiErrno[WasiErrno["EDOM"] = 18] = "EDOM";
    WasiErrno[WasiErrno["EDQUOT"] = 19] = "EDQUOT";
    WasiErrno[WasiErrno["EEXIST"] = 20] = "EEXIST";
    WasiErrno[WasiErrno["EFAULT"] = 21] = "EFAULT";
    WasiErrno[WasiErrno["EFBIG"] = 22] = "EFBIG";
    WasiErrno[WasiErrno["EHOSTUNREACH"] = 23] = "EHOSTUNREACH";
    WasiErrno[WasiErrno["EIDRM"] = 24] = "EIDRM";
    WasiErrno[WasiErrno["EILSEQ"] = 25] = "EILSEQ";
    WasiErrno[WasiErrno["EINPROGRESS"] = 26] = "EINPROGRESS";
    WasiErrno[WasiErrno["EINTR"] = 27] = "EINTR";
    WasiErrno[WasiErrno["EINVAL"] = 28] = "EINVAL";
    WasiErrno[WasiErrno["EIO"] = 29] = "EIO";
    WasiErrno[WasiErrno["EISCONN"] = 30] = "EISCONN";
    WasiErrno[WasiErrno["EISDIR"] = 31] = "EISDIR";
    WasiErrno[WasiErrno["ELOOP"] = 32] = "ELOOP";
    WasiErrno[WasiErrno["EMFILE"] = 33] = "EMFILE";
    WasiErrno[WasiErrno["EMLINK"] = 34] = "EMLINK";
    WasiErrno[WasiErrno["EMSGSIZE"] = 35] = "EMSGSIZE";
    WasiErrno[WasiErrno["EMULTIHOP"] = 36] = "EMULTIHOP";
    WasiErrno[WasiErrno["ENAMETOOLONG"] = 37] = "ENAMETOOLONG";
    WasiErrno[WasiErrno["ENETDOWN"] = 38] = "ENETDOWN";
    WasiErrno[WasiErrno["ENETRESET"] = 39] = "ENETRESET";
    WasiErrno[WasiErrno["ENETUNREACH"] = 40] = "ENETUNREACH";
    WasiErrno[WasiErrno["ENFILE"] = 41] = "ENFILE";
    WasiErrno[WasiErrno["ENOBUFS"] = 42] = "ENOBUFS";
    WasiErrno[WasiErrno["ENODEV"] = 43] = "ENODEV";
    WasiErrno[WasiErrno["ENOENT"] = 44] = "ENOENT";
    WasiErrno[WasiErrno["ENOEXEC"] = 45] = "ENOEXEC";
    WasiErrno[WasiErrno["ENOLCK"] = 46] = "ENOLCK";
    WasiErrno[WasiErrno["ENOLINK"] = 47] = "ENOLINK";
    WasiErrno[WasiErrno["ENOMEM"] = 48] = "ENOMEM";
    WasiErrno[WasiErrno["ENOMSG"] = 49] = "ENOMSG";
    WasiErrno[WasiErrno["ENOPROTOOPT"] = 50] = "ENOPROTOOPT";
    WasiErrno[WasiErrno["ENOSPC"] = 51] = "ENOSPC";
    WasiErrno[WasiErrno["ENOSYS"] = 52] = "ENOSYS";
    WasiErrno[WasiErrno["ENOTCONN"] = 53] = "ENOTCONN";
    WasiErrno[WasiErrno["ENOTDIR"] = 54] = "ENOTDIR";
    WasiErrno[WasiErrno["ENOTEMPTY"] = 55] = "ENOTEMPTY";
    WasiErrno[WasiErrno["ENOTRECOVERABLE"] = 56] = "ENOTRECOVERABLE";
    WasiErrno[WasiErrno["ENOTSOCK"] = 57] = "ENOTSOCK";
    WasiErrno[WasiErrno["ENOTSUP"] = 58] = "ENOTSUP";
    WasiErrno[WasiErrno["ENOTTY"] = 59] = "ENOTTY";
    WasiErrno[WasiErrno["ENXIO"] = 60] = "ENXIO";
    WasiErrno[WasiErrno["EOVERFLOW"] = 61] = "EOVERFLOW";
    WasiErrno[WasiErrno["EOWNERDEAD"] = 62] = "EOWNERDEAD";
    WasiErrno[WasiErrno["EPERM"] = 63] = "EPERM";
    WasiErrno[WasiErrno["EPIPE"] = 64] = "EPIPE";
    WasiErrno[WasiErrno["EPROTO"] = 65] = "EPROTO";
    WasiErrno[WasiErrno["EPROTONOSUPPORT"] = 66] = "EPROTONOSUPPORT";
    WasiErrno[WasiErrno["EPROTOTYPE"] = 67] = "EPROTOTYPE";
    WasiErrno[WasiErrno["ERANGE"] = 68] = "ERANGE";
    WasiErrno[WasiErrno["EROFS"] = 69] = "EROFS";
    WasiErrno[WasiErrno["ESPIPE"] = 70] = "ESPIPE";
    WasiErrno[WasiErrno["ESRCH"] = 71] = "ESRCH";
    WasiErrno[WasiErrno["ESTALE"] = 72] = "ESTALE";
    WasiErrno[WasiErrno["ETIMEDOUT"] = 73] = "ETIMEDOUT";
    WasiErrno[WasiErrno["ETXTBSY"] = 74] = "ETXTBSY";
    WasiErrno[WasiErrno["EXDEV"] = 75] = "EXDEV";
    WasiErrno[WasiErrno["ENOTCAPABLE"] = 76] = "ENOTCAPABLE";
})(WasiErrno || (WasiErrno = {}));
export var WasiFileType;
(function (WasiFileType) {
    WasiFileType[WasiFileType["UNKNOWN"] = 0] = "UNKNOWN";
    WasiFileType[WasiFileType["BLOCK_DEVICE"] = 1] = "BLOCK_DEVICE";
    WasiFileType[WasiFileType["CHARACTER_DEVICE"] = 2] = "CHARACTER_DEVICE";
    WasiFileType[WasiFileType["DIRECTORY"] = 3] = "DIRECTORY";
    WasiFileType[WasiFileType["REGULAR_FILE"] = 4] = "REGULAR_FILE";
    WasiFileType[WasiFileType["SOCKET_DGRAM"] = 5] = "SOCKET_DGRAM";
    WasiFileType[WasiFileType["SOCKET_STREAM"] = 6] = "SOCKET_STREAM";
    WasiFileType[WasiFileType["SYMBOLIC_LINK"] = 7] = "SYMBOLIC_LINK";
})(WasiFileType || (WasiFileType = {}));
const FD_DATASYNC = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(0));
const FD_READ = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(1));
const FD_SEEK = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(2));
const FD_FDSTAT_SET_FLAGS = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(3));
const FD_SYNC = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(4));
const FD_TELL = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(5));
const FD_WRITE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(6));
const FD_ADVISE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(7));
const FD_ALLOCATE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(8));
const PATH_CREATE_DIRECTORY = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(9));
const PATH_CREATE_FILE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(10));
const PATH_LINK_SOURCE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(11));
const PATH_LINK_TARGET = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(12));
const PATH_OPEN = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(13));
const FD_READDIR = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(14));
const PATH_READLINK = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(15));
const PATH_RENAME_SOURCE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(16));
const PATH_RENAME_TARGET = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(17));
const PATH_FILESTAT_GET = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(18));
const PATH_FILESTAT_SET_SIZE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(19));
const PATH_FILESTAT_SET_TIMES = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(20));
const FD_FILESTAT_GET = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(21));
const FD_FILESTAT_SET_SIZE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(22));
const FD_FILESTAT_SET_TIMES = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(23));
const PATH_SYMLINK = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(24));
const PATH_REMOVE_DIRECTORY = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(25));
const PATH_UNLINK_FILE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(26));
const POLL_FD_READWRITE = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(27));
const SOCK_SHUTDOWN = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(28));
const SOCK_ACCEPT = ( /*#__PURE__*/BigInt(1) << /*#__PURE__*/ BigInt(29));
export const WasiRights = {
    FD_DATASYNC,
    FD_READ,
    FD_SEEK,
    FD_FDSTAT_SET_FLAGS,
    FD_SYNC,
    FD_TELL,
    FD_WRITE,
    FD_ADVISE,
    FD_ALLOCATE,
    PATH_CREATE_DIRECTORY,
    PATH_CREATE_FILE,
    PATH_LINK_SOURCE,
    PATH_LINK_TARGET,
    PATH_OPEN,
    FD_READDIR,
    PATH_READLINK,
    PATH_RENAME_SOURCE,
    PATH_RENAME_TARGET,
    PATH_FILESTAT_GET,
    PATH_FILESTAT_SET_SIZE,
    PATH_FILESTAT_SET_TIMES,
    FD_FILESTAT_GET,
    FD_FILESTAT_SET_SIZE,
    FD_FILESTAT_SET_TIMES,
    PATH_SYMLINK,
    PATH_REMOVE_DIRECTORY,
    PATH_UNLINK_FILE,
    POLL_FD_READWRITE,
    SOCK_SHUTDOWN,
    SOCK_ACCEPT
};
export var WasiWhence;
(function (WasiWhence) {
    WasiWhence[WasiWhence["SET"] = 0] = "SET";
    WasiWhence[WasiWhence["CUR"] = 1] = "CUR";
    WasiWhence[WasiWhence["END"] = 2] = "END";
})(WasiWhence || (WasiWhence = {}));
export var FileControlFlag;
(function (FileControlFlag) {
    FileControlFlag[FileControlFlag["O_RDONLY"] = 0] = "O_RDONLY";
    FileControlFlag[FileControlFlag["O_WRONLY"] = 1] = "O_WRONLY";
    FileControlFlag[FileControlFlag["O_RDWR"] = 2] = "O_RDWR";
    FileControlFlag[FileControlFlag["O_CREAT"] = 64] = "O_CREAT";
    FileControlFlag[FileControlFlag["O_EXCL"] = 128] = "O_EXCL";
    FileControlFlag[FileControlFlag["O_NOCTTY"] = 256] = "O_NOCTTY";
    FileControlFlag[FileControlFlag["O_TRUNC"] = 512] = "O_TRUNC";
    FileControlFlag[FileControlFlag["O_APPEND"] = 1024] = "O_APPEND";
    FileControlFlag[FileControlFlag["O_DIRECTORY"] = 65536] = "O_DIRECTORY";
    FileControlFlag[FileControlFlag["O_NOATIME"] = 262144] = "O_NOATIME";
    FileControlFlag[FileControlFlag["O_NOFOLLOW"] = 131072] = "O_NOFOLLOW";
    FileControlFlag[FileControlFlag["O_SYNC"] = 1052672] = "O_SYNC";
    FileControlFlag[FileControlFlag["O_DIRECT"] = 16384] = "O_DIRECT";
    FileControlFlag[FileControlFlag["O_NONBLOCK"] = 2048] = "O_NONBLOCK";
})(FileControlFlag || (FileControlFlag = {}));
export var WasiFileControlFlag;
(function (WasiFileControlFlag) {
    WasiFileControlFlag[WasiFileControlFlag["O_CREAT"] = 1] = "O_CREAT";
    WasiFileControlFlag[WasiFileControlFlag["O_DIRECTORY"] = 2] = "O_DIRECTORY";
    WasiFileControlFlag[WasiFileControlFlag["O_EXCL"] = 4] = "O_EXCL";
    WasiFileControlFlag[WasiFileControlFlag["O_TRUNC"] = 8] = "O_TRUNC";
})(WasiFileControlFlag || (WasiFileControlFlag = {}));
export var WasiFdFlag;
(function (WasiFdFlag) {
    WasiFdFlag[WasiFdFlag["APPEND"] = 1] = "APPEND";
    WasiFdFlag[WasiFdFlag["DSYNC"] = 2] = "DSYNC";
    WasiFdFlag[WasiFdFlag["NONBLOCK"] = 4] = "NONBLOCK";
    WasiFdFlag[WasiFdFlag["RSYNC"] = 8] = "RSYNC";
    WasiFdFlag[WasiFdFlag["SYNC"] = 16] = "SYNC";
})(WasiFdFlag || (WasiFdFlag = {}));
export var WasiClockid;
(function (WasiClockid) {
    WasiClockid[WasiClockid["REALTIME"] = 0] = "REALTIME";
    WasiClockid[WasiClockid["MONOTONIC"] = 1] = "MONOTONIC";
    WasiClockid[WasiClockid["PROCESS_CPUTIME_ID"] = 2] = "PROCESS_CPUTIME_ID";
    WasiClockid[WasiClockid["THREAD_CPUTIME_ID"] = 3] = "THREAD_CPUTIME_ID";
})(WasiClockid || (WasiClockid = {}));
export var WasiFstFlag;
(function (WasiFstFlag) {
    WasiFstFlag[WasiFstFlag["SET_ATIM"] = 1] = "SET_ATIM";
    WasiFstFlag[WasiFstFlag["SET_ATIM_NOW"] = 2] = "SET_ATIM_NOW";
    WasiFstFlag[WasiFstFlag["SET_MTIM"] = 4] = "SET_MTIM";
    WasiFstFlag[WasiFstFlag["SET_MTIM_NOW"] = 8] = "SET_MTIM_NOW";
})(WasiFstFlag || (WasiFstFlag = {}));
export var WasiEventType;
(function (WasiEventType) {
    WasiEventType[WasiEventType["CLOCK"] = 0] = "CLOCK";
    WasiEventType[WasiEventType["FD_READ"] = 1] = "FD_READ";
    WasiEventType[WasiEventType["FD_WRITE"] = 2] = "FD_WRITE";
})(WasiEventType || (WasiEventType = {}));
export var WasiSubclockflags;
(function (WasiSubclockflags) {
    WasiSubclockflags[WasiSubclockflags["ABSTIME"] = 1] = "ABSTIME";
})(WasiSubclockflags || (WasiSubclockflags = {}));
