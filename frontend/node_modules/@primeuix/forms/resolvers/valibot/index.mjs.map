{"version": 3, "sources": ["../../../src/resolvers/valibot/index.ts"], "sourcesContent": ["import { toValues } from '@primeuix/forms/utils';\nimport { isNotEmpty } from '@primeuix/utils/object';\nimport { getDotPath, InferOutput, safeParse, safeParseAsync } from 'valibot';\nimport type { ResolverOptions, ResolverResult } from '..';\n\nexport const valibotResolver =\n    <T>(schema: any, schemaOptions?: any, resolverOptions?: ResolverOptions) =>\n    async ({ values, name }: any): Promise<ResolverResult<T>> => {\n        const { sync = false, raw = false } = resolverOptions || {};\n\n        try {\n            const result: InferOutput<any> | Promise<InferOutput<any>> = sync ? safeParse(schema, values, { abortPipeEarly: false, ...schemaOptions }) : await safeParseAsync(schema, values, { abortPipeEarly: false, ...schemaOptions });\n\n            if (result.success) {\n                return {\n                    values: toValues(raw ? values : result.output, name),\n                    errors: {}\n                };\n            } else {\n                return {\n                    values: toValues(raw ? values : undefined, name),\n                    errors: result.issues?.reduce((acc: Record<string, any[]>, error: any) => {\n                        const path = getDotPath(error);\n                        const pathKey = isNotEmpty(path) ? path : name;\n\n                        if (pathKey) {\n                            acc[pathKey] ||= [];\n                            acc[pathKey].push(error);\n                        }\n\n                        return acc;\n                    }, {})\n                };\n            }\n        } catch (e: any) {\n            throw e;\n        }\n    };\n"], "mappings": "AAAA,OAAS,YAAAA,MAAgB,wBACzB,OAAS,cAAAC,MAAkB,yBAC3B,OAAS,cAAAC,EAAyB,aAAAC,EAAW,kBAAAC,MAAsB,UAG5D,IAAMC,EACT,CAAIC,EAAaC,EAAqBC,IACtC,MAAO,CAAE,OAAAC,EAAQ,KAAAC,CAAK,IAAuC,CACzD,GAAM,CAAE,KAAAC,EAAO,GAAO,IAAAC,EAAM,EAAM,EAAIJ,GAAmB,CAAC,EAE1D,GAAI,CACA,IAAMK,EAAuDF,EAAOR,EAAUG,EAAQG,EAAQ,CAAE,eAAgB,GAAO,GAAGF,CAAc,CAAC,EAAI,MAAMH,EAAeE,EAAQG,EAAQ,CAAE,eAAgB,GAAO,GAAGF,CAAc,CAAC,EAE7N,OAAIM,EAAO,QACA,CACH,OAAQb,EAASY,EAAMH,EAASI,EAAO,OAAQH,CAAI,EACnD,OAAQ,CAAC,CACb,EAEO,CACH,OAAQV,EAASY,EAAMH,EAAS,OAAWC,CAAI,EAC/C,OAAQG,EAAO,QAAQ,OAAO,CAACC,EAA4BC,IAAe,CACtE,IAAMC,EAAOd,EAAWa,CAAK,EACvBE,EAAUhB,EAAWe,CAAI,EAAIA,EAAON,EAE1C,OAAIO,IACAH,EAAIG,CAAO,IAAM,CAAC,EAClBH,EAAIG,CAAO,EAAE,KAAKF,CAAK,GAGpBD,CACX,EAAG,CAAC,CAAC,CACT,CAER,OAAS,EAAQ,CACb,MAAM,CACV,CACJ", "names": ["to<PERSON><PERSON><PERSON>", "isNotEmpty", "getDotPath", "safeParse", "safeParseAsync", "valibotResolver", "schema", "schemaOptions", "resolverOptions", "values", "name", "sync", "raw", "result", "acc", "error", "path", "path<PERSON><PERSON>"]}