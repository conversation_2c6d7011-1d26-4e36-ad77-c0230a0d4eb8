{"version": 3, "sources": ["../../../src/resolvers/yup/index.ts"], "sourcesContent": ["import { toValues } from '@primeuix/forms/utils';\nimport { isNotEmpty } from '@primeuix/utils/object';\nimport type { AnyObjectSchema, ValidateOptions, ValidationError } from 'yup';\nimport type { ResolverOptions, ResolverResult } from '..';\n\nexport const yupResolver =\n    <T>(schema: AnyObjectSchema, schemaOptions?: ValidateOptions<any>, resolverOptions?: ResolverOptions) =>\n    async ({ values, name }: any): Promise<ResolverResult<T>> => {\n        const { sync = false, raw = false } = resolverOptions || {};\n\n        try {\n            const result = await schema[sync ? 'validateSync' : 'validate'](values, { abortEarly: false, ...schemaOptions });\n\n            return {\n                values: toValues(raw ? values : result, name),\n                errors: {}\n            };\n        } catch (e: any) {\n            if (e?.inner) {\n                return {\n                    values: toValues(raw ? values : undefined, name),\n                    errors: e.inner.reduce((acc: Record<string, any[]>, error: ValidationError) => {\n                        const pathKey = isNotEmpty(error.path) ? error.path : name;\n\n                        if (pathKey) {\n                            acc[pathKey] ||= [];\n                            acc[pathKey].push(error);\n                        }\n\n                        return acc;\n                    }, {})\n                };\n            }\n\n            throw e;\n        }\n    };\n"], "mappings": "AAAA,OAAS,YAAAA,MAAgB,wBACzB,OAAS,cAAAC,MAAkB,yBAIpB,IAAMC,EACT,CAAIC,EAAyBC,EAAsCC,IACnE,MAAO,CAAE,OAAAC,EAAQ,KAAAC,CAAK,IAAuC,CACzD,GAAM,CAAE,KAAAC,EAAO,GAAO,IAAAC,EAAM,EAAM,EAAIJ,GAAmB,CAAC,EAE1D,GAAI,CACA,IAAMK,EAAS,MAAMP,EAAOK,EAAO,eAAiB,UAAU,EAAEF,EAAQ,CAAE,WAAY,GAAO,GAAGF,CAAc,CAAC,EAE/G,MAAO,CACH,OAAQJ,EAASS,EAAMH,EAASI,EAAQH,CAAI,EAC5C,OAAQ,CAAC,CACb,CACJ,OAASI,EAAQ,CACb,GAAIA,GAAG,MACH,MAAO,CACH,OAAQX,EAASS,EAAMH,EAAS,OAAWC,CAAI,EAC/C,OAAQI,EAAE,MAAM,OAAO,CAACC,EAA4BC,IAA2B,CAC3E,IAAMC,EAAUb,EAAWY,EAAM,IAAI,EAAIA,EAAM,KAAON,EAEtD,OAAIO,IACAF,EAAIE,CAAO,IAAM,CAAC,EAClBF,EAAIE,CAAO,EAAE,KAAKD,CAAK,GAGpBD,CACX,EAAG,CAAC,CAAC,CACT,EAGJ,MAAMD,CACV,CACJ", "names": ["to<PERSON><PERSON><PERSON>", "isNotEmpty", "yupResolver", "schema", "schemaOptions", "resolverOptions", "values", "name", "sync", "raw", "result", "e", "acc", "error", "path<PERSON><PERSON>"]}