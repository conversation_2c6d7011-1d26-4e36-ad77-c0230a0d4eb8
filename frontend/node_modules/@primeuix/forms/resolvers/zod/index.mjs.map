{"version": 3, "sources": ["../../../src/resolvers/zod/index.ts"], "sourcesContent": ["import { toValues } from '@primeuix/forms/utils';\nimport { isNotEmpty } from '@primeuix/utils/object';\nimport type { ParseParams, Schema } from 'zod';\nimport type { ResolverOptions, ResolverResult } from '..';\n\nexport const zodResolver =\n    <T extends Schema<any, any>>(schema: T, schemaOptions?: ParseParams, resolverOptions?: ResolverOptions) =>\n    async ({ values, name }: any): Promise<ResolverResult<T>> => {\n        const { sync = false, raw = false } = resolverOptions || {};\n\n        try {\n            const result = await schema[sync ? 'parse' : 'parseAsync'](values, schemaOptions);\n\n            return {\n                values: toValues(raw ? values : result, name),\n                errors: {}\n            };\n        } catch (e: any) {\n            if (Array.isArray(e?.errors)) {\n                return {\n                    values: toValues(raw ? values : undefined, name),\n                    errors: e.errors.reduce((acc: Record<string, any[]>, error: any) => {\n                        const pathKey = isNotEmpty(error.path) ? error.path.join('.') : name;\n\n                        if (pathKey) {\n                            acc[pathKey] ||= [];\n                            acc[pathKey].push(error);\n                        }\n\n                        return acc;\n                    }, {})\n                };\n            }\n\n            throw e;\n        }\n    };\n"], "mappings": "AAAA,OAAS,YAAAA,MAAgB,wBACzB,OAAS,cAAAC,MAAkB,yBAIpB,IAAMC,EACT,CAA6BC,EAAWC,EAA6BC,IACrE,MAAO,CAAE,OAAAC,EAAQ,KAAAC,CAAK,IAAuC,CACzD,GAAM,CAAE,KAAAC,EAAO,GAAO,IAAAC,EAAM,EAAM,EAAIJ,GAAmB,CAAC,EAE1D,GAAI,CACA,IAAMK,EAAS,MAAMP,EAAOK,EAAO,QAAU,YAAY,EAAEF,EAAQF,CAAa,EAEhF,MAAO,CACH,OAAQJ,EAASS,EAAMH,EAASI,EAAQH,CAAI,EAC5C,OAAQ,CAAC,CACb,CACJ,OAASI,EAAQ,CACb,GAAI,MAAM,QAAQA,GAAG,MAAM,EACvB,MAAO,CACH,OAAQX,EAASS,EAAMH,EAAS,OAAWC,CAAI,EAC/C,OAAQI,EAAE,OAAO,OAAO,CAACC,EAA4BC,IAAe,CAChE,IAAMC,EAAUb,EAAWY,EAAM,IAAI,EAAIA,EAAM,KAAK,KAAK,GAAG,EAAIN,EAEhE,OAAIO,IACAF,EAAIE,CAAO,IAAM,CAAC,EAClBF,EAAIE,CAAO,EAAE,KAAKD,CAAK,GAGpBD,CACX,EAAG,CAAC,CAAC,CACT,EAGJ,MAAMD,CACV,CACJ", "names": ["to<PERSON><PERSON><PERSON>", "isNotEmpty", "zodResolver", "schema", "schemaOptions", "resolverOptions", "values", "name", "sync", "raw", "result", "e", "acc", "error", "path<PERSON><PERSON>"]}