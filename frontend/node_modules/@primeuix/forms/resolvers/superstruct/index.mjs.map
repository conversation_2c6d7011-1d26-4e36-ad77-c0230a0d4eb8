{"version": 3, "sources": ["../../../src/resolvers/superstruct/index.ts"], "sourcesContent": ["import { toValues } from '@primeuix/forms/utils';\nimport { isNotEmpty } from '@primeuix/utils/object';\nimport type { Struct } from 'superstruct';\nimport type { ResolverOptions, ResolverResult } from '..';\n\nexport const superStructResolver =\n    <T>(schema: Struct<T>, schemaOptions?: any, resolverOptions?: ResolverOptions) =>\n    async ({ values, name }: any): Promise<ResolverResult<T>> => {\n        const { raw = false } = resolverOptions || {};\n\n        try {\n            const [errors, data] = schema.validate(values, schemaOptions);\n\n            if (errors) {\n                return {\n                    values: toValues(undefined, name),\n                    errors: errors.failures().reduce((acc: Record<string, any[]>, error: any) => {\n                        const pathKey = isNotEmpty(error.path) ? error.path.join('.') : name;\n\n                        if (pathKey) {\n                            acc[pathKey] ||= [];\n                            acc[pathKey].push(error);\n                        }\n\n                        return acc;\n                    }, {})\n                };\n            }\n\n            return {\n                values: toValues(raw ? values : data, name),\n                errors: {}\n            };\n        } catch (e: any) {\n            throw e;\n        }\n    };\n"], "mappings": "AAAA,OAAS,YAAAA,MAAgB,wBACzB,OAAS,cAAAC,MAAkB,yBAIpB,IAAMC,EACT,CAAIC,EAAmBC,EAAqBC,IAC5C,MAAO,CAAE,OAAAC,EAAQ,KAAAC,CAAK,IAAuC,CACzD,GAAM,CAAE,IAAAC,EAAM,EAAM,EAAIH,GAAmB,CAAC,EAE5C,GAAI,CACA,GAAM,CAACI,EAAQC,CAAI,EAAIP,EAAO,SAASG,EAAQF,CAAa,EAE5D,OAAIK,EACO,CACH,OAAQT,EAAS,OAAWO,CAAI,EAChC,OAAQE,EAAO,SAAS,EAAE,OAAO,CAACE,EAA4BC,IAAe,CACzE,IAAMC,EAAUZ,EAAWW,EAAM,IAAI,EAAIA,EAAM,KAAK,KAAK,GAAG,EAAIL,EAEhE,OAAIM,IACAF,EAAIE,CAAO,IAAM,CAAC,EAClBF,EAAIE,CAAO,EAAE,KAAKD,CAAK,GAGpBD,CACX,EAAG,CAAC,CAAC,CACT,EAGG,CACH,OAAQX,EAASQ,EAAMF,EAASI,EAAMH,CAAI,EAC1C,OAAQ,CAAC,CACb,CACJ,OAASO,EAAQ,CACb,MAAMA,CACV,CACJ", "names": ["to<PERSON><PERSON><PERSON>", "isNotEmpty", "superStructResolver", "schema", "schemaOptions", "resolverOptions", "values", "name", "raw", "errors", "data", "acc", "error", "path<PERSON><PERSON>", "e"]}