"use strict";var PrimeUIX=PrimeUIX||{};PrimeUIX.Forms=PrimeUIX.Forms||{};PrimeUIX.Forms.Valibot=(()=>{var v=Object.defineProperty;var T=Object.getOwnPropertyDescriptor;var S=Object.getOwnPropertyNames,g=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var A=(e,n,r)=>n in e?v(e,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[n]=r,p=(e,n)=>{for(var r in n||(n={}))w.call(n,r)&&A(e,r,n[r]);if(g)for(var r of g(n))R.call(n,r)&&A(e,r,n[r]);return e};var P=(e,n)=>{for(var r in n)v(e,r,{get:n[r],enumerable:!0})},F=(e,n,r,i)=>{if(n&&typeof n=="object"||typeof n=="function")for(let u of S(n))!w.call(e,u)&&u!==r&&v(e,u,{get:()=>n[u],enumerable:!(i=T(n,u))||i.enumerable});return e};var N=e=>F(v({},"__esModule",{value:!0}),e);var m=(e,n,r)=>new Promise((i,u)=>{var C=t=>{try{c(r.next(t))}catch(s){u(s)}},k=t=>{try{c(r.throw(t))}catch(s){u(s)}},c=t=>t.done?i(t.value):Promise.resolve(t.value).then(C,k);c((r=r.apply(e,n)).next())});var W={};P(W,{valibotResolver:()=>K});function y(e){return e==null||e===""||Array.isArray(e)&&e.length===0||!(e instanceof Date)&&typeof e=="object"&&Object.keys(e).length===0}function l(e){return!y(e)}function f(e,n=!0){return e instanceof Object&&e.constructor===Object&&(n||Object.keys(e).length!==0)}var x=(e,n)=>f(e)&&e.hasOwnProperty(n)?e:n?{[n]:e}:e;var Dr=new RegExp("^(?:[\\u{1F1E6}-\\u{1F1FF}]{2}|\\u{1F3F4}[\\u{E0061}-\\u{E007A}]{2}[\\u{E0030}-\\u{E0039}\\u{E0061}-\\u{E007A}]{1,3}\\u{E007F}|(?:\\p{Emoji}\\uFE0F\\u20E3?|\\p{Emoji_Modifier_Base}\\p{Emoji_Modifier}?|\\p{Emoji_Presentation})(?:\\u200D(?:\\p{Emoji}\\uFE0F\\u20E3?|\\p{Emoji_Modifier_Base}\\p{Emoji_Modifier}?|\\p{Emoji_Presentation}))*)+$","u");var o;function $(e){var n,r,i;return{lang:(n=e==null?void 0:e.lang)!=null?n:o==null?void 0:o.lang,message:e==null?void 0:e.message,abortEarly:(r=e==null?void 0:e.abortEarly)!=null?r:o==null?void 0:o.abortEarly,abortPipeEarly:(i=e==null?void 0:e.abortPipeEarly)!=null?i:o==null?void 0:o.abortPipeEarly}}function O(e){if(e.path){let n="";for(let r of e.path)if(typeof r.key=="string"||typeof r.key=="number")n?n+=`.${r.key}`:n+=r.key;else return null;return n}return null}function j(e,n,r){let i=e._run({typed:!1,value:n},$(r));return{typed:i.typed,success:!i.issues,output:i.value,issues:i.issues}}function q(e,n,r){return m(this,null,function*(){let i=yield e._run({typed:!1,value:n},$(r));return{typed:i.typed,success:!i.issues,output:i.value,issues:i.issues}})}var K=(e,n,r)=>C=>m(void 0,[C],function*({values:i,name:u}){var t;let{sync:k=!1,raw:c=!1}=r||{};try{let s=k?j(e,i,p({abortPipeEarly:!1},n)):yield q(e,i,p({abortPipeEarly:!1},n));return s.success?{values:x(c?i:s.output,u),errors:{}}:{values:x(c?i:void 0,u),errors:(t=s.issues)==null?void 0:t.reduce((a,b)=>{let E=O(b),h=l(E)?E:u;return h&&(a[h]||(a[h]=[]),a[h].push(b)),a},{})}}catch(s){throw s}});return N(W);})();
