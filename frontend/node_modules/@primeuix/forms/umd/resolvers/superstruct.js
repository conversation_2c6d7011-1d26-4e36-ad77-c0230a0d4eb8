"use strict";var PrimeUIX=PrimeUIX||{};PrimeUIX.Forms=PrimeUIX.Forms||{};PrimeUIX.Forms.Superstruct=(()=>{var x=Object.defineProperty;var b=Object.getOwnPropertyDescriptor;var O=Object.getOwnPropertyNames;var F=Object.prototype.hasOwnProperty;var D=(e,t)=>{for(var n in t)x(e,n,{get:t[n],enumerable:!0})},A=(e,t,n,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of O(t))!F.call(e,r)&&r!==n&&x(e,r,{get:()=>t[r],enumerable:!(s=b(t,r))||s.enumerable});return e};var S=e=>A(x({},"__esModule",{value:!0}),e);var C=(e,t,n)=>new Promise((s,r)=>{var E=o=>{try{a(n.next(o))}catch(u){r(u)}},c=o=>{try{a(n.throw(o))}catch(u){r(u)}},a=o=>o.done?s(o.value):Promise.resolve(o.value).then(E,c);a((n=n.apply(e,t)).next())});var k={};D(k,{superStructResolver:()=>V});function l(e){return e==null||e===""||Array.isArray(e)&&e.length===0||!(e instanceof Date)&&typeof e=="object"&&Object.keys(e).length===0}function f(e){return!l(e)}function i(e,t=!0){return e instanceof Object&&e.constructor===Object&&(t||Object.keys(e).length!==0)}var y=(e,t)=>i(e)&&e.hasOwnProperty(t)?e:t?{[t]:e}:e;var V=(e,t,n)=>E=>C(void 0,[E],function*({values:s,name:r}){let{raw:c=!1}=n||{};try{let[a,o]=e.validate(s,t);return a?{values:y(void 0,r),errors:a.failures().reduce((u,g)=>{let p=f(g.path)?g.path.join("."):r;return p&&(u[p]||(u[p]=[]),u[p].push(g)),u},{})}:{values:y(c?s:o,r),errors:{}}}catch(a){throw a}});return S(k);})();
