"use strict";var PrimeUIX=PrimeUIX||{};PrimeUIX.Forms=PrimeUIX.Forms||{};PrimeUIX.Forms.Zod=(()=>{var x=Object.defineProperty;var b=Object.getOwnPropertyDescriptor;var O=Object.getOwnPropertyNames;var F=Object.prototype.hasOwnProperty;var A=(e,t)=>{for(var o in t)x(e,o,{get:t[o],enumerable:!0})},D=(e,t,o,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of O(t))!F.call(e,n)&&n!==o&&x(e,n,{get:()=>t[n],enumerable:!(a=b(t,n))||a.enumerable});return e};var S=e=>D(x({},"__esModule",{value:!0}),e);var C=(e,t,o)=>new Promise((a,n)=>{var E=r=>{try{s(o.next(r))}catch(u){n(u)}},c=r=>{try{s(o.throw(r))}catch(u){n(u)}},s=r=>r.done?a(r.value):Promise.resolve(r.value).then(E,c);s((o=o.apply(e,t)).next())});var V={};A(V,{zodResolver:()=>$});function l(e){return e==null||e===""||Array.isArray(e)&&e.length===0||!(e instanceof Date)&&typeof e=="object"&&Object.keys(e).length===0}function f(e){return!l(e)}function i(e,t=!0){return e instanceof Object&&e.constructor===Object&&(t||Object.keys(e).length!==0)}var y=(e,t)=>i(e)&&e.hasOwnProperty(t)?e:t?{[t]:e}:e;var $=(e,t,o)=>E=>C(void 0,[E],function*({values:a,name:n}){let{sync:c=!1,raw:s=!1}=o||{};try{let r=yield e[c?"parse":"parseAsync"](a,t);return{values:y(s?a:r,n),errors:{}}}catch(r){if(Array.isArray(r==null?void 0:r.errors))return{values:y(s?a:void 0,n),errors:r.errors.reduce((u,g)=>{let p=f(g.path)?g.path.join("."):n;return p&&(u[p]||(u[p]=[]),u[p].push(g)),u},{})};throw r}});return S(V);})();
