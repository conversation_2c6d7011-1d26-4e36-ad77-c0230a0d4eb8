"use strict";var PrimeUIX=PrimeUIX||{};PrimeUIX.Forms=PrimeUIX.Forms||{};PrimeUIX.Forms.Joi=(()=>{var m=Object.defineProperty;var D=Object.getOwnPropertyDescriptor;var S=Object.getOwnPropertyNames,h=Object.getOwnPropertySymbols;var T=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var E=(e,t,r)=>t in e?m(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var r in t||(t={}))T.call(t,r)&&E(e,r,t[r]);if(h)for(var r of h(t))R.call(t,r)&&E(e,r,t[r]);return e};var I=(e,t)=>{for(var r in t)m(e,r,{get:t[r],enumerable:!0})},L=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of S(t))!T.call(e,o)&&o!==r&&m(e,o,{get:()=>t[o],enumerable:!(a=D(t,o))||a.enumerable});return e};var w=e=>L(m({},"__esModule",{value:!0}),e);var b=(e,t,r)=>new Promise((a,o)=>{var A=n=>{try{s(r.next(n))}catch(u){o(u)}},x=n=>{try{s(r.throw(n))}catch(u){o(u)}},s=n=>n.done?a(n.value):Promise.resolve(n.value).then(A,x);s((r=r.apply(e,t)).next())});var Z={};I(Z,{joiResolver:()=>P});function l(e){return e==null||e===""||Array.isArray(e)&&e.length===0||!(e instanceof Date)&&typeof e=="object"&&Object.keys(e).length===0}function f(e){return!l(e)}function i(e,t=!0){return e instanceof Object&&e.constructor===Object&&(t||Object.keys(e).length!==0)}var C=(e,t)=>i(e)&&e.hasOwnProperty(t)?e:t?{[t]:e}:e;var P=(e,t,r)=>A=>b(void 0,[A],function*({values:a,name:o}){let{sync:x=!1,raw:s=!1}=r||{};try{let n=yield e[x?"validate":"validateAsync"](a,c({abortEarly:!1},t));return{values:C(s?a:n,o),errors:{}}}catch(n){if(n!=null&&n.details)return{values:C(s?a:void 0,o),errors:n.details.reduce((u,d)=>{let p=f(d.path)?d.path.join("."):o;return p&&(u[p]||(u[p]=[]),u[p].push(d)),u},{})};throw n}});return w(Z);})();
