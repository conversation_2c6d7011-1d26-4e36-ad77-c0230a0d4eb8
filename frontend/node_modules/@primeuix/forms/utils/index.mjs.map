{"version": 3, "sources": ["../../src/utils/index.ts"], "sourcesContent": ["import { isObject } from '@primeuix/utils/object';\n\nexport const toValues = (value: any, name?: string) => {\n    if (isObject(value) && value.hasOwnProperty(name)) {\n        return value;\n    }\n\n    return name ? { [name]: value } : value;\n};\n"], "mappings": "AAAA,OAAS,YAAAA,MAAgB,yBAElB,IAAMC,EAAW,CAACC,EAAYC,IAC7BH,EAASE,CAAK,GAAKA,EAAM,eAAeC,CAAI,EACrCD,EAGJC,EAAO,CAAE,CAACA,CAAI,EAAGD,CAAM,EAAIA", "names": ["isObject", "to<PERSON><PERSON><PERSON>", "value", "name"]}